BASE_URL=https://mobile-dev-gurkha-id.asean-accesstrade.net/
CLIENT_ID='koc-mobile-app'
SUPER_POINT_PAGE_LINK='https://superpoint-staging.accesstrade.global/autologin.php?secret='
SHOW_DEBUG_TOOLS=false

# Cache Configuration (sizes in MB)
MAX_API_CACHE_SIZE=50
MAX_IMAGE_CACHE_SIZE=100
MAX_TOTAL_CACHE_SIZE=200

# Facebook Sign In
FACEBOOK_APP_ID=YOUR_FACEBOOK_APP_ID
FACEBOOK_APP_SECRET=YOUR_FACEBOOK_APP_SECRET
FACEBOOK_CLIENT_TOKEN=YOUR_FACEBOOK_CLIENT_TOKEN
FACEBOOK_REDIRECT_URI=https://YOUR_DOMAIN/auth/facebook/callback

# Google OAuth Credentials
# Common Project Info (Reference for GoogleService-Info.plist & google-services.json)
# GOOGLE_PROJECT_ID=YOUR_GOOGLE_PROJECT_ID
# GOOGLE_PROJECT_NUMBER=YOUR_GOOGLE_PROJECT_NUMBER # (GCM_SENDER_ID)
# GOOGLE_API_KEY=YOUR_GOOGLE_API_KEY # (API_KEY in GoogleService-Info.plist)

# iOS
# GOOGLE_CLIENT_ID_IOS=YOUR_IOS_CLIENT_ID.apps.googleusercontent.com # (CLIENT_ID in GoogleService-Info.plist)
# Key used by pre_build script to update Info.plist URL Schemes:
GOOGLE_REVERSED_CLIENT_ID_IOS=com.googleusercontent.apps.YOUR_IOS_REVERSED_CLIENT_ID_SUFFIX # (REVERSED_CLIENT_ID in GoogleService-Info.plist)

# Android
# GOOGLE_CLIENT_ID_ANDROID=YOUR_ANDROID_CLIENT_ID.apps.googleusercontent.com # (client_id type 3 in google-services.json)
# GOOGLE_ANDROID_SHA1=YOUR_ANDROID_DEBUG_OR_RELEASE_SHA1 # For configuring Firebase/Google Sign In

# Web Client ID
# -----------
# Often used for Android's google_web_client_id or server_client_id string resource
GOOGLE_CLIENT_ID_WEB=YOUR_WEB_CLIENT_ID.apps.googleusercontent.com
GOOGLE_REDIRECT_URI_WEB=YOUR_WEB_REDIRECT_URI

# Android Signing Configuration
# ----------------------------
# These values are used for signing the Android release build
# KEY_ALIAS: The alias name for your signing key
KEY_ALIAS=YOUR_KEY_ALIAS
# KEY_PASSWORD: The password for your signing key
KEY_PASSWORD=YOUR_KEY_PASSWORD
# STORE_FILE: Path to your keystore file (relative to android/app directory)
STORE_FILE=release.keystore
# STORE_PASSWORD: The password for your keystore
STORE_PASSWORD=YOUR_STORE_PASSWORD