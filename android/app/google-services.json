{"project_info": {"project_number": "185534151793", "project_id": "staging-at-koc", "storage_bucket": "staging-at-koc.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:185534151793:android:e9fe5844e3481d3fc02fd0", "android_client_info": {"package_name": "jp.ne.interspace.kocapp"}}, "oauth_client": [{"client_id": "185534151793-4djrud9mn03312gsavr75fsujpuo6d1u.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "jp.ne.interspace.kocapp", "certificate_hash": "ef87b66012d6d694c9301a003c0154abf6d9aaae"}}, {"client_id": "185534151793-5nmjg88noi0pfqbncg5r680c6oci6i7u.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "jp.ne.interspace.kocapp", "certificate_hash": "b89c27df23f35e9e2b66c681b952689cb6f5c26d"}}, {"client_id": "185534151793-cmagnd13iigca9sl6rbipj1d0pev1e4r.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAAwt0GMwKY9T5kOmDm_4NFt0D79-6cNS0"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "185534151793-cmagnd13iigca9sl6rbipj1d0pev1e4r.apps.googleusercontent.com", "client_type": 3}, {"client_id": "185534151793-41ud48ui1bomftpdi81n6c8g8j6cd579.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "jp.ne.interspace.kocapp"}}]}}}], "configuration_version": "1"}