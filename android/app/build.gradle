plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    def keystoreProperties = new Properties()
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
    keystoreProperties.each { key, value ->
        if (!project.hasProperty(key)) {
            project.ext.set(key, value)
        }
    }
}

android {
    namespace = "jp.ne.interspace.kocapp"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "jp.ne.interspace.kocapp"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = Math.max(flutter.minSdkVersion, 23)
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            // Priority: Environment variables > key.properties > defaults
            keyAlias = System.getenv("KEY_ALIAS")
                ?: (project.hasProperty("keyAlias") ? project.getProperty("keyAlias") : "koc-mobile")

            keyPassword = System.getenv("KEY_PASSWORD")
                ?: (project.hasProperty("keyPassword") ? project.getProperty("keyPassword") : "koc_release_password")

            def storeFilePath = System.getenv("STORE_FILE")
                ?: (project.hasProperty("storeFile") ? project.getProperty("storeFile") : "release.keystore")
            def keystoreFile = rootProject.file(storeFilePath)
            if (!keystoreFile.exists()) {
                keystoreFile = file(storeFilePath)
            }
            storeFile = keystoreFile

            storePassword = System.getenv("STORE_PASSWORD")
                ?: (project.hasProperty("storePassword") ? project.getProperty("storePassword") : "koc_release_password")
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            shrinkResources false
            minifyEnabled false
        }
        release {
            signingConfig signingConfigs.release
            shrinkResources true
            minifyEnabled true
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Add the Firebase BoM - using stable version
    implementation platform('com.google.firebase:firebase-bom:32.7.4')

    // Add Firebase Auth dependency
    implementation 'com.google.firebase:firebase-auth'

    // Google Sign-In dependency
    implementation 'com.google.android.gms:play-services-auth:21.0.0'

    // Facebook SDK dependencies
    implementation 'com.facebook.android:facebook-android-sdk:latest.release'
}
