{"appTitle": "koc_app", "helloWorld": "Hello World!", "signInOrCreateAccount": "Sign in or create a free account to continue", "continueWithFacebook": "Continue with Facebook", "continueWithGoogle": "Continue with Google", "existsEmailContinueLogin": "This email address is already exist, click Continue to <PERSON>gin", "createAnAccount": "Create an account", "termsDefinitionAdultPornographyAndViolence": "透過含有非法、暴力或色情內容的網站進行宣傳。", "termsDefinitionBrandBidding": "透過對品牌術語和關鍵字（包括品牌的任何變體或拼字錯誤）出價來建立廣告，以獲得搜尋引擎平台上的有利清單。", "termsDefinitionCashback": "使用現金回饋來增加廣告商網站的流量。", "termsDefinitionCouponAndDiscountCodes": "使用優惠券、折扣代碼、代金券或類似類型來吸引廣告商網站的流量。", "termsDefinitionDeepLinking": "將訪客連結到子頁面。通常是廣告商網站上的特定產品頁面而不是主頁。", "termsDefinitionDirectLinking": "將訪客從廣告直接引導至廣告商的報價，從而消除對登陸頁面的要求。", "termsDefinitionDisplayAds": "在網站上以橫幅或其他視覺格式的形式使用付費廣告來吸引廣告主網站的流量。", "termsDefinitionDisplayBanner": "使用網站上顯示的橫幅來增加廣告商網站的流量，而無需付費廣告。", "termsDefinitionEmailMarketing": "使用電子郵件行銷來增加廣告商網站的流量。", "termsDefinitionGambling": "透過含有賭博內容的網站進行宣傳。", "termsDefinitionIncentiveTrafficLoyalty": "使用會員積分、哩程或其他類似的獎勵補償來增加廣告主網站的流量。", "termsDefinitionInterstitial": "使用插頁式廣告來吸引廣告主網站的流量。\n\n插頁式：網站、應用程式或遊戲的使用者體驗中標準互動之間的全螢幕展示位置。例如，新聞媒體網站上兩篇文章之間的導航或超休閒遊戲等級之間的轉換都可以保證其中一個廣告。", "termsDefinitionNativeAds": "創建與網站內容融合的廣告，以贊助文章或推薦的形式出現，以吸引廣告主網站的流量。", "termsDefinitionPopUpPopUnder": "使用彈出廣告來增加廣告商網站的流量。\n\n彈出視窗：這些是用戶訪問頁面後打開的視窗或選項卡。訪客將在目前瀏覽視窗（或有時在新標籤上）看到「彈出」廣告。\n\n背投式廣告：唯一的區別是背投式廣告出現在訪客目前看到的頁面下方，並且在用戶關閉此頁面後廣告可見。", "termsDefinitionPushNotification": "在桌面或行動裝置上使用推播通知來增加廣告商網站的流量。", "termsDefinitionSearchEngineMarketing": "透過購買廣告空間來促進搜尋引擎結果出現在所有其他排名之上。", "termsDefinitionSelfConversion": "點擊您自己的聯盟連結並自行交易。", "termsDefinitionSocialMediaAds": "在社群媒體網站、線上社群或其他社群入口網站（例如 Facebook、Instagram、Youtube、TikTok 等）上使用付費廣告來吸引廣告主網站的流量。", "termsDefinitionSocialMediaPlatform": "使用社群媒體網站、線上社群或其他社群入口網站（例如 Facebook、Instagram、Youtube、TikTok 等）在不付費廣告的情況下為廣告主網站帶來流量。", "termsDefinitionSocialMessagerApp": "使用 WhatsApp、微信、Line、Messager 等社群訊息管道來吸引廣告主網站的流量。", "termsDefinitionSubAffiliateNetwork": "透過個體出版商網絡進行推廣。\n* 如果允許，發布商必須完全透明地了解子關聯公司的流量來源。", "_voucherCode_": "", "voucherCategory01": "Travel & Hotels", "voucherCategory02": "Electronics", "voucherCategory03": "Fashion", "voucherCategory04": "Beauty & Health", "voucherCategory05": "Home & Living", "voucherCategory06": "Food & Grocery"}