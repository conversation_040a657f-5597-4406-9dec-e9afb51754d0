import 'dart:async';
import 'dart:developer' as developer;

import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/secure_storage_helper.dart';

import '../src/modules/authentication/data/models/auth_token_info.dart';

class TokenInterceptor extends Interceptor {
  static const Duration _bufferDuration = Duration(seconds: 60);
  static final String _clientId = dotenv.env['CLIENT_ID'] ?? 'koc-mobile-app';

  final Dio dio;
  static final SecureStorageHelper storage = SecureStorageHelper();
  DateTime? _lastRefreshTime;
  Completer<AuthTokenInfo?>? _refreshTokenCompleter;
  bool _isRefreshing = false;

  // Cache token details to reduce storage reads
  AuthTokenInfo? _cachedToken;

  TokenInterceptor(this.dio);

  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // Skip token handling for requests that don't require it
    if (!_requiresToken(options)) {
      return handler.next(options);
    }

    try {
      final token = await _getValidToken();

      if (token == null) {
        return _handleNoToken(handler, options);
      }

      options.headers['Authorization'] = 'Bearer ${token.token}';
      handler.next(options);
    } catch (e) {
      developer.log('[TokenInterceptor] Error in onRequest: $e');
      _handleError(handler, options, e);
    }
  }

  @override
  Future<void> onError(
      DioException err, ErrorInterceptorHandler handler) async {
    if (!_requiresToken(err.requestOptions) ||
        err.response?.statusCode != 401) {
      return handler.next(err);
    }

    try {
      // Handle 401 Unauthorized errors
      final newToken = await _refreshAndGetToken();

      if (newToken == null) {
        _redirectToLogin();
        return handler.next(err);
      }

      // Retry the original request
      return await _retryRequestWithNewToken(
          err.requestOptions, handler, newToken.token);
    } catch (e) {
      developer.log('[TokenInterceptor] Error handling 401: $e');
      _redirectToLogin();
      handler.next(err);
    }
  }

  /// Refreshes token if needed and returns a valid token
  Future<AuthTokenInfo?> _getValidToken() async {
    // Use cached token if available and valid
    if (_cachedToken != null && _isTokenTimeValid(_cachedToken!.expiresIn)) {
      return _cachedToken;
    }

    // Check storage for token
    final storedToken = await _getStoredToken();
    if (storedToken == null) {
      return null;
    }

    // Check if token is expired
    if (!_isTokenTimeValid(storedToken.expiresIn)) {
      return await _refreshAndGetToken();
    }

    // Cache valid token for future use
    _cachedToken = storedToken;
    return storedToken;
  }

  /// Gets a fresh token by refreshing, handles concurrency
  Future<AuthTokenInfo?> _refreshAndGetToken() async {
    // Return early if we refreshed recently
    if (_lastRefreshTime != null &&
        DateTime.now().difference(_lastRefreshTime!) < _bufferDuration) {
      return _cachedToken ?? await _getStoredToken();
    }

    // Use completer pattern for concurrent requests
    if (_refreshTokenCompleter != null) {
      return await _refreshTokenCompleter!.future;
    }

    _isRefreshing = true;
    _refreshTokenCompleter = Completer<AuthTokenInfo?>();

    try {
      final refreshToken =
          await storage.read(InstanceConstants.refreshTokenKey);
      if (refreshToken == null) {
        _refreshTokenCompleter!.complete(null);
        return null;
      }

      final response = await _doRefreshToken(refreshToken);
      if (response != null) {
        await _setToken(response);
        _cachedToken = response;
      }

      _lastRefreshTime = DateTime.now();
      _refreshTokenCompleter!.complete(response);
      return response;
    } catch (e) {
      developer.log('[TokenInterceptor] Token refresh failed: $e');
      _refreshTokenCompleter!.complete(null);
      return null;
    } finally {
      _isRefreshing = false;
      _refreshTokenCompleter = null;
    }
  }

  Future<AuthTokenInfo?> _doRefreshToken(String refreshToken) async {
    try {
      final refreshDio = Dio(BaseOptions(
        baseUrl: dio.options.baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ));

      final response = await refreshDio.post(
        '/v1/auth/refresh-token',
        data: RefreshTokenRequest(
          refreshToken: refreshToken,
        ).toJson(),
        options: Options(headers: {
          'Content-Type': 'application/json',
          'clientId': _clientId,
        }),
      );

      return AuthTokenInfo.fromJson(response.data);
    } catch (e) {
      developer.log('[TokenInterceptor] Error refreshing token: $e');
      return null;
    }
  }

  Future<AuthTokenInfo?> _getStoredToken() async {
    try {
      final token = await storage.read(InstanceConstants.tokenKey);
      final refreshToken =
          await storage.read(InstanceConstants.refreshTokenKey);
      final expiresInStr = await storage.read(InstanceConstants.expiresInKey);
      final refreshExpiresInStr =
          await storage.read(InstanceConstants.refreshExpiresInKey);

      if (token == null ||
          refreshToken == null ||
          expiresInStr == null ||
          refreshExpiresInStr == null) {
        return null;
      }

      return AuthTokenInfo(
        token: token,
        refreshToken: refreshToken,
        expiresIn: int.parse(expiresInStr),
        refreshExpiresIn: int.parse(refreshExpiresInStr),
      );
    } catch (e) {
      developer.log('[TokenInterceptor] Error getting stored token: $e');
      return null;
    }
  }

  Future<void> _setToken(AuthTokenInfo authTokenInfo) async {
    await storage.write(InstanceConstants.tokenKey, authTokenInfo.token);
    await storage.write(
        InstanceConstants.refreshTokenKey, authTokenInfo.refreshToken);
    await storage.write(
        InstanceConstants.expiresInKey, authTokenInfo.expiresIn.toString());
    await storage.write(InstanceConstants.refreshExpiresInKey,
        authTokenInfo.refreshExpiresIn.toString());
  }

  bool _isTokenTimeValid(int expiresIn) {
    final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return currentTime < (expiresIn - _bufferDuration.inSeconds);
  }

  void _redirectToLogin() {
    storage.deleteAll();
    _cachedToken = null;
    Modular.to.pushNamedAndRemoveUntil('/', (route) => false);
  }

  Future<void> _retryRequestWithNewToken(
    RequestOptions options,
    ErrorInterceptorHandler handler,
    String newToken,
  ) async {
    options.headers['Authorization'] = 'Bearer $newToken';
    try {
      final response = await dio.fetch(options);
      handler.resolve(response);
    } catch (e) {
      handler.reject(DioException(
        requestOptions: options,
        error: e.toString(),
        type: DioExceptionType.unknown,
      ));
    }
  }

  bool _requiresToken(RequestOptions options) {
    return options.extra[InstanceConstants.requiresTokenKey] as bool? ?? true;
  }

  void _handleNoToken(
      RequestInterceptorHandler handler, RequestOptions options) {
    _redirectToLogin();
    handler.reject(DioException(
      requestOptions: options,
      error: 'No access token',
      type: DioExceptionType.unknown,
    ));
  }

  void _handleError(RequestInterceptorHandler handler, RequestOptions options,
      dynamic error) {
    handler.reject(DioException(
      requestOptions: options,
      error: error.toString(),
      type: DioExceptionType.unknown,
    ));
  }
}

class RefreshTokenRequest {
  final String refreshToken;

  RefreshTokenRequest({
    required this.refreshToken
  });

  Map<String, dynamic> toJson() {
    return {
      'refreshToken': refreshToken
    };
  }
}
