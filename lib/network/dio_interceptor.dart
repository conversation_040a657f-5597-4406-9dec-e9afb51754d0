import 'dart:developer' as developer;

import 'package:dio/dio.dart';

class DioInterceptor extends Interceptor {
  static bool _hasLoggedFullToken = false;
  static String? _lastFullTokenLogged;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    developer.log('\x1B[34m🚀 [REQUEST] ${options.method} ${options.uri}\x1B[0m');
    developer.log('\x1B[34m🔹 Headers: ${_maskSensitiveHeaders(options.headers)}\x1B[0m');
    developer.log('\x1B[34m📡 Data: ${options.data}\x1B[0m');
    super.onRequest(options, handler);
  }

  String _maskSensitiveHeaders(Map<String, dynamic> headers) {
    if (headers.isEmpty) return '{}';

    final maskedHeaders = <String, dynamic>{};

    headers.forEach((key, value) {
      if (key.toLowerCase() == 'authorization' && value is String) {
        maskedHeaders[key] = _maskAuthorizationHeader(value);
      } else {
        maskedHeaders[key] = value;
      }
    });

    return maskedHeaders.toString();
  }

  String _maskAuthorizationHeader(String authValue) {
    if (!authValue.toLowerCase().startsWith('bearer ')) {
      return authValue;
    }

    final token = authValue.substring(7);

    if (!_hasLoggedFullToken || _lastFullTokenLogged != token) {
      developer.log('\x1B[33m🔐 [FULL TOKEN - LOGGED ONCE] Authorization: $authValue\x1B[0m');
      _hasLoggedFullToken = true;
      _lastFullTokenLogged = token;
    }

    if (token.length <= 16) {
      return 'Bearer ${token.substring(0, 3)}...${token.substring(token.length - 3)}';
    } else {
      return 'Bearer ${token.substring(0, 10)}...${token.substring(token.length - 6)}';
    }
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    developer.log('\x1B[32m✅ [RESPONSE] ${response.requestOptions.method} ${response.requestOptions.uri}\x1B[0m');
    developer.log('\x1B[32m📦 Status: ${response.statusCode}\x1B[0m');
    developer.log('\x1B[32m📨 Response: \x1B[37m${response.data}\x1B[0m');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    developer.log('\x1B[31m❌ [ERROR] ${err.requestOptions.method} ${err.requestOptions.uri}\x1B[0m');
    developer.log('\x1B[31m🚨 Status: ${err.response?.statusCode}\x1B[0m');
    developer.log('\x1B[31m📛 Error: ${err.message}\x1B[0m');
    developer.log('\x1B[31m📬 Response: \x1B[37m${err.response?.data}\x1B[0m');
    super.onError(err, handler);
  }
}
