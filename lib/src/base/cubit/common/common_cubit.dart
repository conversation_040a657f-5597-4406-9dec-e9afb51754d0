import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/base/cubit/common/common_state.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';

import '../../../modules/authentication/data/models/auth_token_info.dart';
import '../../../shared/services/secure_storage_helper.dart';
import '../../../shared/services/shared_preferences_service.dart';

class CommonCubit extends BaseCubit<CommonState> {
  final sharedPreferencesService = Modular.get<SharedPreferencesService>();
  static final storage = SecureStorageHelper();

  CommonCubit() : super(const CommonState());

  FutureOr<void> loadingVisibility(bool isLoading) {
    emit(state.copyWith(isLoading: isLoading));
  }

  FutureOr<void> buttonLoadingVisibility(bool isLoadingButton) {
    emit(state.copyWith(isLoadingButton: isLoadingButton));
  }

  Future<void> saveToken(AuthTokenInfo authData) async {
    await storage.write(InstanceConstants.tokenKey, authData.token);
    await storage.write(InstanceConstants.refreshTokenKey, authData.refreshToken);
    await storage.write(InstanceConstants.expiresInKey, authData.expiresIn.toString());
    await storage.write(InstanceConstants.refreshExpiresInKey, authData.refreshExpiresIn.toString());
  }
}
