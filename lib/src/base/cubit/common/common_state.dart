import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';

part 'common_state.freezed.dart';
part 'common_state.g.dart';

@freezed
class CommonState extends BaseCubitState with _$CommonState {
  const factory CommonState({
    @Default(0) int loadingCount,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingButton,
  }) = _CommonState;

  factory CommonState.fromJson(Map<String, Object?> json) =>
      _$CommonStateFromJson(json);
}
