import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/app/cubit/app_cubit.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';

abstract class BaseCubit<S extends BaseCubitState>
    extends BaseCubitDelegate<S> {
  BaseCubit(super.initialState);
}

abstract class BaseCubitDelegate<S extends BaseCubitState> extends Cubit<S> {
  BaseCubitDelegate(super.initialState);

  late final AppCubit appCubit = Modular.get<AppCubit>();
  late final CommonCubit commonCubit = Modular.get<CommonCubit>();

  void showLoading() {
    commonCubit.loadingVisibility(true);
  }

  void hideLoading() {
    commonCubit.loadingVisibility(false);
  }

  void showButtonLoading() {
    commonCubit.buttonLoadingVisibility(true);
  }

  void hideButtonLoading() {
    commonCubit.buttonLoadingVisibility(false);
  }
}
