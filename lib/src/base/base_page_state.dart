import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koc_app/src/app/cubit/app_cubit.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/base/cubit/common/common_state.dart';

abstract class BasePageState<T extends StatefulWidget, B extends BaseCubit> extends BasePageStateDelegate<T, B> {}

abstract class BasePageStateDelegate<T extends StatefulWidget, B extends BaseCubit> extends State<T> {
  late final AppCubit appCubit = Modular.get<AppCubit>();
  late final CommonCubit commonCubit = Modular.get<CommonCubit>();
  late final B cubit = Modular.get<B>();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: cubit),
        BlocProvider.value(value: commonCubit),
      ],
      child: Stack(
        children: [
          buildPage(context),
          BlocBuilder<CommonCubit, CommonState>(
            builder: (_, state) {
              return Visibility(
                  visible: state.isLoading,
                  child: Stack(children: [
                    ModalBarrier(dismissible: false, color: Colors.black.withValues(alpha: 0.5)),
                    buildPageLoading()
                  ]));
            },
          )
        ],
      ),
    );
  }

  Widget buildPageLoading() => Center(
        child: SizedBox(
          width: 55.r,
          height: 55.r,
          child: const CircularProgressIndicator(
            strokeWidth: 2,
            color: Color(0xFFFFB522),
          ),
        ),
      );

  Future<void> doLoadingAction(Future<void> Function() action) async {
    commonCubit.showLoading();
    await action();
    commonCubit.hideLoading();
  }

  Widget buildPage(BuildContext context);

  Widget buildLogo() {
    return SvgPicture.asset(
      'assets/images/ATlogo.svg',
      width: 46.07.r,
      height: 28.48.r,
    );
  }

  Widget buildTitle(String name, VoidCallback? onPressed) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          name,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold),
        ),
        if (onPressed != null)
          IconButton(
            onPressed: onPressed,
            icon: Icon(
              Icons.arrow_forward,
              size: 20.r,
            ),
          ),
      ],
    );
  }
}
