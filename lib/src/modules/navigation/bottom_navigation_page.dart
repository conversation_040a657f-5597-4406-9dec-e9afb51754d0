import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/presentation/page/account_page.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/presentation/custom_link_generation_page.dart';
import 'package:koc_app/src/modules/campaign/presentation/page/campaign_home_page.dart';
import 'package:koc_app/src/modules/home/<USER>/home_page.dart';
import 'package:koc_app/src/modules/navigation/bottom_navigation_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_cubit.dart';
import 'package:koc_app/src/modules/report/presentation/report_page.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:lazy_load_indexed_stack/lazy_load_indexed_stack.dart';

class BottomNavigationPage extends StatefulWidget {
  const BottomNavigationPage({super.key});

  @override
  State<BottomNavigationPage> createState() => _BottomNavigationPageState();
}

class _BottomNavigationPageState extends State<BottomNavigationPage> with CommonMixin {
  late CommonCubit commonCubit = Modular.get<CommonCubit>();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BottomNavigationCubit, int>(
      builder: (_, selectedIndex) {
        return Scaffold(
          body: _getNavigationBodies(selectedIndex),
          bottomNavigationBar: BottomNavigationBar(
              backgroundColor: Colors.white,
              currentIndex: selectedIndex,
              onTap: (index) async {
                if (index == 2) {
                  showLoadingDialog(context);
                  await Modular.get<CustomLinkCampaignSearchCubit>().getAffiliatedCampaigns();
                  hideLoadingDialog(context);
                  _showCustomLinkGenerationBottomModalSheet(context);
                } else {
                  SiteCubit siteCubit = Modular.get<SiteCubit>();
                  CampaignHomeCubit campaignHomeCubit = Modular.get<CampaignHomeCubit>();
                  ReportCubit reportCubit = Modular.get<ReportCubit>();
                  if (index == 1 && campaignHomeCubit.state.selectedSiteId != 0) {
                    if (campaignHomeCubit.state.selectedSiteId != siteCubit.state.currentSiteId &&
                        !campaignHomeCubit.state.isSiteSwitching) {
                      campaignHomeCubit.startSiteSwitching();
                      commonCubit.showLoading();
                      await campaignHomeCubit.fetchHomeCampaigns();
                      commonCubit.hideLoading();
                      campaignHomeCubit.endSiteSwitching();
                    }
                  } else if (index == 3 && reportCubit.state.selectedSiteId != 0) {
                    // report page
                    if (reportCubit.state.selectedSiteId != siteCubit.state.currentSiteId) {
                      commonCubit.showLoading();
                      await reportCubit.findReportData();
                      commonCubit.hideLoading();
                    }
                  }
                  ReadContext(context).read<BottomNavigationCubit>().navigateTo(index);
                }
              },
              items: const [
                BottomNavigationBarItem(label: 'Home', icon: Icon(Icons.home), backgroundColor: Colors.white),
                BottomNavigationBarItem(label: 'Campaign', icon: Icon(Icons.campaign), backgroundColor: Colors.white),
                BottomNavigationBarItem(
                  label: '',
                  icon: Icon(Icons.add_link_outlined),
                  backgroundColor: Colors.white,
                ),
                BottomNavigationBarItem(
                    label: 'Report', icon: Icon(Icons.insert_chart_outlined_outlined), backgroundColor: Colors.white),
                BottomNavigationBarItem(
                    label: 'Account',
                    icon: Icon(
                      Icons.account_circle_outlined,
                    ),
                    backgroundColor: Colors.white),
              ]),
        );
      },
    );
  }

  LazyLoadIndexedStack _getNavigationBodies(int selectedIndex) {
    return LazyLoadIndexedStack(
      index: selectedIndex,
      children: [const HomePage(), const CampaignHomePage(), Container(), const ReportPage(), const AccountPage()],
    );
  }

  void _showCustomLinkGenerationBottomModalSheet(BuildContext context) {
    CustomLinkHistoryCubit historyCubit = Modular.get<CustomLinkHistoryCubit>();
    CustomLinkCampaignSearchCubit customLinkCampaignSearchCubit = Modular.get<CustomLinkCampaignSearchCubit>();
    CustomLinkCubit customLinkCubit = Modular.get<CustomLinkCubit>();
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        useSafeArea: true,
        backgroundColor: Colors.white,
        builder: (BuildContext context) {
          return ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: MultiBlocProvider(providers: [
                BlocProvider.value(value: customLinkCubit),
                BlocProvider.value(value: historyCubit),
                BlocProvider.value(value: customLinkCampaignSearchCubit)
              ], child: const CustomLinkGenerationPage()));
        });
  }
}
