import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/account/account_module.dart';
import 'package:koc_app/src/modules/campaign/campaign_module.dart';
import 'package:koc_app/src/modules/home/<USER>';
import 'package:koc_app/src/modules/navigation/bottom_navigation_cubit.dart';
import 'package:koc_app/src/modules/navigation/bottom_navigation_page.dart';
import 'package:koc_app/src/modules/report/report_module.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';
import 'package:koc_app/src/modules/survey/survey_module.dart';

class BottomNavigationModule extends Module {
  @override
  List<Module> get imports => [
        SharedModule(),
        SurveySharedModule(),
        CampaignSharedModule(),
        HomeModule(),
        AccountSharedModule(),
        ReportSharedModule(),
      ];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addLazySingleton(BottomNavigationCubit.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.child('/',
        child: (_) => BlocProvider.value(
            value: Modular.get<BottomNavigationCubit>(),
            child: const BottomNavigationPage()));
  }
}
