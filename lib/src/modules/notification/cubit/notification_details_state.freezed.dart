// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NotificationDetailsState {
  NotificationDetails? get notificationDetails =>
      throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Create a copy of NotificationDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationDetailsStateCopyWith<NotificationDetailsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationDetailsStateCopyWith<$Res> {
  factory $NotificationDetailsStateCopyWith(NotificationDetailsState value,
          $Res Function(NotificationDetailsState) then) =
      _$NotificationDetailsStateCopyWithImpl<$Res, NotificationDetailsState>;
  @useResult
  $Res call({NotificationDetails? notificationDetails, String errorMessage});

  $NotificationDetailsCopyWith<$Res>? get notificationDetails;
}

/// @nodoc
class _$NotificationDetailsStateCopyWithImpl<$Res,
        $Val extends NotificationDetailsState>
    implements $NotificationDetailsStateCopyWith<$Res> {
  _$NotificationDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationDetails = freezed,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      notificationDetails: freezed == notificationDetails
          ? _value.notificationDetails
          : notificationDetails // ignore: cast_nullable_to_non_nullable
              as NotificationDetails?,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of NotificationDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NotificationDetailsCopyWith<$Res>? get notificationDetails {
    if (_value.notificationDetails == null) {
      return null;
    }

    return $NotificationDetailsCopyWith<$Res>(_value.notificationDetails!,
        (value) {
      return _then(_value.copyWith(notificationDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$NotificationDetailsStateImplCopyWith<$Res>
    implements $NotificationDetailsStateCopyWith<$Res> {
  factory _$$NotificationDetailsStateImplCopyWith(
          _$NotificationDetailsStateImpl value,
          $Res Function(_$NotificationDetailsStateImpl) then) =
      __$$NotificationDetailsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({NotificationDetails? notificationDetails, String errorMessage});

  @override
  $NotificationDetailsCopyWith<$Res>? get notificationDetails;
}

/// @nodoc
class __$$NotificationDetailsStateImplCopyWithImpl<$Res>
    extends _$NotificationDetailsStateCopyWithImpl<$Res,
        _$NotificationDetailsStateImpl>
    implements _$$NotificationDetailsStateImplCopyWith<$Res> {
  __$$NotificationDetailsStateImplCopyWithImpl(
      _$NotificationDetailsStateImpl _value,
      $Res Function(_$NotificationDetailsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationDetails = freezed,
    Object? errorMessage = null,
  }) {
    return _then(_$NotificationDetailsStateImpl(
      notificationDetails: freezed == notificationDetails
          ? _value.notificationDetails
          : notificationDetails // ignore: cast_nullable_to_non_nullable
              as NotificationDetails?,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NotificationDetailsStateImpl implements _NotificationDetailsState {
  _$NotificationDetailsStateImpl(
      {this.notificationDetails, this.errorMessage = ''});

  @override
  final NotificationDetails? notificationDetails;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'NotificationDetailsState(notificationDetails: $notificationDetails, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationDetailsStateImpl &&
            (identical(other.notificationDetails, notificationDetails) ||
                other.notificationDetails == notificationDetails) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, notificationDetails, errorMessage);

  /// Create a copy of NotificationDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationDetailsStateImplCopyWith<_$NotificationDetailsStateImpl>
      get copyWith => __$$NotificationDetailsStateImplCopyWithImpl<
          _$NotificationDetailsStateImpl>(this, _$identity);
}

abstract class _NotificationDetailsState implements NotificationDetailsState {
  factory _NotificationDetailsState(
      {final NotificationDetails? notificationDetails,
      final String errorMessage}) = _$NotificationDetailsStateImpl;

  @override
  NotificationDetails? get notificationDetails;
  @override
  String get errorMessage;

  /// Create a copy of NotificationDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationDetailsStateImplCopyWith<_$NotificationDetailsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
