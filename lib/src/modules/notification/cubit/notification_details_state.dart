import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/notification/data/model/notification_details.dart';

part 'notification_details_state.freezed.dart';

@freezed
class NotificationDetailsState extends BaseCubitState with _$NotificationDetailsState {
  factory NotificationDetailsState({
    NotificationDetails? notificationDetails,
    @Default('') String errorMessage,
  }) = _NotificationDetailsState;
}
