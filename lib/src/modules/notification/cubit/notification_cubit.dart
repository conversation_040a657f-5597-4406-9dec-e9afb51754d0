import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/notification/cubit/notification_state.dart';
import 'package:koc_app/src/modules/notification/data/model/notification_data.dart';
import 'package:koc_app/src/modules/notification/data/repository/notification_repository.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class NotificationCubit extends BaseCubit<NotificationState> {
  final NotificationRepository _notificationRepository;
  NotificationCubit(this._notificationRepository) : super(NotificationState());

  Future<void> findNotificationData() async {
    try {
      final result = await _notificationRepository.findNotifications(style: "DEFAULT");
      List<NotificationData> reportData = List<NotificationData>.from(
        result.map((e) => NotificationData.fromJson(e as Map<String, dynamic>)),
      )..sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
      emit(state.copyWith(notifications: reportData));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> markAsViewed(int id) async {
    final notifications = state.notifications.map((e) => e.id == id ? e.copyWith(isViewed: true) : e).toList();
    emit(state.copyWith(notifications: notifications));
  }

  /// Pull-to-refresh method that bypasses cache to get fresh notifications
  Future<void> pullToRefresh() async {
    try {
      final apiService = Modular.get<ApiService>();
      await apiService.clearCacheForEndpoint('/v3/publishers/me/notifications');

      await findNotificationData();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
