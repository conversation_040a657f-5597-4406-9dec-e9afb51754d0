import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/notification/cubit/notification_details_state.dart';
import 'package:koc_app/src/modules/notification/data/model/notification_details.dart';
import 'package:koc_app/src/modules/notification/data/repository/notification_repository.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class NotificationDetailsCubit extends BaseCubit<NotificationDetailsState> {
  final NotificationRepository _notificationRepository;

  NotificationDetailsCubit(this._notificationRepository) : super(NotificationDetailsState());

  Future<void> findNotificationBy(int id) async {
    try {
      final result = await _notificationRepository.findNotificationBy(id);
      emit(state.copyWith(notificationDetails: NotificationDetails.fromJson(result)));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
