import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/notification/data/model/notification_data.dart';

part 'notification_state.freezed.dart';

@freezed
class NotificationState extends BaseCubitState with _$NotificationState {
  factory NotificationState({
    @Default([]) List<NotificationData> notifications,
    @Default('') String errorMessage,
  }) = _NotificationState;
}
