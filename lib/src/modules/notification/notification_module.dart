import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/campaign/campaign_module.dart';
import 'package:koc_app/src/modules/notification/cubit/notification_cubit.dart';
import 'package:koc_app/src/modules/notification/cubit/notification_details_cubit.dart';
import 'package:koc_app/src/modules/notification/data/repository/notification_repository.dart';
import 'package:koc_app/src/modules/notification/presentation/notification_page.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';

class NotificationModule extends Module {
  @override
  List<Module> get imports => [SharedModule(), CampaignSharedModule()];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addLazySingleton(NotificationRepository.new);
    i.add(NotificationCubit.new);
    i.add(NotificationDetailsCubit.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.child('/', child: (i) => const NotificationPage());
  }
}
