import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/notification/cubit/notification_cubit.dart';
import 'package:koc_app/src/modules/notification/cubit/notification_state.dart';
import 'package:koc_app/src/modules/notification/data/model/notification_data.dart';
import 'package:koc_app/src/modules/notification/presentation/notification_details_page.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends BasePageState<NotificationPage, NotificationCubit> {
  @override
  void initState() {
    doLoadingAction(() async {
      await cubit.findNotificationData();
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Notifications')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<NotificationCubit, NotificationState>(
      bloc: cubit,
      builder: (_, state) {
        if (state.notifications.isNotEmpty) {
          return PullToRefreshWrapper(
            onRefresh: () => cubit.pullToRefresh(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.only(left: 12.r, top: 16.r, bottom: 16.r, right: 16.r),
                child: Column(
                  children: state.notifications
                      .map((data) => Stack(
                            children: [
                              Positioned.fill(
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      left: 10.r,
                                      top: state.notifications.indexOf(data) == 0 ? 30.r : 0,
                                      bottom: state.notifications.indexOf(data) == state.notifications.length - 1
                                          ? 30.r
                                          : 0,
                                    ),
                                    child: Container(
                                      width: 1.r,
                                      color: ColorConstants.borderColor,
                                    ),
                                  ),
                                ),
                              ),
                              Column(
                                children: [
                                  Row(
                                    spacing: 4.r,
                                    children: [
                                      Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          CircleAvatar(
                                            backgroundColor: Colors.white,
                                            radius: 10.r,
                                          ),
                                          CircleAvatar(
                                            backgroundColor:
                                                data.isViewed ? const Color(0xFFF5F5F7) : const Color(0xFFFFB522),
                                            radius: 5.r,
                                          ),
                                        ],
                                      ),
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () {
                                            if (!data.isViewed) {
                                              cubit.markAsViewed(data.id);
                                            }
                                            _showNotificationDetails(data);
                                          },
                                          child: Container(
                                            padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 12.r),
                                            decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(12.r),
                                                color: data.isViewed ? const Color(0xFFF5F5F7) : null,
                                                border: Border.all(
                                                  color: data.isViewed
                                                      ? const Color(0xFFF5F5F7)
                                                      : ColorConstants.borderColor,
                                                  width: 1.r,
                                                )),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  data.subject,
                                                  style: context.textBodySmall(
                                                    fontWeight: data.isViewed ? FontWeight.w500 : FontWeight.bold,
                                                    color: data.isViewed ? const Color(0xFF767676) : null,
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                                Text(
                                                  data.createdAt!.toHowManyBefore(),
                                                  style: context.textLabelLarge(
                                                    color: const Color(0xFF767676),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (state.notifications.indexOf(data) != state.notifications.length - 1)
                                    SizedBox(height: 8.r),
                                ],
                              ),
                            ],
                          ))
                      .toList(),
                ),
              ),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  void _showNotificationDetails(NotificationData notification) {
    showModalBottomSheet(
        context: context,
        useSafeArea: true,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: NotificationDetailsPage(id: notification.id),
          );
        });
  }
}
