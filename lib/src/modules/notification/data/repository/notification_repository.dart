import 'package:koc_app/src/shared/services/api_service.dart';

class NotificationRepository {
  final ApiService apiService;
  NotificationRepository(this.apiService);

  Future<dynamic> findNotifications({String? style}) async {
    final queryParameters = <String, dynamic>{};
    if (style != null) {
      queryParameters['style'] = style;
    }
    return await apiService.getData('/v3/publishers/me/notifications', params: queryParameters);
  }

  Future<dynamic> findNotificationBy(int id) async {
    return await apiService.getData('/v3/publishers/me/notifications/$id');
  }
}
