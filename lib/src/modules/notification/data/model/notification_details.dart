import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_details.freezed.dart';
part 'notification_details.g.dart';

@freezed
class NotificationDetails with _$NotificationDetails {
  factory NotificationDetails({
    @Default('') String subject,
    @Default('') String description,
    @Default('') String imageUrl,
    @Default('') String targetUrl,
  }) = _NotificationDetails;

  factory NotificationDetails.fromJson(Map<String, Object?> json) => _$NotificationDetailsFromJson(json);
}
