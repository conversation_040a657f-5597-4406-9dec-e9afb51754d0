// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_data.freezed.dart';
part 'notification_data.g.dart';

@freezed
class NotificationData with _$NotificationData {
  factory NotificationData(
      {@Default(0) int id,
      @Default('') String subject,
      @Default('') String description,
      @Default(NotificationType.INFORMATION) NotificationType type,
      @Default(NotificationStyle.DEFAULT) NotificationStyle style,
      @Default(false) bool isViewed,
      @Default(false) bool isShowDescription,
      @Default('') String featureImage,
      @Default('') String featureImageMobile,
      @Default('') String targetUrl,
      DateTime? createdAt}) = _Notification;

  factory NotificationData.fromJson(Map<String, Object?> json) =>
      _$NotificationDataFromJson(json);
}

enum NotificationType {
  INFORMATION(0),
  EVENT(1);

  final int value;
  const NotificationType(this.value);
}

enum NotificationStyle {
  DEFAULT(0),
  POPUP_ONECE(1),
  POPUP_EVERY_TIME(2),
  CAROUSEL(3),
  CAROUSEL_POPUP_ONECE(4),
  CAROUSEL_POPUP_EVERY_TIME(5);

  final int value;
  const NotificationStyle(this.value);
}
