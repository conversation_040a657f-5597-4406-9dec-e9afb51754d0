// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationDetails _$NotificationDetailsFromJson(Map<String, dynamic> json) {
  return _NotificationDetails.fromJson(json);
}

/// @nodoc
mixin _$NotificationDetails {
  String get subject => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  String get targetUrl => throw _privateConstructorUsedError;

  /// Serializes this NotificationDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationDetailsCopyWith<NotificationDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationDetailsCopyWith<$Res> {
  factory $NotificationDetailsCopyWith(
          NotificationDetails value, $Res Function(NotificationDetails) then) =
      _$NotificationDetailsCopyWithImpl<$Res, NotificationDetails>;
  @useResult
  $Res call(
      {String subject, String description, String imageUrl, String targetUrl});
}

/// @nodoc
class _$NotificationDetailsCopyWithImpl<$Res, $Val extends NotificationDetails>
    implements $NotificationDetailsCopyWith<$Res> {
  _$NotificationDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subject = null,
    Object? description = null,
    Object? imageUrl = null,
    Object? targetUrl = null,
  }) {
    return _then(_value.copyWith(
      subject: null == subject
          ? _value.subject
          : subject // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationDetailsImplCopyWith<$Res>
    implements $NotificationDetailsCopyWith<$Res> {
  factory _$$NotificationDetailsImplCopyWith(_$NotificationDetailsImpl value,
          $Res Function(_$NotificationDetailsImpl) then) =
      __$$NotificationDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String subject, String description, String imageUrl, String targetUrl});
}

/// @nodoc
class __$$NotificationDetailsImplCopyWithImpl<$Res>
    extends _$NotificationDetailsCopyWithImpl<$Res, _$NotificationDetailsImpl>
    implements _$$NotificationDetailsImplCopyWith<$Res> {
  __$$NotificationDetailsImplCopyWithImpl(_$NotificationDetailsImpl _value,
      $Res Function(_$NotificationDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subject = null,
    Object? description = null,
    Object? imageUrl = null,
    Object? targetUrl = null,
  }) {
    return _then(_$NotificationDetailsImpl(
      subject: null == subject
          ? _value.subject
          : subject // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationDetailsImpl implements _NotificationDetails {
  _$NotificationDetailsImpl(
      {this.subject = '',
      this.description = '',
      this.imageUrl = '',
      this.targetUrl = ''});

  factory _$NotificationDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationDetailsImplFromJson(json);

  @override
  @JsonKey()
  final String subject;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final String imageUrl;
  @override
  @JsonKey()
  final String targetUrl;

  @override
  String toString() {
    return 'NotificationDetails(subject: $subject, description: $description, imageUrl: $imageUrl, targetUrl: $targetUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationDetailsImpl &&
            (identical(other.subject, subject) || other.subject == subject) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.targetUrl, targetUrl) ||
                other.targetUrl == targetUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, subject, description, imageUrl, targetUrl);

  /// Create a copy of NotificationDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationDetailsImplCopyWith<_$NotificationDetailsImpl> get copyWith =>
      __$$NotificationDetailsImplCopyWithImpl<_$NotificationDetailsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationDetailsImplToJson(
      this,
    );
  }
}

abstract class _NotificationDetails implements NotificationDetails {
  factory _NotificationDetails(
      {final String subject,
      final String description,
      final String imageUrl,
      final String targetUrl}) = _$NotificationDetailsImpl;

  factory _NotificationDetails.fromJson(Map<String, dynamic> json) =
      _$NotificationDetailsImpl.fromJson;

  @override
  String get subject;
  @override
  String get description;
  @override
  String get imageUrl;
  @override
  String get targetUrl;

  /// Create a copy of NotificationDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationDetailsImplCopyWith<_$NotificationDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
