// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationImpl _$$NotificationImplFromJson(Map<String, dynamic> json) =>
    _$NotificationImpl(
      id: (json['id'] as num?)?.toInt() ?? 0,
      subject: json['subject'] as String? ?? '',
      description: json['description'] as String? ?? '',
      type: $enumDecodeNullable(_$NotificationTypeEnumMap, json['type']) ??
          NotificationType.INFORMATION,
      style: $enumDecodeNullable(_$NotificationStyleEnumMap, json['style']) ??
          NotificationStyle.DEFAULT,
      isViewed: json['isViewed'] as bool? ?? false,
      isShowDescription: json['isShowDescription'] as bool? ?? false,
      featureImage: json['featureImage'] as String? ?? '',
      featureImageMobile: json['featureImageMobile'] as String? ?? '',
      targetUrl: json['targetUrl'] as String? ?? '',
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$NotificationImplToJson(_$NotificationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'subject': instance.subject,
      'description': instance.description,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'style': _$NotificationStyleEnumMap[instance.style]!,
      'isViewed': instance.isViewed,
      'isShowDescription': instance.isShowDescription,
      'featureImage': instance.featureImage,
      'featureImageMobile': instance.featureImageMobile,
      'targetUrl': instance.targetUrl,
      'createdAt': instance.createdAt?.toIso8601String(),
    };

const _$NotificationTypeEnumMap = {
  NotificationType.INFORMATION: 'INFORMATION',
  NotificationType.EVENT: 'EVENT',
};

const _$NotificationStyleEnumMap = {
  NotificationStyle.DEFAULT: 'DEFAULT',
  NotificationStyle.POPUP_ONECE: 'POPUP_ONECE',
  NotificationStyle.POPUP_EVERY_TIME: 'POPUP_EVERY_TIME',
  NotificationStyle.CAROUSEL: 'CAROUSEL',
  NotificationStyle.CAROUSEL_POPUP_ONECE: 'CAROUSEL_POPUP_ONECE',
  NotificationStyle.CAROUSEL_POPUP_EVERY_TIME: 'CAROUSEL_POPUP_EVERY_TIME',
};
