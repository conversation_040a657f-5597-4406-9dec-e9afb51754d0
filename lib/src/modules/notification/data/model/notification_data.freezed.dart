// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationData _$NotificationDataFromJson(Map<String, dynamic> json) {
  return _Notification.fromJson(json);
}

/// @nodoc
mixin _$NotificationData {
  int get id => throw _privateConstructorUsedError;
  String get subject => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  NotificationType get type => throw _privateConstructorUsedError;
  NotificationStyle get style => throw _privateConstructorUsedError;
  bool get isViewed => throw _privateConstructorUsedError;
  bool get isShowDescription => throw _privateConstructorUsedError;
  String get featureImage => throw _privateConstructorUsedError;
  String get featureImageMobile => throw _privateConstructorUsedError;
  String get targetUrl => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;

  /// Serializes this NotificationData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationDataCopyWith<NotificationData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationDataCopyWith<$Res> {
  factory $NotificationDataCopyWith(
          NotificationData value, $Res Function(NotificationData) then) =
      _$NotificationDataCopyWithImpl<$Res, NotificationData>;
  @useResult
  $Res call(
      {int id,
      String subject,
      String description,
      NotificationType type,
      NotificationStyle style,
      bool isViewed,
      bool isShowDescription,
      String featureImage,
      String featureImageMobile,
      String targetUrl,
      DateTime? createdAt});
}

/// @nodoc
class _$NotificationDataCopyWithImpl<$Res, $Val extends NotificationData>
    implements $NotificationDataCopyWith<$Res> {
  _$NotificationDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? subject = null,
    Object? description = null,
    Object? type = null,
    Object? style = null,
    Object? isViewed = null,
    Object? isShowDescription = null,
    Object? featureImage = null,
    Object? featureImageMobile = null,
    Object? targetUrl = null,
    Object? createdAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      subject: null == subject
          ? _value.subject
          : subject // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as NotificationType,
      style: null == style
          ? _value.style
          : style // ignore: cast_nullable_to_non_nullable
              as NotificationStyle,
      isViewed: null == isViewed
          ? _value.isViewed
          : isViewed // ignore: cast_nullable_to_non_nullable
              as bool,
      isShowDescription: null == isShowDescription
          ? _value.isShowDescription
          : isShowDescription // ignore: cast_nullable_to_non_nullable
              as bool,
      featureImage: null == featureImage
          ? _value.featureImage
          : featureImage // ignore: cast_nullable_to_non_nullable
              as String,
      featureImageMobile: null == featureImageMobile
          ? _value.featureImageMobile
          : featureImageMobile // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationImplCopyWith<$Res>
    implements $NotificationDataCopyWith<$Res> {
  factory _$$NotificationImplCopyWith(
          _$NotificationImpl value, $Res Function(_$NotificationImpl) then) =
      __$$NotificationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String subject,
      String description,
      NotificationType type,
      NotificationStyle style,
      bool isViewed,
      bool isShowDescription,
      String featureImage,
      String featureImageMobile,
      String targetUrl,
      DateTime? createdAt});
}

/// @nodoc
class __$$NotificationImplCopyWithImpl<$Res>
    extends _$NotificationDataCopyWithImpl<$Res, _$NotificationImpl>
    implements _$$NotificationImplCopyWith<$Res> {
  __$$NotificationImplCopyWithImpl(
      _$NotificationImpl _value, $Res Function(_$NotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? subject = null,
    Object? description = null,
    Object? type = null,
    Object? style = null,
    Object? isViewed = null,
    Object? isShowDescription = null,
    Object? featureImage = null,
    Object? featureImageMobile = null,
    Object? targetUrl = null,
    Object? createdAt = freezed,
  }) {
    return _then(_$NotificationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      subject: null == subject
          ? _value.subject
          : subject // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as NotificationType,
      style: null == style
          ? _value.style
          : style // ignore: cast_nullable_to_non_nullable
              as NotificationStyle,
      isViewed: null == isViewed
          ? _value.isViewed
          : isViewed // ignore: cast_nullable_to_non_nullable
              as bool,
      isShowDescription: null == isShowDescription
          ? _value.isShowDescription
          : isShowDescription // ignore: cast_nullable_to_non_nullable
              as bool,
      featureImage: null == featureImage
          ? _value.featureImage
          : featureImage // ignore: cast_nullable_to_non_nullable
              as String,
      featureImageMobile: null == featureImageMobile
          ? _value.featureImageMobile
          : featureImageMobile // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationImpl implements _Notification {
  _$NotificationImpl(
      {this.id = 0,
      this.subject = '',
      this.description = '',
      this.type = NotificationType.INFORMATION,
      this.style = NotificationStyle.DEFAULT,
      this.isViewed = false,
      this.isShowDescription = false,
      this.featureImage = '',
      this.featureImageMobile = '',
      this.targetUrl = '',
      this.createdAt});

  factory _$NotificationImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationImplFromJson(json);

  @override
  @JsonKey()
  final int id;
  @override
  @JsonKey()
  final String subject;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final NotificationType type;
  @override
  @JsonKey()
  final NotificationStyle style;
  @override
  @JsonKey()
  final bool isViewed;
  @override
  @JsonKey()
  final bool isShowDescription;
  @override
  @JsonKey()
  final String featureImage;
  @override
  @JsonKey()
  final String featureImageMobile;
  @override
  @JsonKey()
  final String targetUrl;
  @override
  final DateTime? createdAt;

  @override
  String toString() {
    return 'NotificationData(id: $id, subject: $subject, description: $description, type: $type, style: $style, isViewed: $isViewed, isShowDescription: $isShowDescription, featureImage: $featureImage, featureImageMobile: $featureImageMobile, targetUrl: $targetUrl, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.subject, subject) || other.subject == subject) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.style, style) || other.style == style) &&
            (identical(other.isViewed, isViewed) ||
                other.isViewed == isViewed) &&
            (identical(other.isShowDescription, isShowDescription) ||
                other.isShowDescription == isShowDescription) &&
            (identical(other.featureImage, featureImage) ||
                other.featureImage == featureImage) &&
            (identical(other.featureImageMobile, featureImageMobile) ||
                other.featureImageMobile == featureImageMobile) &&
            (identical(other.targetUrl, targetUrl) ||
                other.targetUrl == targetUrl) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      subject,
      description,
      type,
      style,
      isViewed,
      isShowDescription,
      featureImage,
      featureImageMobile,
      targetUrl,
      createdAt);

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationImplCopyWith<_$NotificationImpl> get copyWith =>
      __$$NotificationImplCopyWithImpl<_$NotificationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationImplToJson(
      this,
    );
  }
}

abstract class _Notification implements NotificationData {
  factory _Notification(
      {final int id,
      final String subject,
      final String description,
      final NotificationType type,
      final NotificationStyle style,
      final bool isViewed,
      final bool isShowDescription,
      final String featureImage,
      final String featureImageMobile,
      final String targetUrl,
      final DateTime? createdAt}) = _$NotificationImpl;

  factory _Notification.fromJson(Map<String, dynamic> json) =
      _$NotificationImpl.fromJson;

  @override
  int get id;
  @override
  String get subject;
  @override
  String get description;
  @override
  NotificationType get type;
  @override
  NotificationStyle get style;
  @override
  bool get isViewed;
  @override
  bool get isShowDescription;
  @override
  String get featureImage;
  @override
  String get featureImageMobile;
  @override
  String get targetUrl;
  @override
  DateTime? get createdAt;

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationImplCopyWith<_$NotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
