// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignDetailsImpl _$$CampaignDetailsImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignDetailsImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: $enumDecode(_$CampaignTypeEnumMap, json['type']),
      url: json['url'] as String,
      imageUrl: json['imageUrl'] as String?,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      description: json['description'] as String? ?? '',
      englishDescription: json['englishDescription'] as String? ?? '',
      budgets: (json['budgets'] as List<dynamic>?)
              ?.map((e) =>
                  CampaignBudget.fromJson((e as Map<String, dynamic>).map(
                    (k, e) => MapEntry(k, e as Object),
                  )))
              .toList() ??
          const [],
      defaultRewards: (json['defaultRewards'] as List<dynamic>?)
              ?.map((e) => DefaultReward.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      categoryRewards: (json['categoryRewards'] as List<dynamic>?)
              ?.map((e) => ProductCategoryReward.fromJson(
                      (e as Map<String, dynamic>).map(
                    (k, e) => MapEntry(k, e as Object),
                  )))
              .toList() ??
          const [],
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      affiliationStatus: $enumDecodeNullable(
          _$AffiliationStatusEnumMap, json['affiliationStatus']),
      currency: json['currency'] as String?,
      isRewardsByCategoriesVisible:
          json['isRewardsByCategoriesVisible'] as bool?,
      campaignStatus:
          $enumDecodeNullable(_$CampaignStatusEnumMap, json['campaignStatus']),
      deviceTypes: (json['deviceTypes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      customerCountries: (json['customerCountries'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      rejectConditions: (json['rejectConditions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      requiredActionLocal: json['requiredActionLocal'] as String?,
      requiredActionEnglish: json['requiredActionEnglish'] as String?,
      rejectConditionLocal: json['rejectConditionLocal'] as String?,
      rejectConditionEnglish: json['rejectConditionEnglish'] as String?,
      validationTermLocal: json['validationTermLocal'] as String?,
      validationTermEnglish: json['validationTermEnglish'] as String?,
      conversionWindow: json['conversionWindow'] as String?,
      appliedDate: json['appliedDate'] == null
          ? null
          : DateTime.parse(json['appliedDate'] as String),
      rejectedDate: json['rejectedDate'] == null
          ? null
          : DateTime.parse(json['rejectedDate'] as String),
      isReferralCampaign: json['isReferralCampaign'] as bool?,
      validTrafficRestrictions:
          (json['validTrafficRestrictions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      invalidTrafficRestrictions:
          (json['invalidTrafficRestrictions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      campaignApplication: $enumDecodeNullable(
          _$CampaignApplicationEnumMap, json['campaignApplication']),
      haveUpsizedReward: json['haveUpsizedReward'] ?? false,
    );

Map<String, dynamic> _$$CampaignDetailsImplToJson(
        _$CampaignDetailsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$CampaignTypeEnumMap[instance.type]!,
      'url': instance.url,
      'imageUrl': instance.imageUrl,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'description': instance.description,
      'englishDescription': instance.englishDescription,
      'budgets': instance.budgets,
      'defaultRewards': instance.defaultRewards,
      'categoryRewards': instance.categoryRewards,
      'categories': instance.categories,
      'affiliationStatus':
          _$AffiliationStatusEnumMap[instance.affiliationStatus],
      'currency': instance.currency,
      'isRewardsByCategoriesVisible': instance.isRewardsByCategoriesVisible,
      'campaignStatus': _$CampaignStatusEnumMap[instance.campaignStatus],
      'deviceTypes': instance.deviceTypes,
      'customerCountries': instance.customerCountries,
      'rejectConditions': instance.rejectConditions,
      'requiredActionLocal': instance.requiredActionLocal,
      'requiredActionEnglish': instance.requiredActionEnglish,
      'rejectConditionLocal': instance.rejectConditionLocal,
      'rejectConditionEnglish': instance.rejectConditionEnglish,
      'validationTermLocal': instance.validationTermLocal,
      'validationTermEnglish': instance.validationTermEnglish,
      'conversionWindow': instance.conversionWindow,
      'appliedDate': instance.appliedDate?.toIso8601String(),
      'rejectedDate': instance.rejectedDate?.toIso8601String(),
      'isReferralCampaign': instance.isReferralCampaign,
      'validTrafficRestrictions': instance.validTrafficRestrictions,
      'invalidTrafficRestrictions': instance.invalidTrafficRestrictions,
      'campaignApplication':
          _$CampaignApplicationEnumMap[instance.campaignApplication],
      'haveUpsizedReward': instance.haveUpsizedReward,
    };

const _$CampaignTypeEnumMap = {
  CampaignType.CPA: 'CPA',
  CampaignType.CPC: 'CPC',
  CampaignType.CPL: 'CPL',
  CampaignType.CPS: 'CPS',
};

const _$AffiliationStatusEnumMap = {
  AffiliationStatus.NEW: 'NEW',
  AffiliationStatus.APPLYING: 'APPLYING',
  AffiliationStatus.APPROVED: 'APPROVED',
  AffiliationStatus.REJECTED: 'REJECTED',
  AffiliationStatus.CANCELED: 'CANCELED',
};

const _$CampaignStatusEnumMap = {
  CampaignStatus.GETTING_READY: 'GETTING_READY',
  CampaignStatus.RUNNING: 'RUNNING',
  CampaignStatus.PAUSED: 'PAUSED',
  CampaignStatus.TERMINATED: 'TERMINATED',
  CampaignStatus.WONT_RUN: 'WONT_RUN',
  CampaignStatus.OTHER: 'OTHER',
};

const _$CampaignApplicationEnumMap = {
  CampaignApplication.WEB_ONLY: 'WEB_ONLY',
  CampaignApplication.MOBILE_APP_ONLY: 'MOBILE_APP_ONLY',
  CampaignApplication.WEB_AND_MOBILE_APP: 'WEB_AND_MOBILE_APP',
};

_$ApplyCampaignRequestImpl _$$ApplyCampaignRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ApplyCampaignRequestImpl(
      campaignIds: (json['campaignIds'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      siteId: (json['siteId'] as num).toInt(),
    );

Map<String, dynamic> _$$ApplyCampaignRequestImplToJson(
        _$ApplyCampaignRequestImpl instance) =>
    <String, dynamic>{
      'campaignIds': instance.campaignIds,
      'siteId': instance.siteId,
    };

_$CampaignBudgetImpl _$$CampaignBudgetImplFromJson(Map<String, dynamic> json) =>
    _$CampaignBudgetImpl(
      type: $enumDecode(_$CampaignBudgetTypeEnumMap, json['type']),
      cap: (json['cap'] as num).toInt(),
    );

Map<String, dynamic> _$$CampaignBudgetImplToJson(
        _$CampaignBudgetImpl instance) =>
    <String, dynamic>{
      'type': _$CampaignBudgetTypeEnumMap[instance.type]!,
      'cap': instance.cap,
    };

const _$CampaignBudgetTypeEnumMap = {
  CampaignBudgetType.CONVERSION_COUNT: 'CONVERSION_COUNT',
  CampaignBudgetType.CONVERSION_TOTAL_COMMISSION: 'CONVERSION_TOTAL_COMMISSION',
  CampaignBudgetType.CLICK_COUNT: 'CLICK_COUNT',
  CampaignBudgetType.CLICK_TOTAL_COMMISSION: 'CLICK_TOTAL_COMMISSION',
};

_$DefaultRewardImpl _$$DefaultRewardImplFromJson(Map<String, dynamic> json) =>
    _$DefaultRewardImpl(
      type: json['type'] as String?,
      name: json['name'] as String?,
      reward: (json['reward'] as num?)?.toDouble(),
      previousReward: (json['previousReward'] as num?)?.toDouble(),
      maxPublisherReward: (json['maxPublisherReward'] as num?)?.toDouble(),
      rewardByPublisherCurrency:
          (json['rewardByPublisherCurrency'] as num?)?.toDouble(),
      previousRewardByPublisherCurrency:
          (json['previousRewardByPublisherCurrency'] as num?)?.toDouble(),
      customerType: json['customerType'] as String?,
      customerTypeName: json['customerTypeName'] as String?,
      isAllSameRewardAmount: json['isAllSameRewardAmount'] as bool?,
      targetTimeFrom: json['targetTimeFrom'] as String?,
      targetTimeTo: json['targetTimeTo'] as String?,
      isExpired: json['isExpired'] as bool?,
    );

Map<String, dynamic> _$$DefaultRewardImplToJson(_$DefaultRewardImpl instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'reward': instance.reward,
      'previousReward': instance.previousReward,
      'maxPublisherReward': instance.maxPublisherReward,
      'rewardByPublisherCurrency': instance.rewardByPublisherCurrency,
      'previousRewardByPublisherCurrency':
          instance.previousRewardByPublisherCurrency,
      'customerType': instance.customerType,
      'customerTypeName': instance.customerTypeName,
      'isAllSameRewardAmount': instance.isAllSameRewardAmount,
      'targetTimeFrom': instance.targetTimeFrom,
      'targetTimeTo': instance.targetTimeTo,
      'isExpired': instance.isExpired,
    };

_$ProductCategoryRewardImpl _$$ProductCategoryRewardImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductCategoryRewardImpl(
      maxReward: json['maxReward'] == null
          ? null
          : DefaultReward.fromJson(json['maxReward'] as Map<String, dynamic>),
      allCategoryRewards: (json['allCategoryRewards'] as List<dynamic>?)
              ?.map((e) => DefaultReward.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$ProductCategoryRewardImplToJson(
        _$ProductCategoryRewardImpl instance) =>
    <String, dynamic>{
      'maxReward': instance.maxReward,
      'allCategoryRewards': instance.allCategoryRewards,
    };

_$UpsizedRewardImpl _$$UpsizedRewardImplFromJson(Map<String, dynamic> json) =>
    _$UpsizedRewardImpl(
      type: $enumDecodeNullable(_$RewardTypeEnumMap, json['type']),
      name: json['name'] as String? ?? '',
      previousReward: (json['previousReward'] as num?)?.toDouble() ?? 0,
      reward: (json['reward'] as num?)?.toDouble() ?? 0,
      customerType: json['customerType'] as String? ?? '',
      targetTimeFrom: json['targetTimeFrom'] == null
          ? null
          : DateTime.parse(json['targetTimeFrom'] as String),
      targetTimeTo: json['targetTimeTo'] == null
          ? null
          : DateTime.parse(json['targetTimeTo'] as String),
      isExpired: json['isExpired'] as bool? ?? false,
    );

Map<String, dynamic> _$$UpsizedRewardImplToJson(_$UpsizedRewardImpl instance) =>
    <String, dynamic>{
      'type': _$RewardTypeEnumMap[instance.type],
      'name': instance.name,
      'previousReward': instance.previousReward,
      'reward': instance.reward,
      'customerType': instance.customerType,
      'targetTimeFrom': instance.targetTimeFrom?.toIso8601String(),
      'targetTimeTo': instance.targetTimeTo?.toIso8601String(),
      'isExpired': instance.isExpired,
    };

const _$RewardTypeEnumMap = {
  RewardType.CPC: 'CPC',
  RewardType.CPA_FIXED: 'CPA_FIXED',
  RewardType.CPA_SALES: 'CPA_SALES',
};

_$CategoryImpl _$$CategoryImplFromJson(Map<String, dynamic> json) =>
    _$CategoryImpl(
      name: json['name'] as String,
      value: (json['value'] as num).toInt(),
    );

Map<String, dynamic> _$$CategoryImplToJson(_$CategoryImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'value': instance.value,
    };

_$DefaultCampaignSummaryImpl _$$DefaultCampaignSummaryImplFromJson(
        Map<String, dynamic> json) =>
    _$DefaultCampaignSummaryImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      imageUrl: json['imageUrl'] as String?,
      category: json['category'] as String?,
      highestRewardSummaries: (json['highestRewardSummaries'] as List<dynamic>?)
              ?.map((e) =>
                  HighestRewardSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      haveUpsizedReward: json['haveUpsizedReward'] as bool? ?? false,
      isNew: json['isNew'] as bool? ?? false,
    );

Map<String, dynamic> _$$DefaultCampaignSummaryImplToJson(
        _$DefaultCampaignSummaryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'category': instance.category,
      'highestRewardSummaries': instance.highestRewardSummaries,
      'haveUpsizedReward': instance.haveUpsizedReward,
      'isNew': instance.isNew,
    };

_$HighestRewardSummaryImpl _$$HighestRewardSummaryImplFromJson(
        Map<String, dynamic> json) =>
    _$HighestRewardSummaryImpl(
      type: json['type'] as String,
      reward: (json['reward'] as num).toInt(),
    );

Map<String, dynamic> _$$HighestRewardSummaryImplToJson(
        _$HighestRewardSummaryImpl instance) =>
    <String, dynamic>{
      'type': instance.type,
      'reward': instance.reward,
    };

_$CampaignSummaryImpl _$$CampaignSummaryImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignSummaryImpl(
      id: (json['id'] as num?)?.toInt() ?? 0,
      imageUrl: json['imageUrl'] as String? ?? "",
      name: json['name'] as String? ?? "",
      category: json['category'] as String? ?? "",
    );

Map<String, dynamic> _$$CampaignSummaryImplToJson(
        _$CampaignSummaryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'imageUrl': instance.imageUrl,
      'name': instance.name,
      'category': instance.category,
    };

_$CampaignSearchImpl _$$CampaignSearchImplFromJson(Map<String, dynamic> json) =>
    _$CampaignSearchImpl(
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      selectedCategories: (json['selectedCategories'] as List<dynamic>?)
              ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      types: (json['types'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$CampaignTypeEnumMap, e))
              .toList() ??
          CampaignType.values,
      selectedTypes: (json['selectedTypes'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$CampaignTypeEnumMap, e))
              .toList() ??
          const [],
      campaignResults: (json['campaignResults'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CampaignSearchImplToJson(
        _$CampaignSearchImpl instance) =>
    <String, dynamic>{
      'categories': instance.categories,
      'selectedCategories': instance.selectedCategories,
      'types': instance.types.map((e) => _$CampaignTypeEnumMap[e]!).toList(),
      'selectedTypes':
          instance.selectedTypes.map((e) => _$CampaignTypeEnumMap[e]!).toList(),
      'campaignResults': instance.campaignResults,
    };

_$CampaignFilterImpl _$$CampaignFilterImplFromJson(Map<String, dynamic> json) =>
    _$CampaignFilterImpl(
      keyword: json['keyword'] as String? ?? '',
      excludedCampaignId: (json['excludedCampaignId'] as num?)?.toInt() ?? 0,
      categoryIds: (json['categoryIds'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      campaignTypes: (json['campaignTypes'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$CampaignTypeEnumMap, e))
              .toList() ??
          const [],
      campaignApplications: (json['campaignApplications'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$CampaignApplicationEnumMap, e))
              .toList() ??
          const [],
      customerCountries: (json['customerCountries'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      limit: (json['limit'] as num?)?.toInt() ?? 10,
      page: (json['page'] as num?)?.toInt() ?? 1,
    );

Map<String, dynamic> _$$CampaignFilterImplToJson(
        _$CampaignFilterImpl instance) =>
    <String, dynamic>{
      'keyword': instance.keyword,
      'excludedCampaignId': instance.excludedCampaignId,
      'categoryIds': instance.categoryIds,
      'campaignTypes':
          instance.campaignTypes.map((e) => _$CampaignTypeEnumMap[e]!).toList(),
      'campaignApplications': instance.campaignApplications
          .map((e) => _$CampaignApplicationEnumMap[e]!)
          .toList(),
      'customerCountries': instance.customerCountries,
      'limit': instance.limit,
      'page': instance.page,
    };

_$CampaignCountSummaryImpl _$$CampaignCountSummaryImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignCountSummaryImpl(
      affiliatedCount: (json['affiliatedCount'] as num?)?.toInt() ?? 0,
      availableCount: (json['availableCount'] as num?)?.toInt() ?? 0,
      pausedCount: (json['pausedCount'] as num?)?.toInt() ?? 0,
      waitingCount: (json['waitingCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CampaignCountSummaryImplToJson(
        _$CampaignCountSummaryImpl instance) =>
    <String, dynamic>{
      'affiliatedCount': instance.affiliatedCount,
      'availableCount': instance.availableCount,
      'pausedCount': instance.pausedCount,
      'waitingCount': instance.waitingCount,
    };
