import 'package:freezed_annotation/freezed_annotation.dart';

part 'coupon.freezed.dart';
part 'coupon.g.dart';

@freezed
class Coupon with _$Coupon {
  factory Coupon(
      {required final int id,
      required final String name,
      required final String shareLink,
      required final String imageUrl,
      required final String promoteCode,
      required final DateTime expireDay,
      required final String category,
      required final String description
      }) = _Coupon;

  factory Coupon.fromJson(Map<String, Object?> json) =>
      _$CouponFromJson(json);
}