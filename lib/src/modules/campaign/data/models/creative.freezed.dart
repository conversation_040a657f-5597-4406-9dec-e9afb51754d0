// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'creative.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Creative _$CreativeFromJson(Map<String, dynamic> json) {
  return _Creative.fromJson(json);
}

/// @nodoc
mixin _$Creative {
  int get width => throw _privateConstructorUsedError;
  int get height => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get affiliateLink => throw _privateConstructorUsedError;

  /// Serializes this Creative to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Creative
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreativeCopyWith<Creative> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreativeCopyWith<$Res> {
  factory $CreativeCopyWith(Creative value, $Res Function(Creative) then) =
      _$CreativeCopyWithImpl<$Res, Creative>;
  @useResult
  $Res call({int width, int height, String? imageUrl, String? affiliateLink});
}

/// @nodoc
class _$CreativeCopyWithImpl<$Res, $Val extends Creative>
    implements $CreativeCopyWith<$Res> {
  _$CreativeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Creative
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = null,
    Object? height = null,
    Object? imageUrl = freezed,
    Object? affiliateLink = freezed,
  }) {
    return _then(_value.copyWith(
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      affiliateLink: freezed == affiliateLink
          ? _value.affiliateLink
          : affiliateLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreativeImplCopyWith<$Res>
    implements $CreativeCopyWith<$Res> {
  factory _$$CreativeImplCopyWith(
          _$CreativeImpl value, $Res Function(_$CreativeImpl) then) =
      __$$CreativeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int width, int height, String? imageUrl, String? affiliateLink});
}

/// @nodoc
class __$$CreativeImplCopyWithImpl<$Res>
    extends _$CreativeCopyWithImpl<$Res, _$CreativeImpl>
    implements _$$CreativeImplCopyWith<$Res> {
  __$$CreativeImplCopyWithImpl(
      _$CreativeImpl _value, $Res Function(_$CreativeImpl) _then)
      : super(_value, _then);

  /// Create a copy of Creative
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = null,
    Object? height = null,
    Object? imageUrl = freezed,
    Object? affiliateLink = freezed,
  }) {
    return _then(_$CreativeImpl(
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      affiliateLink: freezed == affiliateLink
          ? _value.affiliateLink
          : affiliateLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreativeImpl implements _Creative {
  _$CreativeImpl(
      {required this.width,
      required this.height,
      required this.imageUrl,
      required this.affiliateLink});

  factory _$CreativeImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreativeImplFromJson(json);

  @override
  final int width;
  @override
  final int height;
  @override
  final String? imageUrl;
  @override
  final String? affiliateLink;

  @override
  String toString() {
    return 'Creative(width: $width, height: $height, imageUrl: $imageUrl, affiliateLink: $affiliateLink)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreativeImpl &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.affiliateLink, affiliateLink) ||
                other.affiliateLink == affiliateLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, width, height, imageUrl, affiliateLink);

  /// Create a copy of Creative
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreativeImplCopyWith<_$CreativeImpl> get copyWith =>
      __$$CreativeImplCopyWithImpl<_$CreativeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreativeImplToJson(
      this,
    );
  }
}

abstract class _Creative implements Creative {
  factory _Creative(
      {required final int width,
      required final int height,
      required final String? imageUrl,
      required final String? affiliateLink}) = _$CreativeImpl;

  factory _Creative.fromJson(Map<String, dynamic> json) =
      _$CreativeImpl.fromJson;

  @override
  int get width;
  @override
  int get height;
  @override
  String? get imageUrl;
  @override
  String? get affiliateLink;

  /// Create a copy of Creative
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreativeImplCopyWith<_$CreativeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreativeGroup _$CreativeGroupFromJson(Map<String, dynamic> json) {
  return _CreativeGroup.fromJson(json);
}

/// @nodoc
mixin _$CreativeGroup {
  int? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<Creative> get creatives => throw _privateConstructorUsedError;

  /// Serializes this CreativeGroup to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreativeGroup
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreativeGroupCopyWith<CreativeGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreativeGroupCopyWith<$Res> {
  factory $CreativeGroupCopyWith(
          CreativeGroup value, $Res Function(CreativeGroup) then) =
      _$CreativeGroupCopyWithImpl<$Res, CreativeGroup>;
  @useResult
  $Res call(
      {int? id,
      String? name,
      String? imageUrl,
      String? description,
      List<Creative> creatives});
}

/// @nodoc
class _$CreativeGroupCopyWithImpl<$Res, $Val extends CreativeGroup>
    implements $CreativeGroupCopyWith<$Res> {
  _$CreativeGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreativeGroup
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? description = freezed,
    Object? creatives = null,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      creatives: null == creatives
          ? _value.creatives
          : creatives // ignore: cast_nullable_to_non_nullable
              as List<Creative>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreativeGroupImplCopyWith<$Res>
    implements $CreativeGroupCopyWith<$Res> {
  factory _$$CreativeGroupImplCopyWith(
          _$CreativeGroupImpl value, $Res Function(_$CreativeGroupImpl) then) =
      __$$CreativeGroupImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? name,
      String? imageUrl,
      String? description,
      List<Creative> creatives});
}

/// @nodoc
class __$$CreativeGroupImplCopyWithImpl<$Res>
    extends _$CreativeGroupCopyWithImpl<$Res, _$CreativeGroupImpl>
    implements _$$CreativeGroupImplCopyWith<$Res> {
  __$$CreativeGroupImplCopyWithImpl(
      _$CreativeGroupImpl _value, $Res Function(_$CreativeGroupImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreativeGroup
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? description = freezed,
    Object? creatives = null,
  }) {
    return _then(_$CreativeGroupImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      creatives: null == creatives
          ? _value._creatives
          : creatives // ignore: cast_nullable_to_non_nullable
              as List<Creative>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreativeGroupImpl implements _CreativeGroup {
  _$CreativeGroupImpl(
      {this.id,
      required this.name,
      required this.imageUrl,
      required this.description,
      required final List<Creative> creatives})
      : _creatives = creatives;

  factory _$CreativeGroupImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreativeGroupImplFromJson(json);

  @override
  final int? id;
  @override
  final String? name;
  @override
  final String? imageUrl;
  @override
  final String? description;
  final List<Creative> _creatives;
  @override
  List<Creative> get creatives {
    if (_creatives is EqualUnmodifiableListView) return _creatives;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_creatives);
  }

  @override
  String toString() {
    return 'CreativeGroup(id: $id, name: $name, imageUrl: $imageUrl, description: $description, creatives: $creatives)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreativeGroupImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._creatives, _creatives));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, imageUrl, description,
      const DeepCollectionEquality().hash(_creatives));

  /// Create a copy of CreativeGroup
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreativeGroupImplCopyWith<_$CreativeGroupImpl> get copyWith =>
      __$$CreativeGroupImplCopyWithImpl<_$CreativeGroupImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreativeGroupImplToJson(
      this,
    );
  }
}

abstract class _CreativeGroup implements CreativeGroup {
  factory _CreativeGroup(
      {final int? id,
      required final String? name,
      required final String? imageUrl,
      required final String? description,
      required final List<Creative> creatives}) = _$CreativeGroupImpl;

  factory _CreativeGroup.fromJson(Map<String, dynamic> json) =
      _$CreativeGroupImpl.fromJson;

  @override
  int? get id;
  @override
  String? get name;
  @override
  String? get imageUrl;
  @override
  String? get description;
  @override
  List<Creative> get creatives;

  /// Create a copy of CreativeGroup
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreativeGroupImplCopyWith<_$CreativeGroupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreativeImage _$CreativeImageFromJson(Map<String, dynamic> json) {
  return _CreativeImage.fromJson(json);
}

/// @nodoc
mixin _$CreativeImage {
  String? get name => throw _privateConstructorUsedError;
  List<Creative> get creatives => throw _privateConstructorUsedError;

  /// Serializes this CreativeImage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreativeImage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreativeImageCopyWith<CreativeImage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreativeImageCopyWith<$Res> {
  factory $CreativeImageCopyWith(
          CreativeImage value, $Res Function(CreativeImage) then) =
      _$CreativeImageCopyWithImpl<$Res, CreativeImage>;
  @useResult
  $Res call({String? name, List<Creative> creatives});
}

/// @nodoc
class _$CreativeImageCopyWithImpl<$Res, $Val extends CreativeImage>
    implements $CreativeImageCopyWith<$Res> {
  _$CreativeImageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreativeImage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? creatives = null,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      creatives: null == creatives
          ? _value.creatives
          : creatives // ignore: cast_nullable_to_non_nullable
              as List<Creative>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreativeImageImplCopyWith<$Res>
    implements $CreativeImageCopyWith<$Res> {
  factory _$$CreativeImageImplCopyWith(
          _$CreativeImageImpl value, $Res Function(_$CreativeImageImpl) then) =
      __$$CreativeImageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, List<Creative> creatives});
}

/// @nodoc
class __$$CreativeImageImplCopyWithImpl<$Res>
    extends _$CreativeImageCopyWithImpl<$Res, _$CreativeImageImpl>
    implements _$$CreativeImageImplCopyWith<$Res> {
  __$$CreativeImageImplCopyWithImpl(
      _$CreativeImageImpl _value, $Res Function(_$CreativeImageImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreativeImage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? creatives = null,
  }) {
    return _then(_$CreativeImageImpl(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      creatives: null == creatives
          ? _value._creatives
          : creatives // ignore: cast_nullable_to_non_nullable
              as List<Creative>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreativeImageImpl implements _CreativeImage {
  const _$CreativeImageImpl(
      {required this.name, required final List<Creative> creatives})
      : _creatives = creatives;

  factory _$CreativeImageImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreativeImageImplFromJson(json);

  @override
  final String? name;
  final List<Creative> _creatives;
  @override
  List<Creative> get creatives {
    if (_creatives is EqualUnmodifiableListView) return _creatives;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_creatives);
  }

  @override
  String toString() {
    return 'CreativeImage(name: $name, creatives: $creatives)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreativeImageImpl &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality()
                .equals(other._creatives, _creatives));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, const DeepCollectionEquality().hash(_creatives));

  /// Create a copy of CreativeImage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreativeImageImplCopyWith<_$CreativeImageImpl> get copyWith =>
      __$$CreativeImageImplCopyWithImpl<_$CreativeImageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreativeImageImplToJson(
      this,
    );
  }
}

abstract class _CreativeImage implements CreativeImage {
  const factory _CreativeImage(
      {required final String? name,
      required final List<Creative> creatives}) = _$CreativeImageImpl;

  factory _CreativeImage.fromJson(Map<String, dynamic> json) =
      _$CreativeImageImpl.fromJson;

  @override
  String? get name;
  @override
  List<Creative> get creatives;

  /// Create a copy of CreativeImage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreativeImageImplCopyWith<_$CreativeImageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
