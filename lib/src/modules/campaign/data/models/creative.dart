import 'package:freezed_annotation/freezed_annotation.dart';

part 'creative.freezed.dart';
part 'creative.g.dart';

@freezed
class Creative with _$Creative {
  factory Creative({
    required int width,
    required int height,
    required String? imageUrl,
    required String? affiliateLink,
  }) = _Creative;

  factory Creative.fromJson(Map<String, Object?> json) =>
      _$CreativeFromJson(json);
}

@freezed
class CreativeGroup with _$CreativeGroup {
  factory CreativeGroup({
    int? id,
    required String? name,
    required String? imageUrl,
    required String? description,
    required List<Creative> creatives,
  }) = _CreativeGroup;

  factory CreativeGroup.fromJson(Map<String, Object?> json) =>
      _$CreativeGroupFromJson(json);
}

@freezed
class CreativeImage with _$CreativeImage {
  const factory CreativeImage({
    required String? name,
    required List<Creative> creatives,
  }) = _CreativeImage;

  factory CreativeImage.fromJson(Map<String, dynamic> json) =>
      _$CreativeImageFromJson(json);
}
