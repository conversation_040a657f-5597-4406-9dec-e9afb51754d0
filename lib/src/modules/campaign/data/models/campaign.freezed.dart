// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignDetails _$CampaignDetailsFromJson(Map<String, dynamic> json) {
  return _CampaignDetails.fromJson(json);
}

/// @nodoc
mixin _$CampaignDetails {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  CampaignType get type => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get englishDescription => throw _privateConstructorUsedError;
  List<CampaignBudget> get budgets => throw _privateConstructorUsedError;
  List<DefaultReward> get defaultRewards => throw _privateConstructorUsedError;
  List<ProductCategoryReward> get categoryRewards =>
      throw _privateConstructorUsedError;
  List<Category> get categories => throw _privateConstructorUsedError;
  AffiliationStatus? get affiliationStatus =>
      throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  bool? get isRewardsByCategoriesVisible => throw _privateConstructorUsedError;
  CampaignStatus? get campaignStatus => throw _privateConstructorUsedError;
  List<String>? get deviceTypes => throw _privateConstructorUsedError;
  List<String>? get customerCountries => throw _privateConstructorUsedError;
  List<String>? get rejectConditions => throw _privateConstructorUsedError;
  String? get requiredActionLocal => throw _privateConstructorUsedError;
  String? get requiredActionEnglish => throw _privateConstructorUsedError;
  String? get rejectConditionLocal => throw _privateConstructorUsedError;
  String? get rejectConditionEnglish => throw _privateConstructorUsedError;
  String? get validationTermLocal => throw _privateConstructorUsedError;
  String? get validationTermEnglish => throw _privateConstructorUsedError;
  String? get conversionWindow => throw _privateConstructorUsedError;
  DateTime? get appliedDate => throw _privateConstructorUsedError;
  DateTime? get rejectedDate => throw _privateConstructorUsedError;
  bool? get isReferralCampaign => throw _privateConstructorUsedError;
  List<String>? get validTrafficRestrictions =>
      throw _privateConstructorUsedError;
  List<String>? get invalidTrafficRestrictions =>
      throw _privateConstructorUsedError;
  CampaignApplication? get campaignApplication =>
      throw _privateConstructorUsedError;
  dynamic get haveUpsizedReward => throw _privateConstructorUsedError;

  /// Serializes this CampaignDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignDetailsCopyWith<CampaignDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignDetailsCopyWith<$Res> {
  factory $CampaignDetailsCopyWith(
          CampaignDetails value, $Res Function(CampaignDetails) then) =
      _$CampaignDetailsCopyWithImpl<$Res, CampaignDetails>;
  @useResult
  $Res call(
      {int id,
      String name,
      CampaignType type,
      String url,
      String? imageUrl,
      DateTime? startDate,
      DateTime? endDate,
      String description,
      String englishDescription,
      List<CampaignBudget> budgets,
      List<DefaultReward> defaultRewards,
      List<ProductCategoryReward> categoryRewards,
      List<Category> categories,
      AffiliationStatus? affiliationStatus,
      String? currency,
      bool? isRewardsByCategoriesVisible,
      CampaignStatus? campaignStatus,
      List<String>? deviceTypes,
      List<String>? customerCountries,
      List<String>? rejectConditions,
      String? requiredActionLocal,
      String? requiredActionEnglish,
      String? rejectConditionLocal,
      String? rejectConditionEnglish,
      String? validationTermLocal,
      String? validationTermEnglish,
      String? conversionWindow,
      DateTime? appliedDate,
      DateTime? rejectedDate,
      bool? isReferralCampaign,
      List<String>? validTrafficRestrictions,
      List<String>? invalidTrafficRestrictions,
      CampaignApplication? campaignApplication,
      dynamic haveUpsizedReward});
}

/// @nodoc
class _$CampaignDetailsCopyWithImpl<$Res, $Val extends CampaignDetails>
    implements $CampaignDetailsCopyWith<$Res> {
  _$CampaignDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? url = null,
    Object? imageUrl = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? description = null,
    Object? englishDescription = null,
    Object? budgets = null,
    Object? defaultRewards = null,
    Object? categoryRewards = null,
    Object? categories = null,
    Object? affiliationStatus = freezed,
    Object? currency = freezed,
    Object? isRewardsByCategoriesVisible = freezed,
    Object? campaignStatus = freezed,
    Object? deviceTypes = freezed,
    Object? customerCountries = freezed,
    Object? rejectConditions = freezed,
    Object? requiredActionLocal = freezed,
    Object? requiredActionEnglish = freezed,
    Object? rejectConditionLocal = freezed,
    Object? rejectConditionEnglish = freezed,
    Object? validationTermLocal = freezed,
    Object? validationTermEnglish = freezed,
    Object? conversionWindow = freezed,
    Object? appliedDate = freezed,
    Object? rejectedDate = freezed,
    Object? isReferralCampaign = freezed,
    Object? validTrafficRestrictions = freezed,
    Object? invalidTrafficRestrictions = freezed,
    Object? campaignApplication = freezed,
    Object? haveUpsizedReward = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as CampaignType,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      englishDescription: null == englishDescription
          ? _value.englishDescription
          : englishDescription // ignore: cast_nullable_to_non_nullable
              as String,
      budgets: null == budgets
          ? _value.budgets
          : budgets // ignore: cast_nullable_to_non_nullable
              as List<CampaignBudget>,
      defaultRewards: null == defaultRewards
          ? _value.defaultRewards
          : defaultRewards // ignore: cast_nullable_to_non_nullable
              as List<DefaultReward>,
      categoryRewards: null == categoryRewards
          ? _value.categoryRewards
          : categoryRewards // ignore: cast_nullable_to_non_nullable
              as List<ProductCategoryReward>,
      categories: null == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Category>,
      affiliationStatus: freezed == affiliationStatus
          ? _value.affiliationStatus
          : affiliationStatus // ignore: cast_nullable_to_non_nullable
              as AffiliationStatus?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      isRewardsByCategoriesVisible: freezed == isRewardsByCategoriesVisible
          ? _value.isRewardsByCategoriesVisible
          : isRewardsByCategoriesVisible // ignore: cast_nullable_to_non_nullable
              as bool?,
      campaignStatus: freezed == campaignStatus
          ? _value.campaignStatus
          : campaignStatus // ignore: cast_nullable_to_non_nullable
              as CampaignStatus?,
      deviceTypes: freezed == deviceTypes
          ? _value.deviceTypes
          : deviceTypes // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      customerCountries: freezed == customerCountries
          ? _value.customerCountries
          : customerCountries // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      rejectConditions: freezed == rejectConditions
          ? _value.rejectConditions
          : rejectConditions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      requiredActionLocal: freezed == requiredActionLocal
          ? _value.requiredActionLocal
          : requiredActionLocal // ignore: cast_nullable_to_non_nullable
              as String?,
      requiredActionEnglish: freezed == requiredActionEnglish
          ? _value.requiredActionEnglish
          : requiredActionEnglish // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectConditionLocal: freezed == rejectConditionLocal
          ? _value.rejectConditionLocal
          : rejectConditionLocal // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectConditionEnglish: freezed == rejectConditionEnglish
          ? _value.rejectConditionEnglish
          : rejectConditionEnglish // ignore: cast_nullable_to_non_nullable
              as String?,
      validationTermLocal: freezed == validationTermLocal
          ? _value.validationTermLocal
          : validationTermLocal // ignore: cast_nullable_to_non_nullable
              as String?,
      validationTermEnglish: freezed == validationTermEnglish
          ? _value.validationTermEnglish
          : validationTermEnglish // ignore: cast_nullable_to_non_nullable
              as String?,
      conversionWindow: freezed == conversionWindow
          ? _value.conversionWindow
          : conversionWindow // ignore: cast_nullable_to_non_nullable
              as String?,
      appliedDate: freezed == appliedDate
          ? _value.appliedDate
          : appliedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      rejectedDate: freezed == rejectedDate
          ? _value.rejectedDate
          : rejectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isReferralCampaign: freezed == isReferralCampaign
          ? _value.isReferralCampaign
          : isReferralCampaign // ignore: cast_nullable_to_non_nullable
              as bool?,
      validTrafficRestrictions: freezed == validTrafficRestrictions
          ? _value.validTrafficRestrictions
          : validTrafficRestrictions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      invalidTrafficRestrictions: freezed == invalidTrafficRestrictions
          ? _value.invalidTrafficRestrictions
          : invalidTrafficRestrictions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      campaignApplication: freezed == campaignApplication
          ? _value.campaignApplication
          : campaignApplication // ignore: cast_nullable_to_non_nullable
              as CampaignApplication?,
      haveUpsizedReward: freezed == haveUpsizedReward
          ? _value.haveUpsizedReward
          : haveUpsizedReward // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignDetailsImplCopyWith<$Res>
    implements $CampaignDetailsCopyWith<$Res> {
  factory _$$CampaignDetailsImplCopyWith(_$CampaignDetailsImpl value,
          $Res Function(_$CampaignDetailsImpl) then) =
      __$$CampaignDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      CampaignType type,
      String url,
      String? imageUrl,
      DateTime? startDate,
      DateTime? endDate,
      String description,
      String englishDescription,
      List<CampaignBudget> budgets,
      List<DefaultReward> defaultRewards,
      List<ProductCategoryReward> categoryRewards,
      List<Category> categories,
      AffiliationStatus? affiliationStatus,
      String? currency,
      bool? isRewardsByCategoriesVisible,
      CampaignStatus? campaignStatus,
      List<String>? deviceTypes,
      List<String>? customerCountries,
      List<String>? rejectConditions,
      String? requiredActionLocal,
      String? requiredActionEnglish,
      String? rejectConditionLocal,
      String? rejectConditionEnglish,
      String? validationTermLocal,
      String? validationTermEnglish,
      String? conversionWindow,
      DateTime? appliedDate,
      DateTime? rejectedDate,
      bool? isReferralCampaign,
      List<String>? validTrafficRestrictions,
      List<String>? invalidTrafficRestrictions,
      CampaignApplication? campaignApplication,
      dynamic haveUpsizedReward});
}

/// @nodoc
class __$$CampaignDetailsImplCopyWithImpl<$Res>
    extends _$CampaignDetailsCopyWithImpl<$Res, _$CampaignDetailsImpl>
    implements _$$CampaignDetailsImplCopyWith<$Res> {
  __$$CampaignDetailsImplCopyWithImpl(
      _$CampaignDetailsImpl _value, $Res Function(_$CampaignDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? url = null,
    Object? imageUrl = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? description = null,
    Object? englishDescription = null,
    Object? budgets = null,
    Object? defaultRewards = null,
    Object? categoryRewards = null,
    Object? categories = null,
    Object? affiliationStatus = freezed,
    Object? currency = freezed,
    Object? isRewardsByCategoriesVisible = freezed,
    Object? campaignStatus = freezed,
    Object? deviceTypes = freezed,
    Object? customerCountries = freezed,
    Object? rejectConditions = freezed,
    Object? requiredActionLocal = freezed,
    Object? requiredActionEnglish = freezed,
    Object? rejectConditionLocal = freezed,
    Object? rejectConditionEnglish = freezed,
    Object? validationTermLocal = freezed,
    Object? validationTermEnglish = freezed,
    Object? conversionWindow = freezed,
    Object? appliedDate = freezed,
    Object? rejectedDate = freezed,
    Object? isReferralCampaign = freezed,
    Object? validTrafficRestrictions = freezed,
    Object? invalidTrafficRestrictions = freezed,
    Object? campaignApplication = freezed,
    Object? haveUpsizedReward = freezed,
  }) {
    return _then(_$CampaignDetailsImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as CampaignType,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      englishDescription: null == englishDescription
          ? _value.englishDescription
          : englishDescription // ignore: cast_nullable_to_non_nullable
              as String,
      budgets: null == budgets
          ? _value._budgets
          : budgets // ignore: cast_nullable_to_non_nullable
              as List<CampaignBudget>,
      defaultRewards: null == defaultRewards
          ? _value._defaultRewards
          : defaultRewards // ignore: cast_nullable_to_non_nullable
              as List<DefaultReward>,
      categoryRewards: null == categoryRewards
          ? _value._categoryRewards
          : categoryRewards // ignore: cast_nullable_to_non_nullable
              as List<ProductCategoryReward>,
      categories: null == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Category>,
      affiliationStatus: freezed == affiliationStatus
          ? _value.affiliationStatus
          : affiliationStatus // ignore: cast_nullable_to_non_nullable
              as AffiliationStatus?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      isRewardsByCategoriesVisible: freezed == isRewardsByCategoriesVisible
          ? _value.isRewardsByCategoriesVisible
          : isRewardsByCategoriesVisible // ignore: cast_nullable_to_non_nullable
              as bool?,
      campaignStatus: freezed == campaignStatus
          ? _value.campaignStatus
          : campaignStatus // ignore: cast_nullable_to_non_nullable
              as CampaignStatus?,
      deviceTypes: freezed == deviceTypes
          ? _value._deviceTypes
          : deviceTypes // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      customerCountries: freezed == customerCountries
          ? _value._customerCountries
          : customerCountries // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      rejectConditions: freezed == rejectConditions
          ? _value._rejectConditions
          : rejectConditions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      requiredActionLocal: freezed == requiredActionLocal
          ? _value.requiredActionLocal
          : requiredActionLocal // ignore: cast_nullable_to_non_nullable
              as String?,
      requiredActionEnglish: freezed == requiredActionEnglish
          ? _value.requiredActionEnglish
          : requiredActionEnglish // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectConditionLocal: freezed == rejectConditionLocal
          ? _value.rejectConditionLocal
          : rejectConditionLocal // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectConditionEnglish: freezed == rejectConditionEnglish
          ? _value.rejectConditionEnglish
          : rejectConditionEnglish // ignore: cast_nullable_to_non_nullable
              as String?,
      validationTermLocal: freezed == validationTermLocal
          ? _value.validationTermLocal
          : validationTermLocal // ignore: cast_nullable_to_non_nullable
              as String?,
      validationTermEnglish: freezed == validationTermEnglish
          ? _value.validationTermEnglish
          : validationTermEnglish // ignore: cast_nullable_to_non_nullable
              as String?,
      conversionWindow: freezed == conversionWindow
          ? _value.conversionWindow
          : conversionWindow // ignore: cast_nullable_to_non_nullable
              as String?,
      appliedDate: freezed == appliedDate
          ? _value.appliedDate
          : appliedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      rejectedDate: freezed == rejectedDate
          ? _value.rejectedDate
          : rejectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isReferralCampaign: freezed == isReferralCampaign
          ? _value.isReferralCampaign
          : isReferralCampaign // ignore: cast_nullable_to_non_nullable
              as bool?,
      validTrafficRestrictions: freezed == validTrafficRestrictions
          ? _value._validTrafficRestrictions
          : validTrafficRestrictions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      invalidTrafficRestrictions: freezed == invalidTrafficRestrictions
          ? _value._invalidTrafficRestrictions
          : invalidTrafficRestrictions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      campaignApplication: freezed == campaignApplication
          ? _value.campaignApplication
          : campaignApplication // ignore: cast_nullable_to_non_nullable
              as CampaignApplication?,
      haveUpsizedReward: freezed == haveUpsizedReward
          ? _value.haveUpsizedReward!
          : haveUpsizedReward,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignDetailsImpl implements _CampaignDetails {
  const _$CampaignDetailsImpl(
      {required this.id,
      required this.name,
      required this.type,
      required this.url,
      this.imageUrl,
      this.startDate,
      this.endDate,
      this.description = '',
      this.englishDescription = '',
      final List<CampaignBudget> budgets = const [],
      final List<DefaultReward> defaultRewards = const [],
      final List<ProductCategoryReward> categoryRewards = const [],
      final List<Category> categories = const [],
      this.affiliationStatus,
      this.currency,
      this.isRewardsByCategoriesVisible,
      this.campaignStatus,
      final List<String>? deviceTypes,
      final List<String>? customerCountries,
      final List<String>? rejectConditions,
      this.requiredActionLocal,
      this.requiredActionEnglish,
      this.rejectConditionLocal,
      this.rejectConditionEnglish,
      this.validationTermLocal,
      this.validationTermEnglish,
      this.conversionWindow,
      this.appliedDate,
      this.rejectedDate,
      this.isReferralCampaign,
      final List<String>? validTrafficRestrictions,
      final List<String>? invalidTrafficRestrictions,
      this.campaignApplication,
      this.haveUpsizedReward = false})
      : _budgets = budgets,
        _defaultRewards = defaultRewards,
        _categoryRewards = categoryRewards,
        _categories = categories,
        _deviceTypes = deviceTypes,
        _customerCountries = customerCountries,
        _rejectConditions = rejectConditions,
        _validTrafficRestrictions = validTrafficRestrictions,
        _invalidTrafficRestrictions = invalidTrafficRestrictions;

  factory _$CampaignDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignDetailsImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  final CampaignType type;
  @override
  final String url;
  @override
  final String? imageUrl;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final String englishDescription;
  final List<CampaignBudget> _budgets;
  @override
  @JsonKey()
  List<CampaignBudget> get budgets {
    if (_budgets is EqualUnmodifiableListView) return _budgets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_budgets);
  }

  final List<DefaultReward> _defaultRewards;
  @override
  @JsonKey()
  List<DefaultReward> get defaultRewards {
    if (_defaultRewards is EqualUnmodifiableListView) return _defaultRewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_defaultRewards);
  }

  final List<ProductCategoryReward> _categoryRewards;
  @override
  @JsonKey()
  List<ProductCategoryReward> get categoryRewards {
    if (_categoryRewards is EqualUnmodifiableListView) return _categoryRewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categoryRewards);
  }

  final List<Category> _categories;
  @override
  @JsonKey()
  List<Category> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  @override
  final AffiliationStatus? affiliationStatus;
  @override
  final String? currency;
  @override
  final bool? isRewardsByCategoriesVisible;
  @override
  final CampaignStatus? campaignStatus;
  final List<String>? _deviceTypes;
  @override
  List<String>? get deviceTypes {
    final value = _deviceTypes;
    if (value == null) return null;
    if (_deviceTypes is EqualUnmodifiableListView) return _deviceTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _customerCountries;
  @override
  List<String>? get customerCountries {
    final value = _customerCountries;
    if (value == null) return null;
    if (_customerCountries is EqualUnmodifiableListView)
      return _customerCountries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _rejectConditions;
  @override
  List<String>? get rejectConditions {
    final value = _rejectConditions;
    if (value == null) return null;
    if (_rejectConditions is EqualUnmodifiableListView)
      return _rejectConditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? requiredActionLocal;
  @override
  final String? requiredActionEnglish;
  @override
  final String? rejectConditionLocal;
  @override
  final String? rejectConditionEnglish;
  @override
  final String? validationTermLocal;
  @override
  final String? validationTermEnglish;
  @override
  final String? conversionWindow;
  @override
  final DateTime? appliedDate;
  @override
  final DateTime? rejectedDate;
  @override
  final bool? isReferralCampaign;
  final List<String>? _validTrafficRestrictions;
  @override
  List<String>? get validTrafficRestrictions {
    final value = _validTrafficRestrictions;
    if (value == null) return null;
    if (_validTrafficRestrictions is EqualUnmodifiableListView)
      return _validTrafficRestrictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _invalidTrafficRestrictions;
  @override
  List<String>? get invalidTrafficRestrictions {
    final value = _invalidTrafficRestrictions;
    if (value == null) return null;
    if (_invalidTrafficRestrictions is EqualUnmodifiableListView)
      return _invalidTrafficRestrictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final CampaignApplication? campaignApplication;
  @override
  @JsonKey()
  final dynamic haveUpsizedReward;

  @override
  String toString() {
    return 'CampaignDetails(id: $id, name: $name, type: $type, url: $url, imageUrl: $imageUrl, startDate: $startDate, endDate: $endDate, description: $description, englishDescription: $englishDescription, budgets: $budgets, defaultRewards: $defaultRewards, categoryRewards: $categoryRewards, categories: $categories, affiliationStatus: $affiliationStatus, currency: $currency, isRewardsByCategoriesVisible: $isRewardsByCategoriesVisible, campaignStatus: $campaignStatus, deviceTypes: $deviceTypes, customerCountries: $customerCountries, rejectConditions: $rejectConditions, requiredActionLocal: $requiredActionLocal, requiredActionEnglish: $requiredActionEnglish, rejectConditionLocal: $rejectConditionLocal, rejectConditionEnglish: $rejectConditionEnglish, validationTermLocal: $validationTermLocal, validationTermEnglish: $validationTermEnglish, conversionWindow: $conversionWindow, appliedDate: $appliedDate, rejectedDate: $rejectedDate, isReferralCampaign: $isReferralCampaign, validTrafficRestrictions: $validTrafficRestrictions, invalidTrafficRestrictions: $invalidTrafficRestrictions, campaignApplication: $campaignApplication, haveUpsizedReward: $haveUpsizedReward)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignDetailsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.englishDescription, englishDescription) ||
                other.englishDescription == englishDescription) &&
            const DeepCollectionEquality().equals(other._budgets, _budgets) &&
            const DeepCollectionEquality()
                .equals(other._defaultRewards, _defaultRewards) &&
            const DeepCollectionEquality()
                .equals(other._categoryRewards, _categoryRewards) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            (identical(other.affiliationStatus, affiliationStatus) ||
                other.affiliationStatus == affiliationStatus) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.isRewardsByCategoriesVisible,
                    isRewardsByCategoriesVisible) ||
                other.isRewardsByCategoriesVisible ==
                    isRewardsByCategoriesVisible) &&
            (identical(other.campaignStatus, campaignStatus) ||
                other.campaignStatus == campaignStatus) &&
            const DeepCollectionEquality()
                .equals(other._deviceTypes, _deviceTypes) &&
            const DeepCollectionEquality()
                .equals(other._customerCountries, _customerCountries) &&
            const DeepCollectionEquality()
                .equals(other._rejectConditions, _rejectConditions) &&
            (identical(other.requiredActionLocal, requiredActionLocal) ||
                other.requiredActionLocal == requiredActionLocal) &&
            (identical(other.requiredActionEnglish, requiredActionEnglish) ||
                other.requiredActionEnglish == requiredActionEnglish) &&
            (identical(other.rejectConditionLocal, rejectConditionLocal) ||
                other.rejectConditionLocal == rejectConditionLocal) &&
            (identical(other.rejectConditionEnglish, rejectConditionEnglish) ||
                other.rejectConditionEnglish == rejectConditionEnglish) &&
            (identical(other.validationTermLocal, validationTermLocal) ||
                other.validationTermLocal == validationTermLocal) &&
            (identical(other.validationTermEnglish, validationTermEnglish) ||
                other.validationTermEnglish == validationTermEnglish) &&
            (identical(other.conversionWindow, conversionWindow) ||
                other.conversionWindow == conversionWindow) &&
            (identical(other.appliedDate, appliedDate) ||
                other.appliedDate == appliedDate) &&
            (identical(other.rejectedDate, rejectedDate) ||
                other.rejectedDate == rejectedDate) &&
            (identical(other.isReferralCampaign, isReferralCampaign) ||
                other.isReferralCampaign == isReferralCampaign) &&
            const DeepCollectionEquality().equals(
                other._validTrafficRestrictions, _validTrafficRestrictions) &&
            const DeepCollectionEquality().equals(
                other._invalidTrafficRestrictions,
                _invalidTrafficRestrictions) &&
            (identical(other.campaignApplication, campaignApplication) ||
                other.campaignApplication == campaignApplication) &&
            const DeepCollectionEquality()
                .equals(other.haveUpsizedReward, haveUpsizedReward));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        name,
        type,
        url,
        imageUrl,
        startDate,
        endDate,
        description,
        englishDescription,
        const DeepCollectionEquality().hash(_budgets),
        const DeepCollectionEquality().hash(_defaultRewards),
        const DeepCollectionEquality().hash(_categoryRewards),
        const DeepCollectionEquality().hash(_categories),
        affiliationStatus,
        currency,
        isRewardsByCategoriesVisible,
        campaignStatus,
        const DeepCollectionEquality().hash(_deviceTypes),
        const DeepCollectionEquality().hash(_customerCountries),
        const DeepCollectionEquality().hash(_rejectConditions),
        requiredActionLocal,
        requiredActionEnglish,
        rejectConditionLocal,
        rejectConditionEnglish,
        validationTermLocal,
        validationTermEnglish,
        conversionWindow,
        appliedDate,
        rejectedDate,
        isReferralCampaign,
        const DeepCollectionEquality().hash(_validTrafficRestrictions),
        const DeepCollectionEquality().hash(_invalidTrafficRestrictions),
        campaignApplication,
        const DeepCollectionEquality().hash(haveUpsizedReward)
      ]);

  /// Create a copy of CampaignDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignDetailsImplCopyWith<_$CampaignDetailsImpl> get copyWith =>
      __$$CampaignDetailsImplCopyWithImpl<_$CampaignDetailsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignDetailsImplToJson(
      this,
    );
  }
}

abstract class _CampaignDetails implements CampaignDetails {
  const factory _CampaignDetails(
      {required final int id,
      required final String name,
      required final CampaignType type,
      required final String url,
      final String? imageUrl,
      final DateTime? startDate,
      final DateTime? endDate,
      final String description,
      final String englishDescription,
      final List<CampaignBudget> budgets,
      final List<DefaultReward> defaultRewards,
      final List<ProductCategoryReward> categoryRewards,
      final List<Category> categories,
      final AffiliationStatus? affiliationStatus,
      final String? currency,
      final bool? isRewardsByCategoriesVisible,
      final CampaignStatus? campaignStatus,
      final List<String>? deviceTypes,
      final List<String>? customerCountries,
      final List<String>? rejectConditions,
      final String? requiredActionLocal,
      final String? requiredActionEnglish,
      final String? rejectConditionLocal,
      final String? rejectConditionEnglish,
      final String? validationTermLocal,
      final String? validationTermEnglish,
      final String? conversionWindow,
      final DateTime? appliedDate,
      final DateTime? rejectedDate,
      final bool? isReferralCampaign,
      final List<String>? validTrafficRestrictions,
      final List<String>? invalidTrafficRestrictions,
      final CampaignApplication? campaignApplication,
      final dynamic haveUpsizedReward}) = _$CampaignDetailsImpl;

  factory _CampaignDetails.fromJson(Map<String, dynamic> json) =
      _$CampaignDetailsImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  CampaignType get type;
  @override
  String get url;
  @override
  String? get imageUrl;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  String get description;
  @override
  String get englishDescription;
  @override
  List<CampaignBudget> get budgets;
  @override
  List<DefaultReward> get defaultRewards;
  @override
  List<ProductCategoryReward> get categoryRewards;
  @override
  List<Category> get categories;
  @override
  AffiliationStatus? get affiliationStatus;
  @override
  String? get currency;
  @override
  bool? get isRewardsByCategoriesVisible;
  @override
  CampaignStatus? get campaignStatus;
  @override
  List<String>? get deviceTypes;
  @override
  List<String>? get customerCountries;
  @override
  List<String>? get rejectConditions;
  @override
  String? get requiredActionLocal;
  @override
  String? get requiredActionEnglish;
  @override
  String? get rejectConditionLocal;
  @override
  String? get rejectConditionEnglish;
  @override
  String? get validationTermLocal;
  @override
  String? get validationTermEnglish;
  @override
  String? get conversionWindow;
  @override
  DateTime? get appliedDate;
  @override
  DateTime? get rejectedDate;
  @override
  bool? get isReferralCampaign;
  @override
  List<String>? get validTrafficRestrictions;
  @override
  List<String>? get invalidTrafficRestrictions;
  @override
  CampaignApplication? get campaignApplication;
  @override
  dynamic get haveUpsizedReward;

  /// Create a copy of CampaignDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignDetailsImplCopyWith<_$CampaignDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ApplyCampaignRequest _$ApplyCampaignRequestFromJson(Map<String, dynamic> json) {
  return _ApplyCampaignRequest.fromJson(json);
}

/// @nodoc
mixin _$ApplyCampaignRequest {
  List<int> get campaignIds => throw _privateConstructorUsedError;
  int get siteId => throw _privateConstructorUsedError;

  /// Serializes this ApplyCampaignRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ApplyCampaignRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApplyCampaignRequestCopyWith<ApplyCampaignRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApplyCampaignRequestCopyWith<$Res> {
  factory $ApplyCampaignRequestCopyWith(ApplyCampaignRequest value,
          $Res Function(ApplyCampaignRequest) then) =
      _$ApplyCampaignRequestCopyWithImpl<$Res, ApplyCampaignRequest>;
  @useResult
  $Res call({List<int> campaignIds, int siteId});
}

/// @nodoc
class _$ApplyCampaignRequestCopyWithImpl<$Res,
        $Val extends ApplyCampaignRequest>
    implements $ApplyCampaignRequestCopyWith<$Res> {
  _$ApplyCampaignRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApplyCampaignRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignIds = null,
    Object? siteId = null,
  }) {
    return _then(_value.copyWith(
      campaignIds: null == campaignIds
          ? _value.campaignIds
          : campaignIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ApplyCampaignRequestImplCopyWith<$Res>
    implements $ApplyCampaignRequestCopyWith<$Res> {
  factory _$$ApplyCampaignRequestImplCopyWith(_$ApplyCampaignRequestImpl value,
          $Res Function(_$ApplyCampaignRequestImpl) then) =
      __$$ApplyCampaignRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<int> campaignIds, int siteId});
}

/// @nodoc
class __$$ApplyCampaignRequestImplCopyWithImpl<$Res>
    extends _$ApplyCampaignRequestCopyWithImpl<$Res, _$ApplyCampaignRequestImpl>
    implements _$$ApplyCampaignRequestImplCopyWith<$Res> {
  __$$ApplyCampaignRequestImplCopyWithImpl(_$ApplyCampaignRequestImpl _value,
      $Res Function(_$ApplyCampaignRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApplyCampaignRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignIds = null,
    Object? siteId = null,
  }) {
    return _then(_$ApplyCampaignRequestImpl(
      campaignIds: null == campaignIds
          ? _value._campaignIds
          : campaignIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ApplyCampaignRequestImpl implements _ApplyCampaignRequest {
  _$ApplyCampaignRequestImpl(
      {required final List<int> campaignIds, required this.siteId})
      : _campaignIds = campaignIds;

  factory _$ApplyCampaignRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ApplyCampaignRequestImplFromJson(json);

  final List<int> _campaignIds;
  @override
  List<int> get campaignIds {
    if (_campaignIds is EqualUnmodifiableListView) return _campaignIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaignIds);
  }

  @override
  final int siteId;

  @override
  String toString() {
    return 'ApplyCampaignRequest(campaignIds: $campaignIds, siteId: $siteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApplyCampaignRequestImpl &&
            const DeepCollectionEquality()
                .equals(other._campaignIds, _campaignIds) &&
            (identical(other.siteId, siteId) || other.siteId == siteId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_campaignIds), siteId);

  /// Create a copy of ApplyCampaignRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApplyCampaignRequestImplCopyWith<_$ApplyCampaignRequestImpl>
      get copyWith =>
          __$$ApplyCampaignRequestImplCopyWithImpl<_$ApplyCampaignRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ApplyCampaignRequestImplToJson(
      this,
    );
  }
}

abstract class _ApplyCampaignRequest implements ApplyCampaignRequest {
  factory _ApplyCampaignRequest(
      {required final List<int> campaignIds,
      required final int siteId}) = _$ApplyCampaignRequestImpl;

  factory _ApplyCampaignRequest.fromJson(Map<String, dynamic> json) =
      _$ApplyCampaignRequestImpl.fromJson;

  @override
  List<int> get campaignIds;
  @override
  int get siteId;

  /// Create a copy of ApplyCampaignRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApplyCampaignRequestImplCopyWith<_$ApplyCampaignRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CampaignBudget _$CampaignBudgetFromJson(Map<String, dynamic> json) {
  return _CampaignBudget.fromJson(json);
}

/// @nodoc
mixin _$CampaignBudget {
  CampaignBudgetType get type => throw _privateConstructorUsedError;
  int get cap => throw _privateConstructorUsedError;

  /// Serializes this CampaignBudget to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignBudget
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignBudgetCopyWith<CampaignBudget> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignBudgetCopyWith<$Res> {
  factory $CampaignBudgetCopyWith(
          CampaignBudget value, $Res Function(CampaignBudget) then) =
      _$CampaignBudgetCopyWithImpl<$Res, CampaignBudget>;
  @useResult
  $Res call({CampaignBudgetType type, int cap});
}

/// @nodoc
class _$CampaignBudgetCopyWithImpl<$Res, $Val extends CampaignBudget>
    implements $CampaignBudgetCopyWith<$Res> {
  _$CampaignBudgetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignBudget
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? cap = null,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as CampaignBudgetType,
      cap: null == cap
          ? _value.cap
          : cap // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignBudgetImplCopyWith<$Res>
    implements $CampaignBudgetCopyWith<$Res> {
  factory _$$CampaignBudgetImplCopyWith(_$CampaignBudgetImpl value,
          $Res Function(_$CampaignBudgetImpl) then) =
      __$$CampaignBudgetImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({CampaignBudgetType type, int cap});
}

/// @nodoc
class __$$CampaignBudgetImplCopyWithImpl<$Res>
    extends _$CampaignBudgetCopyWithImpl<$Res, _$CampaignBudgetImpl>
    implements _$$CampaignBudgetImplCopyWith<$Res> {
  __$$CampaignBudgetImplCopyWithImpl(
      _$CampaignBudgetImpl _value, $Res Function(_$CampaignBudgetImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignBudget
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? cap = null,
  }) {
    return _then(_$CampaignBudgetImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as CampaignBudgetType,
      cap: null == cap
          ? _value.cap
          : cap // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignBudgetImpl implements _CampaignBudget {
  _$CampaignBudgetImpl({required this.type, required this.cap});

  factory _$CampaignBudgetImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignBudgetImplFromJson(json);

  @override
  final CampaignBudgetType type;
  @override
  final int cap;

  @override
  String toString() {
    return 'CampaignBudget(type: $type, cap: $cap)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignBudgetImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.cap, cap) || other.cap == cap));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, cap);

  /// Create a copy of CampaignBudget
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignBudgetImplCopyWith<_$CampaignBudgetImpl> get copyWith =>
      __$$CampaignBudgetImplCopyWithImpl<_$CampaignBudgetImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignBudgetImplToJson(
      this,
    );
  }
}

abstract class _CampaignBudget implements CampaignBudget {
  factory _CampaignBudget(
      {required final CampaignBudgetType type,
      required final int cap}) = _$CampaignBudgetImpl;

  factory _CampaignBudget.fromJson(Map<String, dynamic> json) =
      _$CampaignBudgetImpl.fromJson;

  @override
  CampaignBudgetType get type;
  @override
  int get cap;

  /// Create a copy of CampaignBudget
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignBudgetImplCopyWith<_$CampaignBudgetImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DefaultReward _$DefaultRewardFromJson(Map<String, dynamic> json) {
  return _DefaultReward.fromJson(json);
}

/// @nodoc
mixin _$DefaultReward {
  String? get type => throw _privateConstructorUsedError;
  set type(String? value) => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  set name(String? value) => throw _privateConstructorUsedError;
  double? get reward => throw _privateConstructorUsedError;
  set reward(double? value) => throw _privateConstructorUsedError;
  double? get previousReward => throw _privateConstructorUsedError;
  set previousReward(double? value) => throw _privateConstructorUsedError;
  double? get maxPublisherReward => throw _privateConstructorUsedError;
  set maxPublisherReward(double? value) => throw _privateConstructorUsedError;
  double? get rewardByPublisherCurrency => throw _privateConstructorUsedError;
  set rewardByPublisherCurrency(double? value) =>
      throw _privateConstructorUsedError;
  double? get previousRewardByPublisherCurrency =>
      throw _privateConstructorUsedError;
  set previousRewardByPublisherCurrency(double? value) =>
      throw _privateConstructorUsedError;
  String? get customerType => throw _privateConstructorUsedError;
  set customerType(String? value) => throw _privateConstructorUsedError;
  String? get customerTypeName => throw _privateConstructorUsedError;
  set customerTypeName(String? value) => throw _privateConstructorUsedError;
  bool? get isAllSameRewardAmount => throw _privateConstructorUsedError;
  set isAllSameRewardAmount(bool? value) => throw _privateConstructorUsedError;
  String? get targetTimeFrom => throw _privateConstructorUsedError;
  set targetTimeFrom(String? value) => throw _privateConstructorUsedError;
  String? get targetTimeTo => throw _privateConstructorUsedError;
  set targetTimeTo(String? value) => throw _privateConstructorUsedError;
  bool? get isExpired => throw _privateConstructorUsedError;
  set isExpired(bool? value) => throw _privateConstructorUsedError;

  /// Serializes this DefaultReward to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DefaultReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DefaultRewardCopyWith<DefaultReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DefaultRewardCopyWith<$Res> {
  factory $DefaultRewardCopyWith(
          DefaultReward value, $Res Function(DefaultReward) then) =
      _$DefaultRewardCopyWithImpl<$Res, DefaultReward>;
  @useResult
  $Res call(
      {String? type,
      String? name,
      double? reward,
      double? previousReward,
      double? maxPublisherReward,
      double? rewardByPublisherCurrency,
      double? previousRewardByPublisherCurrency,
      String? customerType,
      String? customerTypeName,
      bool? isAllSameRewardAmount,
      String? targetTimeFrom,
      String? targetTimeTo,
      bool? isExpired});
}

/// @nodoc
class _$DefaultRewardCopyWithImpl<$Res, $Val extends DefaultReward>
    implements $DefaultRewardCopyWith<$Res> {
  _$DefaultRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DefaultReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? reward = freezed,
    Object? previousReward = freezed,
    Object? maxPublisherReward = freezed,
    Object? rewardByPublisherCurrency = freezed,
    Object? previousRewardByPublisherCurrency = freezed,
    Object? customerType = freezed,
    Object? customerTypeName = freezed,
    Object? isAllSameRewardAmount = freezed,
    Object? targetTimeFrom = freezed,
    Object? targetTimeTo = freezed,
    Object? isExpired = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      reward: freezed == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double?,
      previousReward: freezed == previousReward
          ? _value.previousReward
          : previousReward // ignore: cast_nullable_to_non_nullable
              as double?,
      maxPublisherReward: freezed == maxPublisherReward
          ? _value.maxPublisherReward
          : maxPublisherReward // ignore: cast_nullable_to_non_nullable
              as double?,
      rewardByPublisherCurrency: freezed == rewardByPublisherCurrency
          ? _value.rewardByPublisherCurrency
          : rewardByPublisherCurrency // ignore: cast_nullable_to_non_nullable
              as double?,
      previousRewardByPublisherCurrency: freezed ==
              previousRewardByPublisherCurrency
          ? _value.previousRewardByPublisherCurrency
          : previousRewardByPublisherCurrency // ignore: cast_nullable_to_non_nullable
              as double?,
      customerType: freezed == customerType
          ? _value.customerType
          : customerType // ignore: cast_nullable_to_non_nullable
              as String?,
      customerTypeName: freezed == customerTypeName
          ? _value.customerTypeName
          : customerTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      isAllSameRewardAmount: freezed == isAllSameRewardAmount
          ? _value.isAllSameRewardAmount
          : isAllSameRewardAmount // ignore: cast_nullable_to_non_nullable
              as bool?,
      targetTimeFrom: freezed == targetTimeFrom
          ? _value.targetTimeFrom
          : targetTimeFrom // ignore: cast_nullable_to_non_nullable
              as String?,
      targetTimeTo: freezed == targetTimeTo
          ? _value.targetTimeTo
          : targetTimeTo // ignore: cast_nullable_to_non_nullable
              as String?,
      isExpired: freezed == isExpired
          ? _value.isExpired
          : isExpired // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DefaultRewardImplCopyWith<$Res>
    implements $DefaultRewardCopyWith<$Res> {
  factory _$$DefaultRewardImplCopyWith(
          _$DefaultRewardImpl value, $Res Function(_$DefaultRewardImpl) then) =
      __$$DefaultRewardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? type,
      String? name,
      double? reward,
      double? previousReward,
      double? maxPublisherReward,
      double? rewardByPublisherCurrency,
      double? previousRewardByPublisherCurrency,
      String? customerType,
      String? customerTypeName,
      bool? isAllSameRewardAmount,
      String? targetTimeFrom,
      String? targetTimeTo,
      bool? isExpired});
}

/// @nodoc
class __$$DefaultRewardImplCopyWithImpl<$Res>
    extends _$DefaultRewardCopyWithImpl<$Res, _$DefaultRewardImpl>
    implements _$$DefaultRewardImplCopyWith<$Res> {
  __$$DefaultRewardImplCopyWithImpl(
      _$DefaultRewardImpl _value, $Res Function(_$DefaultRewardImpl) _then)
      : super(_value, _then);

  /// Create a copy of DefaultReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? reward = freezed,
    Object? previousReward = freezed,
    Object? maxPublisherReward = freezed,
    Object? rewardByPublisherCurrency = freezed,
    Object? previousRewardByPublisherCurrency = freezed,
    Object? customerType = freezed,
    Object? customerTypeName = freezed,
    Object? isAllSameRewardAmount = freezed,
    Object? targetTimeFrom = freezed,
    Object? targetTimeTo = freezed,
    Object? isExpired = freezed,
  }) {
    return _then(_$DefaultRewardImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      reward: freezed == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double?,
      previousReward: freezed == previousReward
          ? _value.previousReward
          : previousReward // ignore: cast_nullable_to_non_nullable
              as double?,
      maxPublisherReward: freezed == maxPublisherReward
          ? _value.maxPublisherReward
          : maxPublisherReward // ignore: cast_nullable_to_non_nullable
              as double?,
      rewardByPublisherCurrency: freezed == rewardByPublisherCurrency
          ? _value.rewardByPublisherCurrency
          : rewardByPublisherCurrency // ignore: cast_nullable_to_non_nullable
              as double?,
      previousRewardByPublisherCurrency: freezed ==
              previousRewardByPublisherCurrency
          ? _value.previousRewardByPublisherCurrency
          : previousRewardByPublisherCurrency // ignore: cast_nullable_to_non_nullable
              as double?,
      customerType: freezed == customerType
          ? _value.customerType
          : customerType // ignore: cast_nullable_to_non_nullable
              as String?,
      customerTypeName: freezed == customerTypeName
          ? _value.customerTypeName
          : customerTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      isAllSameRewardAmount: freezed == isAllSameRewardAmount
          ? _value.isAllSameRewardAmount
          : isAllSameRewardAmount // ignore: cast_nullable_to_non_nullable
              as bool?,
      targetTimeFrom: freezed == targetTimeFrom
          ? _value.targetTimeFrom
          : targetTimeFrom // ignore: cast_nullable_to_non_nullable
              as String?,
      targetTimeTo: freezed == targetTimeTo
          ? _value.targetTimeTo
          : targetTimeTo // ignore: cast_nullable_to_non_nullable
              as String?,
      isExpired: freezed == isExpired
          ? _value.isExpired
          : isExpired // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DefaultRewardImpl implements _DefaultReward {
  _$DefaultRewardImpl(
      {this.type,
      this.name,
      this.reward,
      this.previousReward,
      this.maxPublisherReward,
      this.rewardByPublisherCurrency,
      this.previousRewardByPublisherCurrency,
      this.customerType,
      this.customerTypeName,
      this.isAllSameRewardAmount,
      this.targetTimeFrom,
      this.targetTimeTo,
      this.isExpired});

  factory _$DefaultRewardImpl.fromJson(Map<String, dynamic> json) =>
      _$$DefaultRewardImplFromJson(json);

  @override
  String? type;
  @override
  String? name;
  @override
  double? reward;
  @override
  double? previousReward;
  @override
  double? maxPublisherReward;
  @override
  double? rewardByPublisherCurrency;
  @override
  double? previousRewardByPublisherCurrency;
  @override
  String? customerType;
  @override
  String? customerTypeName;
  @override
  bool? isAllSameRewardAmount;
  @override
  String? targetTimeFrom;
  @override
  String? targetTimeTo;
  @override
  bool? isExpired;

  @override
  String toString() {
    return 'DefaultReward(type: $type, name: $name, reward: $reward, previousReward: $previousReward, maxPublisherReward: $maxPublisherReward, rewardByPublisherCurrency: $rewardByPublisherCurrency, previousRewardByPublisherCurrency: $previousRewardByPublisherCurrency, customerType: $customerType, customerTypeName: $customerTypeName, isAllSameRewardAmount: $isAllSameRewardAmount, targetTimeFrom: $targetTimeFrom, targetTimeTo: $targetTimeTo, isExpired: $isExpired)';
  }

  /// Create a copy of DefaultReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DefaultRewardImplCopyWith<_$DefaultRewardImpl> get copyWith =>
      __$$DefaultRewardImplCopyWithImpl<_$DefaultRewardImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DefaultRewardImplToJson(
      this,
    );
  }
}

abstract class _DefaultReward implements DefaultReward {
  factory _DefaultReward(
      {String? type,
      String? name,
      double? reward,
      double? previousReward,
      double? maxPublisherReward,
      double? rewardByPublisherCurrency,
      double? previousRewardByPublisherCurrency,
      String? customerType,
      String? customerTypeName,
      bool? isAllSameRewardAmount,
      String? targetTimeFrom,
      String? targetTimeTo,
      bool? isExpired}) = _$DefaultRewardImpl;

  factory _DefaultReward.fromJson(Map<String, dynamic> json) =
      _$DefaultRewardImpl.fromJson;

  @override
  String? get type;
  set type(String? value);
  @override
  String? get name;
  set name(String? value);
  @override
  double? get reward;
  set reward(double? value);
  @override
  double? get previousReward;
  set previousReward(double? value);
  @override
  double? get maxPublisherReward;
  set maxPublisherReward(double? value);
  @override
  double? get rewardByPublisherCurrency;
  set rewardByPublisherCurrency(double? value);
  @override
  double? get previousRewardByPublisherCurrency;
  set previousRewardByPublisherCurrency(double? value);
  @override
  String? get customerType;
  set customerType(String? value);
  @override
  String? get customerTypeName;
  set customerTypeName(String? value);
  @override
  bool? get isAllSameRewardAmount;
  set isAllSameRewardAmount(bool? value);
  @override
  String? get targetTimeFrom;
  set targetTimeFrom(String? value);
  @override
  String? get targetTimeTo;
  set targetTimeTo(String? value);
  @override
  bool? get isExpired;
  set isExpired(bool? value);

  /// Create a copy of DefaultReward
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DefaultRewardImplCopyWith<_$DefaultRewardImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductCategoryReward _$ProductCategoryRewardFromJson(
    Map<String, dynamic> json) {
  return _ProductCategoryReward.fromJson(json);
}

/// @nodoc
mixin _$ProductCategoryReward {
  DefaultReward? get maxReward => throw _privateConstructorUsedError;
  set maxReward(DefaultReward? value) => throw _privateConstructorUsedError;
  List<DefaultReward> get allCategoryRewards =>
      throw _privateConstructorUsedError;
  set allCategoryRewards(List<DefaultReward> value) =>
      throw _privateConstructorUsedError;

  /// Serializes this ProductCategoryReward to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductCategoryReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductCategoryRewardCopyWith<ProductCategoryReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductCategoryRewardCopyWith<$Res> {
  factory $ProductCategoryRewardCopyWith(ProductCategoryReward value,
          $Res Function(ProductCategoryReward) then) =
      _$ProductCategoryRewardCopyWithImpl<$Res, ProductCategoryReward>;
  @useResult
  $Res call({DefaultReward? maxReward, List<DefaultReward> allCategoryRewards});

  $DefaultRewardCopyWith<$Res>? get maxReward;
}

/// @nodoc
class _$ProductCategoryRewardCopyWithImpl<$Res,
        $Val extends ProductCategoryReward>
    implements $ProductCategoryRewardCopyWith<$Res> {
  _$ProductCategoryRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductCategoryReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxReward = freezed,
    Object? allCategoryRewards = null,
  }) {
    return _then(_value.copyWith(
      maxReward: freezed == maxReward
          ? _value.maxReward
          : maxReward // ignore: cast_nullable_to_non_nullable
              as DefaultReward?,
      allCategoryRewards: null == allCategoryRewards
          ? _value.allCategoryRewards
          : allCategoryRewards // ignore: cast_nullable_to_non_nullable
              as List<DefaultReward>,
    ) as $Val);
  }

  /// Create a copy of ProductCategoryReward
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DefaultRewardCopyWith<$Res>? get maxReward {
    if (_value.maxReward == null) {
      return null;
    }

    return $DefaultRewardCopyWith<$Res>(_value.maxReward!, (value) {
      return _then(_value.copyWith(maxReward: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductCategoryRewardImplCopyWith<$Res>
    implements $ProductCategoryRewardCopyWith<$Res> {
  factory _$$ProductCategoryRewardImplCopyWith(
          _$ProductCategoryRewardImpl value,
          $Res Function(_$ProductCategoryRewardImpl) then) =
      __$$ProductCategoryRewardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DefaultReward? maxReward, List<DefaultReward> allCategoryRewards});

  @override
  $DefaultRewardCopyWith<$Res>? get maxReward;
}

/// @nodoc
class __$$ProductCategoryRewardImplCopyWithImpl<$Res>
    extends _$ProductCategoryRewardCopyWithImpl<$Res,
        _$ProductCategoryRewardImpl>
    implements _$$ProductCategoryRewardImplCopyWith<$Res> {
  __$$ProductCategoryRewardImplCopyWithImpl(_$ProductCategoryRewardImpl _value,
      $Res Function(_$ProductCategoryRewardImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductCategoryReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxReward = freezed,
    Object? allCategoryRewards = null,
  }) {
    return _then(_$ProductCategoryRewardImpl(
      maxReward: freezed == maxReward
          ? _value.maxReward
          : maxReward // ignore: cast_nullable_to_non_nullable
              as DefaultReward?,
      allCategoryRewards: null == allCategoryRewards
          ? _value.allCategoryRewards
          : allCategoryRewards // ignore: cast_nullable_to_non_nullable
              as List<DefaultReward>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductCategoryRewardImpl implements _ProductCategoryReward {
  _$ProductCategoryRewardImpl(
      {this.maxReward, this.allCategoryRewards = const []});

  factory _$ProductCategoryRewardImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductCategoryRewardImplFromJson(json);

  @override
  DefaultReward? maxReward;
  @override
  @JsonKey()
  List<DefaultReward> allCategoryRewards;

  @override
  String toString() {
    return 'ProductCategoryReward(maxReward: $maxReward, allCategoryRewards: $allCategoryRewards)';
  }

  /// Create a copy of ProductCategoryReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductCategoryRewardImplCopyWith<_$ProductCategoryRewardImpl>
      get copyWith => __$$ProductCategoryRewardImplCopyWithImpl<
          _$ProductCategoryRewardImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductCategoryRewardImplToJson(
      this,
    );
  }
}

abstract class _ProductCategoryReward implements ProductCategoryReward {
  factory _ProductCategoryReward(
      {DefaultReward? maxReward,
      List<DefaultReward> allCategoryRewards}) = _$ProductCategoryRewardImpl;

  factory _ProductCategoryReward.fromJson(Map<String, dynamic> json) =
      _$ProductCategoryRewardImpl.fromJson;

  @override
  DefaultReward? get maxReward;
  set maxReward(DefaultReward? value);
  @override
  List<DefaultReward> get allCategoryRewards;
  set allCategoryRewards(List<DefaultReward> value);

  /// Create a copy of ProductCategoryReward
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductCategoryRewardImplCopyWith<_$ProductCategoryRewardImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UpsizedReward _$UpsizedRewardFromJson(Map<String, dynamic> json) {
  return _UpsizedReward.fromJson(json);
}

/// @nodoc
mixin _$UpsizedReward {
  RewardType? get type => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  double get previousReward => throw _privateConstructorUsedError;
  double get reward => throw _privateConstructorUsedError;
  String get customerType => throw _privateConstructorUsedError;
  DateTime? get targetTimeFrom => throw _privateConstructorUsedError;
  DateTime? get targetTimeTo => throw _privateConstructorUsedError;
  bool get isExpired => throw _privateConstructorUsedError;

  /// Serializes this UpsizedReward to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpsizedReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpsizedRewardCopyWith<UpsizedReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpsizedRewardCopyWith<$Res> {
  factory $UpsizedRewardCopyWith(
          UpsizedReward value, $Res Function(UpsizedReward) then) =
      _$UpsizedRewardCopyWithImpl<$Res, UpsizedReward>;
  @useResult
  $Res call(
      {RewardType? type,
      String name,
      double previousReward,
      double reward,
      String customerType,
      DateTime? targetTimeFrom,
      DateTime? targetTimeTo,
      bool isExpired});
}

/// @nodoc
class _$UpsizedRewardCopyWithImpl<$Res, $Val extends UpsizedReward>
    implements $UpsizedRewardCopyWith<$Res> {
  _$UpsizedRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpsizedReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = null,
    Object? previousReward = null,
    Object? reward = null,
    Object? customerType = null,
    Object? targetTimeFrom = freezed,
    Object? targetTimeTo = freezed,
    Object? isExpired = null,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as RewardType?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      previousReward: null == previousReward
          ? _value.previousReward
          : previousReward // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      customerType: null == customerType
          ? _value.customerType
          : customerType // ignore: cast_nullable_to_non_nullable
              as String,
      targetTimeFrom: freezed == targetTimeFrom
          ? _value.targetTimeFrom
          : targetTimeFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      targetTimeTo: freezed == targetTimeTo
          ? _value.targetTimeTo
          : targetTimeTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isExpired: null == isExpired
          ? _value.isExpired
          : isExpired // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpsizedRewardImplCopyWith<$Res>
    implements $UpsizedRewardCopyWith<$Res> {
  factory _$$UpsizedRewardImplCopyWith(
          _$UpsizedRewardImpl value, $Res Function(_$UpsizedRewardImpl) then) =
      __$$UpsizedRewardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {RewardType? type,
      String name,
      double previousReward,
      double reward,
      String customerType,
      DateTime? targetTimeFrom,
      DateTime? targetTimeTo,
      bool isExpired});
}

/// @nodoc
class __$$UpsizedRewardImplCopyWithImpl<$Res>
    extends _$UpsizedRewardCopyWithImpl<$Res, _$UpsizedRewardImpl>
    implements _$$UpsizedRewardImplCopyWith<$Res> {
  __$$UpsizedRewardImplCopyWithImpl(
      _$UpsizedRewardImpl _value, $Res Function(_$UpsizedRewardImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpsizedReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = null,
    Object? previousReward = null,
    Object? reward = null,
    Object? customerType = null,
    Object? targetTimeFrom = freezed,
    Object? targetTimeTo = freezed,
    Object? isExpired = null,
  }) {
    return _then(_$UpsizedRewardImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as RewardType?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      previousReward: null == previousReward
          ? _value.previousReward
          : previousReward // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      customerType: null == customerType
          ? _value.customerType
          : customerType // ignore: cast_nullable_to_non_nullable
              as String,
      targetTimeFrom: freezed == targetTimeFrom
          ? _value.targetTimeFrom
          : targetTimeFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      targetTimeTo: freezed == targetTimeTo
          ? _value.targetTimeTo
          : targetTimeTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isExpired: null == isExpired
          ? _value.isExpired
          : isExpired // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpsizedRewardImpl implements _UpsizedReward {
  _$UpsizedRewardImpl(
      {this.type,
      this.name = '',
      this.previousReward = 0,
      this.reward = 0,
      this.customerType = '',
      this.targetTimeFrom,
      this.targetTimeTo,
      this.isExpired = false});

  factory _$UpsizedRewardImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpsizedRewardImplFromJson(json);

  @override
  final RewardType? type;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final double previousReward;
  @override
  @JsonKey()
  final double reward;
  @override
  @JsonKey()
  final String customerType;
  @override
  final DateTime? targetTimeFrom;
  @override
  final DateTime? targetTimeTo;
  @override
  @JsonKey()
  final bool isExpired;

  @override
  String toString() {
    return 'UpsizedReward(type: $type, name: $name, previousReward: $previousReward, reward: $reward, customerType: $customerType, targetTimeFrom: $targetTimeFrom, targetTimeTo: $targetTimeTo, isExpired: $isExpired)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpsizedRewardImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.previousReward, previousReward) ||
                other.previousReward == previousReward) &&
            (identical(other.reward, reward) || other.reward == reward) &&
            (identical(other.customerType, customerType) ||
                other.customerType == customerType) &&
            (identical(other.targetTimeFrom, targetTimeFrom) ||
                other.targetTimeFrom == targetTimeFrom) &&
            (identical(other.targetTimeTo, targetTimeTo) ||
                other.targetTimeTo == targetTimeTo) &&
            (identical(other.isExpired, isExpired) ||
                other.isExpired == isExpired));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, name, previousReward,
      reward, customerType, targetTimeFrom, targetTimeTo, isExpired);

  /// Create a copy of UpsizedReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpsizedRewardImplCopyWith<_$UpsizedRewardImpl> get copyWith =>
      __$$UpsizedRewardImplCopyWithImpl<_$UpsizedRewardImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpsizedRewardImplToJson(
      this,
    );
  }
}

abstract class _UpsizedReward implements UpsizedReward {
  factory _UpsizedReward(
      {final RewardType? type,
      final String name,
      final double previousReward,
      final double reward,
      final String customerType,
      final DateTime? targetTimeFrom,
      final DateTime? targetTimeTo,
      final bool isExpired}) = _$UpsizedRewardImpl;

  factory _UpsizedReward.fromJson(Map<String, dynamic> json) =
      _$UpsizedRewardImpl.fromJson;

  @override
  RewardType? get type;
  @override
  String get name;
  @override
  double get previousReward;
  @override
  double get reward;
  @override
  String get customerType;
  @override
  DateTime? get targetTimeFrom;
  @override
  DateTime? get targetTimeTo;
  @override
  bool get isExpired;

  /// Create a copy of UpsizedReward
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpsizedRewardImplCopyWith<_$UpsizedRewardImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Category _$CategoryFromJson(Map<String, dynamic> json) {
  return _Category.fromJson(json);
}

/// @nodoc
mixin _$Category {
  String get name => throw _privateConstructorUsedError;
  int get value => throw _privateConstructorUsedError;

  /// Serializes this Category to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Category
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CategoryCopyWith<Category> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CategoryCopyWith<$Res> {
  factory $CategoryCopyWith(Category value, $Res Function(Category) then) =
      _$CategoryCopyWithImpl<$Res, Category>;
  @useResult
  $Res call({String name, int value});
}

/// @nodoc
class _$CategoryCopyWithImpl<$Res, $Val extends Category>
    implements $CategoryCopyWith<$Res> {
  _$CategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Category
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CategoryImplCopyWith<$Res>
    implements $CategoryCopyWith<$Res> {
  factory _$$CategoryImplCopyWith(
          _$CategoryImpl value, $Res Function(_$CategoryImpl) then) =
      __$$CategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, int value});
}

/// @nodoc
class __$$CategoryImplCopyWithImpl<$Res>
    extends _$CategoryCopyWithImpl<$Res, _$CategoryImpl>
    implements _$$CategoryImplCopyWith<$Res> {
  __$$CategoryImplCopyWithImpl(
      _$CategoryImpl _value, $Res Function(_$CategoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of Category
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
  }) {
    return _then(_$CategoryImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CategoryImpl implements _Category {
  _$CategoryImpl({required this.name, required this.value});

  factory _$CategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$CategoryImplFromJson(json);

  @override
  final String name;
  @override
  final int value;

  @override
  String toString() {
    return 'Category(name: $name, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CategoryImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, value);

  /// Create a copy of Category
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CategoryImplCopyWith<_$CategoryImpl> get copyWith =>
      __$$CategoryImplCopyWithImpl<_$CategoryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CategoryImplToJson(
      this,
    );
  }
}

abstract class _Category implements Category {
  factory _Category({required final String name, required final int value}) =
      _$CategoryImpl;

  factory _Category.fromJson(Map<String, dynamic> json) =
      _$CategoryImpl.fromJson;

  @override
  String get name;
  @override
  int get value;

  /// Create a copy of Category
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CategoryImplCopyWith<_$CategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DefaultCampaignSummary _$DefaultCampaignSummaryFromJson(
    Map<String, dynamic> json) {
  return _DefaultCampaignSummary.fromJson(json);
}

/// @nodoc
mixin _$DefaultCampaignSummary {
  int get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get category => throw _privateConstructorUsedError;
  List<HighestRewardSummary> get highestRewardSummaries =>
      throw _privateConstructorUsedError;
  bool get haveUpsizedReward => throw _privateConstructorUsedError;
  bool get isNew => throw _privateConstructorUsedError;

  /// Serializes this DefaultCampaignSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DefaultCampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DefaultCampaignSummaryCopyWith<DefaultCampaignSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DefaultCampaignSummaryCopyWith<$Res> {
  factory $DefaultCampaignSummaryCopyWith(DefaultCampaignSummary value,
          $Res Function(DefaultCampaignSummary) then) =
      _$DefaultCampaignSummaryCopyWithImpl<$Res, DefaultCampaignSummary>;
  @useResult
  $Res call(
      {int id,
      String? name,
      String? imageUrl,
      String? category,
      List<HighestRewardSummary> highestRewardSummaries,
      bool haveUpsizedReward,
      bool isNew});
}

/// @nodoc
class _$DefaultCampaignSummaryCopyWithImpl<$Res,
        $Val extends DefaultCampaignSummary>
    implements $DefaultCampaignSummaryCopyWith<$Res> {
  _$DefaultCampaignSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DefaultCampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? category = freezed,
    Object? highestRewardSummaries = null,
    Object? haveUpsizedReward = null,
    Object? isNew = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      highestRewardSummaries: null == highestRewardSummaries
          ? _value.highestRewardSummaries
          : highestRewardSummaries // ignore: cast_nullable_to_non_nullable
              as List<HighestRewardSummary>,
      haveUpsizedReward: null == haveUpsizedReward
          ? _value.haveUpsizedReward
          : haveUpsizedReward // ignore: cast_nullable_to_non_nullable
              as bool,
      isNew: null == isNew
          ? _value.isNew
          : isNew // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DefaultCampaignSummaryImplCopyWith<$Res>
    implements $DefaultCampaignSummaryCopyWith<$Res> {
  factory _$$DefaultCampaignSummaryImplCopyWith(
          _$DefaultCampaignSummaryImpl value,
          $Res Function(_$DefaultCampaignSummaryImpl) then) =
      __$$DefaultCampaignSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String? name,
      String? imageUrl,
      String? category,
      List<HighestRewardSummary> highestRewardSummaries,
      bool haveUpsizedReward,
      bool isNew});
}

/// @nodoc
class __$$DefaultCampaignSummaryImplCopyWithImpl<$Res>
    extends _$DefaultCampaignSummaryCopyWithImpl<$Res,
        _$DefaultCampaignSummaryImpl>
    implements _$$DefaultCampaignSummaryImplCopyWith<$Res> {
  __$$DefaultCampaignSummaryImplCopyWithImpl(
      _$DefaultCampaignSummaryImpl _value,
      $Res Function(_$DefaultCampaignSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of DefaultCampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? category = freezed,
    Object? highestRewardSummaries = null,
    Object? haveUpsizedReward = null,
    Object? isNew = null,
  }) {
    return _then(_$DefaultCampaignSummaryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      highestRewardSummaries: null == highestRewardSummaries
          ? _value._highestRewardSummaries
          : highestRewardSummaries // ignore: cast_nullable_to_non_nullable
              as List<HighestRewardSummary>,
      haveUpsizedReward: null == haveUpsizedReward
          ? _value.haveUpsizedReward
          : haveUpsizedReward // ignore: cast_nullable_to_non_nullable
              as bool,
      isNew: null == isNew
          ? _value.isNew
          : isNew // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DefaultCampaignSummaryImpl implements _DefaultCampaignSummary {
  const _$DefaultCampaignSummaryImpl(
      {required this.id,
      this.name,
      this.imageUrl,
      this.category,
      final List<HighestRewardSummary> highestRewardSummaries = const [],
      this.haveUpsizedReward = false,
      this.isNew = false})
      : _highestRewardSummaries = highestRewardSummaries;

  factory _$DefaultCampaignSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$DefaultCampaignSummaryImplFromJson(json);

  @override
  final int id;
  @override
  final String? name;
  @override
  final String? imageUrl;
  @override
  final String? category;
  final List<HighestRewardSummary> _highestRewardSummaries;
  @override
  @JsonKey()
  List<HighestRewardSummary> get highestRewardSummaries {
    if (_highestRewardSummaries is EqualUnmodifiableListView)
      return _highestRewardSummaries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_highestRewardSummaries);
  }

  @override
  @JsonKey()
  final bool haveUpsizedReward;
  @override
  @JsonKey()
  final bool isNew;

  @override
  String toString() {
    return 'DefaultCampaignSummary(id: $id, name: $name, imageUrl: $imageUrl, category: $category, highestRewardSummaries: $highestRewardSummaries, haveUpsizedReward: $haveUpsizedReward, isNew: $isNew)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DefaultCampaignSummaryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(
                other._highestRewardSummaries, _highestRewardSummaries) &&
            (identical(other.haveUpsizedReward, haveUpsizedReward) ||
                other.haveUpsizedReward == haveUpsizedReward) &&
            (identical(other.isNew, isNew) || other.isNew == isNew));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      imageUrl,
      category,
      const DeepCollectionEquality().hash(_highestRewardSummaries),
      haveUpsizedReward,
      isNew);

  /// Create a copy of DefaultCampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DefaultCampaignSummaryImplCopyWith<_$DefaultCampaignSummaryImpl>
      get copyWith => __$$DefaultCampaignSummaryImplCopyWithImpl<
          _$DefaultCampaignSummaryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DefaultCampaignSummaryImplToJson(
      this,
    );
  }
}

abstract class _DefaultCampaignSummary implements DefaultCampaignSummary {
  const factory _DefaultCampaignSummary(
      {required final int id,
      final String? name,
      final String? imageUrl,
      final String? category,
      final List<HighestRewardSummary> highestRewardSummaries,
      final bool haveUpsizedReward,
      final bool isNew}) = _$DefaultCampaignSummaryImpl;

  factory _DefaultCampaignSummary.fromJson(Map<String, dynamic> json) =
      _$DefaultCampaignSummaryImpl.fromJson;

  @override
  int get id;
  @override
  String? get name;
  @override
  String? get imageUrl;
  @override
  String? get category;
  @override
  List<HighestRewardSummary> get highestRewardSummaries;
  @override
  bool get haveUpsizedReward;
  @override
  bool get isNew;

  /// Create a copy of DefaultCampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DefaultCampaignSummaryImplCopyWith<_$DefaultCampaignSummaryImpl>
      get copyWith => throw _privateConstructorUsedError;
}

HighestRewardSummary _$HighestRewardSummaryFromJson(Map<String, dynamic> json) {
  return _HighestRewardSummary.fromJson(json);
}

/// @nodoc
mixin _$HighestRewardSummary {
  String get type => throw _privateConstructorUsedError;
  int get reward => throw _privateConstructorUsedError;

  /// Serializes this HighestRewardSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HighestRewardSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HighestRewardSummaryCopyWith<HighestRewardSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HighestRewardSummaryCopyWith<$Res> {
  factory $HighestRewardSummaryCopyWith(HighestRewardSummary value,
          $Res Function(HighestRewardSummary) then) =
      _$HighestRewardSummaryCopyWithImpl<$Res, HighestRewardSummary>;
  @useResult
  $Res call({String type, int reward});
}

/// @nodoc
class _$HighestRewardSummaryCopyWithImpl<$Res,
        $Val extends HighestRewardSummary>
    implements $HighestRewardSummaryCopyWith<$Res> {
  _$HighestRewardSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HighestRewardSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? reward = null,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HighestRewardSummaryImplCopyWith<$Res>
    implements $HighestRewardSummaryCopyWith<$Res> {
  factory _$$HighestRewardSummaryImplCopyWith(_$HighestRewardSummaryImpl value,
          $Res Function(_$HighestRewardSummaryImpl) then) =
      __$$HighestRewardSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String type, int reward});
}

/// @nodoc
class __$$HighestRewardSummaryImplCopyWithImpl<$Res>
    extends _$HighestRewardSummaryCopyWithImpl<$Res, _$HighestRewardSummaryImpl>
    implements _$$HighestRewardSummaryImplCopyWith<$Res> {
  __$$HighestRewardSummaryImplCopyWithImpl(_$HighestRewardSummaryImpl _value,
      $Res Function(_$HighestRewardSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of HighestRewardSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? reward = null,
  }) {
    return _then(_$HighestRewardSummaryImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HighestRewardSummaryImpl implements _HighestRewardSummary {
  const _$HighestRewardSummaryImpl({required this.type, required this.reward});

  factory _$HighestRewardSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$HighestRewardSummaryImplFromJson(json);

  @override
  final String type;
  @override
  final int reward;

  @override
  String toString() {
    return 'HighestRewardSummary(type: $type, reward: $reward)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HighestRewardSummaryImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.reward, reward) || other.reward == reward));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, reward);

  /// Create a copy of HighestRewardSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HighestRewardSummaryImplCopyWith<_$HighestRewardSummaryImpl>
      get copyWith =>
          __$$HighestRewardSummaryImplCopyWithImpl<_$HighestRewardSummaryImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HighestRewardSummaryImplToJson(
      this,
    );
  }
}

abstract class _HighestRewardSummary implements HighestRewardSummary {
  const factory _HighestRewardSummary(
      {required final String type,
      required final int reward}) = _$HighestRewardSummaryImpl;

  factory _HighestRewardSummary.fromJson(Map<String, dynamic> json) =
      _$HighestRewardSummaryImpl.fromJson;

  @override
  String get type;
  @override
  int get reward;

  /// Create a copy of HighestRewardSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HighestRewardSummaryImplCopyWith<_$HighestRewardSummaryImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CampaignSummary _$CampaignSummaryFromJson(Map<String, dynamic> json) {
  return _CampaignSummary.fromJson(json);
}

/// @nodoc
mixin _$CampaignSummary {
  int get id => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;

  /// Serializes this CampaignSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignSummaryCopyWith<CampaignSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignSummaryCopyWith<$Res> {
  factory $CampaignSummaryCopyWith(
          CampaignSummary value, $Res Function(CampaignSummary) then) =
      _$CampaignSummaryCopyWithImpl<$Res, CampaignSummary>;
  @useResult
  $Res call({int id, String imageUrl, String name, String category});
}

/// @nodoc
class _$CampaignSummaryCopyWithImpl<$Res, $Val extends CampaignSummary>
    implements $CampaignSummaryCopyWith<$Res> {
  _$CampaignSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? imageUrl = null,
    Object? name = null,
    Object? category = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignSummaryImplCopyWith<$Res>
    implements $CampaignSummaryCopyWith<$Res> {
  factory _$$CampaignSummaryImplCopyWith(_$CampaignSummaryImpl value,
          $Res Function(_$CampaignSummaryImpl) then) =
      __$$CampaignSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, String imageUrl, String name, String category});
}

/// @nodoc
class __$$CampaignSummaryImplCopyWithImpl<$Res>
    extends _$CampaignSummaryCopyWithImpl<$Res, _$CampaignSummaryImpl>
    implements _$$CampaignSummaryImplCopyWith<$Res> {
  __$$CampaignSummaryImplCopyWithImpl(
      _$CampaignSummaryImpl _value, $Res Function(_$CampaignSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? imageUrl = null,
    Object? name = null,
    Object? category = null,
  }) {
    return _then(_$CampaignSummaryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignSummaryImpl implements _CampaignSummary {
  _$CampaignSummaryImpl(
      {this.id = 0, this.imageUrl = "", this.name = "", this.category = ""});

  factory _$CampaignSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignSummaryImplFromJson(json);

  @override
  @JsonKey()
  final int id;
  @override
  @JsonKey()
  final String imageUrl;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String category;

  @override
  String toString() {
    return 'CampaignSummary(id: $id, imageUrl: $imageUrl, name: $name, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignSummaryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.category, category) ||
                other.category == category));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, imageUrl, name, category);

  /// Create a copy of CampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignSummaryImplCopyWith<_$CampaignSummaryImpl> get copyWith =>
      __$$CampaignSummaryImplCopyWithImpl<_$CampaignSummaryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignSummaryImplToJson(
      this,
    );
  }
}

abstract class _CampaignSummary implements CampaignSummary {
  factory _CampaignSummary(
      {final int id,
      final String imageUrl,
      final String name,
      final String category}) = _$CampaignSummaryImpl;

  factory _CampaignSummary.fromJson(Map<String, dynamic> json) =
      _$CampaignSummaryImpl.fromJson;

  @override
  int get id;
  @override
  String get imageUrl;
  @override
  String get name;
  @override
  String get category;

  /// Create a copy of CampaignSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignSummaryImplCopyWith<_$CampaignSummaryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CampaignSearch _$CampaignSearchFromJson(Map<String, dynamic> json) {
  return _CampaignSearch.fromJson(json);
}

/// @nodoc
mixin _$CampaignSearch {
  List<Category> get categories => throw _privateConstructorUsedError;
  List<Category> get selectedCategories => throw _privateConstructorUsedError;
  List<CampaignType> get types => throw _privateConstructorUsedError;
  List<CampaignType> get selectedTypes => throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get campaignResults =>
      throw _privateConstructorUsedError;

  /// Serializes this CampaignSearch to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignSearch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignSearchCopyWith<CampaignSearch> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignSearchCopyWith<$Res> {
  factory $CampaignSearchCopyWith(
          CampaignSearch value, $Res Function(CampaignSearch) then) =
      _$CampaignSearchCopyWithImpl<$Res, CampaignSearch>;
  @useResult
  $Res call(
      {List<Category> categories,
      List<Category> selectedCategories,
      List<CampaignType> types,
      List<CampaignType> selectedTypes,
      List<DefaultCampaignSummary> campaignResults});
}

/// @nodoc
class _$CampaignSearchCopyWithImpl<$Res, $Val extends CampaignSearch>
    implements $CampaignSearchCopyWith<$Res> {
  _$CampaignSearchCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignSearch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
    Object? selectedCategories = null,
    Object? types = null,
    Object? selectedTypes = null,
    Object? campaignResults = null,
  }) {
    return _then(_value.copyWith(
      categories: null == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Category>,
      selectedCategories: null == selectedCategories
          ? _value.selectedCategories
          : selectedCategories // ignore: cast_nullable_to_non_nullable
              as List<Category>,
      types: null == types
          ? _value.types
          : types // ignore: cast_nullable_to_non_nullable
              as List<CampaignType>,
      selectedTypes: null == selectedTypes
          ? _value.selectedTypes
          : selectedTypes // ignore: cast_nullable_to_non_nullable
              as List<CampaignType>,
      campaignResults: null == campaignResults
          ? _value.campaignResults
          : campaignResults // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignSearchImplCopyWith<$Res>
    implements $CampaignSearchCopyWith<$Res> {
  factory _$$CampaignSearchImplCopyWith(_$CampaignSearchImpl value,
          $Res Function(_$CampaignSearchImpl) then) =
      __$$CampaignSearchImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Category> categories,
      List<Category> selectedCategories,
      List<CampaignType> types,
      List<CampaignType> selectedTypes,
      List<DefaultCampaignSummary> campaignResults});
}

/// @nodoc
class __$$CampaignSearchImplCopyWithImpl<$Res>
    extends _$CampaignSearchCopyWithImpl<$Res, _$CampaignSearchImpl>
    implements _$$CampaignSearchImplCopyWith<$Res> {
  __$$CampaignSearchImplCopyWithImpl(
      _$CampaignSearchImpl _value, $Res Function(_$CampaignSearchImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignSearch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
    Object? selectedCategories = null,
    Object? types = null,
    Object? selectedTypes = null,
    Object? campaignResults = null,
  }) {
    return _then(_$CampaignSearchImpl(
      categories: null == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Category>,
      selectedCategories: null == selectedCategories
          ? _value._selectedCategories
          : selectedCategories // ignore: cast_nullable_to_non_nullable
              as List<Category>,
      types: null == types
          ? _value._types
          : types // ignore: cast_nullable_to_non_nullable
              as List<CampaignType>,
      selectedTypes: null == selectedTypes
          ? _value._selectedTypes
          : selectedTypes // ignore: cast_nullable_to_non_nullable
              as List<CampaignType>,
      campaignResults: null == campaignResults
          ? _value._campaignResults
          : campaignResults // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignSearchImpl implements _CampaignSearch {
  _$CampaignSearchImpl(
      {final List<Category> categories = const [],
      final List<Category> selectedCategories = const [],
      final List<CampaignType> types = CampaignType.values,
      final List<CampaignType> selectedTypes = const [],
      final List<DefaultCampaignSummary> campaignResults = const []})
      : _categories = categories,
        _selectedCategories = selectedCategories,
        _types = types,
        _selectedTypes = selectedTypes,
        _campaignResults = campaignResults;

  factory _$CampaignSearchImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignSearchImplFromJson(json);

  final List<Category> _categories;
  @override
  @JsonKey()
  List<Category> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  final List<Category> _selectedCategories;
  @override
  @JsonKey()
  List<Category> get selectedCategories {
    if (_selectedCategories is EqualUnmodifiableListView)
      return _selectedCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedCategories);
  }

  final List<CampaignType> _types;
  @override
  @JsonKey()
  List<CampaignType> get types {
    if (_types is EqualUnmodifiableListView) return _types;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_types);
  }

  final List<CampaignType> _selectedTypes;
  @override
  @JsonKey()
  List<CampaignType> get selectedTypes {
    if (_selectedTypes is EqualUnmodifiableListView) return _selectedTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedTypes);
  }

  final List<DefaultCampaignSummary> _campaignResults;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get campaignResults {
    if (_campaignResults is EqualUnmodifiableListView) return _campaignResults;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaignResults);
  }

  @override
  String toString() {
    return 'CampaignSearch(categories: $categories, selectedCategories: $selectedCategories, types: $types, selectedTypes: $selectedTypes, campaignResults: $campaignResults)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignSearchImpl &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._selectedCategories, _selectedCategories) &&
            const DeepCollectionEquality().equals(other._types, _types) &&
            const DeepCollectionEquality()
                .equals(other._selectedTypes, _selectedTypes) &&
            const DeepCollectionEquality()
                .equals(other._campaignResults, _campaignResults));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_selectedCategories),
      const DeepCollectionEquality().hash(_types),
      const DeepCollectionEquality().hash(_selectedTypes),
      const DeepCollectionEquality().hash(_campaignResults));

  /// Create a copy of CampaignSearch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignSearchImplCopyWith<_$CampaignSearchImpl> get copyWith =>
      __$$CampaignSearchImplCopyWithImpl<_$CampaignSearchImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignSearchImplToJson(
      this,
    );
  }
}

abstract class _CampaignSearch implements CampaignSearch {
  factory _CampaignSearch(
          {final List<Category> categories,
          final List<Category> selectedCategories,
          final List<CampaignType> types,
          final List<CampaignType> selectedTypes,
          final List<DefaultCampaignSummary> campaignResults}) =
      _$CampaignSearchImpl;

  factory _CampaignSearch.fromJson(Map<String, dynamic> json) =
      _$CampaignSearchImpl.fromJson;

  @override
  List<Category> get categories;
  @override
  List<Category> get selectedCategories;
  @override
  List<CampaignType> get types;
  @override
  List<CampaignType> get selectedTypes;
  @override
  List<DefaultCampaignSummary> get campaignResults;

  /// Create a copy of CampaignSearch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignSearchImplCopyWith<_$CampaignSearchImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CampaignFilter _$CampaignFilterFromJson(Map<String, dynamic> json) {
  return _CampaignFilter.fromJson(json);
}

/// @nodoc
mixin _$CampaignFilter {
  String get keyword => throw _privateConstructorUsedError;
  int get excludedCampaignId => throw _privateConstructorUsedError;
  List<int> get categoryIds => throw _privateConstructorUsedError;
  List<CampaignType> get campaignTypes => throw _privateConstructorUsedError;
  List<CampaignApplication> get campaignApplications =>
      throw _privateConstructorUsedError;
  List<String> get customerCountries => throw _privateConstructorUsedError;
  int get limit => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;

  /// Serializes this CampaignFilter to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignFilterCopyWith<CampaignFilter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignFilterCopyWith<$Res> {
  factory $CampaignFilterCopyWith(
          CampaignFilter value, $Res Function(CampaignFilter) then) =
      _$CampaignFilterCopyWithImpl<$Res, CampaignFilter>;
  @useResult
  $Res call(
      {String keyword,
      int excludedCampaignId,
      List<int> categoryIds,
      List<CampaignType> campaignTypes,
      List<CampaignApplication> campaignApplications,
      List<String> customerCountries,
      int limit,
      int page});
}

/// @nodoc
class _$CampaignFilterCopyWithImpl<$Res, $Val extends CampaignFilter>
    implements $CampaignFilterCopyWith<$Res> {
  _$CampaignFilterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? keyword = null,
    Object? excludedCampaignId = null,
    Object? categoryIds = null,
    Object? campaignTypes = null,
    Object? campaignApplications = null,
    Object? customerCountries = null,
    Object? limit = null,
    Object? page = null,
  }) {
    return _then(_value.copyWith(
      keyword: null == keyword
          ? _value.keyword
          : keyword // ignore: cast_nullable_to_non_nullable
              as String,
      excludedCampaignId: null == excludedCampaignId
          ? _value.excludedCampaignId
          : excludedCampaignId // ignore: cast_nullable_to_non_nullable
              as int,
      categoryIds: null == categoryIds
          ? _value.categoryIds
          : categoryIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
      campaignTypes: null == campaignTypes
          ? _value.campaignTypes
          : campaignTypes // ignore: cast_nullable_to_non_nullable
              as List<CampaignType>,
      campaignApplications: null == campaignApplications
          ? _value.campaignApplications
          : campaignApplications // ignore: cast_nullable_to_non_nullable
              as List<CampaignApplication>,
      customerCountries: null == customerCountries
          ? _value.customerCountries
          : customerCountries // ignore: cast_nullable_to_non_nullable
              as List<String>,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignFilterImplCopyWith<$Res>
    implements $CampaignFilterCopyWith<$Res> {
  factory _$$CampaignFilterImplCopyWith(_$CampaignFilterImpl value,
          $Res Function(_$CampaignFilterImpl) then) =
      __$$CampaignFilterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String keyword,
      int excludedCampaignId,
      List<int> categoryIds,
      List<CampaignType> campaignTypes,
      List<CampaignApplication> campaignApplications,
      List<String> customerCountries,
      int limit,
      int page});
}

/// @nodoc
class __$$CampaignFilterImplCopyWithImpl<$Res>
    extends _$CampaignFilterCopyWithImpl<$Res, _$CampaignFilterImpl>
    implements _$$CampaignFilterImplCopyWith<$Res> {
  __$$CampaignFilterImplCopyWithImpl(
      _$CampaignFilterImpl _value, $Res Function(_$CampaignFilterImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignFilter
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? keyword = null,
    Object? excludedCampaignId = null,
    Object? categoryIds = null,
    Object? campaignTypes = null,
    Object? campaignApplications = null,
    Object? customerCountries = null,
    Object? limit = null,
    Object? page = null,
  }) {
    return _then(_$CampaignFilterImpl(
      keyword: null == keyword
          ? _value.keyword
          : keyword // ignore: cast_nullable_to_non_nullable
              as String,
      excludedCampaignId: null == excludedCampaignId
          ? _value.excludedCampaignId
          : excludedCampaignId // ignore: cast_nullable_to_non_nullable
              as int,
      categoryIds: null == categoryIds
          ? _value._categoryIds
          : categoryIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
      campaignTypes: null == campaignTypes
          ? _value._campaignTypes
          : campaignTypes // ignore: cast_nullable_to_non_nullable
              as List<CampaignType>,
      campaignApplications: null == campaignApplications
          ? _value._campaignApplications
          : campaignApplications // ignore: cast_nullable_to_non_nullable
              as List<CampaignApplication>,
      customerCountries: null == customerCountries
          ? _value._customerCountries
          : customerCountries // ignore: cast_nullable_to_non_nullable
              as List<String>,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignFilterImpl implements _CampaignFilter {
  _$CampaignFilterImpl(
      {this.keyword = '',
      this.excludedCampaignId = 0,
      final List<int> categoryIds = const [],
      final List<CampaignType> campaignTypes = const [],
      final List<CampaignApplication> campaignApplications = const [],
      final List<String> customerCountries = const [],
      this.limit = 10,
      this.page = 1})
      : _categoryIds = categoryIds,
        _campaignTypes = campaignTypes,
        _campaignApplications = campaignApplications,
        _customerCountries = customerCountries;

  factory _$CampaignFilterImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignFilterImplFromJson(json);

  @override
  @JsonKey()
  final String keyword;
  @override
  @JsonKey()
  final int excludedCampaignId;
  final List<int> _categoryIds;
  @override
  @JsonKey()
  List<int> get categoryIds {
    if (_categoryIds is EqualUnmodifiableListView) return _categoryIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categoryIds);
  }

  final List<CampaignType> _campaignTypes;
  @override
  @JsonKey()
  List<CampaignType> get campaignTypes {
    if (_campaignTypes is EqualUnmodifiableListView) return _campaignTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaignTypes);
  }

  final List<CampaignApplication> _campaignApplications;
  @override
  @JsonKey()
  List<CampaignApplication> get campaignApplications {
    if (_campaignApplications is EqualUnmodifiableListView)
      return _campaignApplications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaignApplications);
  }

  final List<String> _customerCountries;
  @override
  @JsonKey()
  List<String> get customerCountries {
    if (_customerCountries is EqualUnmodifiableListView)
      return _customerCountries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_customerCountries);
  }

  @override
  @JsonKey()
  final int limit;
  @override
  @JsonKey()
  final int page;

  @override
  String toString() {
    return 'CampaignFilter(keyword: $keyword, excludedCampaignId: $excludedCampaignId, categoryIds: $categoryIds, campaignTypes: $campaignTypes, campaignApplications: $campaignApplications, customerCountries: $customerCountries, limit: $limit, page: $page)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignFilterImpl &&
            (identical(other.keyword, keyword) || other.keyword == keyword) &&
            (identical(other.excludedCampaignId, excludedCampaignId) ||
                other.excludedCampaignId == excludedCampaignId) &&
            const DeepCollectionEquality()
                .equals(other._categoryIds, _categoryIds) &&
            const DeepCollectionEquality()
                .equals(other._campaignTypes, _campaignTypes) &&
            const DeepCollectionEquality()
                .equals(other._campaignApplications, _campaignApplications) &&
            const DeepCollectionEquality()
                .equals(other._customerCountries, _customerCountries) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.page, page) || other.page == page));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      keyword,
      excludedCampaignId,
      const DeepCollectionEquality().hash(_categoryIds),
      const DeepCollectionEquality().hash(_campaignTypes),
      const DeepCollectionEquality().hash(_campaignApplications),
      const DeepCollectionEquality().hash(_customerCountries),
      limit,
      page);

  /// Create a copy of CampaignFilter
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignFilterImplCopyWith<_$CampaignFilterImpl> get copyWith =>
      __$$CampaignFilterImplCopyWithImpl<_$CampaignFilterImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignFilterImplToJson(
      this,
    );
  }
}

abstract class _CampaignFilter implements CampaignFilter {
  factory _CampaignFilter(
      {final String keyword,
      final int excludedCampaignId,
      final List<int> categoryIds,
      final List<CampaignType> campaignTypes,
      final List<CampaignApplication> campaignApplications,
      final List<String> customerCountries,
      final int limit,
      final int page}) = _$CampaignFilterImpl;

  factory _CampaignFilter.fromJson(Map<String, dynamic> json) =
      _$CampaignFilterImpl.fromJson;

  @override
  String get keyword;
  @override
  int get excludedCampaignId;
  @override
  List<int> get categoryIds;
  @override
  List<CampaignType> get campaignTypes;
  @override
  List<CampaignApplication> get campaignApplications;
  @override
  List<String> get customerCountries;
  @override
  int get limit;
  @override
  int get page;

  /// Create a copy of CampaignFilter
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignFilterImplCopyWith<_$CampaignFilterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CampaignCountSummary _$CampaignCountSummaryFromJson(Map<String, dynamic> json) {
  return _CampaignCountSummary.fromJson(json);
}

/// @nodoc
mixin _$CampaignCountSummary {
  int get affiliatedCount => throw _privateConstructorUsedError;
  int get availableCount => throw _privateConstructorUsedError;
  int get pausedCount => throw _privateConstructorUsedError;
  int get waitingCount => throw _privateConstructorUsedError;

  /// Serializes this CampaignCountSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignCountSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignCountSummaryCopyWith<CampaignCountSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignCountSummaryCopyWith<$Res> {
  factory $CampaignCountSummaryCopyWith(CampaignCountSummary value,
          $Res Function(CampaignCountSummary) then) =
      _$CampaignCountSummaryCopyWithImpl<$Res, CampaignCountSummary>;
  @useResult
  $Res call(
      {int affiliatedCount,
      int availableCount,
      int pausedCount,
      int waitingCount});
}

/// @nodoc
class _$CampaignCountSummaryCopyWithImpl<$Res,
        $Val extends CampaignCountSummary>
    implements $CampaignCountSummaryCopyWith<$Res> {
  _$CampaignCountSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignCountSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? affiliatedCount = null,
    Object? availableCount = null,
    Object? pausedCount = null,
    Object? waitingCount = null,
  }) {
    return _then(_value.copyWith(
      affiliatedCount: null == affiliatedCount
          ? _value.affiliatedCount
          : affiliatedCount // ignore: cast_nullable_to_non_nullable
              as int,
      availableCount: null == availableCount
          ? _value.availableCount
          : availableCount // ignore: cast_nullable_to_non_nullable
              as int,
      pausedCount: null == pausedCount
          ? _value.pausedCount
          : pausedCount // ignore: cast_nullable_to_non_nullable
              as int,
      waitingCount: null == waitingCount
          ? _value.waitingCount
          : waitingCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignCountSummaryImplCopyWith<$Res>
    implements $CampaignCountSummaryCopyWith<$Res> {
  factory _$$CampaignCountSummaryImplCopyWith(_$CampaignCountSummaryImpl value,
          $Res Function(_$CampaignCountSummaryImpl) then) =
      __$$CampaignCountSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int affiliatedCount,
      int availableCount,
      int pausedCount,
      int waitingCount});
}

/// @nodoc
class __$$CampaignCountSummaryImplCopyWithImpl<$Res>
    extends _$CampaignCountSummaryCopyWithImpl<$Res, _$CampaignCountSummaryImpl>
    implements _$$CampaignCountSummaryImplCopyWith<$Res> {
  __$$CampaignCountSummaryImplCopyWithImpl(_$CampaignCountSummaryImpl _value,
      $Res Function(_$CampaignCountSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignCountSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? affiliatedCount = null,
    Object? availableCount = null,
    Object? pausedCount = null,
    Object? waitingCount = null,
  }) {
    return _then(_$CampaignCountSummaryImpl(
      affiliatedCount: null == affiliatedCount
          ? _value.affiliatedCount
          : affiliatedCount // ignore: cast_nullable_to_non_nullable
              as int,
      availableCount: null == availableCount
          ? _value.availableCount
          : availableCount // ignore: cast_nullable_to_non_nullable
              as int,
      pausedCount: null == pausedCount
          ? _value.pausedCount
          : pausedCount // ignore: cast_nullable_to_non_nullable
              as int,
      waitingCount: null == waitingCount
          ? _value.waitingCount
          : waitingCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignCountSummaryImpl implements _CampaignCountSummary {
  const _$CampaignCountSummaryImpl(
      {this.affiliatedCount = 0,
      this.availableCount = 0,
      this.pausedCount = 0,
      this.waitingCount = 0});

  factory _$CampaignCountSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignCountSummaryImplFromJson(json);

  @override
  @JsonKey()
  final int affiliatedCount;
  @override
  @JsonKey()
  final int availableCount;
  @override
  @JsonKey()
  final int pausedCount;
  @override
  @JsonKey()
  final int waitingCount;

  @override
  String toString() {
    return 'CampaignCountSummary(affiliatedCount: $affiliatedCount, availableCount: $availableCount, pausedCount: $pausedCount, waitingCount: $waitingCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignCountSummaryImpl &&
            (identical(other.affiliatedCount, affiliatedCount) ||
                other.affiliatedCount == affiliatedCount) &&
            (identical(other.availableCount, availableCount) ||
                other.availableCount == availableCount) &&
            (identical(other.pausedCount, pausedCount) ||
                other.pausedCount == pausedCount) &&
            (identical(other.waitingCount, waitingCount) ||
                other.waitingCount == waitingCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, affiliatedCount, availableCount, pausedCount, waitingCount);

  /// Create a copy of CampaignCountSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignCountSummaryImplCopyWith<_$CampaignCountSummaryImpl>
      get copyWith =>
          __$$CampaignCountSummaryImplCopyWithImpl<_$CampaignCountSummaryImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignCountSummaryImplToJson(
      this,
    );
  }
}

abstract class _CampaignCountSummary implements CampaignCountSummary {
  const factory _CampaignCountSummary(
      {final int affiliatedCount,
      final int availableCount,
      final int pausedCount,
      final int waitingCount}) = _$CampaignCountSummaryImpl;

  factory _CampaignCountSummary.fromJson(Map<String, dynamic> json) =
      _$CampaignCountSummaryImpl.fromJson;

  @override
  int get affiliatedCount;
  @override
  int get availableCount;
  @override
  int get pausedCount;
  @override
  int get waitingCount;

  /// Create a copy of CampaignCountSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignCountSummaryImplCopyWith<_$CampaignCountSummaryImpl>
      get copyWith => throw _privateConstructorUsedError;
}
