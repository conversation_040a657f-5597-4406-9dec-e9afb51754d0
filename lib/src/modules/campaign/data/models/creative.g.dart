// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'creative.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreativeImpl _$$CreativeImplFromJson(Map<String, dynamic> json) =>
    _$CreativeImpl(
      width: (json['width'] as num).toInt(),
      height: (json['height'] as num).toInt(),
      imageUrl: json['imageUrl'] as String?,
      affiliateLink: json['affiliateLink'] as String?,
    );

Map<String, dynamic> _$$CreativeImplToJson(_$CreativeImpl instance) =>
    <String, dynamic>{
      'width': instance.width,
      'height': instance.height,
      'imageUrl': instance.imageUrl,
      'affiliateLink': instance.affiliateLink,
    };

_$CreativeGroupImpl _$$CreativeGroupImplFromJson(Map<String, dynamic> json) =>
    _$CreativeGroupImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      imageUrl: json['imageUrl'] as String?,
      description: json['description'] as String?,
      creatives: (json['creatives'] as List<dynamic>)
          .map((e) => Creative.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CreativeGroupImplToJson(_$CreativeGroupImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'description': instance.description,
      'creatives': instance.creatives,
    };

_$CreativeImageImpl _$$CreativeImageImplFromJson(Map<String, dynamic> json) =>
    _$CreativeImageImpl(
      name: json['name'] as String?,
      creatives: (json['creatives'] as List<dynamic>)
          .map((e) => Creative.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CreativeImageImplToJson(_$CreativeImageImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'creatives': instance.creatives,
    };
