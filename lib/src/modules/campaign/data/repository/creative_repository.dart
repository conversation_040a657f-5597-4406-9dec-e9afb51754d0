import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

import '../../custom_link/cubit/custom_link_campaign_search_state.dart';

class CreativeRepository {
  final ApiService apiService;
  final SharedPreferencesService? sharedPreferencesService;

  CreativeRepository(this.apiService, {this.sharedPreferencesService});

  Future<dynamic> getQuickLink(int siteId, int campaignId) async {
    return await apiService.getPlainText('/v3/publishers/me/sites/$siteId/campaigns/$campaignId/creatives/quicklink');
  }

  Future<dynamic> getCreatives(int siteId, int campaignId) async {
    return await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/$campaignId/creatives/image');
  }

  Future<List<CustomCreativeEnabledCampaign>> getCustomCreativeEnabledCampaign(int siteId) async {
    final result =
        await apiService.getData('/v3/publishers/me/sites/$siteId/affiliated/custom-creative-enabled-campaigns');
    List<CustomCreativeEnabledCampaign> responseData =
        (result as List).map((item) => CustomCreativeEnabledCampaign.fromJson(item)).toList();
    return responseData;
  }
}
