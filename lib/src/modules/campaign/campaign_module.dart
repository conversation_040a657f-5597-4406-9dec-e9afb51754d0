import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_detail_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_view_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_search_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_upsized_reward_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/creative_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';
import 'package:koc_app/src/modules/campaign/data/repository/creative_repository.dart';
import 'package:koc_app/src/modules/campaign/presentation/page/campaign_conversion_descirption_page.dart';
import 'package:koc_app/src/modules/campaign/presentation/page/campaign_list_page.dart';
import 'package:koc_app/src/modules/campaign/presentation/page/campaign_reward_page.dart';
import 'package:koc_app/src/modules/campaign/presentation/page/campaign_search_page.dart';
import 'package:koc_app/src/modules/campaign/presentation/page/campaign_traffic_restrictions_page.dart';
import 'package:koc_app/src/modules/campaign/presentation/page/campaign_upsized_reward_page.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/data/repository/voucher_repository.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';
import 'package:koc_app/src/modules/survey/data/repository/survey_repository.dart';

import 'custom_link/data/repository/custom_link_repository.dart';

class CampaignModule extends Module {
  @override
  List<Module> get imports => [SharedModule(), CampaignSharedModule()];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addLazySingleton(CampaignSearchCubit.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.child('/list',
        child: (i) => MultiBlocProvider(
              providers: [
                BlocProvider.value(value: Modular.get<CampaignListCubit>()),
                BlocProvider.value(value: Modular.get<CampaignDetailCubit>()),
                BlocProvider.value(value: Modular.get<VoucherCubit>()),
                BlocProvider.value(value: Modular.get<CustomLinkCubit>()),
                BlocProvider.value(value: Modular.get<CustomLinkHistoryCubit>()),
                BlocProvider.value(value: Modular.get<CreativeCubit>()),
              ],
              child: const CampaignListPage(),
            ));
    r.child(
      '/search',
      child: (i) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: Modular.get<CampaignDetailCubit>()),
          BlocProvider.value(value: Modular.get<VoucherCubit>()),
          BlocProvider.value(value: Modular.get<CustomLinkCubit>()),
          BlocProvider.value(value: Modular.get<CustomLinkHistoryCubit>()),
          BlocProvider.value(value: Modular.get<CreativeCubit>()),
        ],
        child: const CampaignSearchPage(),
      ),
    );
    r.child('/conversion', child: (i) => const CampaignConversionDescirptionPage());
    r.child('/traffic-restrictions', child: (i) => const CampaignTrafficRestrictionsPage());
    r.child('/reward', child: (i) => const CampaignRewardPage());
    r.child('/upsized-reward', child: (i) => const CampaignUpsizedRewardPage());
  }
}

class CampaignSharedModule extends Module {
  @override
  List<Module> get imports => [SharedModule()];

  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);
    i.addLazySingleton(SurveyRepository.new);
    i.addLazySingleton(CampaignRepository.new);
    i.addLazySingleton(CampaignHomeCubit.new);
    i.addLazySingleton(CampaignDetailCubit.new);
    i.add(CampaignListCubit.new);
    i.addLazySingleton(CreativeRepository.new);
    i.add(CreativeCubit.new);
    i.addLazySingleton(VoucherRepository.new);
    i.add(VoucherCubit.new);
    i.addLazySingleton(CustomLinkRepository.new);
    i.add(CustomLinkHistoryCubit.new);
    i.add(CustomLinkCubit.new);
    i.add(CustomLinkCampaignSearchCubit.new);
    i.add(CampaignUpsizedRewardCubit.new);
    i.addLazySingleton(CampaignListViewCubit.new);
  }
}
