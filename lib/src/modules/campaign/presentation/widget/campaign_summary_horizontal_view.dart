import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/mixin/campaign_detail_mixin.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/campaign_grid_item.dart';
class CampaignSummaryHorizontalView extends StatelessWidget with CampaignDetailMixin {
  final String title;
  final String currency;
  final VoidCallback? onPressed;
  final List<DefaultCampaignSummary> campaigns;
  final int spacerSize;
  final Color? cardColor;
  final int? maxDisplayCount;

  const CampaignSummaryHorizontalView(this.title, this.currency, this.onPressed, this.campaigns,
      {this.spacerSize = 14, this.cardColor, this.maxDisplayCount, super.key});

  List<DefaultCampaignSummary> _getDisplayCampaigns() {
    return campaigns.take(maxDisplayCount ?? campaigns.length).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: cardColor != null ? EdgeInsets.all(8.r) : null,
      decoration: cardColor != null
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: cardColor,
            )
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold),
              ),
              if (onPressed != null)
                IconButton(
                  onPressed: onPressed,
                  icon: Icon(
                    Icons.arrow_forward,
                    size: 20.r,
                  ),
                ),
            ],
          ),
          SizedBox(height: spacerSize.r),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              spacing: 8.r,
              children: _getDisplayCampaigns()
                  .map((campaign) => CampaignGridItem(campaign, currency, 120.r, () async {
                        AffiliationStatus? status = await showCampaignDetailModal(context, campaign.id, initialHeight: 0.5);
                        if (status != null) {
                          moveCampaignTab(status);
                        }
                      }))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
}
