import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class CampaignConversionDescirptionPage extends StatelessWidget {
  const CampaignConversionDescirptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: Text('Conversion'),
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    CampaignDetails campaignDetails = Modular.args.data;
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 16.r,
          children: [
            if (campaignDetails.conversionWindow != null)
              _buildBaseDescription('Conversion Window', campaignDetails.conversionWindow!, context),
            if (campaignDetails.validationTermLocal != null)
              _buildBaseDescription('Validation Term', campaignDetails.validationTermLocal!, context),
            if (campaignDetails.requiredActionLocal != null)
              _buildBaseDescription('Required Action', campaignDetails.requiredActionLocal!, context),
            if (campaignDetails.rejectConditionLocal != null && campaignDetails.rejectConditions != null)
              _buildRejectConditions('Reject Condition', campaignDetails.rejectConditionLocal!,
                  campaignDetails.rejectConditions!, context),
          ],
        ),
      ),
    );
  }

  Widget _buildRejectConditions(
      String title, String rejectCondition, List<String> rejectConditions, BuildContext context) {
    return Column(spacing: 8.r, crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        title,
        style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
      ),
      ...rejectConditions.map((item) => Row(
            children: [
              Icon(Icons.close, size: 18.r),
              Text(
                " $item",
                style: Theme.of(context).textTheme.labelLarge,
              )
            ],
          )),
      if (rejectCondition.isNotEmpty)
        Text(
          rejectCondition,
          style: Theme.of(context).textTheme.labelLarge,
        ),
    ]);
  }

  Widget _buildBaseDescription(String title, String description, BuildContext context) {
    return Column(
      spacing: 8.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
        ),
        Text(
          ' · $description',
          style: Theme.of(context).textTheme.labelLarge,
        )
      ],
    );
  }
}
