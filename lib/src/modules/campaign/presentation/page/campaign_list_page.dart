import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_state.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_view_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_view_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/mixin/campaign_detail_mixin.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/campaign_grid_item.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/campaign_list_item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

extension ComparableDouble on double {
  double get toComparableDouble => this;
}

class CampaignListPage extends StatefulWidget {
  const CampaignListPage({super.key});

  @override
  State<CampaignListPage> createState() => _CampaignListPageState();
}

class _CampaignListPageState
    extends BasePageState<CampaignListPage, CampaignListViewCubit>
    with CampaignDetailMixin {
  late final String currency = 'IDR';

  @override
  void initState() {
    super.initState();
    getCampaigns();
  }

  Future<void> getCampaigns() async {
    cubit.showLoading();
    await ReadContext(context)
        .read<CampaignListCubit>()
        .loadCampaigns(0, Modular.args.data);
    cubit.hideLoading();
  }

  @override
  Widget buildPage(BuildContext context) {
    CampaignViewType type = Modular.args.data;
    return Scaffold(
      appBar: CommonAppBar(
        title: Text(type.name.toTitleCase()),
        showFindCampaignAction: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.only(left: 16.r, right: 16.r, bottom: 16.r),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTitle(),
            SizedBox(
              height: 8.r,
            ),
            _buildView(),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.only(
        left: 4.r,
        top: 8.r,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                'Reward',
                style: Theme.of(context).textTheme.labelLarge,
              ),
              IconButton(
                onPressed: () {
                  cubit.toggleSort();
                },
                icon: BlocBuilder<CampaignListViewCubit, CampaignListViewState>(
                    builder: (context, state) {
                  return Icon(
                    state.isAsc ? Icons.arrow_downward : Icons.arrow_upward,
                    size: 20.r,
                  );
                }),
              )
            ],
          ),
          IconButton(
            onPressed: () {
              cubit.toggleView();
            },
            icon: BlocBuilder<CampaignListViewCubit, CampaignListViewState>(
                builder: (context, state) {
              return Icon(
                state.isListView ? Icons.grid_view : Icons.view_list_rounded,
                size: 20.r,
              );
            }),
          )
        ],
      ),
    );
  }

  Widget _buildView() {
    return BlocBuilder<CampaignListViewCubit, CampaignListViewState>(
        bloc: cubit,
        builder: (context, state) {
          return BlocBuilder<CampaignListCubit, CampaignListState>(
              builder: (context, listState) {
            List<DefaultCampaignSummary> campaigns = listState.campaigns.sorted(
                (a, b) => state.isAsc
                    ? (a.highestRewardSummaries.first.reward).compareTo(b.highestRewardSummaries.first.reward)
                    : (b.highestRewardSummaries.first.reward).compareTo(a.highestRewardSummaries.first.reward));

            return state.isListView
                ? _buildListView(campaigns)
                : _buildGridView(campaigns);
          });
        });
  }

  Widget _buildGridView(List<DefaultCampaignSummary> campaigns) {
    return Wrap(
      spacing: 0.12.sw - 32.r,
      runSpacing: 10.r,
      children: campaigns
          .map((campaign) => CampaignGridItem(campaign,  currency, 0.44.sw, () async {
                await showCampaignDetailAndChangeTab(context, campaign.id);
              }))
          .toList(),
    );
  }

  Widget _buildListView(List<DefaultCampaignSummary> campaigns) {
    return Column(
      spacing: 8.r,
      children: campaigns
          .map((campaign) => CampaignListItem(campaign, currency, () async {
                await showCampaignDetailAndChangeTab(context, campaign.id);
              }))
          .toList(),
    );
  }

  Future<void> showCampaignDetailAndChangeTab(
      BuildContext context, int campaignId) async {
    AffiliationStatus? status =
        await showCampaignDetailModal(context, campaignId);
    if (status != null) {
      Modular.get<CampaignHomeCubit>()
          .changeTab(status == AffiliationStatus.APPLYING ? 1 : 2);
      Navigator.of(context).pop();
    }
  }
}
