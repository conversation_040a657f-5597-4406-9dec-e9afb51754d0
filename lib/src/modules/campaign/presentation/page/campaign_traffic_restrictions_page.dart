import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class CampaignTrafficRestrictionsPage extends StatelessWidget {
  const CampaignTrafficRestrictionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: const Text('Traffic Restrictions'),
        customAction: IconButton(
          onPressed: () {
            _showTrafficRestrictionsTerms(context);
          },
          icon: Icon(
            Icons.help_outline,
            size: 20.r,
          ),
        ),
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    CampaignDetails campaignDetails = Modular.args.data;
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: SingleChildScrollView(
        child: Column(
          spacing: 8.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTitle(context),
            ..._buildInvalidTrafficRestrictions(
                context, campaignDetails.invalidTrafficRestrictions ?? []),
            ..._buildValidTrafficRestrictions(
                context, campaignDetails.validTrafficRestrictions ?? []),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      'Reject Condition',
      style: Theme.of(context)
          .textTheme
          .bodySmall!
          .copyWith(fontWeight: FontWeight.w500),
    );
  }

  List<Widget> _buildInvalidTrafficRestrictions(
      BuildContext context, List<String> restrictions) {
    return restrictions
        .map((item) => Row(
              children: [
                Icon(
                  Icons.close,
                  size: 18.r,
                ),
                item != 'Brand Bidding'
                    ? Text(
                        " $item",
                        style: Theme.of(context).textTheme.labelLarge,
                      )
                    : Text(" $item",
                        style: Theme.of(context)
                            .textTheme
                            .labelLarge!
                            .copyWith(color: const Color(0xFF767676))),
              ],
            ))
        .toList();
  }

  List<Widget> _buildValidTrafficRestrictions(
      BuildContext context, List<String> restrictions) {
    return restrictions
        .map((item) => Row(
              children: [
                Icon(
                  Icons.check,
                  size: 18.r,
                ),
                Text(
                  " $item",
                  style: Theme.of(context).textTheme.labelLarge,
                ),
              ],
            ))
        .toList();
  }

  void _showTrafficRestrictionsTerms(BuildContext context) {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        backgroundColor: Colors.white,
        builder: (_) {
          return Container(
            padding: EdgeInsets.symmetric(vertical: 12.r, horizontal: 16.r),
            height:
                ScreenUtil().screenHeight - MediaQuery.of(context).padding.top,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Column(
              spacing: 20.r,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [_buildModalHeader(context), _buildModalBody(context)],
            ),
          );
        });
  }

  Widget _buildModalBody(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          spacing: 16.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildModalBodyItem(context, 'Adult, Pornographic, & Violence',
                'Promoting through sites with illegal, violent or pornographic content.'),
            _buildModalBodyItem(context, 'Brand Bidding',
                'Creating ads by bidding on branded terms and keywords (including any variations or misspellings of the brand) to gain favorable listings on search engine platforms.'),
            _buildModalBodyItem(context, 'Cashback',
                'The use of cashback to drive traffic to the advertiser\'s site.'),
            _buildModalBodyItem(context, 'Coupon & Discount Codes',
                'The use of coupon, discount code, voucher, or similar type to drive traffic to the advertiser\'s site.'),
            _buildModalBodyItem(context, 'Deep Linking',
                'To link vistors to a subpage. Usually to a specific product page on the advertiser\'s site instead of the homepage.'),
            _buildModalBodyItem(context, 'Direct Linking',
                'To send visitors from an Ad directly to an advertiser\'s offer, eliminating the requirement for a landing page.'),
            _buildModalBodyItem(context, 'Display Ads',
                'The use of paid ads in the form of banners or other visual formats on the websites to drive traffic to the advertiser\'s site.'),
            _buildModalBodyItem(context, 'Display Banner',
                'The use of banner displays on the websites to drive traffic to the advertiser\'s site without paid ads.'),
            _buildModalBodyItem(context, 'Email Marketing',
                'The use of email marketing to drive traffic to the advertiser\'s site.'),
            _buildModalBodyItem(context, 'Gambling',
                'Promoting through sites with gambling content.'),
          ],
        ),
      ),
    );
  }

  Widget _buildModalBodyItem(
      BuildContext context, String title, String description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(fontWeight: FontWeight.w500),
        ),
        Text(
          description,
          style: Theme.of(context)
              .textTheme
              .labelLarge!
              .copyWith(color: const Color(0xFF767676)),
        )
      ],
    );
  }

  Widget _buildModalHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Traffic Restrictions Terms',
          style: Theme.of(context)
              .textTheme
              .titleSmall!
              .copyWith(fontWeight: FontWeight.w500),
        ),
        IconButton.filled(
          style: IconButton.styleFrom(
            backgroundColor: Colors.grey[200],
          ),
          color: Colors.black,
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Icon(
            Icons.close,
            size: 16.r,
          ),
        ),
      ],
    );
  }
}
