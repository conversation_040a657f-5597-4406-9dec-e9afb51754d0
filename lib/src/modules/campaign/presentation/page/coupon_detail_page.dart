import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/coupon.dart';

class CouponDetailPage extends StatefulWidget {
  final Coupon coupon;
  const CouponDetailPage(this.coupon, {super.key});

  @override
  State<CouponDetailPage> createState() => _CouponDetailPageState();
}

class _CouponDetailPageState extends State<CouponDetailPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.coupon.name, style: TextStyle(fontSize: 24.r),),
        actions: [
          IconButton(
            icon: Icon(Icons.more_vert, size: 30.r,),
            onPressed: () {},
          )
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1.r),
          child: Divider(
            height: 1.r,
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(10.r),
        child: Column(
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(10.r),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Promote code',
                          style: TextStyle(fontSize: 18.r),
                        ),
                        Text(
                          widget.coupon.promoteCode,
                          style: TextStyle(fontSize: 18.r),
                        )
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Expired in',
                          style: TextStyle(fontSize: 18.r),
                        ),
                        Text(
                          '${widget.coupon.expireDay.difference(DateTime.now()).inDays} days',
                          style: TextStyle(fontSize: 18.r),
                        )
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Category',
                          style: TextStyle(fontSize: 18.r),
                        ),
                        Text(
                          widget.coupon.category,
                          style: TextStyle(fontSize: 18.r),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 20.r,
            ),
            Text(
              widget.coupon.description,
              style: TextStyle(fontSize: 18.r),
            )
          ],
        ),
      ),
    );
  }
}
