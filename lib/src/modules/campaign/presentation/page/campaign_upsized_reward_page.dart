import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_upsized_reward_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_upsized_reward_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class CampaignUpsizedRewardPage extends StatefulWidget {
  const CampaignUpsizedRewardPage({super.key});

  @override
  State<CampaignUpsizedRewardPage> createState() =>
      _CampaignUpsizedRewardPageState();
}

class _CampaignUpsizedRewardPageState
    extends BasePageState<CampaignUpsizedRewardPage, CampaignUpsizedRewardCubit>
    with ReportMixin, FilterMixin {
  late FilterCubit filterCubit = Modular.get<FilterCubit>()..clear();

  @override
  void initState() {
    doLoadingAction(() async {
      DateTime now = DateTime.now();
      DateTime firstDayOfMonth = DateTime(now.year, now.month, 1);
      DateTime lastDayOfMonth;
      if (now.month == 12) {
        lastDayOfMonth = DateTime(now.year + 1, 1, 0);
      } else {
        lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
      }
      await cubit.findUpsizedRewards(firstDayOfMonth, lastDayOfMonth);
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: const Text('Upsized rewards'),
        customAction: IconButton(
          onPressed: () {
            _showFilter();
          },
          icon: Icon(
            Icons.tune,
            size: 20.r,
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Future<void> _showFilter() async {
    bool? result = await showFilters(
        context,
        [
          ReportPeriod.THIS_MONTH,
          ReportPeriod.LAST_6_MONTHS,
          ReportPeriod.LAST_3_MONTHS,
          ReportPeriod.NEXT_3_MONTHS,
          ReportPeriod.NEXT_6_MONTHS
        ],
        showCampaigns: false,
        showDateType: false,
        showStatus: false);

    if (true == result) {
      doLoadingAction(() async {
        FilterState reportFilterState = Modular.get<FilterCubit>().state;
        DateTimeRange range = getTimeRange(reportFilterState.selectedPeriod,
            reportFilterState.startDate, reportFilterState.endDate);
        await cubit.findUpsizedRewards(range.start, range.end);
      });
    }
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child:
            BlocBuilder<CampaignUpsizedRewardCubit, CampaignUpsizedRewardState>(
                builder: (_, state) {
          if (state.upsizedRewards.isNotEmpty) {
            return _buildDataTable(state.upsizedRewards, state.currency);
          }
          return const SizedBox.shrink();
        }),
      ),
    );
  }

  Widget _buildDataTable(List<UpsizedReward> upsizedRewards, String currency) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: ColorConstants.borderColor, width: 1.r)),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: DataTable(
            headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
            columnSpacing: 8.r,
            columns: [
              buildDataColumn(context, 'Category'),
              buildDataColumn(context, 'Upsized Period'),
              buildDataColumn(context, 'Reward (${currency.currencySymbol})'),
            ],
            rows: upsizedRewards.map((e) {
              return DataRow(cells: [
                buildDataCell(context, e.name),
                buildDataCell(context,
                    '${e.targetTimeFrom!.toStandard()}\n${e.targetTimeTo!.toStandard()}'),
                DataCell(Row(
                  spacing: 4.r,
                  children: [
                    Text(e.previousReward.toReward(e.type!),
                        style: context.textLabelLarge()),
                    Icon(
                      Icons.arrow_forward,
                      size: 12.r,
                    ),
                    Container(
                      padding: EdgeInsets.all(4.r),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.r),
                        color: const Color(0xFFDEFFEC),
                      ),
                      child: Row(
                        spacing: 4.r,
                        children: [
                          Icon(
                            Icons.arrow_upward,
                            size: 12.r,
                            color: const Color(0xFF168845),
                          ),
                          Text(e.reward.toReward(e.type!),
                              style: context.textLabelLarge(
                                  color: const Color(0xFF168845),
                                  fontWeight: FontWeight.w500))
                        ],
                      ),
                    )
                  ],
                )),
              ]);
            }).toList(),
          ),
        ),
      ),
    );
  }
}
