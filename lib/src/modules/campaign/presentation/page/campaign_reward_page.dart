import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/common_tab.dart';

class CampaignRewardPage extends StatefulWidget {
  const CampaignRewardPage({super.key});

  @override
  State<CampaignRewardPage> createState() => _CampaignRewardPageState();
}

class _CampaignRewardPageState extends State<CampaignRewardPage> {
  int _tabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: Text('Reward'),
        showBottomDivider: false,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    CampaignDetails campaignDetails = Modular.args.data;
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
        child: Column(spacing: 16.r, children: [
          // if (campaignDetails.haveUpsizedReward.isNotEmpty)
          //   _buildUpsizedReward(campaignDetails.haveUpsizedReward), //TODO: should implement
          if (campaignDetails.defaultRewards.isNotEmpty) _buildReward(campaignDetails.defaultRewards),
          if (campaignDetails.categoryRewards.isNotEmpty) _buildRewardByCategory(campaignDetails.categoryRewards)
        ]),
      ),
    );
  }

  Widget _buildUpsizedReward(List<UpsizedReward> upsizedRewards) {
    return Stack(
      children: [
        DottedBorder(
          borderType: BorderType.RRect,
          radius: Radius.circular(12.r),
          color: const Color(0xFFFFB522),
          dashPattern: const [6, 6],
          strokeWidth: 3.r,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Column(
              children: [
                GestureDetector(
                  onTap: () {
                    Modular.to.pushNamed('/campaign/upsized-reward');
                  },
                  child: Container(
                    padding: EdgeInsets.only(top: 6.r, right: 12.r),
                    alignment: Alignment.centerRight,
                    child: Icon(
                      Icons.arrow_forward,
                      size: 20.r,
                    ),
                  ),
                ),
                ...upsizedRewards
                    .map((e) => _buildUpsizedRewardBody(e, upsizedRewards.indexOf(e) < upsizedRewards.length - 1)),
              ],
            ),
          ),
        ),
        Positioned(
          top: -2,
          left: -2,
          child: Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                bottomRight: Radius.circular(12.r),
              ),
              color: const Color(0xFFFFB522),
            ),
            child: Text(
              'Upsized Rewards',
              style: context.textLabelLarge(color: Colors.white, fontWeight: FontWeight.w500),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildUpsizedRewardBody(UpsizedReward upsizedReward, bool hasDivider) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    upsizedReward.name,
                    style: context.textLabelLarge(),
                  ),
                  Container(
                    padding: EdgeInsets.all(4.r),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.r),
                      border: Border.all(
                        color: ColorConstants.borderColor,
                        width: 1.r,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.arrow_upward,
                          size: 12.r,
                          color: Colors.green,
                        ),
                        Text(
                          upsizedReward.type == RewardType.CPA_SALES
                              ? '${(upsizedReward.reward * 100)}%'
                              : upsizedReward.reward.toCommaSeparated(),
                          style: context.textLabelLarge(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Text(
                '${upsizedReward.targetTimeFrom!.toStandard()} ~ ${upsizedReward.targetTimeTo!.toStandard()}',
                style: context.textLabelMedium(color: const Color(0xFF767676)),
              )
            ],
          ),
        ),
        if (hasDivider)
          Divider(
            color: ColorConstants.borderColor,
            height: 1.r,
          )
      ],
    );
  }

  Widget _buildReward(List<DefaultReward> defaultRewards) {
    if (defaultRewards.isNotEmpty) {
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: const Color(0xFFE7E7E7),
            width: 1.r,
          ),
        ),
        child: _buildRewardContents(defaultRewards),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildRewardContents(List<DefaultReward> rewards) {
    return Column(
      children: Iterable.generate(
          rewards.length,
          (index) => Column(
                children: [
                  Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          rewards[index].type == RewardType.CPA_SALES
                              ? '${rewards[index].name ?? ''} (${rewards[index].customerTypeName ?? ''})'
                              : rewards[index].name ?? '',
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 4.r, vertical: 2.r),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6.r),
                              color: const Color(0xFFFCFCFF),
                              border: Border.all(
                                color: const Color(0xFFE7E7E7),
                                width: 1.r,
                              )),
                          child: Text(
                            "${rewards[index].reward}${rewards[index].type == RewardType.CPA_SALES ? '%' : ''}",
                            style: Theme.of(context).textTheme.labelLarge,
                          ),
                        )
                      ],
                    ),
                  ),
                  if (rewards.length - 1 > index)
                    Divider(
                      height: 1.r,
                      color: const Color(0xFFE7E7E7),
                    )
                ],
              )).toList(),
    );
  }

  Widget _buildRewardByCategory(List<ProductCategoryReward> rewards) {
    return DefaultTabController(
      length: rewards.length,
      child: Container(
        padding: EdgeInsets.only(top: 16.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
        ),
        child: Column(
          children: [
            TabBar(
                onTap: (index) {
                  setState(() {
                    _tabIndex = index;
                  });
                },
                isScrollable: true,
                labelStyle: Theme.of(context).textTheme.labelLarge,
                tabs: rewards
                    .map((item) => CommonTab('${item.maxReward!.customerType?.toTitleCase()} Customer'))
                    .toList()),
            rewards.map((item) => _buildRewardContents(item.allCategoryRewards)).toList()[_tabIndex]
          ],
        ),
      ),
    );
  }
}
