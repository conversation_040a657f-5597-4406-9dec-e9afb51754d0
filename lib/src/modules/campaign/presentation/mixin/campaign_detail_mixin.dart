import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_detail_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/creative_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/page/campaign_detail_page.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_cubit.dart';
import 'package:koc_app/src/modules/navigation/bottom_navigation_cubit.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/utils/modal_utils.dart';

mixin CampaignDetailMixin {
  Future<AffiliationStatus?> showCampaignDetailModal(BuildContext context, int campaignId,
      {double initialHeight = 1.0}) async {
    Modular.get<CampaignDetailCubit>().resetCampaignDetailState();

    return await ModalUtils.showDraggableBottomSheet<AffiliationStatus?>(
      context: context,
      useSafeArea: true,
      radius: 12.r,
      snapToMid: true,
      midHeight: 0.5,
      initialHeight: initialHeight,
      builder: (context, modalHeight) {
        return MultiBlocProvider(providers: [
          BlocProvider.value(value: Modular.get<CampaignDetailCubit>()),
          BlocProvider.value(value: Modular.get<CreativeCubit>()),
          BlocProvider.value(value: Modular.get<VoucherCubit>()),
          BlocProvider.value(value: Modular.get<CustomLinkCubit>()),
          BlocProvider.value(value: Modular.get<CustomLinkHistoryCubit>()),
        ], child: CampaignDetailPage(campaignId, modalHeight));
      },
    );
  }

  Future<void> moveCampaignTab(AffiliationStatus status, {BuildContext? context, bool navigateToTab = true}) async {
    if (navigateToTab) {
      Modular.get<BottomNavigationCubit>().navigateTo(1);
    }

    String message = "";

    switch (status) {
      case AffiliationStatus.APPLYING:
        message = "The campaign is waiting for approval.";
        break;
      case AffiliationStatus.APPROVED:
        message = "The campaign has been approved and ready for promotion now.";
        break;
      case AffiliationStatus.NEW:
        message = "The campaign is waiting for approval.";
        break;
      default:
        message = "Your campaign application has been submitted.";
        break;
    }

    CampaignHomeCubit campaignHomeCubit = Modular.get<CampaignHomeCubit>();

    await campaignHomeCubit.refreshAfterCampaignApplication();

    campaignHomeCubit.setNotificationMessage(message);

    if (context != null && context.mounted) {
      context.showSnackBar("Thank you for applying!\n$message");
    }
  }
}
