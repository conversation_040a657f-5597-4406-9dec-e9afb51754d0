import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';

part 'campaign_list_state.freezed.dart';

@freezed
class CampaignListState extends BaseCubitState with _$CampaignListState {
  factory CampaignListState({
    @Default([]) List<DefaultCampaignSummary> campaigns,
    @Default([]) List<DefaultCampaignSummary> filteredCampaigns,
    DefaultCampaignSummary? selectedCampaign,
    @Default('') String searchText,
  }) = _CampaignListState;
}
