// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_home_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignHomeStateImpl _$$CampaignHomeStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignHomeStateImpl(
      recommendCampaigns: (json['recommendCampaigns'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      latestCampaigns: (json['latestCampaigns'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      waitingCampaigns: (json['waitingCampaigns'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      affiliatedCampaigns: (json['affiliatedCampaigns'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      pausedCampaigns: (json['pausedCampaigns'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      availableCampaigns: (json['availableCampaigns'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      campaignSummariesCount: json['campaignSummariesCount'] == null
          ? const CampaignCountSummary()
          : CampaignCountSummary.fromJson(
              json['campaignSummariesCount'] as Map<String, dynamic>),
      tabIndex: (json['tabIndex'] as num?)?.toInt() ?? 0,
      notificationMessage: json['notificationMessage'] as String? ?? '',
      passionateInfo: json['passionateInfo'] == null
          ? const PassionateInfo()
          : PassionateInfo.fromJson(
              json['passionateInfo'] as Map<String, dynamic>),
      errorMessage: json['errorMessage'] as String? ?? '',
      isLoading: json['isLoading'] ?? false,
      isLoadingMore: json['isLoadingMore'] ?? false,
      isPullToRefresh: json['isPullToRefresh'] ?? false,
      isSiteSwitching: json['isSiteSwitching'] ?? false,
      hasMore: json['hasMore'] ?? false,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      currency: json['currency'] as String? ?? '',
      selectedSiteId: (json['selectedSiteId'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CampaignHomeStateImplToJson(
        _$CampaignHomeStateImpl instance) =>
    <String, dynamic>{
      'recommendCampaigns': instance.recommendCampaigns,
      'latestCampaigns': instance.latestCampaigns,
      'waitingCampaigns': instance.waitingCampaigns,
      'affiliatedCampaigns': instance.affiliatedCampaigns,
      'pausedCampaigns': instance.pausedCampaigns,
      'availableCampaigns': instance.availableCampaigns,
      'campaignSummariesCount': instance.campaignSummariesCount,
      'tabIndex': instance.tabIndex,
      'notificationMessage': instance.notificationMessage,
      'passionateInfo': instance.passionateInfo,
      'errorMessage': instance.errorMessage,
      'isLoading': instance.isLoading,
      'isLoadingMore': instance.isLoadingMore,
      'isPullToRefresh': instance.isPullToRefresh,
      'isSiteSwitching': instance.isSiteSwitching,
      'hasMore': instance.hasMore,
      'currentPage': instance.currentPage,
      'currency': instance.currency,
      'selectedSiteId': instance.selectedSiteId,
    };
