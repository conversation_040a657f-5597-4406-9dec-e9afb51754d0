import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';

class CampaignListCubit extends BaseCubit<CampaignListState> {
  final CampaignRepository _campaignRepository;
  CampaignListCubit(this._campaignRepository) : super(CampaignListState());

  Future<void> loadCampaigns(int userId, CampaignViewType type) async {
    await Future.delayed(const Duration(seconds: 1));
    final List<DefaultCampaignSummary> campaignSummaries = [
      const DefaultCampaignSummary(
        id: 1,
        imageUrl:
        'https://s3-ap-southeast-1.amazonaws.com/images.accesstrade.co.id/70efdf2ec9b086079795c442636b55fb/logo_20211013075435.png',
        name: 'pepe1',
        category: 'Travel & Leisure',
        highestRewardSummaries: [
          HighestRewardSummary(type: 'cashback', reward: 20),
        ],
      ),
      const DefaultCampaignSummary(
        id: 2,
        imageUrl:
        'https://img.freepik.com/free-vector/bird-colorful-logo-gradient-vector_343694-1365.jpg',
        name: 'pepe2',
        category: 'E-commerce',
        highestRewardSummaries: [
          HighestRewardSummary(type: 'cashback', reward: 13),
        ],
      ),
      const DefaultCampaignSummary(
        id: 3,
        imageUrl:
        'https://s3-ap-southeast-1.amazonaws.com/images.accesstrade.co.id/5f93f983524def3dca464469d2cf9f3e/logo_20170522043141.png',
        name: 'pepe3',
        category: 'Game',
        highestRewardSummaries: [
          HighestRewardSummary(type: 'cashback', reward: 14),
        ],
      ),
      const DefaultCampaignSummary(
        id: 4,
        imageUrl:
        'https://s3-ap-southeast-1.amazonaws.com/images.accesstrade.co.id/69adc1e107f7f7d035d7baf04342e1ca/logo_20170519071104.png',
        name: 'pepe4',
        category: 'Travel & Leisure',
        highestRewardSummaries: [
          HighestRewardSummary(type: 'cashback', reward: 15),
        ],
      ),
      const DefaultCampaignSummary(
        id: 1,
        imageUrl:
        'https://s3-ap-southeast-1.amazonaws.com/images.accesstrade.co.id/9cfdf10e8fc047a44b08ed031e1f0ed1/logo_20230120080350.png',
        name: 'pepe1',
        category: 'Travel & Leisure',
        highestRewardSummaries: [
          HighestRewardSummary(type: 'cashback', reward: 20),
        ],
      ),
      const DefaultCampaignSummary(
        id: 2,
        imageUrl:
        'https://s3-ap-southeast-1.amazonaws.com/images.accesstrade.co.id/01161aaa0b6d1345dd8fe4e481144d84/logo_20180104083753.png',
        name: 'pepe2',
        category: 'Travel & Leisure',
        highestRewardSummaries: [
          HighestRewardSummary(type: 'cashback', reward: 13),
        ],
      ),
      const DefaultCampaignSummary(
        id: 3,
        imageUrl:
        'https://s3-ap-southeast-1.amazonaws.com/images.accesstrade.co.id/fe73f687e5bc5280214e0486b273a5f9/logo_20220817100820.png',
        name: 'pepe3',
        category: 'Travel & Leisure',
        highestRewardSummaries: [
          HighestRewardSummary(type: 'cashback', reward: 14),
        ],
      ),
      const DefaultCampaignSummary(
        id: 4,
        imageUrl:
        'https://s3-ap-southeast-1.amazonaws.com/images.accesstrade.co.id/357a6fdf7642bf815a88822c447d9dc4/logo_20170502021310.png',
        name: 'pepe4',
        category: 'Travel & Leisure',
        highestRewardSummaries: [
          HighestRewardSummary(type: 'cashback', reward: 15),
        ],
      ),
    ];
    emit(state.copyWith(campaigns: campaignSummaries + campaignSummaries));
  }

  Future<void> filterCampaigns() async {
    emit(state.copyWith(
        filteredCampaigns: state.campaigns
            .where((c) =>
                c.name!.toLowerCase().contains(state.searchText.toLowerCase()))
            .toList()));
  }
}
