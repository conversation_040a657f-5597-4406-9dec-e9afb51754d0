// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_detail_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignDetailState _$CampaignDetailStateFromJson(Map<String, dynamic> json) {
  return _CampaignDetailState.fromJson(json);
}

/// @nodoc
mixin _$CampaignDetailState {
  CampaignDetails? get campaignDetails => throw _privateConstructorUsedError;
  String get quickLink => throw _privateConstructorUsedError;
  bool get showStickyBottom => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  List<CreativeGroup> get creativeGroups => throw _privateConstructorUsedError;
  bool get loadingCreatives => throw _privateConstructorUsedError;
  List<VoucherCategory> get voucherCategories =>
      throw _privateConstructorUsedError;
  bool get loadingVoucherCategories => throw _privateConstructorUsedError;
  List<Voucher> get vouchers => throw _privateConstructorUsedError;
  bool get loadingVouchers => throw _privateConstructorUsedError;
  List<AcceptedUrl> get acceptedUrls => throw _privateConstructorUsedError;
  bool get isCustomLinksEnabled => throw _privateConstructorUsedError;

  /// Serializes this CampaignDetailState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignDetailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignDetailStateCopyWith<CampaignDetailState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignDetailStateCopyWith<$Res> {
  factory $CampaignDetailStateCopyWith(
          CampaignDetailState value, $Res Function(CampaignDetailState) then) =
      _$CampaignDetailStateCopyWithImpl<$Res, CampaignDetailState>;
  @useResult
  $Res call(
      {CampaignDetails? campaignDetails,
      String quickLink,
      bool showStickyBottom,
      String errorMessage,
      List<CreativeGroup> creativeGroups,
      bool loadingCreatives,
      List<VoucherCategory> voucherCategories,
      bool loadingVoucherCategories,
      List<Voucher> vouchers,
      bool loadingVouchers,
      List<AcceptedUrl> acceptedUrls,
      bool isCustomLinksEnabled});

  $CampaignDetailsCopyWith<$Res>? get campaignDetails;
}

/// @nodoc
class _$CampaignDetailStateCopyWithImpl<$Res, $Val extends CampaignDetailState>
    implements $CampaignDetailStateCopyWith<$Res> {
  _$CampaignDetailStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignDetailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignDetails = freezed,
    Object? quickLink = null,
    Object? showStickyBottom = null,
    Object? errorMessage = null,
    Object? creativeGroups = null,
    Object? loadingCreatives = null,
    Object? voucherCategories = null,
    Object? loadingVoucherCategories = null,
    Object? vouchers = null,
    Object? loadingVouchers = null,
    Object? acceptedUrls = null,
    Object? isCustomLinksEnabled = null,
  }) {
    return _then(_value.copyWith(
      campaignDetails: freezed == campaignDetails
          ? _value.campaignDetails
          : campaignDetails // ignore: cast_nullable_to_non_nullable
              as CampaignDetails?,
      quickLink: null == quickLink
          ? _value.quickLink
          : quickLink // ignore: cast_nullable_to_non_nullable
              as String,
      showStickyBottom: null == showStickyBottom
          ? _value.showStickyBottom
          : showStickyBottom // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      creativeGroups: null == creativeGroups
          ? _value.creativeGroups
          : creativeGroups // ignore: cast_nullable_to_non_nullable
              as List<CreativeGroup>,
      loadingCreatives: null == loadingCreatives
          ? _value.loadingCreatives
          : loadingCreatives // ignore: cast_nullable_to_non_nullable
              as bool,
      voucherCategories: null == voucherCategories
          ? _value.voucherCategories
          : voucherCategories // ignore: cast_nullable_to_non_nullable
              as List<VoucherCategory>,
      loadingVoucherCategories: null == loadingVoucherCategories
          ? _value.loadingVoucherCategories
          : loadingVoucherCategories // ignore: cast_nullable_to_non_nullable
              as bool,
      vouchers: null == vouchers
          ? _value.vouchers
          : vouchers // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      loadingVouchers: null == loadingVouchers
          ? _value.loadingVouchers
          : loadingVouchers // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptedUrls: null == acceptedUrls
          ? _value.acceptedUrls
          : acceptedUrls // ignore: cast_nullable_to_non_nullable
              as List<AcceptedUrl>,
      isCustomLinksEnabled: null == isCustomLinksEnabled
          ? _value.isCustomLinksEnabled
          : isCustomLinksEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of CampaignDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CampaignDetailsCopyWith<$Res>? get campaignDetails {
    if (_value.campaignDetails == null) {
      return null;
    }

    return $CampaignDetailsCopyWith<$Res>(_value.campaignDetails!, (value) {
      return _then(_value.copyWith(campaignDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CampaignDetailStateImplCopyWith<$Res>
    implements $CampaignDetailStateCopyWith<$Res> {
  factory _$$CampaignDetailStateImplCopyWith(_$CampaignDetailStateImpl value,
          $Res Function(_$CampaignDetailStateImpl) then) =
      __$$CampaignDetailStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CampaignDetails? campaignDetails,
      String quickLink,
      bool showStickyBottom,
      String errorMessage,
      List<CreativeGroup> creativeGroups,
      bool loadingCreatives,
      List<VoucherCategory> voucherCategories,
      bool loadingVoucherCategories,
      List<Voucher> vouchers,
      bool loadingVouchers,
      List<AcceptedUrl> acceptedUrls,
      bool isCustomLinksEnabled});

  @override
  $CampaignDetailsCopyWith<$Res>? get campaignDetails;
}

/// @nodoc
class __$$CampaignDetailStateImplCopyWithImpl<$Res>
    extends _$CampaignDetailStateCopyWithImpl<$Res, _$CampaignDetailStateImpl>
    implements _$$CampaignDetailStateImplCopyWith<$Res> {
  __$$CampaignDetailStateImplCopyWithImpl(_$CampaignDetailStateImpl _value,
      $Res Function(_$CampaignDetailStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignDetailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignDetails = freezed,
    Object? quickLink = null,
    Object? showStickyBottom = null,
    Object? errorMessage = null,
    Object? creativeGroups = null,
    Object? loadingCreatives = null,
    Object? voucherCategories = null,
    Object? loadingVoucherCategories = null,
    Object? vouchers = null,
    Object? loadingVouchers = null,
    Object? acceptedUrls = null,
    Object? isCustomLinksEnabled = null,
  }) {
    return _then(_$CampaignDetailStateImpl(
      campaignDetails: freezed == campaignDetails
          ? _value.campaignDetails
          : campaignDetails // ignore: cast_nullable_to_non_nullable
              as CampaignDetails?,
      quickLink: null == quickLink
          ? _value.quickLink
          : quickLink // ignore: cast_nullable_to_non_nullable
              as String,
      showStickyBottom: null == showStickyBottom
          ? _value.showStickyBottom
          : showStickyBottom // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      creativeGroups: null == creativeGroups
          ? _value._creativeGroups
          : creativeGroups // ignore: cast_nullable_to_non_nullable
              as List<CreativeGroup>,
      loadingCreatives: null == loadingCreatives
          ? _value.loadingCreatives
          : loadingCreatives // ignore: cast_nullable_to_non_nullable
              as bool,
      voucherCategories: null == voucherCategories
          ? _value._voucherCategories
          : voucherCategories // ignore: cast_nullable_to_non_nullable
              as List<VoucherCategory>,
      loadingVoucherCategories: null == loadingVoucherCategories
          ? _value.loadingVoucherCategories
          : loadingVoucherCategories // ignore: cast_nullable_to_non_nullable
              as bool,
      vouchers: null == vouchers
          ? _value._vouchers
          : vouchers // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      loadingVouchers: null == loadingVouchers
          ? _value.loadingVouchers
          : loadingVouchers // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptedUrls: null == acceptedUrls
          ? _value._acceptedUrls
          : acceptedUrls // ignore: cast_nullable_to_non_nullable
              as List<AcceptedUrl>,
      isCustomLinksEnabled: null == isCustomLinksEnabled
          ? _value.isCustomLinksEnabled
          : isCustomLinksEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignDetailStateImpl implements _CampaignDetailState {
  _$CampaignDetailStateImpl(
      {this.campaignDetails,
      this.quickLink = '',
      this.showStickyBottom = false,
      this.errorMessage = '',
      final List<CreativeGroup> creativeGroups = const [],
      this.loadingCreatives = false,
      final List<VoucherCategory> voucherCategories = const [],
      this.loadingVoucherCategories = false,
      final List<Voucher> vouchers = const [],
      this.loadingVouchers = false,
      final List<AcceptedUrl> acceptedUrls = const [],
      this.isCustomLinksEnabled = false})
      : _creativeGroups = creativeGroups,
        _voucherCategories = voucherCategories,
        _vouchers = vouchers,
        _acceptedUrls = acceptedUrls;

  factory _$CampaignDetailStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignDetailStateImplFromJson(json);

  @override
  final CampaignDetails? campaignDetails;
  @override
  @JsonKey()
  final String quickLink;
  @override
  @JsonKey()
  final bool showStickyBottom;
  @override
  @JsonKey()
  final String errorMessage;
  final List<CreativeGroup> _creativeGroups;
  @override
  @JsonKey()
  List<CreativeGroup> get creativeGroups {
    if (_creativeGroups is EqualUnmodifiableListView) return _creativeGroups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_creativeGroups);
  }

  @override
  @JsonKey()
  final bool loadingCreatives;
  final List<VoucherCategory> _voucherCategories;
  @override
  @JsonKey()
  List<VoucherCategory> get voucherCategories {
    if (_voucherCategories is EqualUnmodifiableListView)
      return _voucherCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_voucherCategories);
  }

  @override
  @JsonKey()
  final bool loadingVoucherCategories;
  final List<Voucher> _vouchers;
  @override
  @JsonKey()
  List<Voucher> get vouchers {
    if (_vouchers is EqualUnmodifiableListView) return _vouchers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vouchers);
  }

  @override
  @JsonKey()
  final bool loadingVouchers;
  final List<AcceptedUrl> _acceptedUrls;
  @override
  @JsonKey()
  List<AcceptedUrl> get acceptedUrls {
    if (_acceptedUrls is EqualUnmodifiableListView) return _acceptedUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_acceptedUrls);
  }

  @override
  @JsonKey()
  final bool isCustomLinksEnabled;

  @override
  String toString() {
    return 'CampaignDetailState(campaignDetails: $campaignDetails, quickLink: $quickLink, showStickyBottom: $showStickyBottom, errorMessage: $errorMessage, creativeGroups: $creativeGroups, loadingCreatives: $loadingCreatives, voucherCategories: $voucherCategories, loadingVoucherCategories: $loadingVoucherCategories, vouchers: $vouchers, loadingVouchers: $loadingVouchers, acceptedUrls: $acceptedUrls, isCustomLinksEnabled: $isCustomLinksEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignDetailStateImpl &&
            (identical(other.campaignDetails, campaignDetails) ||
                other.campaignDetails == campaignDetails) &&
            (identical(other.quickLink, quickLink) ||
                other.quickLink == quickLink) &&
            (identical(other.showStickyBottom, showStickyBottom) ||
                other.showStickyBottom == showStickyBottom) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._creativeGroups, _creativeGroups) &&
            (identical(other.loadingCreatives, loadingCreatives) ||
                other.loadingCreatives == loadingCreatives) &&
            const DeepCollectionEquality()
                .equals(other._voucherCategories, _voucherCategories) &&
            (identical(
                    other.loadingVoucherCategories, loadingVoucherCategories) ||
                other.loadingVoucherCategories == loadingVoucherCategories) &&
            const DeepCollectionEquality().equals(other._vouchers, _vouchers) &&
            (identical(other.loadingVouchers, loadingVouchers) ||
                other.loadingVouchers == loadingVouchers) &&
            const DeepCollectionEquality()
                .equals(other._acceptedUrls, _acceptedUrls) &&
            (identical(other.isCustomLinksEnabled, isCustomLinksEnabled) ||
                other.isCustomLinksEnabled == isCustomLinksEnabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      campaignDetails,
      quickLink,
      showStickyBottom,
      errorMessage,
      const DeepCollectionEquality().hash(_creativeGroups),
      loadingCreatives,
      const DeepCollectionEquality().hash(_voucherCategories),
      loadingVoucherCategories,
      const DeepCollectionEquality().hash(_vouchers),
      loadingVouchers,
      const DeepCollectionEquality().hash(_acceptedUrls),
      isCustomLinksEnabled);

  /// Create a copy of CampaignDetailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignDetailStateImplCopyWith<_$CampaignDetailStateImpl> get copyWith =>
      __$$CampaignDetailStateImplCopyWithImpl<_$CampaignDetailStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignDetailStateImplToJson(
      this,
    );
  }
}

abstract class _CampaignDetailState implements CampaignDetailState {
  factory _CampaignDetailState(
      {final CampaignDetails? campaignDetails,
      final String quickLink,
      final bool showStickyBottom,
      final String errorMessage,
      final List<CreativeGroup> creativeGroups,
      final bool loadingCreatives,
      final List<VoucherCategory> voucherCategories,
      final bool loadingVoucherCategories,
      final List<Voucher> vouchers,
      final bool loadingVouchers,
      final List<AcceptedUrl> acceptedUrls,
      final bool isCustomLinksEnabled}) = _$CampaignDetailStateImpl;

  factory _CampaignDetailState.fromJson(Map<String, dynamic> json) =
      _$CampaignDetailStateImpl.fromJson;

  @override
  CampaignDetails? get campaignDetails;
  @override
  String get quickLink;
  @override
  bool get showStickyBottom;
  @override
  String get errorMessage;
  @override
  List<CreativeGroup> get creativeGroups;
  @override
  bool get loadingCreatives;
  @override
  List<VoucherCategory> get voucherCategories;
  @override
  bool get loadingVoucherCategories;
  @override
  List<Voucher> get vouchers;
  @override
  bool get loadingVouchers;
  @override
  List<AcceptedUrl> get acceptedUrls;
  @override
  bool get isCustomLinksEnabled;

  /// Create a copy of CampaignDetailState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignDetailStateImplCopyWith<_$CampaignDetailStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
