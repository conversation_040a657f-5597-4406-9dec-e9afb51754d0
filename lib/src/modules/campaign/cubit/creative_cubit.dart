import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_detail_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/creative_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/creative.dart';
import 'package:koc_app/src/modules/campaign/data/repository/creative_repository.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class CreativeCubit extends BaseCubit<CreativeState> {
  final CreativeRepository creativeRepository;

  CreativeCubit(this.creativeRepository) : super(CreativeState());

  Future<void> getCreatives(int campaignId, {bool forceRefresh = false}) async {
    try {
      emit(state.copyWith(isLoading: true));

      final campaignDetailCubit = Modular.get<CampaignDetailCubit>();
      final result = await campaignDetailCubit.getCreatives(campaignId);

      List<CreativeGroup> processedCreatives = [];

      if (result is List) {
        processedCreatives = result
            .map((item) {
              if (item is Map<String, dynamic>) {
                return CreativeGroup.fromJson(item);
              } else if (item is CreativeGroup) {
                return item;
              }
              return null;
            })
            .whereType<CreativeGroup>()
            .toList();
      } else if (result is List<CreativeGroup>) {
        processedCreatives = result;
      }

      Map<int, int> initialImageIndices = {};
      for (var creative in processedCreatives) {
        initialImageIndices[creative.id ?? 0] = 0;
      }

      emit(state.copyWith(
          creatives: processedCreatives, isLoading: false, isDataLoaded: true, imageIndex: initialImageIndices));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message, isLoading: false, isDataLoaded: true)));
    }
  }

  void updateImageIndex(int creativeId, int value) {
    Map<int, int> imageIndex = Map.from(state.imageIndex);
    imageIndex[creativeId] = value;
    emit(state.copyWith(imageIndex: imageIndex));
  }
}
