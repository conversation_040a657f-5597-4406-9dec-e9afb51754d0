import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';

part 'campaign_upsized_reward_state.freezed.dart';
part 'campaign_upsized_reward_state.g.dart';

@freezed
class CampaignUpsizedRewardState extends BaseCubitState
    with _$CampaignUpsizedRewardState {
  factory CampaignUpsizedRewardState({
    @Default('') String currency,
    @Default([]) List<UpsizedReward> upsizedRewards,
  }) = _CampaignUpsizedRewardState;

  factory CampaignUpsizedRewardState.fromJson(Map<String, Object> json) =>
      _$CampaignUpsizedRewardStateFromJson(json);
}
