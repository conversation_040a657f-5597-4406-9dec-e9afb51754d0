import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_view_state.dart';

class CampaignListViewCubit extends BaseCubit<CampaignListViewState> {
  CampaignListViewCubit() : super(CampaignListViewState());

  void toggleView() => emit(state.copyWith(isListView: !state.isListView));
  void toggleSort() => emit(state.copyWith(isAsc: !state.isAsc));
}
