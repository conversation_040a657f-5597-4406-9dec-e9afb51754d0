// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_upsized_reward_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignUpsizedRewardState _$CampaignUpsizedRewardStateFromJson(
    Map<String, dynamic> json) {
  return _CampaignUpsizedRewardState.fromJson(json);
}

/// @nodoc
mixin _$CampaignUpsizedRewardState {
  String get currency => throw _privateConstructorUsedError;
  List<UpsizedReward> get upsizedRewards => throw _privateConstructorUsedError;

  /// Serializes this CampaignUpsizedRewardState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignUpsizedRewardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignUpsizedRewardStateCopyWith<CampaignUpsizedRewardState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignUpsizedRewardStateCopyWith<$Res> {
  factory $CampaignUpsizedRewardStateCopyWith(CampaignUpsizedRewardState value,
          $Res Function(CampaignUpsizedRewardState) then) =
      _$CampaignUpsizedRewardStateCopyWithImpl<$Res,
          CampaignUpsizedRewardState>;
  @useResult
  $Res call({String currency, List<UpsizedReward> upsizedRewards});
}

/// @nodoc
class _$CampaignUpsizedRewardStateCopyWithImpl<$Res,
        $Val extends CampaignUpsizedRewardState>
    implements $CampaignUpsizedRewardStateCopyWith<$Res> {
  _$CampaignUpsizedRewardStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignUpsizedRewardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? upsizedRewards = null,
  }) {
    return _then(_value.copyWith(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      upsizedRewards: null == upsizedRewards
          ? _value.upsizedRewards
          : upsizedRewards // ignore: cast_nullable_to_non_nullable
              as List<UpsizedReward>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignUpsizedRewardStateImplCopyWith<$Res>
    implements $CampaignUpsizedRewardStateCopyWith<$Res> {
  factory _$$CampaignUpsizedRewardStateImplCopyWith(
          _$CampaignUpsizedRewardStateImpl value,
          $Res Function(_$CampaignUpsizedRewardStateImpl) then) =
      __$$CampaignUpsizedRewardStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String currency, List<UpsizedReward> upsizedRewards});
}

/// @nodoc
class __$$CampaignUpsizedRewardStateImplCopyWithImpl<$Res>
    extends _$CampaignUpsizedRewardStateCopyWithImpl<$Res,
        _$CampaignUpsizedRewardStateImpl>
    implements _$$CampaignUpsizedRewardStateImplCopyWith<$Res> {
  __$$CampaignUpsizedRewardStateImplCopyWithImpl(
      _$CampaignUpsizedRewardStateImpl _value,
      $Res Function(_$CampaignUpsizedRewardStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignUpsizedRewardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? upsizedRewards = null,
  }) {
    return _then(_$CampaignUpsizedRewardStateImpl(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      upsizedRewards: null == upsizedRewards
          ? _value._upsizedRewards
          : upsizedRewards // ignore: cast_nullable_to_non_nullable
              as List<UpsizedReward>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignUpsizedRewardStateImpl implements _CampaignUpsizedRewardState {
  _$CampaignUpsizedRewardStateImpl(
      {this.currency = '', final List<UpsizedReward> upsizedRewards = const []})
      : _upsizedRewards = upsizedRewards;

  factory _$CampaignUpsizedRewardStateImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CampaignUpsizedRewardStateImplFromJson(json);

  @override
  @JsonKey()
  final String currency;
  final List<UpsizedReward> _upsizedRewards;
  @override
  @JsonKey()
  List<UpsizedReward> get upsizedRewards {
    if (_upsizedRewards is EqualUnmodifiableListView) return _upsizedRewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_upsizedRewards);
  }

  @override
  String toString() {
    return 'CampaignUpsizedRewardState(currency: $currency, upsizedRewards: $upsizedRewards)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignUpsizedRewardStateImpl &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            const DeepCollectionEquality()
                .equals(other._upsizedRewards, _upsizedRewards));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, currency,
      const DeepCollectionEquality().hash(_upsizedRewards));

  /// Create a copy of CampaignUpsizedRewardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignUpsizedRewardStateImplCopyWith<_$CampaignUpsizedRewardStateImpl>
      get copyWith => __$$CampaignUpsizedRewardStateImplCopyWithImpl<
          _$CampaignUpsizedRewardStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignUpsizedRewardStateImplToJson(
      this,
    );
  }
}

abstract class _CampaignUpsizedRewardState
    implements CampaignUpsizedRewardState {
  factory _CampaignUpsizedRewardState(
          {final String currency, final List<UpsizedReward> upsizedRewards}) =
      _$CampaignUpsizedRewardStateImpl;

  factory _CampaignUpsizedRewardState.fromJson(Map<String, dynamic> json) =
      _$CampaignUpsizedRewardStateImpl.fromJson;

  @override
  String get currency;
  @override
  List<UpsizedReward> get upsizedRewards;

  /// Create a copy of CampaignUpsizedRewardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignUpsizedRewardStateImplCopyWith<_$CampaignUpsizedRewardStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
