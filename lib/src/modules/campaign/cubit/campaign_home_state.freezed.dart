// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignHomeState _$CampaignHomeStateFromJson(Map<String, dynamic> json) {
  return _CampaignHomeState.fromJson(json);
}

/// @nodoc
mixin _$CampaignHomeState {
  List<DefaultCampaignSummary> get recommendCampaigns =>
      throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get latestCampaigns =>
      throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get waitingCampaigns =>
      throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get affiliatedCampaigns =>
      throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get pausedCampaigns =>
      throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get availableCampaigns =>
      throw _privateConstructorUsedError;
  CampaignCountSummary get campaignSummariesCount =>
      throw _privateConstructorUsedError;
  int get tabIndex => throw _privateConstructorUsedError;
  String get notificationMessage => throw _privateConstructorUsedError;
  PassionateInfo get passionateInfo => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  dynamic get isLoading => throw _privateConstructorUsedError;
  dynamic get isLoadingMore => throw _privateConstructorUsedError;
  dynamic get isPullToRefresh => throw _privateConstructorUsedError;
  dynamic get isSiteSwitching => throw _privateConstructorUsedError;
  dynamic get hasMore => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  int get selectedSiteId => throw _privateConstructorUsedError;

  /// Serializes this CampaignHomeState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignHomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignHomeStateCopyWith<CampaignHomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignHomeStateCopyWith<$Res> {
  factory $CampaignHomeStateCopyWith(
          CampaignHomeState value, $Res Function(CampaignHomeState) then) =
      _$CampaignHomeStateCopyWithImpl<$Res, CampaignHomeState>;
  @useResult
  $Res call(
      {List<DefaultCampaignSummary> recommendCampaigns,
      List<DefaultCampaignSummary> latestCampaigns,
      List<DefaultCampaignSummary> waitingCampaigns,
      List<DefaultCampaignSummary> affiliatedCampaigns,
      List<DefaultCampaignSummary> pausedCampaigns,
      List<DefaultCampaignSummary> availableCampaigns,
      CampaignCountSummary campaignSummariesCount,
      int tabIndex,
      String notificationMessage,
      PassionateInfo passionateInfo,
      String errorMessage,
      dynamic isLoading,
      dynamic isLoadingMore,
      dynamic isPullToRefresh,
      dynamic isSiteSwitching,
      dynamic hasMore,
      int currentPage,
      String currency,
      int selectedSiteId});

  $CampaignCountSummaryCopyWith<$Res> get campaignSummariesCount;
  $PassionateInfoCopyWith<$Res> get passionateInfo;
}

/// @nodoc
class _$CampaignHomeStateCopyWithImpl<$Res, $Val extends CampaignHomeState>
    implements $CampaignHomeStateCopyWith<$Res> {
  _$CampaignHomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignHomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recommendCampaigns = null,
    Object? latestCampaigns = null,
    Object? waitingCampaigns = null,
    Object? affiliatedCampaigns = null,
    Object? pausedCampaigns = null,
    Object? availableCampaigns = null,
    Object? campaignSummariesCount = null,
    Object? tabIndex = null,
    Object? notificationMessage = null,
    Object? passionateInfo = null,
    Object? errorMessage = null,
    Object? isLoading = freezed,
    Object? isLoadingMore = freezed,
    Object? isPullToRefresh = freezed,
    Object? isSiteSwitching = freezed,
    Object? hasMore = freezed,
    Object? currentPage = null,
    Object? currency = null,
    Object? selectedSiteId = null,
  }) {
    return _then(_value.copyWith(
      recommendCampaigns: null == recommendCampaigns
          ? _value.recommendCampaigns
          : recommendCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      latestCampaigns: null == latestCampaigns
          ? _value.latestCampaigns
          : latestCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      waitingCampaigns: null == waitingCampaigns
          ? _value.waitingCampaigns
          : waitingCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      affiliatedCampaigns: null == affiliatedCampaigns
          ? _value.affiliatedCampaigns
          : affiliatedCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      pausedCampaigns: null == pausedCampaigns
          ? _value.pausedCampaigns
          : pausedCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      availableCampaigns: null == availableCampaigns
          ? _value.availableCampaigns
          : availableCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      campaignSummariesCount: null == campaignSummariesCount
          ? _value.campaignSummariesCount
          : campaignSummariesCount // ignore: cast_nullable_to_non_nullable
              as CampaignCountSummary,
      tabIndex: null == tabIndex
          ? _value.tabIndex
          : tabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      notificationMessage: null == notificationMessage
          ? _value.notificationMessage
          : notificationMessage // ignore: cast_nullable_to_non_nullable
              as String,
      passionateInfo: null == passionateInfo
          ? _value.passionateInfo
          : passionateInfo // ignore: cast_nullable_to_non_nullable
              as PassionateInfo,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: freezed == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isLoadingMore: freezed == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isPullToRefresh: freezed == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSiteSwitching: freezed == isSiteSwitching
          ? _value.isSiteSwitching
          : isSiteSwitching // ignore: cast_nullable_to_non_nullable
              as dynamic,
      hasMore: freezed == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as dynamic,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      selectedSiteId: null == selectedSiteId
          ? _value.selectedSiteId
          : selectedSiteId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of CampaignHomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CampaignCountSummaryCopyWith<$Res> get campaignSummariesCount {
    return $CampaignCountSummaryCopyWith<$Res>(_value.campaignSummariesCount,
        (value) {
      return _then(_value.copyWith(campaignSummariesCount: value) as $Val);
    });
  }

  /// Create a copy of CampaignHomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PassionateInfoCopyWith<$Res> get passionateInfo {
    return $PassionateInfoCopyWith<$Res>(_value.passionateInfo, (value) {
      return _then(_value.copyWith(passionateInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CampaignHomeStateImplCopyWith<$Res>
    implements $CampaignHomeStateCopyWith<$Res> {
  factory _$$CampaignHomeStateImplCopyWith(_$CampaignHomeStateImpl value,
          $Res Function(_$CampaignHomeStateImpl) then) =
      __$$CampaignHomeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<DefaultCampaignSummary> recommendCampaigns,
      List<DefaultCampaignSummary> latestCampaigns,
      List<DefaultCampaignSummary> waitingCampaigns,
      List<DefaultCampaignSummary> affiliatedCampaigns,
      List<DefaultCampaignSummary> pausedCampaigns,
      List<DefaultCampaignSummary> availableCampaigns,
      CampaignCountSummary campaignSummariesCount,
      int tabIndex,
      String notificationMessage,
      PassionateInfo passionateInfo,
      String errorMessage,
      dynamic isLoading,
      dynamic isLoadingMore,
      dynamic isPullToRefresh,
      dynamic isSiteSwitching,
      dynamic hasMore,
      int currentPage,
      String currency,
      int selectedSiteId});

  @override
  $CampaignCountSummaryCopyWith<$Res> get campaignSummariesCount;
  @override
  $PassionateInfoCopyWith<$Res> get passionateInfo;
}

/// @nodoc
class __$$CampaignHomeStateImplCopyWithImpl<$Res>
    extends _$CampaignHomeStateCopyWithImpl<$Res, _$CampaignHomeStateImpl>
    implements _$$CampaignHomeStateImplCopyWith<$Res> {
  __$$CampaignHomeStateImplCopyWithImpl(_$CampaignHomeStateImpl _value,
      $Res Function(_$CampaignHomeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignHomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recommendCampaigns = null,
    Object? latestCampaigns = null,
    Object? waitingCampaigns = null,
    Object? affiliatedCampaigns = null,
    Object? pausedCampaigns = null,
    Object? availableCampaigns = null,
    Object? campaignSummariesCount = null,
    Object? tabIndex = null,
    Object? notificationMessage = null,
    Object? passionateInfo = null,
    Object? errorMessage = null,
    Object? isLoading = freezed,
    Object? isLoadingMore = freezed,
    Object? isPullToRefresh = freezed,
    Object? isSiteSwitching = freezed,
    Object? hasMore = freezed,
    Object? currentPage = null,
    Object? currency = null,
    Object? selectedSiteId = null,
  }) {
    return _then(_$CampaignHomeStateImpl(
      recommendCampaigns: null == recommendCampaigns
          ? _value._recommendCampaigns
          : recommendCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      latestCampaigns: null == latestCampaigns
          ? _value._latestCampaigns
          : latestCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      waitingCampaigns: null == waitingCampaigns
          ? _value._waitingCampaigns
          : waitingCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      affiliatedCampaigns: null == affiliatedCampaigns
          ? _value._affiliatedCampaigns
          : affiliatedCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      pausedCampaigns: null == pausedCampaigns
          ? _value._pausedCampaigns
          : pausedCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      availableCampaigns: null == availableCampaigns
          ? _value._availableCampaigns
          : availableCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      campaignSummariesCount: null == campaignSummariesCount
          ? _value.campaignSummariesCount
          : campaignSummariesCount // ignore: cast_nullable_to_non_nullable
              as CampaignCountSummary,
      tabIndex: null == tabIndex
          ? _value.tabIndex
          : tabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      notificationMessage: null == notificationMessage
          ? _value.notificationMessage
          : notificationMessage // ignore: cast_nullable_to_non_nullable
              as String,
      passionateInfo: null == passionateInfo
          ? _value.passionateInfo
          : passionateInfo // ignore: cast_nullable_to_non_nullable
              as PassionateInfo,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: freezed == isLoading ? _value.isLoading! : isLoading,
      isLoadingMore:
          freezed == isLoadingMore ? _value.isLoadingMore! : isLoadingMore,
      isPullToRefresh: freezed == isPullToRefresh
          ? _value.isPullToRefresh!
          : isPullToRefresh,
      isSiteSwitching: freezed == isSiteSwitching
          ? _value.isSiteSwitching!
          : isSiteSwitching,
      hasMore: freezed == hasMore ? _value.hasMore! : hasMore,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      selectedSiteId: null == selectedSiteId
          ? _value.selectedSiteId
          : selectedSiteId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignHomeStateImpl implements _CampaignHomeState {
  _$CampaignHomeStateImpl(
      {final List<DefaultCampaignSummary> recommendCampaigns = const [],
      final List<DefaultCampaignSummary> latestCampaigns = const [],
      final List<DefaultCampaignSummary> waitingCampaigns = const [],
      final List<DefaultCampaignSummary> affiliatedCampaigns = const [],
      final List<DefaultCampaignSummary> pausedCampaigns = const [],
      final List<DefaultCampaignSummary> availableCampaigns = const [],
      this.campaignSummariesCount = const CampaignCountSummary(),
      this.tabIndex = 0,
      this.notificationMessage = '',
      this.passionateInfo = const PassionateInfo(),
      this.errorMessage = '',
      this.isLoading = false,
      this.isLoadingMore = false,
      this.isPullToRefresh = false,
      this.isSiteSwitching = false,
      this.hasMore = false,
      this.currentPage = 1,
      this.currency = '',
      this.selectedSiteId = 0})
      : _recommendCampaigns = recommendCampaigns,
        _latestCampaigns = latestCampaigns,
        _waitingCampaigns = waitingCampaigns,
        _affiliatedCampaigns = affiliatedCampaigns,
        _pausedCampaigns = pausedCampaigns,
        _availableCampaigns = availableCampaigns;

  factory _$CampaignHomeStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignHomeStateImplFromJson(json);

  final List<DefaultCampaignSummary> _recommendCampaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get recommendCampaigns {
    if (_recommendCampaigns is EqualUnmodifiableListView)
      return _recommendCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recommendCampaigns);
  }

  final List<DefaultCampaignSummary> _latestCampaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get latestCampaigns {
    if (_latestCampaigns is EqualUnmodifiableListView) return _latestCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_latestCampaigns);
  }

  final List<DefaultCampaignSummary> _waitingCampaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get waitingCampaigns {
    if (_waitingCampaigns is EqualUnmodifiableListView)
      return _waitingCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_waitingCampaigns);
  }

  final List<DefaultCampaignSummary> _affiliatedCampaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get affiliatedCampaigns {
    if (_affiliatedCampaigns is EqualUnmodifiableListView)
      return _affiliatedCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_affiliatedCampaigns);
  }

  final List<DefaultCampaignSummary> _pausedCampaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get pausedCampaigns {
    if (_pausedCampaigns is EqualUnmodifiableListView) return _pausedCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pausedCampaigns);
  }

  final List<DefaultCampaignSummary> _availableCampaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get availableCampaigns {
    if (_availableCampaigns is EqualUnmodifiableListView)
      return _availableCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableCampaigns);
  }

  @override
  @JsonKey()
  final CampaignCountSummary campaignSummariesCount;
  @override
  @JsonKey()
  final int tabIndex;
  @override
  @JsonKey()
  final String notificationMessage;
  @override
  @JsonKey()
  final PassionateInfo passionateInfo;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final dynamic isLoading;
  @override
  @JsonKey()
  final dynamic isLoadingMore;
  @override
  @JsonKey()
  final dynamic isPullToRefresh;
  @override
  @JsonKey()
  final dynamic isSiteSwitching;
  @override
  @JsonKey()
  final dynamic hasMore;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final int selectedSiteId;

  @override
  String toString() {
    return 'CampaignHomeState(recommendCampaigns: $recommendCampaigns, latestCampaigns: $latestCampaigns, waitingCampaigns: $waitingCampaigns, affiliatedCampaigns: $affiliatedCampaigns, pausedCampaigns: $pausedCampaigns, availableCampaigns: $availableCampaigns, campaignSummariesCount: $campaignSummariesCount, tabIndex: $tabIndex, notificationMessage: $notificationMessage, passionateInfo: $passionateInfo, errorMessage: $errorMessage, isLoading: $isLoading, isLoadingMore: $isLoadingMore, isPullToRefresh: $isPullToRefresh, isSiteSwitching: $isSiteSwitching, hasMore: $hasMore, currentPage: $currentPage, currency: $currency, selectedSiteId: $selectedSiteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignHomeStateImpl &&
            const DeepCollectionEquality()
                .equals(other._recommendCampaigns, _recommendCampaigns) &&
            const DeepCollectionEquality()
                .equals(other._latestCampaigns, _latestCampaigns) &&
            const DeepCollectionEquality()
                .equals(other._waitingCampaigns, _waitingCampaigns) &&
            const DeepCollectionEquality()
                .equals(other._affiliatedCampaigns, _affiliatedCampaigns) &&
            const DeepCollectionEquality()
                .equals(other._pausedCampaigns, _pausedCampaigns) &&
            const DeepCollectionEquality()
                .equals(other._availableCampaigns, _availableCampaigns) &&
            (identical(other.campaignSummariesCount, campaignSummariesCount) ||
                other.campaignSummariesCount == campaignSummariesCount) &&
            (identical(other.tabIndex, tabIndex) ||
                other.tabIndex == tabIndex) &&
            (identical(other.notificationMessage, notificationMessage) ||
                other.notificationMessage == notificationMessage) &&
            (identical(other.passionateInfo, passionateInfo) ||
                other.passionateInfo == passionateInfo) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(other.isLoading, isLoading) &&
            const DeepCollectionEquality()
                .equals(other.isLoadingMore, isLoadingMore) &&
            const DeepCollectionEquality()
                .equals(other.isPullToRefresh, isPullToRefresh) &&
            const DeepCollectionEquality()
                .equals(other.isSiteSwitching, isSiteSwitching) &&
            const DeepCollectionEquality().equals(other.hasMore, hasMore) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.selectedSiteId, selectedSiteId) ||
                other.selectedSiteId == selectedSiteId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        const DeepCollectionEquality().hash(_recommendCampaigns),
        const DeepCollectionEquality().hash(_latestCampaigns),
        const DeepCollectionEquality().hash(_waitingCampaigns),
        const DeepCollectionEquality().hash(_affiliatedCampaigns),
        const DeepCollectionEquality().hash(_pausedCampaigns),
        const DeepCollectionEquality().hash(_availableCampaigns),
        campaignSummariesCount,
        tabIndex,
        notificationMessage,
        passionateInfo,
        errorMessage,
        const DeepCollectionEquality().hash(isLoading),
        const DeepCollectionEquality().hash(isLoadingMore),
        const DeepCollectionEquality().hash(isPullToRefresh),
        const DeepCollectionEquality().hash(isSiteSwitching),
        const DeepCollectionEquality().hash(hasMore),
        currentPage,
        currency,
        selectedSiteId
      ]);

  /// Create a copy of CampaignHomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignHomeStateImplCopyWith<_$CampaignHomeStateImpl> get copyWith =>
      __$$CampaignHomeStateImplCopyWithImpl<_$CampaignHomeStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignHomeStateImplToJson(
      this,
    );
  }
}

abstract class _CampaignHomeState implements CampaignHomeState {
  factory _CampaignHomeState(
      {final List<DefaultCampaignSummary> recommendCampaigns,
      final List<DefaultCampaignSummary> latestCampaigns,
      final List<DefaultCampaignSummary> waitingCampaigns,
      final List<DefaultCampaignSummary> affiliatedCampaigns,
      final List<DefaultCampaignSummary> pausedCampaigns,
      final List<DefaultCampaignSummary> availableCampaigns,
      final CampaignCountSummary campaignSummariesCount,
      final int tabIndex,
      final String notificationMessage,
      final PassionateInfo passionateInfo,
      final String errorMessage,
      final dynamic isLoading,
      final dynamic isLoadingMore,
      final dynamic isPullToRefresh,
      final dynamic isSiteSwitching,
      final dynamic hasMore,
      final int currentPage,
      final String currency,
      final int selectedSiteId}) = _$CampaignHomeStateImpl;

  factory _CampaignHomeState.fromJson(Map<String, dynamic> json) =
      _$CampaignHomeStateImpl.fromJson;

  @override
  List<DefaultCampaignSummary> get recommendCampaigns;
  @override
  List<DefaultCampaignSummary> get latestCampaigns;
  @override
  List<DefaultCampaignSummary> get waitingCampaigns;
  @override
  List<DefaultCampaignSummary> get affiliatedCampaigns;
  @override
  List<DefaultCampaignSummary> get pausedCampaigns;
  @override
  List<DefaultCampaignSummary> get availableCampaigns;
  @override
  CampaignCountSummary get campaignSummariesCount;
  @override
  int get tabIndex;
  @override
  String get notificationMessage;
  @override
  PassionateInfo get passionateInfo;
  @override
  String get errorMessage;
  @override
  dynamic get isLoading;
  @override
  dynamic get isLoadingMore;
  @override
  dynamic get isPullToRefresh;
  @override
  dynamic get isSiteSwitching;
  @override
  dynamic get hasMore;
  @override
  int get currentPage;
  @override
  String get currency;
  @override
  int get selectedSiteId;

  /// Create a copy of CampaignHomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignHomeStateImplCopyWith<_$CampaignHomeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
