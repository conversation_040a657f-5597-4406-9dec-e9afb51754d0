// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_list_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CampaignListState {
  List<DefaultCampaignSummary> get campaigns =>
      throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get filteredCampaigns =>
      throw _privateConstructorUsedError;
  DefaultCampaignSummary? get selectedCampaign =>
      throw _privateConstructorUsedError;
  String get searchText => throw _privateConstructorUsedError;

  /// Create a copy of CampaignListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignListStateCopyWith<CampaignListState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignListStateCopyWith<$Res> {
  factory $CampaignListStateCopyWith(
          CampaignListState value, $Res Function(CampaignListState) then) =
      _$CampaignListStateCopyWithImpl<$Res, CampaignListState>;
  @useResult
  $Res call(
      {List<DefaultCampaignSummary> campaigns,
      List<DefaultCampaignSummary> filteredCampaigns,
      DefaultCampaignSummary? selectedCampaign,
      String searchText});

  $DefaultCampaignSummaryCopyWith<$Res>? get selectedCampaign;
}

/// @nodoc
class _$CampaignListStateCopyWithImpl<$Res, $Val extends CampaignListState>
    implements $CampaignListStateCopyWith<$Res> {
  _$CampaignListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaigns = null,
    Object? filteredCampaigns = null,
    Object? selectedCampaign = freezed,
    Object? searchText = null,
  }) {
    return _then(_value.copyWith(
      campaigns: null == campaigns
          ? _value.campaigns
          : campaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      filteredCampaigns: null == filteredCampaigns
          ? _value.filteredCampaigns
          : filteredCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      selectedCampaign: freezed == selectedCampaign
          ? _value.selectedCampaign
          : selectedCampaign // ignore: cast_nullable_to_non_nullable
              as DefaultCampaignSummary?,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of CampaignListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DefaultCampaignSummaryCopyWith<$Res>? get selectedCampaign {
    if (_value.selectedCampaign == null) {
      return null;
    }

    return $DefaultCampaignSummaryCopyWith<$Res>(_value.selectedCampaign!,
        (value) {
      return _then(_value.copyWith(selectedCampaign: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CampaignListStateImplCopyWith<$Res>
    implements $CampaignListStateCopyWith<$Res> {
  factory _$$CampaignListStateImplCopyWith(_$CampaignListStateImpl value,
          $Res Function(_$CampaignListStateImpl) then) =
      __$$CampaignListStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<DefaultCampaignSummary> campaigns,
      List<DefaultCampaignSummary> filteredCampaigns,
      DefaultCampaignSummary? selectedCampaign,
      String searchText});

  @override
  $DefaultCampaignSummaryCopyWith<$Res>? get selectedCampaign;
}

/// @nodoc
class __$$CampaignListStateImplCopyWithImpl<$Res>
    extends _$CampaignListStateCopyWithImpl<$Res, _$CampaignListStateImpl>
    implements _$$CampaignListStateImplCopyWith<$Res> {
  __$$CampaignListStateImplCopyWithImpl(_$CampaignListStateImpl _value,
      $Res Function(_$CampaignListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaigns = null,
    Object? filteredCampaigns = null,
    Object? selectedCampaign = freezed,
    Object? searchText = null,
  }) {
    return _then(_$CampaignListStateImpl(
      campaigns: null == campaigns
          ? _value._campaigns
          : campaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      filteredCampaigns: null == filteredCampaigns
          ? _value._filteredCampaigns
          : filteredCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      selectedCampaign: freezed == selectedCampaign
          ? _value.selectedCampaign
          : selectedCampaign // ignore: cast_nullable_to_non_nullable
              as DefaultCampaignSummary?,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CampaignListStateImpl implements _CampaignListState {
  _$CampaignListStateImpl(
      {final List<DefaultCampaignSummary> campaigns = const [],
      final List<DefaultCampaignSummary> filteredCampaigns = const [],
      this.selectedCampaign,
      this.searchText = ''})
      : _campaigns = campaigns,
        _filteredCampaigns = filteredCampaigns;

  final List<DefaultCampaignSummary> _campaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get campaigns {
    if (_campaigns is EqualUnmodifiableListView) return _campaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaigns);
  }

  final List<DefaultCampaignSummary> _filteredCampaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get filteredCampaigns {
    if (_filteredCampaigns is EqualUnmodifiableListView)
      return _filteredCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredCampaigns);
  }

  @override
  final DefaultCampaignSummary? selectedCampaign;
  @override
  @JsonKey()
  final String searchText;

  @override
  String toString() {
    return 'CampaignListState(campaigns: $campaigns, filteredCampaigns: $filteredCampaigns, selectedCampaign: $selectedCampaign, searchText: $searchText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignListStateImpl &&
            const DeepCollectionEquality()
                .equals(other._campaigns, _campaigns) &&
            const DeepCollectionEquality()
                .equals(other._filteredCampaigns, _filteredCampaigns) &&
            (identical(other.selectedCampaign, selectedCampaign) ||
                other.selectedCampaign == selectedCampaign) &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_campaigns),
      const DeepCollectionEquality().hash(_filteredCampaigns),
      selectedCampaign,
      searchText);

  /// Create a copy of CampaignListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignListStateImplCopyWith<_$CampaignListStateImpl> get copyWith =>
      __$$CampaignListStateImplCopyWithImpl<_$CampaignListStateImpl>(
          this, _$identity);
}

abstract class _CampaignListState implements CampaignListState {
  factory _CampaignListState(
      {final List<DefaultCampaignSummary> campaigns,
      final List<DefaultCampaignSummary> filteredCampaigns,
      final DefaultCampaignSummary? selectedCampaign,
      final String searchText}) = _$CampaignListStateImpl;

  @override
  List<DefaultCampaignSummary> get campaigns;
  @override
  List<DefaultCampaignSummary> get filteredCampaigns;
  @override
  DefaultCampaignSummary? get selectedCampaign;
  @override
  String get searchText;

  /// Create a copy of CampaignListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignListStateImplCopyWith<_$CampaignListStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
