// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_search_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignSearchState _$CampaignSearchStateFromJson(Map<String, dynamic> json) {
  return _CampaignSearchState.fromJson(json);
}

/// @nodoc
mixin _$CampaignSearchState {
  List<Category> get categories => throw _privateConstructorUsedError;
  List<bool> get isCategoryChecked => throw _privateConstructorUsedError;
  List<CampaignType> get types => throw _privateConstructorUsedError;
  List<bool> get isTypeChecked => throw _privateConstructorUsedError;
  bool get isDataLoaded => throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get campaignResults =>
      throw _privateConstructorUsedError;
  String get searchText => throw _privateConstructorUsedError;
  List<String> get searchTextHistories => throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get textSearchResults =>
      throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isSearchingByKeyword => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;

  /// Serializes this CampaignSearchState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignSearchStateCopyWith<CampaignSearchState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignSearchStateCopyWith<$Res> {
  factory $CampaignSearchStateCopyWith(
          CampaignSearchState value, $Res Function(CampaignSearchState) then) =
      _$CampaignSearchStateCopyWithImpl<$Res, CampaignSearchState>;
  @useResult
  $Res call(
      {List<Category> categories,
      List<bool> isCategoryChecked,
      List<CampaignType> types,
      List<bool> isTypeChecked,
      bool isDataLoaded,
      List<DefaultCampaignSummary> campaignResults,
      String searchText,
      List<String> searchTextHistories,
      List<DefaultCampaignSummary> textSearchResults,
      String errorMessage,
      bool isLoading,
      bool isSearchingByKeyword,
      String currency});
}

/// @nodoc
class _$CampaignSearchStateCopyWithImpl<$Res, $Val extends CampaignSearchState>
    implements $CampaignSearchStateCopyWith<$Res> {
  _$CampaignSearchStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
    Object? isCategoryChecked = null,
    Object? types = null,
    Object? isTypeChecked = null,
    Object? isDataLoaded = null,
    Object? campaignResults = null,
    Object? searchText = null,
    Object? searchTextHistories = null,
    Object? textSearchResults = null,
    Object? errorMessage = null,
    Object? isLoading = null,
    Object? isSearchingByKeyword = null,
    Object? currency = null,
  }) {
    return _then(_value.copyWith(
      categories: null == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Category>,
      isCategoryChecked: null == isCategoryChecked
          ? _value.isCategoryChecked
          : isCategoryChecked // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      types: null == types
          ? _value.types
          : types // ignore: cast_nullable_to_non_nullable
              as List<CampaignType>,
      isTypeChecked: null == isTypeChecked
          ? _value.isTypeChecked
          : isTypeChecked // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      isDataLoaded: null == isDataLoaded
          ? _value.isDataLoaded
          : isDataLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      campaignResults: null == campaignResults
          ? _value.campaignResults
          : campaignResults // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      searchTextHistories: null == searchTextHistories
          ? _value.searchTextHistories
          : searchTextHistories // ignore: cast_nullable_to_non_nullable
              as List<String>,
      textSearchResults: null == textSearchResults
          ? _value.textSearchResults
          : textSearchResults // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSearchingByKeyword: null == isSearchingByKeyword
          ? _value.isSearchingByKeyword
          : isSearchingByKeyword // ignore: cast_nullable_to_non_nullable
              as bool,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignSearchStateImplCopyWith<$Res>
    implements $CampaignSearchStateCopyWith<$Res> {
  factory _$$CampaignSearchStateImplCopyWith(_$CampaignSearchStateImpl value,
          $Res Function(_$CampaignSearchStateImpl) then) =
      __$$CampaignSearchStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Category> categories,
      List<bool> isCategoryChecked,
      List<CampaignType> types,
      List<bool> isTypeChecked,
      bool isDataLoaded,
      List<DefaultCampaignSummary> campaignResults,
      String searchText,
      List<String> searchTextHistories,
      List<DefaultCampaignSummary> textSearchResults,
      String errorMessage,
      bool isLoading,
      bool isSearchingByKeyword,
      String currency});
}

/// @nodoc
class __$$CampaignSearchStateImplCopyWithImpl<$Res>
    extends _$CampaignSearchStateCopyWithImpl<$Res, _$CampaignSearchStateImpl>
    implements _$$CampaignSearchStateImplCopyWith<$Res> {
  __$$CampaignSearchStateImplCopyWithImpl(_$CampaignSearchStateImpl _value,
      $Res Function(_$CampaignSearchStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categories = null,
    Object? isCategoryChecked = null,
    Object? types = null,
    Object? isTypeChecked = null,
    Object? isDataLoaded = null,
    Object? campaignResults = null,
    Object? searchText = null,
    Object? searchTextHistories = null,
    Object? textSearchResults = null,
    Object? errorMessage = null,
    Object? isLoading = null,
    Object? isSearchingByKeyword = null,
    Object? currency = null,
  }) {
    return _then(_$CampaignSearchStateImpl(
      categories: null == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Category>,
      isCategoryChecked: null == isCategoryChecked
          ? _value._isCategoryChecked
          : isCategoryChecked // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      types: null == types
          ? _value._types
          : types // ignore: cast_nullable_to_non_nullable
              as List<CampaignType>,
      isTypeChecked: null == isTypeChecked
          ? _value._isTypeChecked
          : isTypeChecked // ignore: cast_nullable_to_non_nullable
              as List<bool>,
      isDataLoaded: null == isDataLoaded
          ? _value.isDataLoaded
          : isDataLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      campaignResults: null == campaignResults
          ? _value._campaignResults
          : campaignResults // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      searchTextHistories: null == searchTextHistories
          ? _value._searchTextHistories
          : searchTextHistories // ignore: cast_nullable_to_non_nullable
              as List<String>,
      textSearchResults: null == textSearchResults
          ? _value._textSearchResults
          : textSearchResults // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSearchingByKeyword: null == isSearchingByKeyword
          ? _value.isSearchingByKeyword
          : isSearchingByKeyword // ignore: cast_nullable_to_non_nullable
              as bool,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignSearchStateImpl implements _CampaignSearchState {
  _$CampaignSearchStateImpl(
      {final List<Category> categories = const [],
      final List<bool> isCategoryChecked = const [],
      final List<CampaignType> types = CampaignType.values,
      final List<bool> isTypeChecked = const [],
      this.isDataLoaded = false,
      final List<DefaultCampaignSummary> campaignResults = const [],
      this.searchText = '',
      final List<String> searchTextHistories = const [],
      final List<DefaultCampaignSummary> textSearchResults = const [],
      this.errorMessage = '',
      this.isLoading = false,
      this.isSearchingByKeyword = false,
      this.currency = ''})
      : _categories = categories,
        _isCategoryChecked = isCategoryChecked,
        _types = types,
        _isTypeChecked = isTypeChecked,
        _campaignResults = campaignResults,
        _searchTextHistories = searchTextHistories,
        _textSearchResults = textSearchResults;

  factory _$CampaignSearchStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignSearchStateImplFromJson(json);

  final List<Category> _categories;
  @override
  @JsonKey()
  List<Category> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  final List<bool> _isCategoryChecked;
  @override
  @JsonKey()
  List<bool> get isCategoryChecked {
    if (_isCategoryChecked is EqualUnmodifiableListView)
      return _isCategoryChecked;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_isCategoryChecked);
  }

  final List<CampaignType> _types;
  @override
  @JsonKey()
  List<CampaignType> get types {
    if (_types is EqualUnmodifiableListView) return _types;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_types);
  }

  final List<bool> _isTypeChecked;
  @override
  @JsonKey()
  List<bool> get isTypeChecked {
    if (_isTypeChecked is EqualUnmodifiableListView) return _isTypeChecked;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_isTypeChecked);
  }

  @override
  @JsonKey()
  final bool isDataLoaded;
  final List<DefaultCampaignSummary> _campaignResults;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get campaignResults {
    if (_campaignResults is EqualUnmodifiableListView) return _campaignResults;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaignResults);
  }

  @override
  @JsonKey()
  final String searchText;
  final List<String> _searchTextHistories;
  @override
  @JsonKey()
  List<String> get searchTextHistories {
    if (_searchTextHistories is EqualUnmodifiableListView)
      return _searchTextHistories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_searchTextHistories);
  }

  final List<DefaultCampaignSummary> _textSearchResults;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get textSearchResults {
    if (_textSearchResults is EqualUnmodifiableListView)
      return _textSearchResults;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_textSearchResults);
  }

  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSearchingByKeyword;
  @override
  @JsonKey()
  final String currency;

  @override
  String toString() {
    return 'CampaignSearchState(categories: $categories, isCategoryChecked: $isCategoryChecked, types: $types, isTypeChecked: $isTypeChecked, isDataLoaded: $isDataLoaded, campaignResults: $campaignResults, searchText: $searchText, searchTextHistories: $searchTextHistories, textSearchResults: $textSearchResults, errorMessage: $errorMessage, isLoading: $isLoading, isSearchingByKeyword: $isSearchingByKeyword, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignSearchStateImpl &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._isCategoryChecked, _isCategoryChecked) &&
            const DeepCollectionEquality().equals(other._types, _types) &&
            const DeepCollectionEquality()
                .equals(other._isTypeChecked, _isTypeChecked) &&
            (identical(other.isDataLoaded, isDataLoaded) ||
                other.isDataLoaded == isDataLoaded) &&
            const DeepCollectionEquality()
                .equals(other._campaignResults, _campaignResults) &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText) &&
            const DeepCollectionEquality()
                .equals(other._searchTextHistories, _searchTextHistories) &&
            const DeepCollectionEquality()
                .equals(other._textSearchResults, _textSearchResults) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSearchingByKeyword, isSearchingByKeyword) ||
                other.isSearchingByKeyword == isSearchingByKeyword) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_isCategoryChecked),
      const DeepCollectionEquality().hash(_types),
      const DeepCollectionEquality().hash(_isTypeChecked),
      isDataLoaded,
      const DeepCollectionEquality().hash(_campaignResults),
      searchText,
      const DeepCollectionEquality().hash(_searchTextHistories),
      const DeepCollectionEquality().hash(_textSearchResults),
      errorMessage,
      isLoading,
      isSearchingByKeyword,
      currency);

  /// Create a copy of CampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignSearchStateImplCopyWith<_$CampaignSearchStateImpl> get copyWith =>
      __$$CampaignSearchStateImplCopyWithImpl<_$CampaignSearchStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignSearchStateImplToJson(
      this,
    );
  }
}

abstract class _CampaignSearchState implements CampaignSearchState {
  factory _CampaignSearchState(
      {final List<Category> categories,
      final List<bool> isCategoryChecked,
      final List<CampaignType> types,
      final List<bool> isTypeChecked,
      final bool isDataLoaded,
      final List<DefaultCampaignSummary> campaignResults,
      final String searchText,
      final List<String> searchTextHistories,
      final List<DefaultCampaignSummary> textSearchResults,
      final String errorMessage,
      final bool isLoading,
      final bool isSearchingByKeyword,
      final String currency}) = _$CampaignSearchStateImpl;

  factory _CampaignSearchState.fromJson(Map<String, dynamic> json) =
      _$CampaignSearchStateImpl.fromJson;

  @override
  List<Category> get categories;
  @override
  List<bool> get isCategoryChecked;
  @override
  List<CampaignType> get types;
  @override
  List<bool> get isTypeChecked;
  @override
  bool get isDataLoaded;
  @override
  List<DefaultCampaignSummary> get campaignResults;
  @override
  String get searchText;
  @override
  List<String> get searchTextHistories;
  @override
  List<DefaultCampaignSummary> get textSearchResults;
  @override
  String get errorMessage;
  @override
  bool get isLoading;
  @override
  bool get isSearchingByKeyword;
  @override
  String get currency;

  /// Create a copy of CampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignSearchStateImplCopyWith<_$CampaignSearchStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
