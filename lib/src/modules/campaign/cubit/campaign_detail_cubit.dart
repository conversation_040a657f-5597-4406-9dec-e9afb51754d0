import 'dart:developer' as dev;

import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_detail_state.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/model/custom_link.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/data/models/creative.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';
import 'package:koc_app/src/modules/campaign/data/repository/creative_repository.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/data/repository/voucher_repository.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

import 'campaign_home_cubit.dart';

class CampaignDetailCubit extends BaseCubit<CampaignDetailState> {
  final CampaignRepository campaignRepository;
  final CreativeRepository creativeRepository;
  final VoucherRepository voucherRepository;

  CampaignDetailCubit(this.campaignRepository, this.creativeRepository, this.voucherRepository)
      : super(CampaignDetailState());

  Future<void> fetchCampaignDetails(int campaignId) async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        emit(state.copyWith(errorMessage: "Site ID not found"));
        return;
      }

      final result = await campaignRepository.getDetailsBy(siteId, campaignId);

      CampaignDetails campaignDetails = CampaignDetails.fromJson(result);
      emit(state.copyWith(campaignDetails: campaignDetails));

      if (campaignDetails.affiliationStatus == AffiliationStatus.APPROVED &&
          campaignDetails.campaignStatus == CampaignStatus.RUNNING) {
        final quickLink = await getQuickLink(campaignId);
        if (quickLink != null && quickLink.isNotEmpty) {
          emit(state.copyWith(quickLink: quickLink));
        }
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<String?> getQuickLink(int campaignId) async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        emit(state.copyWith(errorMessage: "Site ID not found"));
        return null;
      }

      final result = await creativeRepository.getQuickLink(siteId, campaignId);

      return result;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return null;
    }
  }

  Future<AffiliationStatus> applyCampaign(int campaignId) async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        emit(state.copyWith(errorMessage: "Site ID not found"));
        return AffiliationStatus.NEW;
      }

      ApplyCampaignRequest request = ApplyCampaignRequest(
        campaignIds: [campaignId],
        siteId: siteId,
      );

      await campaignRepository.applyCampaign(request);

      await _clearCampaignDetailCache(campaignId);

      await fetchCampaignDetails(campaignId);

      final status = state.campaignDetails?.affiliationStatus;

      final resultStatus = status ?? AffiliationStatus.APPLYING;

      await updateCampaignHomeAfterApply(campaignId);

      return resultStatus;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return AffiliationStatus.NEW;
    }
  }

  Future<void> updateCampaignHomeAfterApply(int campaignId) async {
    try {
      final campaignHomeCubit = Modular.get<CampaignHomeCubit>();

      await campaignHomeCubit.refreshAfterCampaignApplication();

      final status = state.campaignDetails?.affiliationStatus;
      if (status != null) {
        String message = "";
        int defaultTabIndex = 0;

        switch (status) {
          case AffiliationStatus.APPLYING:
            message = "The campaign is waiting for approval.";
            defaultTabIndex = CampaignTabIndex.WAITING.index;
            break;
          case AffiliationStatus.APPROVED:
            message = "The campaign has been approved and ready for promotion now.";
            defaultTabIndex = CampaignTabIndex.AFFILIATED.index;
            break;
          default:
            break;
        }

        if (message.isNotEmpty) {
          campaignHomeCubit.setNotificationMessage(message);

          campaignHomeCubit.changeTab(defaultTabIndex);
        }
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<dynamic> getCreatives(int campaignId) async {
    try {
      emit(state.copyWith(loadingCreatives: true));

      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        emit(state.copyWith(errorMessage: "Site ID not found", loadingCreatives: false));
        return [];
      }

      final result = await creativeRepository.getCreatives(siteId, campaignId);

      if (result is List) {
        var creatives = result.map((creative) => CreativeGroup.fromJson(creative)).toList();
        emit(state.copyWith(creativeGroups: creatives, loadingCreatives: false));
        return creatives;
      }

      emit(state.copyWith(loadingCreatives: false));
      return result;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message, loadingCreatives: false)));
      return [];
    }
  }

  Future<List<Voucher>> findVouchers(FindVouchersRequest request) async {
    try {
      emit(state.copyWith(loadingVouchers: true));

      final result = await voucherRepository.findVouchers(request);

      if (result.isNotEmpty) {
        emit(state.copyWith(vouchers: result, loadingVouchers: false));
        return result;
      }

      emit(state.copyWith(loadingVouchers: false));
      return [];
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message, loadingVouchers: false)));
      return [];
    }
  }

  void updateAcceptedUrls(List<AcceptedUrl> urls) {
    emit(state.copyWith(acceptedUrls: urls));
  }

  /// Clear the cache for the specific campaign detail endpoint
  /// This ensures that after applying to a campaign, the fresh campaign status is fetched
  Future<void> _clearCampaignDetailCache(int campaignId) async {
    try {
      final apiService = Modular.get<ApiService>();
      await apiService.clearCacheForEndpoint('/v3/publisher/campaigns/$campaignId');
    } catch (e) {
      handleError(e, (message) => dev.log("Error clearing campaign detail cache: $message"));
    }
  }

  Future<Voucher?> findVoucherById(int voucherId) async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        emit(state.copyWith(errorMessage: "Site ID not found"));
        return null;
      }

      final result = await voucherRepository.findVoucherById(voucherId, siteId);

      return result;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return null;
    }
  }

  void setStickyBottom(bool value) {
    emit(state.copyWith(showStickyBottom: value));
  }

  void resetCampaignDetailState() {
    emit(CampaignDetailState());
  }

  Future<void> checkCustomLinksEnabled(int campaignId) async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId != null) {
        final isEnabled = await campaignRepository.isCustomLinksEnabled(siteId, campaignId);
        emit(state.copyWith(isCustomLinksEnabled: isEnabled));
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
