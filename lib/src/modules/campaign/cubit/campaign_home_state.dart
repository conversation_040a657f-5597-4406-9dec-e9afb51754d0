import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

part 'campaign_home_state.freezed.dart';
part 'campaign_home_state.g.dart';

@freezed
class CampaignHomeState extends BaseCubitState with _$CampaignHomeState {
  factory CampaignHomeState({
    @Default([]) List<DefaultCampaignSummary> recommendCampaigns,
    @Default([]) List<DefaultCampaignSummary> latestCampaigns,
    @Default([]) List<DefaultCampaignSummary> waitingCampaigns,
    @Default([]) List<DefaultCampaignSummary> affiliatedCampaigns,
    @Default([]) List<DefaultCampaignSummary> pausedCampaigns,
    @Default([]) List<DefaultCampaignSummary> availableCampaigns,
    @Default(CampaignCountSummary()) CampaignCountSummary campaignSummariesCount,
    @Default(0) int tabIndex,
    @Default('') String notificationMessage,
    @Default(PassionateInfo()) PassionateInfo passionateInfo,
    @Default('') String errorMessage,
    @Default(false) isLoading,
    @Default(false) isLoadingMore,
    @Default(false) isPullToRefresh,
    @Default(false) isSiteSwitching,
    @Default(false) hasMore,
    @Default(1) int currentPage,
    @Default('') String currency,
    @Default(0) int selectedSiteId,
  }) = _CampaignHomeState;

  factory CampaignHomeState.fromJson(Map<String, Object?> json) => _$CampaignHomeStateFromJson(json);
}
