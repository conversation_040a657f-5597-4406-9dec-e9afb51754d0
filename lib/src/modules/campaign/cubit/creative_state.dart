import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/creative.dart';

part 'creative_state.freezed.dart';
part 'creative_state.g.dart';

@freezed
class CreativeState extends BaseCubitState with _$CreativeState {
  factory CreativeState({
    @Default(false) bool isDataLoaded,
    @Default([]) List<CreativeGroup> creatives,
    @Default({}) Map<int, int> imageIndex,
    @Default(false) bool isLoading,
    @Default("") String errorMessage
  }) = _CreativeState;

  factory CreativeState.fromJson(Map<String, Object?> json) =>
      _$CreativeStateFromJson(json);
}
