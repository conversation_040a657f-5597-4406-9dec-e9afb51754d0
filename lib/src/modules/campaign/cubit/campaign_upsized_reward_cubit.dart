import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_upsized_reward_state.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';

class CampaignUpsizedRewardCubit extends BaseCubit<CampaignUpsizedRewardState> {
  final CampaignRepository campaignRepository;
  CampaignUpsizedRewardCubit(this.campaignRepository)
      : super(CampaignUpsizedRewardState());

  Future<void> findUpsizedRewards(DateTime startDate, DateTime endDate) async {
    var result =
        await campaignRepository.findUpsizedRewards(startDate, endDate);
    emit(state.copyWith(upsizedRewards: result, currency: 'USD'));
  }
}
