// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_list_view_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CampaignListViewState {
  bool get isListView => throw _privateConstructorUsedError;
  bool get isAsc => throw _privateConstructorUsedError;

  /// Create a copy of CampaignListViewState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignListViewStateCopyWith<CampaignListViewState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignListViewStateCopyWith<$Res> {
  factory $CampaignListViewStateCopyWith(CampaignListViewState value,
          $Res Function(CampaignListViewState) then) =
      _$CampaignListViewStateCopyWithImpl<$Res, CampaignListViewState>;
  @useResult
  $Res call({bool isListView, bool isAsc});
}

/// @nodoc
class _$CampaignListViewStateCopyWithImpl<$Res,
        $Val extends CampaignListViewState>
    implements $CampaignListViewStateCopyWith<$Res> {
  _$CampaignListViewStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignListViewState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isListView = null,
    Object? isAsc = null,
  }) {
    return _then(_value.copyWith(
      isListView: null == isListView
          ? _value.isListView
          : isListView // ignore: cast_nullable_to_non_nullable
              as bool,
      isAsc: null == isAsc
          ? _value.isAsc
          : isAsc // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignListViewStateImplCopyWith<$Res>
    implements $CampaignListViewStateCopyWith<$Res> {
  factory _$$CampaignListViewStateImplCopyWith(
          _$CampaignListViewStateImpl value,
          $Res Function(_$CampaignListViewStateImpl) then) =
      __$$CampaignListViewStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isListView, bool isAsc});
}

/// @nodoc
class __$$CampaignListViewStateImplCopyWithImpl<$Res>
    extends _$CampaignListViewStateCopyWithImpl<$Res,
        _$CampaignListViewStateImpl>
    implements _$$CampaignListViewStateImplCopyWith<$Res> {
  __$$CampaignListViewStateImplCopyWithImpl(_$CampaignListViewStateImpl _value,
      $Res Function(_$CampaignListViewStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignListViewState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isListView = null,
    Object? isAsc = null,
  }) {
    return _then(_$CampaignListViewStateImpl(
      isListView: null == isListView
          ? _value.isListView
          : isListView // ignore: cast_nullable_to_non_nullable
              as bool,
      isAsc: null == isAsc
          ? _value.isAsc
          : isAsc // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CampaignListViewStateImpl implements _CampaignListViewState {
  _$CampaignListViewStateImpl({this.isListView = false, this.isAsc = false});

  @override
  @JsonKey()
  final bool isListView;
  @override
  @JsonKey()
  final bool isAsc;

  @override
  String toString() {
    return 'CampaignListViewState(isListView: $isListView, isAsc: $isAsc)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignListViewStateImpl &&
            (identical(other.isListView, isListView) ||
                other.isListView == isListView) &&
            (identical(other.isAsc, isAsc) || other.isAsc == isAsc));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isListView, isAsc);

  /// Create a copy of CampaignListViewState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignListViewStateImplCopyWith<_$CampaignListViewStateImpl>
      get copyWith => __$$CampaignListViewStateImplCopyWithImpl<
          _$CampaignListViewStateImpl>(this, _$identity);
}

abstract class _CampaignListViewState implements CampaignListViewState {
  factory _CampaignListViewState({final bool isListView, final bool isAsc}) =
      _$CampaignListViewStateImpl;

  @override
  bool get isListView;
  @override
  bool get isAsc;

  /// Create a copy of CampaignListViewState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignListViewStateImplCopyWith<_$CampaignListViewStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
