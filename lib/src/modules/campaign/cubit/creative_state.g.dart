// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'creative_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreativeStateImpl _$$CreativeStateImplFromJson(Map<String, dynamic> json) =>
    _$CreativeStateImpl(
      isDataLoaded: json['isDataLoaded'] as bool? ?? false,
      creatives: (json['creatives'] as List<dynamic>?)
              ?.map((e) => CreativeGroup.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      imageIndex: (json['imageIndex'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(int.parse(k), (e as num).toInt()),
          ) ??
          const {},
      isLoading: json['isLoading'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String? ?? "",
    );

Map<String, dynamic> _$$CreativeStateImplToJson(_$CreativeStateImpl instance) =>
    <String, dynamic>{
      'isDataLoaded': instance.isDataLoaded,
      'creatives': instance.creatives,
      'imageIndex':
          instance.imageIndex.map((k, e) => MapEntry(k.toString(), e)),
      'isLoading': instance.isLoading,
      'errorMessage': instance.errorMessage,
    };
