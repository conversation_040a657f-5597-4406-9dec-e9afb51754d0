// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_detail_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignDetailStateImpl _$$CampaignDetailStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignDetailStateImpl(
      campaignDetails: json['campaignDetails'] == null
          ? null
          : CampaignDetails.fromJson(
              json['campaignDetails'] as Map<String, dynamic>),
      quickLink: json['quickLink'] as String? ?? '',
      showStickyBottom: json['showStickyBottom'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String? ?? '',
      creativeGroups: (json['creativeGroups'] as List<dynamic>?)
              ?.map((e) => CreativeGroup.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      loadingCreatives: json['loadingCreatives'] as bool? ?? false,
      voucherCategories: (json['voucherCategories'] as List<dynamic>?)
              ?.map((e) => VoucherCategory.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      loadingVoucherCategories:
          json['loadingVoucherCategories'] as bool? ?? false,
      vouchers: (json['vouchers'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      loadingVouchers: json['loadingVouchers'] as bool? ?? false,
      acceptedUrls: (json['acceptedUrls'] as List<dynamic>?)
              ?.map((e) => AcceptedUrl.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isCustomLinksEnabled: json['isCustomLinksEnabled'] as bool? ?? false,
    );

Map<String, dynamic> _$$CampaignDetailStateImplToJson(
        _$CampaignDetailStateImpl instance) =>
    <String, dynamic>{
      'campaignDetails': instance.campaignDetails,
      'quickLink': instance.quickLink,
      'showStickyBottom': instance.showStickyBottom,
      'errorMessage': instance.errorMessage,
      'creativeGroups': instance.creativeGroups,
      'loadingCreatives': instance.loadingCreatives,
      'voucherCategories': instance.voucherCategories,
      'loadingVoucherCategories': instance.loadingVoucherCategories,
      'vouchers': instance.vouchers,
      'loadingVouchers': instance.loadingVouchers,
      'acceptedUrls': instance.acceptedUrls,
      'isCustomLinksEnabled': instance.isCustomLinksEnabled,
    };
