// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'creative_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreativeState _$CreativeStateFromJson(Map<String, dynamic> json) {
  return _CreativeState.fromJson(json);
}

/// @nodoc
mixin _$CreativeState {
  bool get isDataLoaded => throw _privateConstructorUsedError;
  List<CreativeGroup> get creatives => throw _privateConstructorUsedError;
  Map<int, int> get imageIndex => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this CreativeState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreativeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreativeStateCopyWith<CreativeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreativeStateCopyWith<$Res> {
  factory $CreativeStateCopyWith(
          CreativeState value, $Res Function(CreativeState) then) =
      _$CreativeStateCopyWithImpl<$Res, CreativeState>;
  @useResult
  $Res call(
      {bool isDataLoaded,
      List<CreativeGroup> creatives,
      Map<int, int> imageIndex,
      bool isLoading,
      String errorMessage});
}

/// @nodoc
class _$CreativeStateCopyWithImpl<$Res, $Val extends CreativeState>
    implements $CreativeStateCopyWith<$Res> {
  _$CreativeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreativeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDataLoaded = null,
    Object? creatives = null,
    Object? imageIndex = null,
    Object? isLoading = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      isDataLoaded: null == isDataLoaded
          ? _value.isDataLoaded
          : isDataLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      creatives: null == creatives
          ? _value.creatives
          : creatives // ignore: cast_nullable_to_non_nullable
              as List<CreativeGroup>,
      imageIndex: null == imageIndex
          ? _value.imageIndex
          : imageIndex // ignore: cast_nullable_to_non_nullable
              as Map<int, int>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreativeStateImplCopyWith<$Res>
    implements $CreativeStateCopyWith<$Res> {
  factory _$$CreativeStateImplCopyWith(
          _$CreativeStateImpl value, $Res Function(_$CreativeStateImpl) then) =
      __$$CreativeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isDataLoaded,
      List<CreativeGroup> creatives,
      Map<int, int> imageIndex,
      bool isLoading,
      String errorMessage});
}

/// @nodoc
class __$$CreativeStateImplCopyWithImpl<$Res>
    extends _$CreativeStateCopyWithImpl<$Res, _$CreativeStateImpl>
    implements _$$CreativeStateImplCopyWith<$Res> {
  __$$CreativeStateImplCopyWithImpl(
      _$CreativeStateImpl _value, $Res Function(_$CreativeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreativeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDataLoaded = null,
    Object? creatives = null,
    Object? imageIndex = null,
    Object? isLoading = null,
    Object? errorMessage = null,
  }) {
    return _then(_$CreativeStateImpl(
      isDataLoaded: null == isDataLoaded
          ? _value.isDataLoaded
          : isDataLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      creatives: null == creatives
          ? _value._creatives
          : creatives // ignore: cast_nullable_to_non_nullable
              as List<CreativeGroup>,
      imageIndex: null == imageIndex
          ? _value._imageIndex
          : imageIndex // ignore: cast_nullable_to_non_nullable
              as Map<int, int>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreativeStateImpl implements _CreativeState {
  _$CreativeStateImpl(
      {this.isDataLoaded = false,
      final List<CreativeGroup> creatives = const [],
      final Map<int, int> imageIndex = const {},
      this.isLoading = false,
      this.errorMessage = ""})
      : _creatives = creatives,
        _imageIndex = imageIndex;

  factory _$CreativeStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreativeStateImplFromJson(json);

  @override
  @JsonKey()
  final bool isDataLoaded;
  final List<CreativeGroup> _creatives;
  @override
  @JsonKey()
  List<CreativeGroup> get creatives {
    if (_creatives is EqualUnmodifiableListView) return _creatives;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_creatives);
  }

  final Map<int, int> _imageIndex;
  @override
  @JsonKey()
  Map<int, int> get imageIndex {
    if (_imageIndex is EqualUnmodifiableMapView) return _imageIndex;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_imageIndex);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'CreativeState(isDataLoaded: $isDataLoaded, creatives: $creatives, imageIndex: $imageIndex, isLoading: $isLoading, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreativeStateImpl &&
            (identical(other.isDataLoaded, isDataLoaded) ||
                other.isDataLoaded == isDataLoaded) &&
            const DeepCollectionEquality()
                .equals(other._creatives, _creatives) &&
            const DeepCollectionEquality()
                .equals(other._imageIndex, _imageIndex) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isDataLoaded,
      const DeepCollectionEquality().hash(_creatives),
      const DeepCollectionEquality().hash(_imageIndex),
      isLoading,
      errorMessage);

  /// Create a copy of CreativeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreativeStateImplCopyWith<_$CreativeStateImpl> get copyWith =>
      __$$CreativeStateImplCopyWithImpl<_$CreativeStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreativeStateImplToJson(
      this,
    );
  }
}

abstract class _CreativeState implements CreativeState {
  factory _CreativeState(
      {final bool isDataLoaded,
      final List<CreativeGroup> creatives,
      final Map<int, int> imageIndex,
      final bool isLoading,
      final String errorMessage}) = _$CreativeStateImpl;

  factory _CreativeState.fromJson(Map<String, dynamic> json) =
      _$CreativeStateImpl.fromJson;

  @override
  bool get isDataLoaded;
  @override
  List<CreativeGroup> get creatives;
  @override
  Map<int, int> get imageIndex;
  @override
  bool get isLoading;
  @override
  String get errorMessage;

  /// Create a copy of CreativeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreativeStateImplCopyWith<_$CreativeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
