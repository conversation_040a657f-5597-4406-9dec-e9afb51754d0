// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_upsized_reward_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignUpsizedRewardStateImpl _$$CampaignUpsizedRewardStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignUpsizedRewardStateImpl(
      currency: json['currency'] as String? ?? '',
      upsizedRewards: (json['upsizedRewards'] as List<dynamic>?)
              ?.map(
                  (e) => UpsizedReward.fromJson((e as Map<String, dynamic>).map(
                        (k, e) => MapEntry(k, e as Object),
                      )))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CampaignUpsizedRewardStateImplToJson(
        _$CampaignUpsizedRewardStateImpl instance) =>
    <String, dynamic>{
      'currency': instance.currency,
      'upsizedRewards': instance.upsizedRewards,
    };
