import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/model/custom_link.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/data/models/creative.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';

part 'campaign_detail_state.freezed.dart';
part 'campaign_detail_state.g.dart';

@freezed
class CampaignDetailState extends BaseCubitState with _$CampaignDetailState {
  factory CampaignDetailState({
    CampaignDetails? campaignDetails,
    @Default('') String quickLink,
    @Default(false) bool showStickyBottom,
    @Default('') String errorMessage,
    @Default([]) List<CreativeGroup> creativeGroups,
    @Default(false) bool loadingCreatives,
    @Default([]) List<VoucherCategory> voucherCategories,
    @Default(false) bool loadingVoucherCategories,
    @Default([]) List<Voucher> vouchers,
    @Default(false) bool loadingVouchers,
    @Default([]) List<AcceptedUrl> acceptedUrls,
    @Default(false) bool isCustomLinksEnabled
  }) = _CampaignDetailState;

  factory CampaignDetailState.fromJson(Map<String, dynamic> json) => _$CampaignDetailStateFromJson(json);
}
