// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';

part 'custom_link.freezed.dart';
part 'custom_link.g.dart';

@freezed
class CustomLinkState extends BaseCubitState with _$CustomLinkState {
  factory CustomLinkState(
      {@Default(0) int id,
      @Default('') String name,
      @Default(0) int campaignId,
      @Default('') String shareLink,
      DateTime? createdOn,
      @Default([]) List<SubId> subIds,
      @Default('') String errorMessage,
      }) = _CustomLinkState;

  factory CustomLinkState.fromJson(Map<String, Object?> json) => _$CustomLinkStateFromJson(json);
}

@freezed
class AcceptedUrl with _$AcceptedUrl {
  factory AcceptedUrl({
    required String displayValue,
    required String validationValue
  }) = _AcceptedUrl;
  factory AcceptedUrl.fromJson(Map<String, Object?> json) => _$AcceptedUrlFromJson(json);
}

@freezed
class CreateCreativesRequest with _$CreateCreativesRequest {
  factory CreateCreativesRequest(
      {required String name,
      required List<String> landingUrls,
      @Default([]) List<SubId> subIds}) = _CreateCreativesRequest;

  factory CreateCreativesRequest.fromJson(Map<String, Object?> json) => _$CreateCreativesRequestFromJson(json);
}

@freezed
class SubId with _$SubId {
  factory SubId({@Default('') String name, @Default('') String value}) = _SubId;

  factory SubId.fromJson(Map<String, Object?> json) => _$SubIdFromJson(json);
}

@freezed
class CustomLinkResponse with _$CustomLinkResponse {
  factory CustomLinkResponse({
    @Default([]) List<CustomLinkState> customLinks,
    @Default(0) int filteredCount,
  }) = _CustomLinkResponse;

  factory CustomLinkResponse.fromJson(Map<String, Object?> json) => _$CustomLinkResponseFromJson(json);
}

@freezed
class FindCustomCreativesRequest with _$FindCustomCreativesRequest {
  factory FindCustomCreativesRequest({
    required int siteId,
    required int campaignId,
    required String fromDate,
    required String toDate,
    @Default(1) int limit,
    @Default(10) int page,
    String? keyword,
  }) = _FindCustomCreativesRequest;

  factory FindCustomCreativesRequest.fromJson(Map<String, Object?> json) => _$FindCustomCreativesRequestFromJson(json);
}

@freezed
class CustomCreativesContentItem with _$CustomCreativesContentItem {
  factory CustomCreativesContentItem({
    required int id,
    required String name,
    required String affiliateLink,
    required String landingUrl,
    required String createdOn,
  }) = _CustomCreativesContentItem;

  factory CustomCreativesContentItem.fromJson(Map<String, Object?> json) => _$CustomCreativesContentItemFromJson(json);
}

@freezed
class CustomCreativesResponse with _$CustomCreativesResponse {
  factory CustomCreativesResponse({
    @Default([]) List<CustomCreativesContentItem> content,
    @Default(0) int totalItems 
  }) = _CustomCreativesResponse;

  factory CustomCreativesResponse.fromJson(Map<String, Object?> json) => _$CustomCreativesResponseFromJson(json);
}

@freezed
class CreateSingleCreativeRequest with _$CreateSingleCreativeRequest {
  factory CreateSingleCreativeRequest({
    required String name,
    required String landingUrl,
    @Default([]) List<SubId> subIds
  }) = _CreateSingleCreativeRequest;

  factory CreateSingleCreativeRequest.fromJson(Map<String, Object?> json) => 
      _$CreateSingleCreativeRequestFromJson(json);
}
