// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'custom_link.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CustomLinkState _$CustomLinkStateFromJson(Map<String, dynamic> json) {
  return _CustomLinkState.fromJson(json);
}

/// @nodoc
mixin _$CustomLinkState {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get campaignId => throw _privateConstructorUsedError;
  String get shareLink => throw _privateConstructorUsedError;
  DateTime? get createdOn => throw _privateConstructorUsedError;
  List<SubId> get subIds => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this CustomLinkState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomLinkState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomLinkStateCopyWith<CustomLinkState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomLinkStateCopyWith<$Res> {
  factory $CustomLinkStateCopyWith(
          CustomLinkState value, $Res Function(CustomLinkState) then) =
      _$CustomLinkStateCopyWithImpl<$Res, CustomLinkState>;
  @useResult
  $Res call(
      {int id,
      String name,
      int campaignId,
      String shareLink,
      DateTime? createdOn,
      List<SubId> subIds,
      String errorMessage});
}

/// @nodoc
class _$CustomLinkStateCopyWithImpl<$Res, $Val extends CustomLinkState>
    implements $CustomLinkStateCopyWith<$Res> {
  _$CustomLinkStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomLinkState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? campaignId = null,
    Object? shareLink = null,
    Object? createdOn = freezed,
    Object? subIds = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      shareLink: null == shareLink
          ? _value.shareLink
          : shareLink // ignore: cast_nullable_to_non_nullable
              as String,
      createdOn: freezed == createdOn
          ? _value.createdOn
          : createdOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      subIds: null == subIds
          ? _value.subIds
          : subIds // ignore: cast_nullable_to_non_nullable
              as List<SubId>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomLinkStateImplCopyWith<$Res>
    implements $CustomLinkStateCopyWith<$Res> {
  factory _$$CustomLinkStateImplCopyWith(_$CustomLinkStateImpl value,
          $Res Function(_$CustomLinkStateImpl) then) =
      __$$CustomLinkStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      int campaignId,
      String shareLink,
      DateTime? createdOn,
      List<SubId> subIds,
      String errorMessage});
}

/// @nodoc
class __$$CustomLinkStateImplCopyWithImpl<$Res>
    extends _$CustomLinkStateCopyWithImpl<$Res, _$CustomLinkStateImpl>
    implements _$$CustomLinkStateImplCopyWith<$Res> {
  __$$CustomLinkStateImplCopyWithImpl(
      _$CustomLinkStateImpl _value, $Res Function(_$CustomLinkStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomLinkState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? campaignId = null,
    Object? shareLink = null,
    Object? createdOn = freezed,
    Object? subIds = null,
    Object? errorMessage = null,
  }) {
    return _then(_$CustomLinkStateImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      shareLink: null == shareLink
          ? _value.shareLink
          : shareLink // ignore: cast_nullable_to_non_nullable
              as String,
      createdOn: freezed == createdOn
          ? _value.createdOn
          : createdOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      subIds: null == subIds
          ? _value._subIds
          : subIds // ignore: cast_nullable_to_non_nullable
              as List<SubId>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomLinkStateImpl implements _CustomLinkState {
  _$CustomLinkStateImpl(
      {this.id = 0,
      this.name = '',
      this.campaignId = 0,
      this.shareLink = '',
      this.createdOn,
      final List<SubId> subIds = const [],
      this.errorMessage = ''})
      : _subIds = subIds;

  factory _$CustomLinkStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomLinkStateImplFromJson(json);

  @override
  @JsonKey()
  final int id;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final int campaignId;
  @override
  @JsonKey()
  final String shareLink;
  @override
  final DateTime? createdOn;
  final List<SubId> _subIds;
  @override
  @JsonKey()
  List<SubId> get subIds {
    if (_subIds is EqualUnmodifiableListView) return _subIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subIds);
  }

  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'CustomLinkState(id: $id, name: $name, campaignId: $campaignId, shareLink: $shareLink, createdOn: $createdOn, subIds: $subIds, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomLinkStateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.campaignId, campaignId) ||
                other.campaignId == campaignId) &&
            (identical(other.shareLink, shareLink) ||
                other.shareLink == shareLink) &&
            (identical(other.createdOn, createdOn) ||
                other.createdOn == createdOn) &&
            const DeepCollectionEquality().equals(other._subIds, _subIds) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, campaignId, shareLink,
      createdOn, const DeepCollectionEquality().hash(_subIds), errorMessage);

  /// Create a copy of CustomLinkState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomLinkStateImplCopyWith<_$CustomLinkStateImpl> get copyWith =>
      __$$CustomLinkStateImplCopyWithImpl<_$CustomLinkStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomLinkStateImplToJson(
      this,
    );
  }
}

abstract class _CustomLinkState implements CustomLinkState {
  factory _CustomLinkState(
      {final int id,
      final String name,
      final int campaignId,
      final String shareLink,
      final DateTime? createdOn,
      final List<SubId> subIds,
      final String errorMessage}) = _$CustomLinkStateImpl;

  factory _CustomLinkState.fromJson(Map<String, dynamic> json) =
      _$CustomLinkStateImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  int get campaignId;
  @override
  String get shareLink;
  @override
  DateTime? get createdOn;
  @override
  List<SubId> get subIds;
  @override
  String get errorMessage;

  /// Create a copy of CustomLinkState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomLinkStateImplCopyWith<_$CustomLinkStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AcceptedUrl _$AcceptedUrlFromJson(Map<String, dynamic> json) {
  return _AcceptedUrl.fromJson(json);
}

/// @nodoc
mixin _$AcceptedUrl {
  String get displayValue => throw _privateConstructorUsedError;
  String get validationValue => throw _privateConstructorUsedError;

  /// Serializes this AcceptedUrl to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AcceptedUrl
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AcceptedUrlCopyWith<AcceptedUrl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AcceptedUrlCopyWith<$Res> {
  factory $AcceptedUrlCopyWith(
          AcceptedUrl value, $Res Function(AcceptedUrl) then) =
      _$AcceptedUrlCopyWithImpl<$Res, AcceptedUrl>;
  @useResult
  $Res call({String displayValue, String validationValue});
}

/// @nodoc
class _$AcceptedUrlCopyWithImpl<$Res, $Val extends AcceptedUrl>
    implements $AcceptedUrlCopyWith<$Res> {
  _$AcceptedUrlCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AcceptedUrl
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayValue = null,
    Object? validationValue = null,
  }) {
    return _then(_value.copyWith(
      displayValue: null == displayValue
          ? _value.displayValue
          : displayValue // ignore: cast_nullable_to_non_nullable
              as String,
      validationValue: null == validationValue
          ? _value.validationValue
          : validationValue // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AcceptedUrlImplCopyWith<$Res>
    implements $AcceptedUrlCopyWith<$Res> {
  factory _$$AcceptedUrlImplCopyWith(
          _$AcceptedUrlImpl value, $Res Function(_$AcceptedUrlImpl) then) =
      __$$AcceptedUrlImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String displayValue, String validationValue});
}

/// @nodoc
class __$$AcceptedUrlImplCopyWithImpl<$Res>
    extends _$AcceptedUrlCopyWithImpl<$Res, _$AcceptedUrlImpl>
    implements _$$AcceptedUrlImplCopyWith<$Res> {
  __$$AcceptedUrlImplCopyWithImpl(
      _$AcceptedUrlImpl _value, $Res Function(_$AcceptedUrlImpl) _then)
      : super(_value, _then);

  /// Create a copy of AcceptedUrl
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayValue = null,
    Object? validationValue = null,
  }) {
    return _then(_$AcceptedUrlImpl(
      displayValue: null == displayValue
          ? _value.displayValue
          : displayValue // ignore: cast_nullable_to_non_nullable
              as String,
      validationValue: null == validationValue
          ? _value.validationValue
          : validationValue // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AcceptedUrlImpl implements _AcceptedUrl {
  _$AcceptedUrlImpl(
      {required this.displayValue, required this.validationValue});

  factory _$AcceptedUrlImpl.fromJson(Map<String, dynamic> json) =>
      _$$AcceptedUrlImplFromJson(json);

  @override
  final String displayValue;
  @override
  final String validationValue;

  @override
  String toString() {
    return 'AcceptedUrl(displayValue: $displayValue, validationValue: $validationValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AcceptedUrlImpl &&
            (identical(other.displayValue, displayValue) ||
                other.displayValue == displayValue) &&
            (identical(other.validationValue, validationValue) ||
                other.validationValue == validationValue));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, displayValue, validationValue);

  /// Create a copy of AcceptedUrl
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AcceptedUrlImplCopyWith<_$AcceptedUrlImpl> get copyWith =>
      __$$AcceptedUrlImplCopyWithImpl<_$AcceptedUrlImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AcceptedUrlImplToJson(
      this,
    );
  }
}

abstract class _AcceptedUrl implements AcceptedUrl {
  factory _AcceptedUrl(
      {required final String displayValue,
      required final String validationValue}) = _$AcceptedUrlImpl;

  factory _AcceptedUrl.fromJson(Map<String, dynamic> json) =
      _$AcceptedUrlImpl.fromJson;

  @override
  String get displayValue;
  @override
  String get validationValue;

  /// Create a copy of AcceptedUrl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AcceptedUrlImplCopyWith<_$AcceptedUrlImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateCreativesRequest _$CreateCreativesRequestFromJson(
    Map<String, dynamic> json) {
  return _CreateCreativesRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateCreativesRequest {
  String get name => throw _privateConstructorUsedError;
  List<String> get landingUrls => throw _privateConstructorUsedError;
  List<SubId> get subIds => throw _privateConstructorUsedError;

  /// Serializes this CreateCreativesRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateCreativesRequestCopyWith<CreateCreativesRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateCreativesRequestCopyWith<$Res> {
  factory $CreateCreativesRequestCopyWith(CreateCreativesRequest value,
          $Res Function(CreateCreativesRequest) then) =
      _$CreateCreativesRequestCopyWithImpl<$Res, CreateCreativesRequest>;
  @useResult
  $Res call({String name, List<String> landingUrls, List<SubId> subIds});
}

/// @nodoc
class _$CreateCreativesRequestCopyWithImpl<$Res,
        $Val extends CreateCreativesRequest>
    implements $CreateCreativesRequestCopyWith<$Res> {
  _$CreateCreativesRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? landingUrls = null,
    Object? subIds = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      landingUrls: null == landingUrls
          ? _value.landingUrls
          : landingUrls // ignore: cast_nullable_to_non_nullable
              as List<String>,
      subIds: null == subIds
          ? _value.subIds
          : subIds // ignore: cast_nullable_to_non_nullable
              as List<SubId>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateCreativesRequestImplCopyWith<$Res>
    implements $CreateCreativesRequestCopyWith<$Res> {
  factory _$$CreateCreativesRequestImplCopyWith(
          _$CreateCreativesRequestImpl value,
          $Res Function(_$CreateCreativesRequestImpl) then) =
      __$$CreateCreativesRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, List<String> landingUrls, List<SubId> subIds});
}

/// @nodoc
class __$$CreateCreativesRequestImplCopyWithImpl<$Res>
    extends _$CreateCreativesRequestCopyWithImpl<$Res,
        _$CreateCreativesRequestImpl>
    implements _$$CreateCreativesRequestImplCopyWith<$Res> {
  __$$CreateCreativesRequestImplCopyWithImpl(
      _$CreateCreativesRequestImpl _value,
      $Res Function(_$CreateCreativesRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? landingUrls = null,
    Object? subIds = null,
  }) {
    return _then(_$CreateCreativesRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      landingUrls: null == landingUrls
          ? _value._landingUrls
          : landingUrls // ignore: cast_nullable_to_non_nullable
              as List<String>,
      subIds: null == subIds
          ? _value._subIds
          : subIds // ignore: cast_nullable_to_non_nullable
              as List<SubId>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateCreativesRequestImpl implements _CreateCreativesRequest {
  _$CreateCreativesRequestImpl(
      {required this.name,
      required final List<String> landingUrls,
      final List<SubId> subIds = const []})
      : _landingUrls = landingUrls,
        _subIds = subIds;

  factory _$CreateCreativesRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateCreativesRequestImplFromJson(json);

  @override
  final String name;
  final List<String> _landingUrls;
  @override
  List<String> get landingUrls {
    if (_landingUrls is EqualUnmodifiableListView) return _landingUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_landingUrls);
  }

  final List<SubId> _subIds;
  @override
  @JsonKey()
  List<SubId> get subIds {
    if (_subIds is EqualUnmodifiableListView) return _subIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subIds);
  }

  @override
  String toString() {
    return 'CreateCreativesRequest(name: $name, landingUrls: $landingUrls, subIds: $subIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateCreativesRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality()
                .equals(other._landingUrls, _landingUrls) &&
            const DeepCollectionEquality().equals(other._subIds, _subIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      const DeepCollectionEquality().hash(_landingUrls),
      const DeepCollectionEquality().hash(_subIds));

  /// Create a copy of CreateCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateCreativesRequestImplCopyWith<_$CreateCreativesRequestImpl>
      get copyWith => __$$CreateCreativesRequestImplCopyWithImpl<
          _$CreateCreativesRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateCreativesRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateCreativesRequest implements CreateCreativesRequest {
  factory _CreateCreativesRequest(
      {required final String name,
      required final List<String> landingUrls,
      final List<SubId> subIds}) = _$CreateCreativesRequestImpl;

  factory _CreateCreativesRequest.fromJson(Map<String, dynamic> json) =
      _$CreateCreativesRequestImpl.fromJson;

  @override
  String get name;
  @override
  List<String> get landingUrls;
  @override
  List<SubId> get subIds;

  /// Create a copy of CreateCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateCreativesRequestImplCopyWith<_$CreateCreativesRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SubId _$SubIdFromJson(Map<String, dynamic> json) {
  return _SubId.fromJson(json);
}

/// @nodoc
mixin _$SubId {
  String get name => throw _privateConstructorUsedError;
  String get value => throw _privateConstructorUsedError;

  /// Serializes this SubId to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubId
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubIdCopyWith<SubId> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubIdCopyWith<$Res> {
  factory $SubIdCopyWith(SubId value, $Res Function(SubId) then) =
      _$SubIdCopyWithImpl<$Res, SubId>;
  @useResult
  $Res call({String name, String value});
}

/// @nodoc
class _$SubIdCopyWithImpl<$Res, $Val extends SubId>
    implements $SubIdCopyWith<$Res> {
  _$SubIdCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubId
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubIdImplCopyWith<$Res> implements $SubIdCopyWith<$Res> {
  factory _$$SubIdImplCopyWith(
          _$SubIdImpl value, $Res Function(_$SubIdImpl) then) =
      __$$SubIdImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String value});
}

/// @nodoc
class __$$SubIdImplCopyWithImpl<$Res>
    extends _$SubIdCopyWithImpl<$Res, _$SubIdImpl>
    implements _$$SubIdImplCopyWith<$Res> {
  __$$SubIdImplCopyWithImpl(
      _$SubIdImpl _value, $Res Function(_$SubIdImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubId
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? value = null,
  }) {
    return _then(_$SubIdImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubIdImpl implements _SubId {
  _$SubIdImpl({this.name = '', this.value = ''});

  factory _$SubIdImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubIdImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String value;

  @override
  String toString() {
    return 'SubId(name: $name, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubIdImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, value);

  /// Create a copy of SubId
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubIdImplCopyWith<_$SubIdImpl> get copyWith =>
      __$$SubIdImplCopyWithImpl<_$SubIdImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubIdImplToJson(
      this,
    );
  }
}

abstract class _SubId implements SubId {
  factory _SubId({final String name, final String value}) = _$SubIdImpl;

  factory _SubId.fromJson(Map<String, dynamic> json) = _$SubIdImpl.fromJson;

  @override
  String get name;
  @override
  String get value;

  /// Create a copy of SubId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubIdImplCopyWith<_$SubIdImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomLinkResponse _$CustomLinkResponseFromJson(Map<String, dynamic> json) {
  return _CustomLinkResponse.fromJson(json);
}

/// @nodoc
mixin _$CustomLinkResponse {
  List<CustomLinkState> get customLinks => throw _privateConstructorUsedError;
  int get filteredCount => throw _privateConstructorUsedError;

  /// Serializes this CustomLinkResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomLinkResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomLinkResponseCopyWith<CustomLinkResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomLinkResponseCopyWith<$Res> {
  factory $CustomLinkResponseCopyWith(
          CustomLinkResponse value, $Res Function(CustomLinkResponse) then) =
      _$CustomLinkResponseCopyWithImpl<$Res, CustomLinkResponse>;
  @useResult
  $Res call({List<CustomLinkState> customLinks, int filteredCount});
}

/// @nodoc
class _$CustomLinkResponseCopyWithImpl<$Res, $Val extends CustomLinkResponse>
    implements $CustomLinkResponseCopyWith<$Res> {
  _$CustomLinkResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomLinkResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customLinks = null,
    Object? filteredCount = null,
  }) {
    return _then(_value.copyWith(
      customLinks: null == customLinks
          ? _value.customLinks
          : customLinks // ignore: cast_nullable_to_non_nullable
              as List<CustomLinkState>,
      filteredCount: null == filteredCount
          ? _value.filteredCount
          : filteredCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomLinkResponseImplCopyWith<$Res>
    implements $CustomLinkResponseCopyWith<$Res> {
  factory _$$CustomLinkResponseImplCopyWith(_$CustomLinkResponseImpl value,
          $Res Function(_$CustomLinkResponseImpl) then) =
      __$$CustomLinkResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CustomLinkState> customLinks, int filteredCount});
}

/// @nodoc
class __$$CustomLinkResponseImplCopyWithImpl<$Res>
    extends _$CustomLinkResponseCopyWithImpl<$Res, _$CustomLinkResponseImpl>
    implements _$$CustomLinkResponseImplCopyWith<$Res> {
  __$$CustomLinkResponseImplCopyWithImpl(_$CustomLinkResponseImpl _value,
      $Res Function(_$CustomLinkResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomLinkResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customLinks = null,
    Object? filteredCount = null,
  }) {
    return _then(_$CustomLinkResponseImpl(
      customLinks: null == customLinks
          ? _value._customLinks
          : customLinks // ignore: cast_nullable_to_non_nullable
              as List<CustomLinkState>,
      filteredCount: null == filteredCount
          ? _value.filteredCount
          : filteredCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomLinkResponseImpl implements _CustomLinkResponse {
  _$CustomLinkResponseImpl(
      {final List<CustomLinkState> customLinks = const [],
      this.filteredCount = 0})
      : _customLinks = customLinks;

  factory _$CustomLinkResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomLinkResponseImplFromJson(json);

  final List<CustomLinkState> _customLinks;
  @override
  @JsonKey()
  List<CustomLinkState> get customLinks {
    if (_customLinks is EqualUnmodifiableListView) return _customLinks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_customLinks);
  }

  @override
  @JsonKey()
  final int filteredCount;

  @override
  String toString() {
    return 'CustomLinkResponse(customLinks: $customLinks, filteredCount: $filteredCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomLinkResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._customLinks, _customLinks) &&
            (identical(other.filteredCount, filteredCount) ||
                other.filteredCount == filteredCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_customLinks), filteredCount);

  /// Create a copy of CustomLinkResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomLinkResponseImplCopyWith<_$CustomLinkResponseImpl> get copyWith =>
      __$$CustomLinkResponseImplCopyWithImpl<_$CustomLinkResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomLinkResponseImplToJson(
      this,
    );
  }
}

abstract class _CustomLinkResponse implements CustomLinkResponse {
  factory _CustomLinkResponse(
      {final List<CustomLinkState> customLinks,
      final int filteredCount}) = _$CustomLinkResponseImpl;

  factory _CustomLinkResponse.fromJson(Map<String, dynamic> json) =
      _$CustomLinkResponseImpl.fromJson;

  @override
  List<CustomLinkState> get customLinks;
  @override
  int get filteredCount;

  /// Create a copy of CustomLinkResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomLinkResponseImplCopyWith<_$CustomLinkResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FindCustomCreativesRequest _$FindCustomCreativesRequestFromJson(
    Map<String, dynamic> json) {
  return _FindCustomCreativesRequest.fromJson(json);
}

/// @nodoc
mixin _$FindCustomCreativesRequest {
  int get siteId => throw _privateConstructorUsedError;
  int get campaignId => throw _privateConstructorUsedError;
  String get fromDate => throw _privateConstructorUsedError;
  String get toDate => throw _privateConstructorUsedError;
  int get limit => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  String? get keyword => throw _privateConstructorUsedError;

  /// Serializes this FindCustomCreativesRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FindCustomCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FindCustomCreativesRequestCopyWith<FindCustomCreativesRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FindCustomCreativesRequestCopyWith<$Res> {
  factory $FindCustomCreativesRequestCopyWith(FindCustomCreativesRequest value,
          $Res Function(FindCustomCreativesRequest) then) =
      _$FindCustomCreativesRequestCopyWithImpl<$Res,
          FindCustomCreativesRequest>;
  @useResult
  $Res call(
      {int siteId,
      int campaignId,
      String fromDate,
      String toDate,
      int limit,
      int page,
      String? keyword});
}

/// @nodoc
class _$FindCustomCreativesRequestCopyWithImpl<$Res,
        $Val extends FindCustomCreativesRequest>
    implements $FindCustomCreativesRequestCopyWith<$Res> {
  _$FindCustomCreativesRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FindCustomCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? siteId = null,
    Object? campaignId = null,
    Object? fromDate = null,
    Object? toDate = null,
    Object? limit = null,
    Object? page = null,
    Object? keyword = freezed,
  }) {
    return _then(_value.copyWith(
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      fromDate: null == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as String,
      toDate: null == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as String,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      keyword: freezed == keyword
          ? _value.keyword
          : keyword // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FindCustomCreativesRequestImplCopyWith<$Res>
    implements $FindCustomCreativesRequestCopyWith<$Res> {
  factory _$$FindCustomCreativesRequestImplCopyWith(
          _$FindCustomCreativesRequestImpl value,
          $Res Function(_$FindCustomCreativesRequestImpl) then) =
      __$$FindCustomCreativesRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int siteId,
      int campaignId,
      String fromDate,
      String toDate,
      int limit,
      int page,
      String? keyword});
}

/// @nodoc
class __$$FindCustomCreativesRequestImplCopyWithImpl<$Res>
    extends _$FindCustomCreativesRequestCopyWithImpl<$Res,
        _$FindCustomCreativesRequestImpl>
    implements _$$FindCustomCreativesRequestImplCopyWith<$Res> {
  __$$FindCustomCreativesRequestImplCopyWithImpl(
      _$FindCustomCreativesRequestImpl _value,
      $Res Function(_$FindCustomCreativesRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of FindCustomCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? siteId = null,
    Object? campaignId = null,
    Object? fromDate = null,
    Object? toDate = null,
    Object? limit = null,
    Object? page = null,
    Object? keyword = freezed,
  }) {
    return _then(_$FindCustomCreativesRequestImpl(
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      fromDate: null == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as String,
      toDate: null == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as String,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      keyword: freezed == keyword
          ? _value.keyword
          : keyword // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FindCustomCreativesRequestImpl implements _FindCustomCreativesRequest {
  _$FindCustomCreativesRequestImpl(
      {required this.siteId,
      required this.campaignId,
      required this.fromDate,
      required this.toDate,
      this.limit = 1,
      this.page = 10,
      this.keyword});

  factory _$FindCustomCreativesRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$FindCustomCreativesRequestImplFromJson(json);

  @override
  final int siteId;
  @override
  final int campaignId;
  @override
  final String fromDate;
  @override
  final String toDate;
  @override
  @JsonKey()
  final int limit;
  @override
  @JsonKey()
  final int page;
  @override
  final String? keyword;

  @override
  String toString() {
    return 'FindCustomCreativesRequest(siteId: $siteId, campaignId: $campaignId, fromDate: $fromDate, toDate: $toDate, limit: $limit, page: $page, keyword: $keyword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FindCustomCreativesRequestImpl &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.campaignId, campaignId) ||
                other.campaignId == campaignId) &&
            (identical(other.fromDate, fromDate) ||
                other.fromDate == fromDate) &&
            (identical(other.toDate, toDate) || other.toDate == toDate) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.keyword, keyword) || other.keyword == keyword));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, siteId, campaignId, fromDate, toDate, limit, page, keyword);

  /// Create a copy of FindCustomCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FindCustomCreativesRequestImplCopyWith<_$FindCustomCreativesRequestImpl>
      get copyWith => __$$FindCustomCreativesRequestImplCopyWithImpl<
          _$FindCustomCreativesRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FindCustomCreativesRequestImplToJson(
      this,
    );
  }
}

abstract class _FindCustomCreativesRequest
    implements FindCustomCreativesRequest {
  factory _FindCustomCreativesRequest(
      {required final int siteId,
      required final int campaignId,
      required final String fromDate,
      required final String toDate,
      final int limit,
      final int page,
      final String? keyword}) = _$FindCustomCreativesRequestImpl;

  factory _FindCustomCreativesRequest.fromJson(Map<String, dynamic> json) =
      _$FindCustomCreativesRequestImpl.fromJson;

  @override
  int get siteId;
  @override
  int get campaignId;
  @override
  String get fromDate;
  @override
  String get toDate;
  @override
  int get limit;
  @override
  int get page;
  @override
  String? get keyword;

  /// Create a copy of FindCustomCreativesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FindCustomCreativesRequestImplCopyWith<_$FindCustomCreativesRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CustomCreativesContentItem _$CustomCreativesContentItemFromJson(
    Map<String, dynamic> json) {
  return _CustomCreativesContentItem.fromJson(json);
}

/// @nodoc
mixin _$CustomCreativesContentItem {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get affiliateLink => throw _privateConstructorUsedError;
  String get landingUrl => throw _privateConstructorUsedError;
  String get createdOn => throw _privateConstructorUsedError;

  /// Serializes this CustomCreativesContentItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomCreativesContentItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomCreativesContentItemCopyWith<CustomCreativesContentItem>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomCreativesContentItemCopyWith<$Res> {
  factory $CustomCreativesContentItemCopyWith(CustomCreativesContentItem value,
          $Res Function(CustomCreativesContentItem) then) =
      _$CustomCreativesContentItemCopyWithImpl<$Res,
          CustomCreativesContentItem>;
  @useResult
  $Res call(
      {int id,
      String name,
      String affiliateLink,
      String landingUrl,
      String createdOn});
}

/// @nodoc
class _$CustomCreativesContentItemCopyWithImpl<$Res,
        $Val extends CustomCreativesContentItem>
    implements $CustomCreativesContentItemCopyWith<$Res> {
  _$CustomCreativesContentItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomCreativesContentItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? affiliateLink = null,
    Object? landingUrl = null,
    Object? createdOn = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      affiliateLink: null == affiliateLink
          ? _value.affiliateLink
          : affiliateLink // ignore: cast_nullable_to_non_nullable
              as String,
      landingUrl: null == landingUrl
          ? _value.landingUrl
          : landingUrl // ignore: cast_nullable_to_non_nullable
              as String,
      createdOn: null == createdOn
          ? _value.createdOn
          : createdOn // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomCreativesContentItemImplCopyWith<$Res>
    implements $CustomCreativesContentItemCopyWith<$Res> {
  factory _$$CustomCreativesContentItemImplCopyWith(
          _$CustomCreativesContentItemImpl value,
          $Res Function(_$CustomCreativesContentItemImpl) then) =
      __$$CustomCreativesContentItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      String affiliateLink,
      String landingUrl,
      String createdOn});
}

/// @nodoc
class __$$CustomCreativesContentItemImplCopyWithImpl<$Res>
    extends _$CustomCreativesContentItemCopyWithImpl<$Res,
        _$CustomCreativesContentItemImpl>
    implements _$$CustomCreativesContentItemImplCopyWith<$Res> {
  __$$CustomCreativesContentItemImplCopyWithImpl(
      _$CustomCreativesContentItemImpl _value,
      $Res Function(_$CustomCreativesContentItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomCreativesContentItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? affiliateLink = null,
    Object? landingUrl = null,
    Object? createdOn = null,
  }) {
    return _then(_$CustomCreativesContentItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      affiliateLink: null == affiliateLink
          ? _value.affiliateLink
          : affiliateLink // ignore: cast_nullable_to_non_nullable
              as String,
      landingUrl: null == landingUrl
          ? _value.landingUrl
          : landingUrl // ignore: cast_nullable_to_non_nullable
              as String,
      createdOn: null == createdOn
          ? _value.createdOn
          : createdOn // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomCreativesContentItemImpl implements _CustomCreativesContentItem {
  _$CustomCreativesContentItemImpl(
      {required this.id,
      required this.name,
      required this.affiliateLink,
      required this.landingUrl,
      required this.createdOn});

  factory _$CustomCreativesContentItemImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CustomCreativesContentItemImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  final String affiliateLink;
  @override
  final String landingUrl;
  @override
  final String createdOn;

  @override
  String toString() {
    return 'CustomCreativesContentItem(id: $id, name: $name, affiliateLink: $affiliateLink, landingUrl: $landingUrl, createdOn: $createdOn)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomCreativesContentItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.affiliateLink, affiliateLink) ||
                other.affiliateLink == affiliateLink) &&
            (identical(other.landingUrl, landingUrl) ||
                other.landingUrl == landingUrl) &&
            (identical(other.createdOn, createdOn) ||
                other.createdOn == createdOn));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, affiliateLink, landingUrl, createdOn);

  /// Create a copy of CustomCreativesContentItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomCreativesContentItemImplCopyWith<_$CustomCreativesContentItemImpl>
      get copyWith => __$$CustomCreativesContentItemImplCopyWithImpl<
          _$CustomCreativesContentItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomCreativesContentItemImplToJson(
      this,
    );
  }
}

abstract class _CustomCreativesContentItem
    implements CustomCreativesContentItem {
  factory _CustomCreativesContentItem(
      {required final int id,
      required final String name,
      required final String affiliateLink,
      required final String landingUrl,
      required final String createdOn}) = _$CustomCreativesContentItemImpl;

  factory _CustomCreativesContentItem.fromJson(Map<String, dynamic> json) =
      _$CustomCreativesContentItemImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  String get affiliateLink;
  @override
  String get landingUrl;
  @override
  String get createdOn;

  /// Create a copy of CustomCreativesContentItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomCreativesContentItemImplCopyWith<_$CustomCreativesContentItemImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CustomCreativesResponse _$CustomCreativesResponseFromJson(
    Map<String, dynamic> json) {
  return _CustomCreativesResponse.fromJson(json);
}

/// @nodoc
mixin _$CustomCreativesResponse {
  List<CustomCreativesContentItem> get content =>
      throw _privateConstructorUsedError;
  int get totalItems => throw _privateConstructorUsedError;

  /// Serializes this CustomCreativesResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomCreativesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomCreativesResponseCopyWith<CustomCreativesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomCreativesResponseCopyWith<$Res> {
  factory $CustomCreativesResponseCopyWith(CustomCreativesResponse value,
          $Res Function(CustomCreativesResponse) then) =
      _$CustomCreativesResponseCopyWithImpl<$Res, CustomCreativesResponse>;
  @useResult
  $Res call({List<CustomCreativesContentItem> content, int totalItems});
}

/// @nodoc
class _$CustomCreativesResponseCopyWithImpl<$Res,
        $Val extends CustomCreativesResponse>
    implements $CustomCreativesResponseCopyWith<$Res> {
  _$CustomCreativesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomCreativesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
    Object? totalItems = null,
  }) {
    return _then(_value.copyWith(
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as List<CustomCreativesContentItem>,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomCreativesResponseImplCopyWith<$Res>
    implements $CustomCreativesResponseCopyWith<$Res> {
  factory _$$CustomCreativesResponseImplCopyWith(
          _$CustomCreativesResponseImpl value,
          $Res Function(_$CustomCreativesResponseImpl) then) =
      __$$CustomCreativesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CustomCreativesContentItem> content, int totalItems});
}

/// @nodoc
class __$$CustomCreativesResponseImplCopyWithImpl<$Res>
    extends _$CustomCreativesResponseCopyWithImpl<$Res,
        _$CustomCreativesResponseImpl>
    implements _$$CustomCreativesResponseImplCopyWith<$Res> {
  __$$CustomCreativesResponseImplCopyWithImpl(
      _$CustomCreativesResponseImpl _value,
      $Res Function(_$CustomCreativesResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomCreativesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
    Object? totalItems = null,
  }) {
    return _then(_$CustomCreativesResponseImpl(
      content: null == content
          ? _value._content
          : content // ignore: cast_nullable_to_non_nullable
              as List<CustomCreativesContentItem>,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomCreativesResponseImpl implements _CustomCreativesResponse {
  _$CustomCreativesResponseImpl(
      {final List<CustomCreativesContentItem> content = const [],
      this.totalItems = 0})
      : _content = content;

  factory _$CustomCreativesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomCreativesResponseImplFromJson(json);

  final List<CustomCreativesContentItem> _content;
  @override
  @JsonKey()
  List<CustomCreativesContentItem> get content {
    if (_content is EqualUnmodifiableListView) return _content;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_content);
  }

  @override
  @JsonKey()
  final int totalItems;

  @override
  String toString() {
    return 'CustomCreativesResponse(content: $content, totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomCreativesResponseImpl &&
            const DeepCollectionEquality().equals(other._content, _content) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_content), totalItems);

  /// Create a copy of CustomCreativesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomCreativesResponseImplCopyWith<_$CustomCreativesResponseImpl>
      get copyWith => __$$CustomCreativesResponseImplCopyWithImpl<
          _$CustomCreativesResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomCreativesResponseImplToJson(
      this,
    );
  }
}

abstract class _CustomCreativesResponse implements CustomCreativesResponse {
  factory _CustomCreativesResponse(
      {final List<CustomCreativesContentItem> content,
      final int totalItems}) = _$CustomCreativesResponseImpl;

  factory _CustomCreativesResponse.fromJson(Map<String, dynamic> json) =
      _$CustomCreativesResponseImpl.fromJson;

  @override
  List<CustomCreativesContentItem> get content;
  @override
  int get totalItems;

  /// Create a copy of CustomCreativesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomCreativesResponseImplCopyWith<_$CustomCreativesResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CreateSingleCreativeRequest _$CreateSingleCreativeRequestFromJson(
    Map<String, dynamic> json) {
  return _CreateSingleCreativeRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateSingleCreativeRequest {
  String get name => throw _privateConstructorUsedError;
  String get landingUrl => throw _privateConstructorUsedError;
  List<SubId> get subIds => throw _privateConstructorUsedError;

  /// Serializes this CreateSingleCreativeRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateSingleCreativeRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateSingleCreativeRequestCopyWith<CreateSingleCreativeRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateSingleCreativeRequestCopyWith<$Res> {
  factory $CreateSingleCreativeRequestCopyWith(
          CreateSingleCreativeRequest value,
          $Res Function(CreateSingleCreativeRequest) then) =
      _$CreateSingleCreativeRequestCopyWithImpl<$Res,
          CreateSingleCreativeRequest>;
  @useResult
  $Res call({String name, String landingUrl, List<SubId> subIds});
}

/// @nodoc
class _$CreateSingleCreativeRequestCopyWithImpl<$Res,
        $Val extends CreateSingleCreativeRequest>
    implements $CreateSingleCreativeRequestCopyWith<$Res> {
  _$CreateSingleCreativeRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateSingleCreativeRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? landingUrl = null,
    Object? subIds = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      landingUrl: null == landingUrl
          ? _value.landingUrl
          : landingUrl // ignore: cast_nullable_to_non_nullable
              as String,
      subIds: null == subIds
          ? _value.subIds
          : subIds // ignore: cast_nullable_to_non_nullable
              as List<SubId>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateSingleCreativeRequestImplCopyWith<$Res>
    implements $CreateSingleCreativeRequestCopyWith<$Res> {
  factory _$$CreateSingleCreativeRequestImplCopyWith(
          _$CreateSingleCreativeRequestImpl value,
          $Res Function(_$CreateSingleCreativeRequestImpl) then) =
      __$$CreateSingleCreativeRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String landingUrl, List<SubId> subIds});
}

/// @nodoc
class __$$CreateSingleCreativeRequestImplCopyWithImpl<$Res>
    extends _$CreateSingleCreativeRequestCopyWithImpl<$Res,
        _$CreateSingleCreativeRequestImpl>
    implements _$$CreateSingleCreativeRequestImplCopyWith<$Res> {
  __$$CreateSingleCreativeRequestImplCopyWithImpl(
      _$CreateSingleCreativeRequestImpl _value,
      $Res Function(_$CreateSingleCreativeRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateSingleCreativeRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? landingUrl = null,
    Object? subIds = null,
  }) {
    return _then(_$CreateSingleCreativeRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      landingUrl: null == landingUrl
          ? _value.landingUrl
          : landingUrl // ignore: cast_nullable_to_non_nullable
              as String,
      subIds: null == subIds
          ? _value._subIds
          : subIds // ignore: cast_nullable_to_non_nullable
              as List<SubId>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateSingleCreativeRequestImpl
    implements _CreateSingleCreativeRequest {
  _$CreateSingleCreativeRequestImpl(
      {required this.name,
      required this.landingUrl,
      final List<SubId> subIds = const []})
      : _subIds = subIds;

  factory _$CreateSingleCreativeRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CreateSingleCreativeRequestImplFromJson(json);

  @override
  final String name;
  @override
  final String landingUrl;
  final List<SubId> _subIds;
  @override
  @JsonKey()
  List<SubId> get subIds {
    if (_subIds is EqualUnmodifiableListView) return _subIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subIds);
  }

  @override
  String toString() {
    return 'CreateSingleCreativeRequest(name: $name, landingUrl: $landingUrl, subIds: $subIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateSingleCreativeRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.landingUrl, landingUrl) ||
                other.landingUrl == landingUrl) &&
            const DeepCollectionEquality().equals(other._subIds, _subIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, landingUrl,
      const DeepCollectionEquality().hash(_subIds));

  /// Create a copy of CreateSingleCreativeRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateSingleCreativeRequestImplCopyWith<_$CreateSingleCreativeRequestImpl>
      get copyWith => __$$CreateSingleCreativeRequestImplCopyWithImpl<
          _$CreateSingleCreativeRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateSingleCreativeRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateSingleCreativeRequest
    implements CreateSingleCreativeRequest {
  factory _CreateSingleCreativeRequest(
      {required final String name,
      required final String landingUrl,
      final List<SubId> subIds}) = _$CreateSingleCreativeRequestImpl;

  factory _CreateSingleCreativeRequest.fromJson(Map<String, dynamic> json) =
      _$CreateSingleCreativeRequestImpl.fromJson;

  @override
  String get name;
  @override
  String get landingUrl;
  @override
  List<SubId> get subIds;

  /// Create a copy of CreateSingleCreativeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateSingleCreativeRequestImplCopyWith<_$CreateSingleCreativeRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
