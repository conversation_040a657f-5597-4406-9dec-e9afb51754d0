// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'custom_link.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CustomLinkStateImpl _$$CustomLinkStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomLinkStateImpl(
      id: (json['id'] as num?)?.toInt() ?? 0,
      name: json['name'] as String? ?? '',
      campaignId: (json['campaignId'] as num?)?.toInt() ?? 0,
      shareLink: json['shareLink'] as String? ?? '',
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      subIds: (json['subIds'] as List<dynamic>?)
              ?.map((e) => SubId.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      errorMessage: json['errorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$$CustomLinkStateImplToJson(
        _$CustomLinkStateImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'campaignId': instance.campaignId,
      'shareLink': instance.shareLink,
      'createdOn': instance.createdOn?.toIso8601String(),
      'subIds': instance.subIds,
      'errorMessage': instance.errorMessage,
    };

_$AcceptedUrlImpl _$$AcceptedUrlImplFromJson(Map<String, dynamic> json) =>
    _$AcceptedUrlImpl(
      displayValue: json['displayValue'] as String,
      validationValue: json['validationValue'] as String,
    );

Map<String, dynamic> _$$AcceptedUrlImplToJson(_$AcceptedUrlImpl instance) =>
    <String, dynamic>{
      'displayValue': instance.displayValue,
      'validationValue': instance.validationValue,
    };

_$CreateCreativesRequestImpl _$$CreateCreativesRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateCreativesRequestImpl(
      name: json['name'] as String,
      landingUrls: (json['landingUrls'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      subIds: (json['subIds'] as List<dynamic>?)
              ?.map((e) => SubId.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CreateCreativesRequestImplToJson(
        _$CreateCreativesRequestImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'landingUrls': instance.landingUrls,
      'subIds': instance.subIds,
    };

_$SubIdImpl _$$SubIdImplFromJson(Map<String, dynamic> json) => _$SubIdImpl(
      name: json['name'] as String? ?? '',
      value: json['value'] as String? ?? '',
    );

Map<String, dynamic> _$$SubIdImplToJson(_$SubIdImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'value': instance.value,
    };

_$CustomLinkResponseImpl _$$CustomLinkResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomLinkResponseImpl(
      customLinks: (json['customLinks'] as List<dynamic>?)
              ?.map((e) => CustomLinkState.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      filteredCount: (json['filteredCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CustomLinkResponseImplToJson(
        _$CustomLinkResponseImpl instance) =>
    <String, dynamic>{
      'customLinks': instance.customLinks,
      'filteredCount': instance.filteredCount,
    };

_$FindCustomCreativesRequestImpl _$$FindCustomCreativesRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$FindCustomCreativesRequestImpl(
      siteId: (json['siteId'] as num).toInt(),
      campaignId: (json['campaignId'] as num).toInt(),
      fromDate: json['fromDate'] as String,
      toDate: json['toDate'] as String,
      limit: (json['limit'] as num?)?.toInt() ?? 1,
      page: (json['page'] as num?)?.toInt() ?? 10,
      keyword: json['keyword'] as String?,
    );

Map<String, dynamic> _$$FindCustomCreativesRequestImplToJson(
        _$FindCustomCreativesRequestImpl instance) =>
    <String, dynamic>{
      'siteId': instance.siteId,
      'campaignId': instance.campaignId,
      'fromDate': instance.fromDate,
      'toDate': instance.toDate,
      'limit': instance.limit,
      'page': instance.page,
      'keyword': instance.keyword,
    };

_$CustomCreativesContentItemImpl _$$CustomCreativesContentItemImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomCreativesContentItemImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      affiliateLink: json['affiliateLink'] as String,
      landingUrl: json['landingUrl'] as String,
      createdOn: json['createdOn'] as String,
    );

Map<String, dynamic> _$$CustomCreativesContentItemImplToJson(
        _$CustomCreativesContentItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'affiliateLink': instance.affiliateLink,
      'landingUrl': instance.landingUrl,
      'createdOn': instance.createdOn,
    };

_$CustomCreativesResponseImpl _$$CustomCreativesResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomCreativesResponseImpl(
      content: (json['content'] as List<dynamic>?)
              ?.map((e) => CustomCreativesContentItem.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalItems: (json['totalItems'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CustomCreativesResponseImplToJson(
        _$CustomCreativesResponseImpl instance) =>
    <String, dynamic>{
      'content': instance.content,
      'totalItems': instance.totalItems,
    };

_$CreateSingleCreativeRequestImpl _$$CreateSingleCreativeRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateSingleCreativeRequestImpl(
      name: json['name'] as String,
      landingUrl: json['landingUrl'] as String,
      subIds: (json['subIds'] as List<dynamic>?)
              ?.map((e) => SubId.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CreateSingleCreativeRequestImplToJson(
        _$CreateSingleCreativeRequestImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'landingUrl': instance.landingUrl,
      'subIds': instance.subIds,
    };
