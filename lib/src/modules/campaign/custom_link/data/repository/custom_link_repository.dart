import 'package:koc_app/src/modules/campaign/custom_link/data/model/custom_link.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

class CustomLinkRepository {
  final ApiService apiService;
  final SharedPreferencesService sharedPreferencesService;

  CustomLinkRepository(this.apiService, this.sharedPreferencesService);

  Future<CustomCreativesResponse> getCustomLinks(FindCustomCreativesRequest request) async {
    final params = {
      'fromDate': request.fromDate,
      'toDate': request.toDate,
      'page': request.page.toString(),
      'limit': request.limit.toString()
    };

    if (request.keyword != null && request.keyword!.isNotEmpty) {
      params['keyword'] = request.keyword!;
    }

    final result = await apiService.getData(
        '/v3/publishers/me/sites/${request.siteId}/campaigns/${request.campaignId}/creatives/custom',
        params: params);
    return CustomCreativesResponse.fromJson(result);
  }

  Future<int> postCustomLinks(int siteId, int campaignId, CreateCreativesRequest data) async {
    return await apiService.postDataAndExtractStatusCode(
        '/v3/publishers/me/sites/$siteId/campaigns/$campaignId/creatives/multiple-custom', data);
  }

  Future<int> postSingleCustomLink(int siteId, int campaignId, CreateSingleCreativeRequest data) async {
    return await apiService.postDataAndExtractStatusCode(
        '/v3/publishers/me/sites/$siteId/campaigns/$campaignId/creatives/custom', data);
  }

  Future<List<AcceptedUrl>> getAcceptedUrls(int siteId, int campaignId) async {
    final result =
        await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/$campaignId/creatives/custom/acceptedurls');
    List<AcceptedUrl> urlsList = (result as List).map((item) => AcceptedUrl.fromJson(item as Map<String, dynamic>)).toList();
    return urlsList;
  }
}
