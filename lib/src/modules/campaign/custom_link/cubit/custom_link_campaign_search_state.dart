import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';

part 'custom_link_campaign_search_state.freezed.dart';
part 'custom_link_campaign_search_state.g.dart';

@freezed
class CustomLinkCampaignSearchState extends BaseCubitState with _$CustomLinkCampaignSearchState {
  factory CustomLinkCampaignSearchState({
    @Default([]) List<CustomCreativeEnabledCampaign> affiliatedCampaigns,
    @Default([]) List<CustomCreativeEnabledCampaign> filteredCampaigns,
    CustomCreativeEnabledCampaign? selectedCampaign,
    @Default('') String searchText,
    @Default('') String errorMessage,
    @Default(false) bool isLoadingCampaigns,
  }) = _CustomLinkCampaignSearchState;

  factory CustomLinkCampaignSearchState.fromJson(Map<String, Object?> json) =>
      _$CustomLinkCampaignSearchStateFromJson(json);
}

@freezed
class CustomCreativeEnabledCampaign with _$CustomCreativeEnabledCampaign {
  factory CustomCreativeEnabledCampaign(
      {required int campaignId,
      required String campaignName,
      required String ruleType,
      @Default([]) List<String> shouldStartWithDomain}) = _CustomCreativeEnabledCampaign;
  factory CustomCreativeEnabledCampaign.fromJson(Map<String, Object?> json) =>
      _$CustomCreativeEnabledCampaignFromJson(json);
}
