import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/model/custom_link.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';

part 'custom_link_history_state.freezed.dart';
part 'custom_link_history_state.g.dart';

@freezed
class CustomLinkHistoryState extends BaseCubitState with _$CustomLinkHistoryState {
  factory CustomLinkHistoryState({
    CustomCreativesResponse? customLinks,
    @Default(false) bool isRegisteredNew,
    @Default(1) int currentPage,
    @Default(false) bool isLoadingMore,
    @Default(false) bool hasMoreItems,
    @Default(0) int totalCount,
    @Default(false) bool isPullToRefresh,
  }) = _CustomLinkHistoryState;

  factory CustomLinkHistoryState.fromJson(Map<String, Object?> json) => _$CustomLinkHistoryStateFromJson(json);
}
