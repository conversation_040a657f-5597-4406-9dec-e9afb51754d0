import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_state.dart';
import 'package:koc_app/src/modules/campaign/data/repository/creative_repository.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';
import 'package:koc_app/src/shared/data/item.dart';

class CustomLinkCampaignSearchCubit extends BaseCubit<CustomLinkCampaignSearchState> {
  final CreativeRepository _creativeRepository;
  CustomLinkCampaignSearchCubit(this._creativeRepository) : super(CustomLinkCampaignSearchState());

  Future<void> getAffiliatedCampaigns({int? siteId}) async {
    try {
      emit(state.copyWith(isLoadingCampaigns: true));
      final targetSiteId = siteId ?? (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      List<CustomCreativeEnabledCampaign> affiliatedCampaigns =
          await _creativeRepository.getCustomCreativeEnabledCampaign(targetSiteId);
      emit(state.copyWith(affiliatedCampaigns: affiliatedCampaigns, filteredCampaigns: affiliatedCampaigns));
      emit(state.copyWith(isLoadingCampaigns: false));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      emit(state.copyWith(isLoadingCampaigns: false));
    }
  }

  void selectCampaign(CustomCreativeEnabledCampaign campaign) {
    emit(state.copyWith(selectedCampaign: campaign));
  }

  void updateSearchText(String value) {
    emit(state.copyWith(
        searchText: value,
        filteredCampaigns: state.affiliatedCampaigns
            .where((c) => c.campaignName.toLowerCase().contains(value.toLowerCase()))
            .toList()));
  }

  void clearCampaign() {
    emit(state.copyWith(selectedCampaign: null));
  }

  Future<List<Item>> getCustomCreativeEnabledCampaigns({int? siteId}) async {
    await getAffiliatedCampaigns(siteId: siteId);
    return state.filteredCampaigns
        .where((campaign) => campaign.campaignId != 0)
        .map((campaign) =>
            Item(value: campaign.campaignId, name: campaign.campaignName))
        .toList();
  }
}
