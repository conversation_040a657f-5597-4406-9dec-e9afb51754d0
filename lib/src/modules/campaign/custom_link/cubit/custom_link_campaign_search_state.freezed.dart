// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'custom_link_campaign_search_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CustomLinkCampaignSearchState _$CustomLinkCampaignSearchStateFromJson(
    Map<String, dynamic> json) {
  return _CustomLinkCampaignSearchState.fromJson(json);
}

/// @nodoc
mixin _$CustomLinkCampaignSearchState {
  List<CustomCreativeEnabledCampaign> get affiliatedCampaigns =>
      throw _privateConstructorUsedError;
  List<CustomCreativeEnabledCampaign> get filteredCampaigns =>
      throw _privateConstructorUsedError;
  CustomCreativeEnabledCampaign? get selectedCampaign =>
      throw _privateConstructorUsedError;
  String get searchText => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  bool get isLoadingCampaigns => throw _privateConstructorUsedError;

  /// Serializes this CustomLinkCampaignSearchState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomLinkCampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomLinkCampaignSearchStateCopyWith<CustomLinkCampaignSearchState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomLinkCampaignSearchStateCopyWith<$Res> {
  factory $CustomLinkCampaignSearchStateCopyWith(
          CustomLinkCampaignSearchState value,
          $Res Function(CustomLinkCampaignSearchState) then) =
      _$CustomLinkCampaignSearchStateCopyWithImpl<$Res,
          CustomLinkCampaignSearchState>;
  @useResult
  $Res call(
      {List<CustomCreativeEnabledCampaign> affiliatedCampaigns,
      List<CustomCreativeEnabledCampaign> filteredCampaigns,
      CustomCreativeEnabledCampaign? selectedCampaign,
      String searchText,
      String errorMessage,
      bool isLoadingCampaigns});

  $CustomCreativeEnabledCampaignCopyWith<$Res>? get selectedCampaign;
}

/// @nodoc
class _$CustomLinkCampaignSearchStateCopyWithImpl<$Res,
        $Val extends CustomLinkCampaignSearchState>
    implements $CustomLinkCampaignSearchStateCopyWith<$Res> {
  _$CustomLinkCampaignSearchStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomLinkCampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? affiliatedCampaigns = null,
    Object? filteredCampaigns = null,
    Object? selectedCampaign = freezed,
    Object? searchText = null,
    Object? errorMessage = null,
    Object? isLoadingCampaigns = null,
  }) {
    return _then(_value.copyWith(
      affiliatedCampaigns: null == affiliatedCampaigns
          ? _value.affiliatedCampaigns
          : affiliatedCampaigns // ignore: cast_nullable_to_non_nullable
              as List<CustomCreativeEnabledCampaign>,
      filteredCampaigns: null == filteredCampaigns
          ? _value.filteredCampaigns
          : filteredCampaigns // ignore: cast_nullable_to_non_nullable
              as List<CustomCreativeEnabledCampaign>,
      selectedCampaign: freezed == selectedCampaign
          ? _value.selectedCampaign
          : selectedCampaign // ignore: cast_nullable_to_non_nullable
              as CustomCreativeEnabledCampaign?,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isLoadingCampaigns: null == isLoadingCampaigns
          ? _value.isLoadingCampaigns
          : isLoadingCampaigns // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of CustomLinkCampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomCreativeEnabledCampaignCopyWith<$Res>? get selectedCampaign {
    if (_value.selectedCampaign == null) {
      return null;
    }

    return $CustomCreativeEnabledCampaignCopyWith<$Res>(
        _value.selectedCampaign!, (value) {
      return _then(_value.copyWith(selectedCampaign: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CustomLinkCampaignSearchStateImplCopyWith<$Res>
    implements $CustomLinkCampaignSearchStateCopyWith<$Res> {
  factory _$$CustomLinkCampaignSearchStateImplCopyWith(
          _$CustomLinkCampaignSearchStateImpl value,
          $Res Function(_$CustomLinkCampaignSearchStateImpl) then) =
      __$$CustomLinkCampaignSearchStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<CustomCreativeEnabledCampaign> affiliatedCampaigns,
      List<CustomCreativeEnabledCampaign> filteredCampaigns,
      CustomCreativeEnabledCampaign? selectedCampaign,
      String searchText,
      String errorMessage,
      bool isLoadingCampaigns});

  @override
  $CustomCreativeEnabledCampaignCopyWith<$Res>? get selectedCampaign;
}

/// @nodoc
class __$$CustomLinkCampaignSearchStateImplCopyWithImpl<$Res>
    extends _$CustomLinkCampaignSearchStateCopyWithImpl<$Res,
        _$CustomLinkCampaignSearchStateImpl>
    implements _$$CustomLinkCampaignSearchStateImplCopyWith<$Res> {
  __$$CustomLinkCampaignSearchStateImplCopyWithImpl(
      _$CustomLinkCampaignSearchStateImpl _value,
      $Res Function(_$CustomLinkCampaignSearchStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomLinkCampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? affiliatedCampaigns = null,
    Object? filteredCampaigns = null,
    Object? selectedCampaign = freezed,
    Object? searchText = null,
    Object? errorMessage = null,
    Object? isLoadingCampaigns = null,
  }) {
    return _then(_$CustomLinkCampaignSearchStateImpl(
      affiliatedCampaigns: null == affiliatedCampaigns
          ? _value._affiliatedCampaigns
          : affiliatedCampaigns // ignore: cast_nullable_to_non_nullable
              as List<CustomCreativeEnabledCampaign>,
      filteredCampaigns: null == filteredCampaigns
          ? _value._filteredCampaigns
          : filteredCampaigns // ignore: cast_nullable_to_non_nullable
              as List<CustomCreativeEnabledCampaign>,
      selectedCampaign: freezed == selectedCampaign
          ? _value.selectedCampaign
          : selectedCampaign // ignore: cast_nullable_to_non_nullable
              as CustomCreativeEnabledCampaign?,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isLoadingCampaigns: null == isLoadingCampaigns
          ? _value.isLoadingCampaigns
          : isLoadingCampaigns // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomLinkCampaignSearchStateImpl
    implements _CustomLinkCampaignSearchState {
  _$CustomLinkCampaignSearchStateImpl(
      {final List<CustomCreativeEnabledCampaign> affiliatedCampaigns = const [],
      final List<CustomCreativeEnabledCampaign> filteredCampaigns = const [],
      this.selectedCampaign,
      this.searchText = '',
      this.errorMessage = '',
      this.isLoadingCampaigns = false})
      : _affiliatedCampaigns = affiliatedCampaigns,
        _filteredCampaigns = filteredCampaigns;

  factory _$CustomLinkCampaignSearchStateImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CustomLinkCampaignSearchStateImplFromJson(json);

  final List<CustomCreativeEnabledCampaign> _affiliatedCampaigns;
  @override
  @JsonKey()
  List<CustomCreativeEnabledCampaign> get affiliatedCampaigns {
    if (_affiliatedCampaigns is EqualUnmodifiableListView)
      return _affiliatedCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_affiliatedCampaigns);
  }

  final List<CustomCreativeEnabledCampaign> _filteredCampaigns;
  @override
  @JsonKey()
  List<CustomCreativeEnabledCampaign> get filteredCampaigns {
    if (_filteredCampaigns is EqualUnmodifiableListView)
      return _filteredCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredCampaigns);
  }

  @override
  final CustomCreativeEnabledCampaign? selectedCampaign;
  @override
  @JsonKey()
  final String searchText;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool isLoadingCampaigns;

  @override
  String toString() {
    return 'CustomLinkCampaignSearchState(affiliatedCampaigns: $affiliatedCampaigns, filteredCampaigns: $filteredCampaigns, selectedCampaign: $selectedCampaign, searchText: $searchText, errorMessage: $errorMessage, isLoadingCampaigns: $isLoadingCampaigns)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomLinkCampaignSearchStateImpl &&
            const DeepCollectionEquality()
                .equals(other._affiliatedCampaigns, _affiliatedCampaigns) &&
            const DeepCollectionEquality()
                .equals(other._filteredCampaigns, _filteredCampaigns) &&
            (identical(other.selectedCampaign, selectedCampaign) ||
                other.selectedCampaign == selectedCampaign) &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isLoadingCampaigns, isLoadingCampaigns) ||
                other.isLoadingCampaigns == isLoadingCampaigns));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_affiliatedCampaigns),
      const DeepCollectionEquality().hash(_filteredCampaigns),
      selectedCampaign,
      searchText,
      errorMessage,
      isLoadingCampaigns);

  /// Create a copy of CustomLinkCampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomLinkCampaignSearchStateImplCopyWith<
          _$CustomLinkCampaignSearchStateImpl>
      get copyWith => __$$CustomLinkCampaignSearchStateImplCopyWithImpl<
          _$CustomLinkCampaignSearchStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomLinkCampaignSearchStateImplToJson(
      this,
    );
  }
}

abstract class _CustomLinkCampaignSearchState
    implements CustomLinkCampaignSearchState {
  factory _CustomLinkCampaignSearchState(
      {final List<CustomCreativeEnabledCampaign> affiliatedCampaigns,
      final List<CustomCreativeEnabledCampaign> filteredCampaigns,
      final CustomCreativeEnabledCampaign? selectedCampaign,
      final String searchText,
      final String errorMessage,
      final bool isLoadingCampaigns}) = _$CustomLinkCampaignSearchStateImpl;

  factory _CustomLinkCampaignSearchState.fromJson(Map<String, dynamic> json) =
      _$CustomLinkCampaignSearchStateImpl.fromJson;

  @override
  List<CustomCreativeEnabledCampaign> get affiliatedCampaigns;
  @override
  List<CustomCreativeEnabledCampaign> get filteredCampaigns;
  @override
  CustomCreativeEnabledCampaign? get selectedCampaign;
  @override
  String get searchText;
  @override
  String get errorMessage;
  @override
  bool get isLoadingCampaigns;

  /// Create a copy of CustomLinkCampaignSearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomLinkCampaignSearchStateImplCopyWith<
          _$CustomLinkCampaignSearchStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CustomCreativeEnabledCampaign _$CustomCreativeEnabledCampaignFromJson(
    Map<String, dynamic> json) {
  return _CustomCreativeEnabledCampaign.fromJson(json);
}

/// @nodoc
mixin _$CustomCreativeEnabledCampaign {
  int get campaignId => throw _privateConstructorUsedError;
  String get campaignName => throw _privateConstructorUsedError;
  String get ruleType => throw _privateConstructorUsedError;
  List<String> get shouldStartWithDomain => throw _privateConstructorUsedError;

  /// Serializes this CustomCreativeEnabledCampaign to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomCreativeEnabledCampaign
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomCreativeEnabledCampaignCopyWith<CustomCreativeEnabledCampaign>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomCreativeEnabledCampaignCopyWith<$Res> {
  factory $CustomCreativeEnabledCampaignCopyWith(
          CustomCreativeEnabledCampaign value,
          $Res Function(CustomCreativeEnabledCampaign) then) =
      _$CustomCreativeEnabledCampaignCopyWithImpl<$Res,
          CustomCreativeEnabledCampaign>;
  @useResult
  $Res call(
      {int campaignId,
      String campaignName,
      String ruleType,
      List<String> shouldStartWithDomain});
}

/// @nodoc
class _$CustomCreativeEnabledCampaignCopyWithImpl<$Res,
        $Val extends CustomCreativeEnabledCampaign>
    implements $CustomCreativeEnabledCampaignCopyWith<$Res> {
  _$CustomCreativeEnabledCampaignCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomCreativeEnabledCampaign
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignId = null,
    Object? campaignName = null,
    Object? ruleType = null,
    Object? shouldStartWithDomain = null,
  }) {
    return _then(_value.copyWith(
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      ruleType: null == ruleType
          ? _value.ruleType
          : ruleType // ignore: cast_nullable_to_non_nullable
              as String,
      shouldStartWithDomain: null == shouldStartWithDomain
          ? _value.shouldStartWithDomain
          : shouldStartWithDomain // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomCreativeEnabledCampaignImplCopyWith<$Res>
    implements $CustomCreativeEnabledCampaignCopyWith<$Res> {
  factory _$$CustomCreativeEnabledCampaignImplCopyWith(
          _$CustomCreativeEnabledCampaignImpl value,
          $Res Function(_$CustomCreativeEnabledCampaignImpl) then) =
      __$$CustomCreativeEnabledCampaignImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int campaignId,
      String campaignName,
      String ruleType,
      List<String> shouldStartWithDomain});
}

/// @nodoc
class __$$CustomCreativeEnabledCampaignImplCopyWithImpl<$Res>
    extends _$CustomCreativeEnabledCampaignCopyWithImpl<$Res,
        _$CustomCreativeEnabledCampaignImpl>
    implements _$$CustomCreativeEnabledCampaignImplCopyWith<$Res> {
  __$$CustomCreativeEnabledCampaignImplCopyWithImpl(
      _$CustomCreativeEnabledCampaignImpl _value,
      $Res Function(_$CustomCreativeEnabledCampaignImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomCreativeEnabledCampaign
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignId = null,
    Object? campaignName = null,
    Object? ruleType = null,
    Object? shouldStartWithDomain = null,
  }) {
    return _then(_$CustomCreativeEnabledCampaignImpl(
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      ruleType: null == ruleType
          ? _value.ruleType
          : ruleType // ignore: cast_nullable_to_non_nullable
              as String,
      shouldStartWithDomain: null == shouldStartWithDomain
          ? _value._shouldStartWithDomain
          : shouldStartWithDomain // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomCreativeEnabledCampaignImpl
    implements _CustomCreativeEnabledCampaign {
  _$CustomCreativeEnabledCampaignImpl(
      {required this.campaignId,
      required this.campaignName,
      required this.ruleType,
      final List<String> shouldStartWithDomain = const []})
      : _shouldStartWithDomain = shouldStartWithDomain;

  factory _$CustomCreativeEnabledCampaignImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CustomCreativeEnabledCampaignImplFromJson(json);

  @override
  final int campaignId;
  @override
  final String campaignName;
  @override
  final String ruleType;
  final List<String> _shouldStartWithDomain;
  @override
  @JsonKey()
  List<String> get shouldStartWithDomain {
    if (_shouldStartWithDomain is EqualUnmodifiableListView)
      return _shouldStartWithDomain;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_shouldStartWithDomain);
  }

  @override
  String toString() {
    return 'CustomCreativeEnabledCampaign(campaignId: $campaignId, campaignName: $campaignName, ruleType: $ruleType, shouldStartWithDomain: $shouldStartWithDomain)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomCreativeEnabledCampaignImpl &&
            (identical(other.campaignId, campaignId) ||
                other.campaignId == campaignId) &&
            (identical(other.campaignName, campaignName) ||
                other.campaignName == campaignName) &&
            (identical(other.ruleType, ruleType) ||
                other.ruleType == ruleType) &&
            const DeepCollectionEquality()
                .equals(other._shouldStartWithDomain, _shouldStartWithDomain));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, campaignId, campaignName,
      ruleType, const DeepCollectionEquality().hash(_shouldStartWithDomain));

  /// Create a copy of CustomCreativeEnabledCampaign
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomCreativeEnabledCampaignImplCopyWith<
          _$CustomCreativeEnabledCampaignImpl>
      get copyWith => __$$CustomCreativeEnabledCampaignImplCopyWithImpl<
          _$CustomCreativeEnabledCampaignImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomCreativeEnabledCampaignImplToJson(
      this,
    );
  }
}

abstract class _CustomCreativeEnabledCampaign
    implements CustomCreativeEnabledCampaign {
  factory _CustomCreativeEnabledCampaign(
          {required final int campaignId,
          required final String campaignName,
          required final String ruleType,
          final List<String> shouldStartWithDomain}) =
      _$CustomCreativeEnabledCampaignImpl;

  factory _CustomCreativeEnabledCampaign.fromJson(Map<String, dynamic> json) =
      _$CustomCreativeEnabledCampaignImpl.fromJson;

  @override
  int get campaignId;
  @override
  String get campaignName;
  @override
  String get ruleType;
  @override
  List<String> get shouldStartWithDomain;

  /// Create a copy of CustomCreativeEnabledCampaign
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomCreativeEnabledCampaignImplCopyWith<
          _$CustomCreativeEnabledCampaignImpl>
      get copyWith => throw _privateConstructorUsedError;
}
