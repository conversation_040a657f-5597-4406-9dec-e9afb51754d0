import 'dart:developer';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/model/custom_link.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/repository/custom_link_repository.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class CustomLinkCubit extends BaseCubit<CustomLinkState> {
  final CustomLinkRepository _customLinkRepository;
  CustomLinkCubit(this._customLinkRepository) : super(CustomLinkState());

  void updateState(CustomLinkState customLink) {
    emit(customLink);
  }

  Future<bool> saveCustomLink(int? siteId, int campaignId) async {
    try {
      final currentSiteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      List<String> processedUrls = [];
      List<String> lines = state.shareLink.split('\n');

      for (String line in lines) {
        if (line.contains(',')) {
          processedUrls.addAll(line.split(',').map((url) => url.trim()).where((url) => url.isNotEmpty));
        } else {
          String trimmedLine = line.trim();
          if (trimmedLine.isNotEmpty) {
            processedUrls.add(trimmedLine);
          }
        }
      }

      if (processedUrls.isEmpty) {
        return false;
      }

      List<SubId> validSubIds = state.subIds.where((subId) => subId.name.isNotEmpty && subId.value.isNotEmpty).toList();

      bool success = false;
      final effectiveSiteId = siteId ?? currentSiteId!;

      if (processedUrls.length == 1) {
        CreateSingleCreativeRequest request =
            CreateSingleCreativeRequest(name: state.name, landingUrl: processedUrls[0], subIds: validSubIds);

        final statusCode = await _customLinkRepository.postSingleCustomLink(effectiveSiteId, campaignId, request);
        success = statusCode == 201;
      } else {
        CreateCreativesRequest request =
            CreateCreativesRequest(name: state.name, landingUrls: processedUrls, subIds: validSubIds);

        final statusCode = await _customLinkRepository.postCustomLinks(effectiveSiteId, campaignId, request);
        success = statusCode == 201;
      }

      if (success) {
        await _clearCustomLinkHistoryCache(effectiveSiteId, campaignId);
      }

      return success;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<List<AcceptedUrl>> getAcceptedUrls(int campaignId) async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      List<AcceptedUrl> acceptedUrls = await _customLinkRepository.getAcceptedUrls(siteId!, campaignId);
      return acceptedUrls;
    } catch (e) {
      handleError(e, (message) => log(message));
      return [];
    }
  }

  /// Clear cache for custom link history endpoint to ensure data consistency
  /// This ensures that the history list is updated immediately after successful link generation
  Future<void> _clearCustomLinkHistoryCache(int siteId, int campaignId) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/$campaignId/creatives/custom');
    } catch (e) {
      handleError(e, (message) => log('Error clearing custom link history cache: $message'));
    }
  }
}
