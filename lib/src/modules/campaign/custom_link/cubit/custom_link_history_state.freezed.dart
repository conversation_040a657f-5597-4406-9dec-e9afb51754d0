// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'custom_link_history_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CustomLinkHistoryState _$CustomLinkHistoryStateFromJson(
    Map<String, dynamic> json) {
  return _CustomLinkHistoryState.fromJson(json);
}

/// @nodoc
mixin _$CustomLinkHistoryState {
  CustomCreativesResponse? get customLinks =>
      throw _privateConstructorUsedError;
  bool get isRegisteredNew => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  bool get isLoadingMore => throw _privateConstructorUsedError;
  bool get hasMoreItems => throw _privateConstructorUsedError;
  int get totalCount => throw _privateConstructorUsedError;
  bool get isPullToRefresh => throw _privateConstructorUsedError;

  /// Serializes this CustomLinkHistoryState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomLinkHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomLinkHistoryStateCopyWith<CustomLinkHistoryState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomLinkHistoryStateCopyWith<$Res> {
  factory $CustomLinkHistoryStateCopyWith(CustomLinkHistoryState value,
          $Res Function(CustomLinkHistoryState) then) =
      _$CustomLinkHistoryStateCopyWithImpl<$Res, CustomLinkHistoryState>;
  @useResult
  $Res call(
      {CustomCreativesResponse? customLinks,
      bool isRegisteredNew,
      int currentPage,
      bool isLoadingMore,
      bool hasMoreItems,
      int totalCount,
      bool isPullToRefresh});

  $CustomCreativesResponseCopyWith<$Res>? get customLinks;
}

/// @nodoc
class _$CustomLinkHistoryStateCopyWithImpl<$Res,
        $Val extends CustomLinkHistoryState>
    implements $CustomLinkHistoryStateCopyWith<$Res> {
  _$CustomLinkHistoryStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomLinkHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customLinks = freezed,
    Object? isRegisteredNew = null,
    Object? currentPage = null,
    Object? isLoadingMore = null,
    Object? hasMoreItems = null,
    Object? totalCount = null,
    Object? isPullToRefresh = null,
  }) {
    return _then(_value.copyWith(
      customLinks: freezed == customLinks
          ? _value.customLinks
          : customLinks // ignore: cast_nullable_to_non_nullable
              as CustomCreativesResponse?,
      isRegisteredNew: null == isRegisteredNew
          ? _value.isRegisteredNew
          : isRegisteredNew // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMoreItems: null == hasMoreItems
          ? _value.hasMoreItems
          : hasMoreItems // ignore: cast_nullable_to_non_nullable
              as bool,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of CustomLinkHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomCreativesResponseCopyWith<$Res>? get customLinks {
    if (_value.customLinks == null) {
      return null;
    }

    return $CustomCreativesResponseCopyWith<$Res>(_value.customLinks!, (value) {
      return _then(_value.copyWith(customLinks: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CustomLinkHistoryStateImplCopyWith<$Res>
    implements $CustomLinkHistoryStateCopyWith<$Res> {
  factory _$$CustomLinkHistoryStateImplCopyWith(
          _$CustomLinkHistoryStateImpl value,
          $Res Function(_$CustomLinkHistoryStateImpl) then) =
      __$$CustomLinkHistoryStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CustomCreativesResponse? customLinks,
      bool isRegisteredNew,
      int currentPage,
      bool isLoadingMore,
      bool hasMoreItems,
      int totalCount,
      bool isPullToRefresh});

  @override
  $CustomCreativesResponseCopyWith<$Res>? get customLinks;
}

/// @nodoc
class __$$CustomLinkHistoryStateImplCopyWithImpl<$Res>
    extends _$CustomLinkHistoryStateCopyWithImpl<$Res,
        _$CustomLinkHistoryStateImpl>
    implements _$$CustomLinkHistoryStateImplCopyWith<$Res> {
  __$$CustomLinkHistoryStateImplCopyWithImpl(
      _$CustomLinkHistoryStateImpl _value,
      $Res Function(_$CustomLinkHistoryStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomLinkHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customLinks = freezed,
    Object? isRegisteredNew = null,
    Object? currentPage = null,
    Object? isLoadingMore = null,
    Object? hasMoreItems = null,
    Object? totalCount = null,
    Object? isPullToRefresh = null,
  }) {
    return _then(_$CustomLinkHistoryStateImpl(
      customLinks: freezed == customLinks
          ? _value.customLinks
          : customLinks // ignore: cast_nullable_to_non_nullable
              as CustomCreativesResponse?,
      isRegisteredNew: null == isRegisteredNew
          ? _value.isRegisteredNew
          : isRegisteredNew // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMoreItems: null == hasMoreItems
          ? _value.hasMoreItems
          : hasMoreItems // ignore: cast_nullable_to_non_nullable
              as bool,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomLinkHistoryStateImpl implements _CustomLinkHistoryState {
  _$CustomLinkHistoryStateImpl(
      {this.customLinks,
      this.isRegisteredNew = false,
      this.currentPage = 1,
      this.isLoadingMore = false,
      this.hasMoreItems = false,
      this.totalCount = 0,
      this.isPullToRefresh = false});

  factory _$CustomLinkHistoryStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomLinkHistoryStateImplFromJson(json);

  @override
  final CustomCreativesResponse? customLinks;
  @override
  @JsonKey()
  final bool isRegisteredNew;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  @JsonKey()
  final bool hasMoreItems;
  @override
  @JsonKey()
  final int totalCount;
  @override
  @JsonKey()
  final bool isPullToRefresh;

  @override
  String toString() {
    return 'CustomLinkHistoryState(customLinks: $customLinks, isRegisteredNew: $isRegisteredNew, currentPage: $currentPage, isLoadingMore: $isLoadingMore, hasMoreItems: $hasMoreItems, totalCount: $totalCount, isPullToRefresh: $isPullToRefresh)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomLinkHistoryStateImpl &&
            (identical(other.customLinks, customLinks) ||
                other.customLinks == customLinks) &&
            (identical(other.isRegisteredNew, isRegisteredNew) ||
                other.isRegisteredNew == isRegisteredNew) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.hasMoreItems, hasMoreItems) ||
                other.hasMoreItems == hasMoreItems) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            (identical(other.isPullToRefresh, isPullToRefresh) ||
                other.isPullToRefresh == isPullToRefresh));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, customLinks, isRegisteredNew,
      currentPage, isLoadingMore, hasMoreItems, totalCount, isPullToRefresh);

  /// Create a copy of CustomLinkHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomLinkHistoryStateImplCopyWith<_$CustomLinkHistoryStateImpl>
      get copyWith => __$$CustomLinkHistoryStateImplCopyWithImpl<
          _$CustomLinkHistoryStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomLinkHistoryStateImplToJson(
      this,
    );
  }
}

abstract class _CustomLinkHistoryState implements CustomLinkHistoryState {
  factory _CustomLinkHistoryState(
      {final CustomCreativesResponse? customLinks,
      final bool isRegisteredNew,
      final int currentPage,
      final bool isLoadingMore,
      final bool hasMoreItems,
      final int totalCount,
      final bool isPullToRefresh}) = _$CustomLinkHistoryStateImpl;

  factory _CustomLinkHistoryState.fromJson(Map<String, dynamic> json) =
      _$CustomLinkHistoryStateImpl.fromJson;

  @override
  CustomCreativesResponse? get customLinks;
  @override
  bool get isRegisteredNew;
  @override
  int get currentPage;
  @override
  bool get isLoadingMore;
  @override
  bool get hasMoreItems;
  @override
  int get totalCount;
  @override
  bool get isPullToRefresh;

  /// Create a copy of CustomLinkHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomLinkHistoryStateImplCopyWith<_$CustomLinkHistoryStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
