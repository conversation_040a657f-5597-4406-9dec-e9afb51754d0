// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'custom_link_campaign_search_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CustomLinkCampaignSearchStateImpl
    _$$CustomLinkCampaignSearchStateImplFromJson(Map<String, dynamic> json) =>
        _$CustomLinkCampaignSearchStateImpl(
          affiliatedCampaigns: (json['affiliatedCampaigns'] as List<dynamic>?)
                  ?.map((e) => CustomCreativeEnabledCampaign.fromJson(
                      e as Map<String, dynamic>))
                  .toList() ??
              const [],
          filteredCampaigns: (json['filteredCampaigns'] as List<dynamic>?)
                  ?.map((e) => CustomCreativeEnabledCampaign.fromJson(
                      e as Map<String, dynamic>))
                  .toList() ??
              const [],
          selectedCampaign: json['selectedCampaign'] == null
              ? null
              : CustomCreativeEnabledCampaign.fromJson(
                  json['selectedCampaign'] as Map<String, dynamic>),
          searchText: json['searchText'] as String? ?? '',
          errorMessage: json['errorMessage'] as String? ?? '',
          isLoadingCampaigns: json['isLoadingCampaigns'] as bool? ?? false,
        );

Map<String, dynamic> _$$CustomLinkCampaignSearchStateImplToJson(
        _$CustomLinkCampaignSearchStateImpl instance) =>
    <String, dynamic>{
      'affiliatedCampaigns': instance.affiliatedCampaigns,
      'filteredCampaigns': instance.filteredCampaigns,
      'selectedCampaign': instance.selectedCampaign,
      'searchText': instance.searchText,
      'errorMessage': instance.errorMessage,
      'isLoadingCampaigns': instance.isLoadingCampaigns,
    };

_$CustomCreativeEnabledCampaignImpl
    _$$CustomCreativeEnabledCampaignImplFromJson(Map<String, dynamic> json) =>
        _$CustomCreativeEnabledCampaignImpl(
          campaignId: (json['campaignId'] as num).toInt(),
          campaignName: json['campaignName'] as String,
          ruleType: json['ruleType'] as String,
          shouldStartWithDomain:
              (json['shouldStartWithDomain'] as List<dynamic>?)
                      ?.map((e) => e as String)
                      .toList() ??
                  const [],
        );

Map<String, dynamic> _$$CustomCreativeEnabledCampaignImplToJson(
        _$CustomCreativeEnabledCampaignImpl instance) =>
    <String, dynamic>{
      'campaignId': instance.campaignId,
      'campaignName': instance.campaignName,
      'ruleType': instance.ruleType,
      'shouldStartWithDomain': instance.shouldStartWithDomain,
    };
