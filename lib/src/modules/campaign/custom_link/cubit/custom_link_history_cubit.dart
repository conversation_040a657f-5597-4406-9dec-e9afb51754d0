import 'dart:developer';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_state.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/model/custom_link.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/repository/custom_link_repository.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class CustomLinkHistoryCubit extends BaseCubit<CustomLinkHistoryState> {
  final CustomLinkRepository _customLinkRepository;
  CustomLinkHistoryCubit(this._customLinkRepository) : super(CustomLinkHistoryState());

  Future<void> getCustomLinks(int campaignId,
      {String? customName, DateTime? fromDate, DateTime? toDate, int? page, int? limit}) async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        return;
      }

      final countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        return;
      }

      final effectiveFromDate = fromDate ?? DateTime.now().subtract(const Duration(days: 7));
      final effectiveToDate = toDate ?? DateTime.now();
      final effectivePage = page ?? 1;
      final effectiveLimit = limit ?? 10;
      final effectiveKeyword = customName ?? '';

      final startOfDay = DateTime(effectiveFromDate.year, effectiveFromDate.month, effectiveFromDate.day, 0, 0, 0);
      final endOfDay = DateTime(effectiveToDate.year, effectiveToDate.month, effectiveToDate.day, 23, 59, 59);

      FindCustomCreativesRequest request = FindCustomCreativesRequest(
          siteId: siteId,
          campaignId: campaignId,
          fromDate: startOfDay.toZonedIso8601(countryCode.toCountry),
          toDate: endOfDay.toZonedIso8601(countryCode.toCountry),
          keyword: effectiveKeyword,
          page: effectivePage,
          limit: effectiveLimit);
      CustomCreativesResponse customLinks = await _customLinkRepository.getCustomLinks(request);

      if (page != null && page > 1) {
        final currentContent = state.customLinks?.content ?? [];
        final newContent = [...currentContent, ...customLinks.content];
        final updatedCustomLinks = CustomCreativesResponse(content: newContent, totalItems: customLinks.totalItems);
        emit(state.copyWith(
            customLinks: updatedCustomLinks,
            currentPage: effectivePage,
            isLoadingMore: false,
            hasMoreItems: newContent.length < customLinks.totalItems));
      } else {
        emit(state.copyWith(
            customLinks: customLinks,
            currentPage: 1,
            isLoadingMore: false,
            hasMoreItems: customLinks.content.length < customLinks.totalItems));
      }
    } catch (e) {
      emit(state.copyWith(isLoadingMore: false));
      handleError(e, (message) => log(message));
    }
  }

  Future<void> loadMore(int campaignId, {String? customName, DateTime? fromDate, DateTime? toDate}) async {
    if (state.isLoadingMore || !state.hasMoreItems) {
      return;
    }

    emit(state.copyWith(isLoadingMore: true));

    final nextPage = state.currentPage + 1;
    await getCustomLinks(campaignId,
        customName: customName, fromDate: fromDate, toDate: toDate, page: nextPage, limit: 10);
  }

  void setRegisteredNewCustomLink(bool value) {
    emit(state.copyWith(isRegisteredNew: value));
  }

  void clear() {
    emit(CustomLinkHistoryState());
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes custom link history data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh(int campaignId, {String? customName, DateTime? fromDate, DateTime? toDate}) async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      if (siteId != null) {
        await apiService
            .clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/$campaignId/creatives/custom');
      }

      await getCustomLinks(campaignId, customName: customName, fromDate: fromDate, toDate: toDate);
    } catch (e) {
      handleError(e, (message) => log('Error during pull-to-refresh: $message'));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }
}
