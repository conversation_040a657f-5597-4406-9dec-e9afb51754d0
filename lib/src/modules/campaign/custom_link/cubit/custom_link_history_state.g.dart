// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'custom_link_history_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CustomLinkHistoryStateImpl _$$CustomLinkHistoryStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomLinkHistoryStateImpl(
      customLinks: json['customLinks'] == null
          ? null
          : CustomCreativesResponse.fromJson(
              json['customLinks'] as Map<String, dynamic>),
      isRegisteredNew: json['isRegisteredNew'] as bool? ?? false,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      isLoadingMore: json['isLoadingMore'] as bool? ?? false,
      hasMoreItems: json['hasMoreItems'] as bool? ?? false,
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      isPullToRefresh: json['isPullToRefresh'] as bool? ?? false,
    );

Map<String, dynamic> _$$CustomLinkHistoryStateImplToJson(
        _$CustomLinkHistoryStateImpl instance) =>
    <String, dynamic>{
      'customLinks': instance.customLinks,
      'isRegisteredNew': instance.isRegisteredNew,
      'currentPage': instance.currentPage,
      'isLoadingMore': instance.isLoadingMore,
      'hasMoreItems': instance.hasMoreItems,
      'totalCount': instance.totalCount,
      'isPullToRefresh': instance.isPullToRefresh,
    };
