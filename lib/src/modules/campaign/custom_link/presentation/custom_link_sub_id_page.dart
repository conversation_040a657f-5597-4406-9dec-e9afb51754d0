import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Add this import for text input formatters
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/model/custom_link.dart';
import 'package:koc_app/src/shared/utils/intent_utils.dart';

class CustomLinkSubIdPage extends StatefulWidget {
  final List<SubId> subIds;
  CustomLinkSubIdPage({super.key, List<SubId>? subIds})
      : subIds = subIds == null || subIds.isEmpty
            ? List.generate(8, (index) => SubId())
            : subIds;

  @override
  State<CustomLinkSubIdPage> createState() => _CustomLinkSubIdPageState();
}

class _CustomLinkSubIdPageState extends State<CustomLinkSubIdPage> {
  bool _isValid = false;
  final List<TextEditingController> _nameTextController =
      List.generate(8, (index) => TextEditingController());
  final List<TextEditingController> _valueTextController =
      List.generate(8, (index) => TextEditingController());
  final List<bool> _hasDuplicateErrors = List.generate(8, (index) => false);

  @override
  void initState() {
    super.initState();
    for (int i = 0; i < widget.subIds.length; i++) {
      SubId subId = widget.subIds[i];
      if (subId.name.isNotEmpty) {
        _nameTextController[i].text = subId.name;
      }
      if (subId.value.isNotEmpty) {
        _valueTextController[i].text = subId.value;
      }
    }
  }

  @override
  void dispose() {
    for (TextEditingController controller
        in _nameTextController + _valueTextController) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: EdgeInsets.symmetric(vertical: 12.r, horizontal: 16.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.r),
            topRight: Radius.circular(12.r),
          ),
        ),
        child: Column(
          spacing: 12.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            _buildBody(),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildButtons() {
    return Expanded(
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Row(
          spacing: 16.r,
          children: [
            Expanded(
              child: ElevatedButton(
                style: Theme.of(context).elevatedButtonTheme.style!.copyWith(
                      backgroundColor: WidgetStateProperty.all(Colors.white),
                      side: WidgetStateProperty.all(BorderSide(
                        color: const Color(0xFFAAAAAA),
                        width: 1.0.r,
                      )),
                    ),
                onPressed: () {
                  for (var controller
                      in _nameTextController + _valueTextController) {
                    controller.text = '';
                  }
                  
                  for (int i = 0; i < _hasDuplicateErrors.length; i++) {
                    _hasDuplicateErrors[i] = false;
                  }

                  setState(() {
                    _isValid = true;
                  });
                },
                child: Text('Clear',
                    style: Theme.of(context)
                        .textTheme
                        .labelLarge!
                        .copyWith(color: Colors.black)),
              ),
            ),
            Expanded(
              child: ElevatedButton(
                onPressed: _isValid
                    ? () {
                        CustomLinkCubit customLinkCubit =
                            ReadContext(context).read<CustomLinkCubit>();
                        CustomLinkState customLink =
                            ReadContext(context).read<CustomLinkCubit>().state;
                        List<SubId> updatedSubIds =
                            Iterable.generate(widget.subIds.length, (i) {
                          return SubId(
                              name: _nameTextController[i].text,
                              value: _valueTextController[i].text);
                        }).toList();
                        customLinkCubit.updateState(
                            customLink.copyWith(subIds: updatedSubIds));
                        Modular.to.pop();
                      }
                    : null,
                child: Text('Save',
                    style: Theme.of(context)
                        .textTheme
                        .labelLarge!
                        .copyWith(color: Colors.white)),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          spacing: 8.r,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'Sub ID',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            GestureDetector(
              onTap: () {
                _showWhatAreSubIdsModal();
              },
              child: Icon(
                Icons.help_outline,
                size: 16.r,
              ),
            )
          ],
        ),
        GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: CircleAvatar(
            backgroundColor: Colors.grey[200],
            radius: 12.r,
            child: Icon(Icons.close, size: 16.r),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
        spacing: 12.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox.shrink(),
          ...Iterable.generate(widget.subIds.length).map((i) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
              children: [
            Row(
              children: [
                _buildTextField(_nameTextController[i], 'Name', i),
                SizedBox(width: 12.r),
                _buildTextField(_valueTextController[i], 'Value', i),
              ],
            ),
            if (_hasDuplicateErrors[i])
              Padding(
                padding: EdgeInsets.only(top: 4.r, bottom: 8.r),
                child: Text(
                  'Duplicate SubID name. Please use unique names.',
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: Colors.red,
                        fontSize: 12.r,
                      ),
                ),
              ),
              ],
            );
          }),
        ]);
  }

  Widget _buildTextField(TextEditingController controller, String label, int index) {
    return Expanded(
      child: SizedBox(
        height: 36.r,
        child: TextField(
          controller: controller,
          style: Theme.of(context).textTheme.labelLarge,
          inputFormatters: [
            LengthLimitingTextInputFormatter(2048),
          ],
          decoration: InputDecoration(
            labelText: label,
            errorStyle: const TextStyle(height: 0),
            enabledBorder: label == 'Name' && _hasDuplicateErrors[index]
                ? OutlineInputBorder(
                    borderSide: const BorderSide(color: Colors.red, width: 1.0),
                    borderRadius: BorderRadius.circular(4.r),
                  )
                : null,
            focusedBorder: label == 'Name' && _hasDuplicateErrors[index]
                ? OutlineInputBorder(
                    borderSide: const BorderSide(color: Colors.red, width: 2.0),
                    borderRadius: BorderRadius.circular(4.r),
                  )
                : null,
          ),
          onChanged: (value) {
            validateSubIds();
          },
        ),
      ),
    );
  }

  void validateSubIds() {
    bool hasValidPair = false;
    bool allPairsValid = true;
    
    for (int i = 0; i < _hasDuplicateErrors.length; i++) {
      _hasDuplicateErrors[i] = false;
    }
    
    for (int i = 0; i < _nameTextController.length; i++) {
      String name = _nameTextController[i].text;
      String value = _valueTextController[i].text;

      if (name.isNotEmpty && value.isNotEmpty) {
        hasValidPair = true;
      }

      if ((name.isNotEmpty && value.isEmpty) || (name.isEmpty && value.isNotEmpty)) {
        allPairsValid = false;
      }
    }

    bool hasDuplicates = false;
    Map<String, List<int>> nameOccurrences = {};

    for (int i = 0; i < _nameTextController.length; i++) {
      String name = _nameTextController[i].text;
      if (name.isNotEmpty) {
        nameOccurrences[name] = [...nameOccurrences[name] ?? [], i];
      }
    }

    for (var entry in nameOccurrences.entries) {
      if (entry.value.length > 1) {
        hasDuplicates = true;
        for (int index in entry.value) {
          _hasDuplicateErrors[index] = true;
        }
      }
    }

    setState(() {
      _isValid = (hasValidPair || allEmptyFields()) && allPairsValid && !hasDuplicates;
    });
  }

  bool allEmptyFields() {
    for (int i = 0; i < _nameTextController.length; i++) {
      if (_nameTextController[i].text.isNotEmpty || _valueTextController[i].text.isNotEmpty) {
        return false;
      }
    }
    return true;
  }

  void _showWhatAreSubIdsModal() async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          actionsAlignment: MainAxisAlignment.end,
          title: Text(
            'What are Sub IDs?',
            style: Theme.of(context)
                .textTheme
                .bodyMedium!
                .copyWith(color: const Color(0xFF464646)),
          ),
          content: Text(
            'Sub IDs are parameters available for you to store tracking information, especially non-unique values. You can use returned tracking information to analyze what leads to more conversions and optimize the performance.',
            style: Theme.of(context)
                .textTheme
                .labelLarge!
                .copyWith(color: const Color(0xFF464646)),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'Learn more',
                style: Theme.of(context)
                    .textTheme
                    .labelLarge!
                    .copyWith(color: const Color(0xFFEF6507)),
              ),
              onPressed: () {
                IntentUtils.openBrowserURL(
                    url:
                        'https://support.accesstrade.global/system-integration/what-are-sub-ids-and-how-to-use.html');
              },
            ),
          ],
        );
      },
    );
  }
}
