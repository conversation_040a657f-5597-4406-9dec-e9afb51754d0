import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_state.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';

class CustomLinkCampaignSearchPage extends StatefulWidget {
  final int? selectedSiteId;
  const CustomLinkCampaignSearchPage({super.key, this.selectedSiteId,});

  @override
  State<CustomLinkCampaignSearchPage> createState() => _CustomLinkCampaignSearchPageState();
}

class _CustomLinkCampaignSearchPageState
    extends BasePageState<CustomLinkCampaignSearchPage, CustomLinkCampaignSearchCubit> with CommonMixin {
  final TextEditingController _controller = TextEditingController();
  late final CustomLinkCampaignSearchCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = context.read<CustomLinkCampaignSearchCubit>();
    _loadCampaignData();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final searchText = _cubit.state.searchText;
      if (searchText.isNotEmpty) {
        _controller.text = searchText;
        _cubit.updateSearchText(searchText);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _loadCampaignData() async {
    try {
      commonCubit.loadingVisibility(true);
      await _cubit.getAffiliatedCampaigns(siteId: widget.selectedSiteId);

      final searchText = _cubit.state.searchText;
      if (searchText.isNotEmpty) {
        _cubit.updateSearchText(searchText);
      }
    } catch (e) {
      if (context.mounted) {
        context.showSnackBar('Error loading campaigns');
      }
    } finally {
      commonCubit.loadingVisibility(false);
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<CustomLinkCampaignSearchCubit, CustomLinkCampaignSearchState>(
      bloc: _cubit,
      builder: (context, state) {
        if (state.isLoadingCampaigns) {
          return const SizedBox.shrink();
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Column(
            spacing: 16.r,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [_buildHeader('Campaign'), _buildTextField(), _buildSearchResultContent(state)],
          ),
        );
      },
    );
  }

  Widget _buildSearchResultContent(CustomLinkCampaignSearchState state) {
    if (state.filteredCampaigns.isEmpty) {
      return Expanded(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.search_off, size: 48.r, color: Colors.grey),
              SizedBox(height: 12.r),
              Text(
                state.searchText.isEmpty ? 'Not found any campaigns' : 'No campaigns found for "${state.searchText}"',
                style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.grey),
              )
            ],
          ),
        ),
      );
    }

    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r)),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: Iterable.generate(state.filteredCampaigns.length, (index) {
                CustomCreativeEnabledCampaign campaign = state.filteredCampaigns[index];
                List<String> names = splitString(campaign.campaignName, state.searchText);
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        final selectedCampaign = campaign;
                        _cubit.selectCampaign(selectedCampaign);
                        Navigator.of(context).pop({'selectedCampaign': selectedCampaign});
                      },
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
                        color: state.selectedCampaign?.campaignId == campaign.campaignId
                            ? const Color(0x4DFFB522)
                            : Colors.white,
                        child: RichText(
                            text: TextSpan(
                                style: Theme.of(context).textTheme.labelLarge,
                                children: names
                                    .map((textItem) => TextSpan(
                                        text: textItem,
                                        style: textItem.toLowerCase() == state.searchText.toLowerCase()
                                            ? Theme.of(context).textTheme.labelLarge!
                                            : null))
                                    .toList())),
                      ),
                    ),
                    if (index < state.filteredCampaigns.length - 1)
                      Divider(
                        color: const Color(0xFFE7E7E7),
                        height: 1.r,
                      )
                  ],
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField() {
    return TextField(
      controller: _controller,
      style: Theme.of(context).textTheme.labelLarge,
      decoration: InputDecoration(
          labelText: 'Search',
          labelStyle: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676))),
      onChanged: (value) {
        _cubit.updateSearchText(value);
      },
    );
  }

  Widget _buildHeader(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall,
          ),
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: CircleAvatar(
              backgroundColor: Colors.grey[200],
              radius: 12.r,
              child: Icon(Icons.close, size: 16.r),
            ),
          ),
        ],
      ),
    );
  }

  List<String> splitString(String s, String delimiter) {
    if (delimiter.isNotEmpty) {
      String sLower = s.toLowerCase();
      String delimiterLower = delimiter.toLowerCase();

      List<String> parts = [];
      int start = 0;

      while (true) {
        int index = sLower.indexOf(delimiterLower, start);
        if (index == -1) {
          parts.add(s.substring(start));
          break;
        }
        parts.add(s.substring(start, index));
        parts.add(s.substring(index, index + delimiter.length));
        start = index + delimiter.length;
      }

      return parts;
    }
    return [s];
  }
}
