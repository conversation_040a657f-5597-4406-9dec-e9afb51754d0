import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_state.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_state.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/presentation/custom_link_campaign_search_page.dart';
import 'package:koc_app/src/modules/campaign/custom_link/presentation/custom_link_history_page.dart';
import 'package:koc_app/src/modules/campaign/custom_link/presentation/widget/custom_link_generation_view.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';

class CustomLinkGenerationPage extends StatefulWidget {
  const CustomLinkGenerationPage({super.key});

  @override
  State<CustomLinkGenerationPage> createState() => _CustomLinkGenerationPageState();
}

class _CustomLinkGenerationPageState extends State<CustomLinkGenerationPage> with CommonMixin {
  late final CustomLinkCampaignSearchCubit _cubit = ReadContext(context).read<CustomLinkCampaignSearchCubit>();
  final _viewKey = GlobalKey<CustomLinkGenerationViewState>();
  int? selectedSiteId;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
      bottomSheet: _buildGenerateButton(),
    );
  }

  Widget _buildGenerateButton() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.r),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: (_viewKey.currentState?.isValid ?? false)
              ? () async {
                  await _viewKey.currentState?.generate();
                }
              : null,
          child: Text(
            'Generate',
            style: context.textLabelLarge(color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader('Generate Link'),
            SizedBox(height: 16.r),
            buildCustomSelector(_buildSiteName(), () async {
              int? result = await showSiteSelectionBottomSheet(context,
                  canSelectSameId: true, selectedSiteId: selectedSiteId ?? 0, doUpdatingCubit: false);
              if (result != null) {
                _cubit.clearCampaign();
                setState(() {
                  selectedSiteId = result;
                });
                await _cubit.getAffiliatedCampaigns(siteId: result);
              }
            }),
            SizedBox(height: 16.r),
            buildCustomSelector(_buildCampaignName(), () => _showCampaignSelectorModal()),
            BlocBuilder<CustomLinkCampaignSearchCubit, CustomLinkCampaignSearchState>(
                bloc: _cubit,
                builder: (context, state) {
                  return CustomLinkGenerationView(
                      key: _viewKey,
                      siteId: selectedSiteId,
                      campaignId: state.selectedCampaign?.campaignId,
                      customLinkAcceptUrls: state.selectedCampaign?.shouldStartWithDomain,
                      onReload: _reloadPage,
                      onChange: _onChange);
                })
          ],
        ),
      ),
    );
  }

  void _onChange() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {});
    });
  }

  Widget _buildSiteName() {
    return BlocBuilder<SiteCubit, SiteState>(
        bloc: Modular.get<SiteCubit>(),
        builder: (_, state) {
          if (state.sites.isEmpty) {
            return const SizedBox.shrink();
          }
          return Text(
            selectedSiteId != null ? state.sites.firstWhere((site) => site.id == selectedSiteId).name : 'Property',
            style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)),
          );
        });
  }

  Widget _buildCampaignName() {
    return BlocBuilder<CustomLinkCampaignSearchCubit, CustomLinkCampaignSearchState>(
      bloc: _cubit,
      builder: (context, state) {
        if (state.selectedCampaign != null) {
          return SizedBox(
            width: MediaQuery.of(context).size.width * 0.7,
            child: Text(
              state.selectedCampaign!.campaignName,
              style: Theme.of(context).textTheme.labelLarge,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          );
        }
        return Text(
          'Campaign',
          style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)),
        );
      },
    );
  }

  Widget _buildHeader(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall,
          ),
          Row(
            spacing: 12.r,
            children: [
              GestureDetector(
                onTap: () {
                  _showCustomLinkHistoryModal();
                },
                child: CircleAvatar(
                  backgroundColor: Colors.grey[200],
                  radius: 12.r,
                  child: Icon(Icons.history, size: 16.r),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: CircleAvatar(
                  backgroundColor: Colors.grey[200],
                  radius: 12.r,
                  child: Icon(Icons.close, size: 16.r),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showCustomLinkHistoryModal() {
    CustomLinkHistoryCubit historyCubit = ReadContext(context).read<CustomLinkHistoryCubit>();
    showModalBottomSheet(
      useSafeArea: true,
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return BlocProvider.value(
          value: historyCubit,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: CustomLinkHistoryPage(campaignId: _cubit.state.selectedCampaign?.campaignId),
          ),
        );
      },
    );
  }

  Future<void> _showCampaignSelectorModal() async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.white,
      builder: (_) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: BlocProvider.value(
              value: _cubit,
              child: CustomLinkCampaignSearchPage(
                selectedSiteId: selectedSiteId,
              )),
        );
      },
    );
  }

  void _reloadPage() {
    selectedSiteId = null;
    _onChange();
  }
}
