import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/data/model/publisher_sites.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';
import 'package:koc_app/src/shared/widgets/social_media_icon.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/empty_state_widget.dart';

class CustomLinkHistoryPage extends StatefulWidget {
  final int? campaignId;

  const CustomLinkHistoryPage({super.key, this.campaignId});

  @override
  State<CustomLinkHistoryPage> createState() => _CustomLinkHistoryPageState();
}

class _CustomLinkHistoryPageState extends State<CustomLinkHistoryPage> with FilterMixin, CommonMixin {
  late CustomLinkHistoryCubit historyCubit = ReadContext(context).read<CustomLinkHistoryCubit>();
  late FilterCubit filterCubit = Modular.get<FilterCubit>();
  late CommonCubit commonCubit = Modular.get<CommonCubit>();
  late final siteCubit = Modular.get<SiteCubit>();
  late final CustomLinkCampaignSearchCubit _cubit;
  List<Item> _customCampaigns = [];
  bool _hasSearched = false;
  bool _wasFilterCleared = false;
  int? _previousSiteId;

  final ScrollController _scrollController = ScrollController();
  static const double _loadMoreThreshold = 0.8;

  @override
  void initState() {
    _cubit = Modular.get<CustomLinkCampaignSearchCubit>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _hasSearched = false;
      updateFilterAfterGeneration();
      _initData();
    });

    filterCubit.stream.listen((state) {
      if (state == FilterState()) {
        _wasFilterCleared = true;
      }
    });
    _scrollController.addListener(_onScroll);
    filterCubit.stream.listen((state) async {
      if (state.campaigns.any((campaign) => campaign.value == 0)) {
        if (_customCampaigns.isEmpty) {
          _customCampaigns = await _cubit.getCustomCreativeEnabledCampaigns();
        }
        if (_customCampaigns.isNotEmpty) {
          if (filterCubit.state.selectedCampaign != null) {
            filterCubit.selectCampaign(filterCubit.state.selectedCampaign);
          } else {
            filterCubit.selectCampaign(_customCampaigns[0]);
          }
          filterCubit.updateCampaigns(_customCampaigns);
        }
      }
      if (state.selectedSite != null && state.selectedSite?.value != _previousSiteId) {
        filterCubit.showLoading();
        _previousSiteId = state.selectedSite?.value;
        _customCampaigns = await _cubit.getCustomCreativeEnabledCampaigns(siteId: _previousSiteId);
        filterCubit.updateSearchEnabled(_customCampaigns.isNotEmpty);
        if (_customCampaigns.isNotEmpty) {
          filterCubit.updateCampaigns(_customCampaigns);
          filterCubit.selectCampaign(_customCampaigns[0]);
          filterCubit.hideLoading();
        }
        if (_customCampaigns.isEmpty) {
          filterCubit.updateCampaigns([]);
          filterCubit.selectCampaign(null);
          filterCubit.hideLoading();
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              showDialog(
                context: context,
                barrierDismissible: true,
                builder: (BuildContext context) {
                  return AlertDialog(
                    backgroundColor: Colors.white,
                    contentPadding: EdgeInsets.zero,
                    content: Container(
                      width: 312,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: Colors.white,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: 312,
                            padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                if (filterCubit.state.selectedSite != null) ...[
                                  SizedBox(
                                    height: 16.r,
                                    width: 16.r,
                                    child: SocialMediaIcon(
                                      url: siteCubit.state.sites
                                          .firstWhere((site) => site.id == filterCubit.state.selectedSite!.value)
                                          .url,
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                ],
                                Flexible(
                                  child: Text(
                                    filterCubit.state.selectedSite?.name ?? 'Property Name',
                                    style: Theme.of(context).textTheme.titleLarge!.copyWith(
                                          fontFamily: 'Metropolis',
                                          fontSize: 16,
                                          height: 24 / 16,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF464646),
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: 312.r,
                            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                            child: Text(
                              'This property has no custom links',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelLarge!
                            ),
                          ),
                        ],
                      ),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  );
                },
              );
            }
          });
        }
      }
    });
    super.initState();
  }

  Future<void> updateFilterAfterGeneration() async {
    if (widget.campaignId != null) {
      final campaigns = await _cubit.getCustomCreativeEnabledCampaigns();
      final selectedCampaign = campaigns.firstWhere(
        (campaign) => campaign.value == widget.campaignId,
        orElse: () => campaigns.first,
      );
      filterCubit.selectCampaign(selectedCampaign);
      filterCubit.selectPeriod(ReportPeriod.LAST_7_DAYS);
      List<PublisherSite> sites = siteCubit.state.sites;
      if (sites.isNotEmpty) {
        final currentSite = sites.firstWhere(
          (site) => site.id == siteCubit.state.currentSiteId,
          orElse: () => sites.first,
        );
        filterCubit.selectSite(Item(
          value: currentSite.id,
          name: currentSite.name,
        ));
        _hasSearched = true;
        setState(() {});
      }
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _hasSearched = false;
    filterCubit.clear();
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;

    if (currentScroll >= maxScroll * _loadMoreThreshold) {
      final campaignId = widget.campaignId ?? (Modular.args.data?.campaignId);
      if (campaignId != null) {
        FilterState filterState = filterCubit.state;
        historyCubit.loadMore(
          campaignId,
          customName: filterState.customName,
          fromDate: filterState.startDate,
          toDate: filterState.endDate,
        );
      }
    }
  }

  Future<void> _initData({String? customName, DateTime? fromDate, DateTime? toDate}) async {
    if (!historyCubit.state.isPullToRefresh) {
      showLoadingDialog(context);
    }

    final campaignId = widget.campaignId ?? (Modular.args.data?.campaignId);

    if (campaignId != null) {
      await historyCubit.getCustomLinks(campaignId, customName: customName, fromDate: fromDate, toDate: toDate);
    }

    if (!historyCubit.state.isPullToRefresh) {
      hideLoadingDialog(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (historyCubit.state.isRegisteredNew) {
        context.showSnackBar('Affiliate link is generated successfully!');
        historyCubit.setRegisteredNewCustomLink(false);
      }
    });

    return Scaffold(
      body: Container(
        padding: EdgeInsets.only(top: 12.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.r),
            topRight: Radius.circular(12.r),
          ),
        ),
        child: Column(
          spacing: 16.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 16.r),
            _buildActiveFilters(),
            SizedBox(height: 16.r),
            _buildBody(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'History',
            style: context.textTitleSmall(),
          ),
          Row(
            spacing: 12.r,
            children: [
              GestureDetector(
                onTap: () {
                  _showFilterModalBottomSheet();
                },
                child: CircleAvatar(
                  backgroundColor: Colors.grey[200],
                  radius: 12.r,
                  child: Icon(Icons.tune, size: 16.r),
                ),
              ),
              GestureDetector(
                onTap: () {
                  historyCubit.clear();
                  Navigator.pop(context);
                },
                child: CircleAvatar(
                  backgroundColor: Colors.grey[200],
                  radius: 12.r,
                  child: Icon(Icons.close, size: 16.r),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _showFilterModalBottomSheet() async {
    filterCubit.showLoading();

    final campaigns = await _cubit.getCustomCreativeEnabledCampaigns();
    _customCampaigns = campaigns;

    final periods = [
      ReportPeriod.LAST_7_DAYS,
      ReportPeriod.LAST_14_DAYS,
      ReportPeriod.THIS_WEEK,
      ReportPeriod.LAST_WEEK,
      ReportPeriod.CUSTOM_RANGE
    ];

    filterCubit.hideLoading();

    bool? result = await showFilters(
      context,
      periods,
      showSites: true,
      showCustomName: true,
      showDateType: false,
      showStatus: false,
      showCampaigns: true,
      filterButtonName: 'Search',
      preserveState: true,
    );

    if (result == null) {
      if (_wasFilterCleared) {
        _hasSearched = false;
        historyCubit.clear();
        setState(() {});
      }
      return;
    }

    if (result) {
      _hasSearched = true;
      setState(() {});
      FilterState filterState = filterCubit.state;
      showLoadingDialog(context);
      if (filterState.selectedCampaign != null) {
        await historyCubit.getCustomLinks(
          filterState.selectedCampaign!.value,
          customName: filterState.customName,
          fromDate: filterState.startDate,
          toDate: filterState.endDate,
        );
      }
      hideLoadingDialog(context);
    }
  }

  Widget _buildBody() {
    return BlocBuilder<CustomLinkHistoryCubit, CustomLinkHistoryState>(builder: (_, historyState) {
      return Expanded(
        child: Padding(
          padding: EdgeInsets.only(left: 16.r, right: 16.r, bottom: 16.r),
          child: Column(
            children: [
              Expanded(
                child: (historyState.customLinks?.content.isEmpty ?? true)
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (!_hasSearched && !historyState.isRegisteredNew) ...[
                              SvgPicture.asset(
                                'assets/images/search.svg',
                                width: 64.r,
                                height: 64.r,
                              ),
                              SizedBox(height: 16.r),
                              Text(
                                'Add filters to determine what data is shown in your history list',
                                style: Theme.of(context).textTheme.labelLarge!.copyWith(
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                    ),
                                textAlign: TextAlign.center,
                              ),
                            ] else ...[
                              EmptyStateWidget(
                                onButtonPressed: () async {
                                  filterCubit.clear();
                                  _hasSearched = false;
                                  _wasFilterCleared = true;
                                  historyCubit.clear();
                                  await _initData();
                                  setState(() {});
                                },
                              ),
                            ],
                          ],
                        ),
                      )
                    : PullToRefreshWrapper(
                        onRefresh: () => _performPullToRefresh(),
                        child: ListView(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          children: [
                            Column(
                              spacing: 16.r,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: (historyState.customLinks?.content ?? [])
                                  .map((item) => [
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              spacing: 4.r,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(
                                                  width: MediaQuery.of(context).size.width - 90.r,
                                                  child: Text(item.name,
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .labelLarge!
                                                          .copyWith(fontWeight: FontWeight.w500),
                                                      overflow: TextOverflow.ellipsis,
                                                      maxLines: 1),
                                                ),
                                                SizedBox(
                                                  width: MediaQuery.of(context).size.width - 90.r,
                                                  child: Text(item.affiliateLink,
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .labelLarge!
                                                          .copyWith(color: const Color(0xFF767676)),
                                                      overflow: TextOverflow.ellipsis,
                                                      maxLines: 1),
                                                ),
                                                Container(
                                                  padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
                                                  decoration: BoxDecoration(
                                                      color: const Color(0xFFF5F5F7),
                                                      borderRadius: BorderRadius.circular(9999),
                                                      border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r)),
                                                  child: Text(
                                                      'Created: ${DateTime.parse(item.createdOn).toDateMonthYear()}',
                                                      style: Theme.of(context).textTheme.labelMedium!.copyWith(
                                                          color: const Color(0xFF767676), fontWeight: FontWeight.w500)),
                                                )
                                              ],
                                            ),
                                            IconButton(
                                                onPressed: () {
                                                  Share.share(item.affiliateLink);
                                                },
                                                icon: Icon(
                                                  Icons.share_outlined,
                                                  size: 24.r,
                                                ))
                                          ],
                                        ),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Divider(
                                                color: const Color(0xFFE7E7E7),
                                                height: 16.r,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ])
                                  .expand((element) => element)
                                  .toList(),
                            ),
                            if (historyState.isLoadingMore)
                              Padding(
                                padding: EdgeInsets.symmetric(vertical: 16.r),
                                child: const Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Color(0xFFFFB522),
                                  ),
                                ),
                              ),
                            if (!historyState.isLoadingMore &&
                                !historyState.hasMoreItems &&
                                (historyState.customLinks?.content.length ?? 0) > 10 &&
                                (historyState.customLinks?.content.isNotEmpty ?? false))
                              Padding(
                                padding: EdgeInsets.symmetric(vertical: 16.r),
                                child: Center(
                                  child: Text(
                                    'No more custom links available',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          color: Colors.grey[600],
                                        ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildActiveFilters() {
    return BlocBuilder<FilterCubit, FilterState>(
      bloc: filterCubit,
      builder: (context, state) {
        if (state.customName.isEmpty &&
            state.startDate == null &&
            state.endDate == null &&
            state.selectedSite == null &&
            state.selectedCampaign == null) {
          return const SizedBox.shrink();
        }

        if (!_hasSearched) {
          return const SizedBox.shrink();
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Wrap(
            spacing: 8.r,
            runSpacing: 8.r,
            children: [
              if (state.selectedSite != null)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F7),
                    borderRadius: BorderRadius.circular(9999),
                    border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
                  ),
                  child: Text(
                    'Property Name: ${state.selectedSite!.name}',
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium!
                        .copyWith(color: const Color(0xFF767676), fontWeight: FontWeight.w500),
                  ),
                ),
              if (state.selectedCampaign != null)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F7),
                    borderRadius: BorderRadius.circular(9999),
                    border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
                  ),
                  child: Text(
                    '${state.selectedCampaign!.name}',
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium!
                        .copyWith(color: const Color(0xFF767676), fontWeight: FontWeight.w500),
                  ),
                ),
              if (state.customName.isNotEmpty)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F7),
                    borderRadius: BorderRadius.circular(9999),
                    border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
                  ),
                  child: Text(
                    state.customName,
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium!
                        .copyWith(color: const Color(0xFF767676), fontWeight: FontWeight.w500),
                  ),
                ),
              if (state.selectedPeriod != ReportPeriod.CUSTOM_RANGE)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F7),
                    borderRadius: BorderRadius.circular(9999),
                    border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
                  ),
                  child: Text(
                    _getPeriodText(state.selectedPeriod),
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium!
                        .copyWith(color: const Color(0xFF767676), fontWeight: FontWeight.w500),
                  ),
                ),
              if (state.selectedPeriod == ReportPeriod.CUSTOM_RANGE && state.startDate != null && state.endDate != null)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F7),
                    borderRadius: BorderRadius.circular(9999),
                    border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
                  ),
                  child: Text(
                    '${state.startDate!.toDateMonthYear()} - ${state.endDate!.toDateMonthYear()}',
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium!
                        .copyWith(color: const Color(0xFF767676), fontWeight: FontWeight.w500),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  String _getPeriodText(ReportPeriod period) {
    switch (period) {
      case ReportPeriod.LAST_7_DAYS:
        return 'Last 7 Days';
      case ReportPeriod.LAST_14_DAYS:
        return 'Last 14 Days';
      case ReportPeriod.THIS_WEEK:
        return 'This Week';
      case ReportPeriod.LAST_WEEK:
        return 'Last Week';
      case ReportPeriod.CUSTOM_RANGE:
        return 'Custom Range';
      default:
        return period.toString().split('.').last.replaceAll('_', ' ').titleCase;
    }
  }

  /// Perform pull-to-refresh with current filter state
  Future<void> _performPullToRefresh() async {
    final filterState = filterCubit.state;
    if (filterState.selectedCampaign != null) {
      await historyCubit.pullToRefresh(
        filterState.selectedCampaign!.value,
        customName: filterState.customName.isNotEmpty ? filterState.customName : null,
        fromDate: filterState.startDate,
        toDate: filterState.endDate,
      );
    }
  }
}

extension StringExtension on String {
  String get titleCase {
    if (isEmpty) return this;
    return split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }
}
