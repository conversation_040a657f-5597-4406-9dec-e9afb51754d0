import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';

import '../shared/services/secure_storage_helper.dart';

class AuthGuard extends RouteGuard {
  AuthGuard() : super(redirectTo: '/');
  static final storage = SecureStorageHelper();

  @override
  Future<bool> canActivate(String path, ParallelRoute route) async {
    final accessToken = await storage.read(InstanceConstants.tokenKey);
    final refreshToken =
        await storage.read(InstanceConstants.refreshTokenKey);

    if (accessToken == null || refreshToken == null) {
      return false;
    }
    return true;
  }
}
