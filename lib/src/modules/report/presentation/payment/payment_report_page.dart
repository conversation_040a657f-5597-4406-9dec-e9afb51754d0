import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_process_summary.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/modules/shared/widget/no_data.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class PaymentReportPage extends StatefulWidget {
  const PaymentReportPage({super.key});

  @override
  State<PaymentReportPage> createState() => _PaymentReportPageState();
}

class _PaymentReportPageState extends BasePageState<PaymentReportPage, PaymentReportCubit>
    with ReportMixin, FilterMixin, CommonMixin {
  late FilterCubit reportFilterCubit = Modular.get<FilterCubit>()..clear();
  late ScaffoldMessengerState _scaffoldMessengerState;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _scaffoldMessengerState = ScaffoldMessenger.of(context);
  }

  @override
  void dispose() {
    _scaffoldMessengerState.clearSnackBars();
    super.dispose();
  }

  @override
  void initState() {
    Modular.get<FilterCubit>().clear();
    _initData();
    super.initState();
  }

  void _initData() {
    if (cubit.state.isPullToRefresh) {
      _loadPaymentData();
    } else {
      doLoadingAction(() async {
        await _loadPaymentData();
      });
    }
  }

  void _showNotFoundMessage() {
    if (cubit.state.payments.isEmpty) {
      context.showSnackBar('No invoices found! Please try another search.');
    }
  }

  Future<void> _loadPaymentData() async {
    DateTimeRange range = getTimeRange(ReportPeriod.LAST_12_MONTHS, null, null);
    await cubit.findPayments(range.start, range.end, null);
    _showNotFoundMessage();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: const Text('Payment'),
        customAction: IconButton(
          onPressed: () async {
            bool? result = await showFilters(
              context,
              [ReportPeriod.LAST_12_MONTHS, ReportPeriod.THIS_YEAR, ReportPeriod.LAST_YEAR, ReportPeriod.CUSTOM_RANGE],
              showCampaigns: false,
              showDateType: false,
              showStatus: false,
              showInvoiceNumber: true,
              isScrollControlled: false,
            );
            if (result != null) {
              if (cubit.state.isPullToRefresh) {
                FilterState reportFilterState = Modular.get<FilterCubit>().state;
                DateTimeRange range = getTimeRange(reportFilterState.selectedPeriod, reportFilterState.customStartDate,
                    reportFilterState.customEndDate);
                await cubit.findPayments(range.start, range.end, reportFilterState.invoiceId);
                _showNotFoundMessage();
              } else {
                doLoadingAction(() async {
                  FilterState reportFilterState = Modular.get<FilterCubit>().state;
                  DateTimeRange range = getTimeRange(reportFilterState.selectedPeriod,
                      reportFilterState.customStartDate, reportFilterState.customEndDate);
                  await cubit.findPayments(range.start, range.end, reportFilterState.invoiceId);
                  _showNotFoundMessage();
                });
              }
            }
          },
          icon: Icon(
            Icons.search,
            size: 20.r,
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<PaymentReportCubit, PaymentReportState>(builder: (_, state) {
      // Show content with pull-to-refresh when data is available
      if (state != PaymentReportState()) {
        return PullToRefreshWrapper(
          onRefresh: () => cubit.pullToRefresh(),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                spacing: 16.r,
                children: [
                  _buildMinimumPaymentAmount(state),
                  _buildProcessStage(state),
                  _buildContent(state),
                ],
              ),
            ),
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  void _selectCheckbox() {
    List<PaymentReportData> selectedPayments = cubit.state.payments.where((e) => e.isCheck).toList();
    if (selectedPayments.isNotEmpty) {
      context.showSnackBar('${selectedPayments.length} selected', durationSecond: 0, actionTitle: 'Download',
          actionTap: () {
        saveCsvToDownloadFolder(context, _convertToCsv(selectedPayments, cubit.state.currency), 'PaymentReport');
      });
    } else {
      context.clearSnackBars();
    }
  }

  Widget _buildDataTable(PaymentReportState state) {
    return Row(
      children: [
        DataTable(
          headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
          columnSpacing: 16.r,
          columns: [
            DataColumn(
              label: SizedBox(
                width: 20.r,
                child: Checkbox(
                  value: state.payments.where((e) => e.isCheck).length == state.payments.length,
                  onChanged: (value) {
                    cubit.toggleAllPayments();
                    _selectCheckbox();
                  },
                ),
              ),
            ),
            buildDataColumn(context, 'Invoice'),
          ],
          rows: state.payments.map((e) {
            return DataRow(cells: [
              DataCell(SizedBox(
                width: 20.r,
                child: Checkbox(
                  value: e.isCheck,
                  onChanged: (value) {
                    cubit.togglePayment(e.invoiceNumber);
                    _selectCheckbox();
                  },
                ),
              )),
              buildDataCell(context, e.invoiceNumber, color: const Color(0xFFEF6507), onTap: () {
                context.clearSnackBars();
                Modular.to.pushNamed('/report/payment/invoice', arguments: e.invoiceNumber);
              }),
            ]);
          }).toList(),
        ),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
              columns: [
                buildDataColumn(context, 'Approved Month'),
                buildDataColumn(context, 'Reward (${state.currency.currencySymbol})'),
                buildDataColumn(context, 'VAT'),
                buildDataColumn(context, 'WHT'),
                buildDataColumn(context, 'Paid Amount'),
                buildDataColumn(context, 'Paid Date'),
              ],
              rows: state.payments.map((e) {
                return DataRow(cells: [
                  buildDataCell(
                      context, DateFormat(yearMonthFormat).parse(e.rewardApprovedMonthPeriod!.from).toMonthAndYear()),
                  buildDataCell(context, e.totalAmount.toCommaSeparated()),
                  buildDataCell(context, e.vat.toCommaSeparated()),
                  buildDataCell(context, e.wht.toCommaSeparated()),
                  buildDataCell(context, e.paidAmount.toCommaSeparated()),
                  buildDataCell(context, e.paymentDate),
                ]);
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  _buildNoDataReportWidget() {
    return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: double.infinity,
            height: 56.r,
            padding: EdgeInsets.all(16.r),
            color: const Color(0xFFF2F2F2),
            child: Text(
              'Invoice',
              style: context.textLabelLarge(fontWeight: FontWeight.bold),
            ),
          ),
          Padding(padding: EdgeInsets.symmetric(vertical: 32.r), child: const NoDataReportWidget())
        ]);
  }

  Widget _buildContent(PaymentReportState state) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: const Color(0x1F000000),
            width: 1.r,
          )),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: state.payments.isNotEmpty ? _buildDataTable(state) : _buildNoDataReportWidget()),
    );
  }

  Widget _buildProcessStage(PaymentReportState state) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Process stages',
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProcessStageCard(
                'Reward Approved',
                'Reward amount that has been validated and approved but pending payment from merchant',
                state.paymentSummary!.rewardApproved, () {
              context.clearSnackBars();
              Modular.to.pushNamed('/report/payment/details', arguments: PaymentProcessType.REWARD_APPROVED);
            }, 6.5, state.currency, showRedirectionButton: state.paymentSummary!.rewardApproved > 0),
            _buildProcessStageCard('Payment in Progress', 'Amount that will be paid in this month.',
                state.paymentSummary!.rewardProcessedPayment, () {
              context.clearSnackBars();
              Modular.to
                  .pushNamed('/report/payment/details', arguments: PaymentProcessType.REWARD_BEING_PROCESSED_PAYMENT);
            }, 6.5, state.currency, showRedirectionButton: state.paymentSummary!.rewardProcessedPayment > 0),
            _buildProcessStageCard(
                'Payment Held\nUntil Requirement Met',
                'Payment is currently on hold until the minimum payment amount is being achieved.\n\nFor company, the payment will be on hold until the minimum is achieved and an invoice is issued to our finance team.',
                state.paymentSummary!.rewardToBePaid, () {
              context.clearSnackBars();
              Modular.to.pushNamed(
                '/report/payment/details',
                arguments: PaymentProcessType.REWARD_TO_BE_PAID,
              );
            }, 8, state.currency,
                showBottomPadding: false, showRedirectionButton: state.paymentSummary!.rewardToBePaid > 0),
          ],
        ),
      ],
    );
  }

  Widget _buildProcessStageCard(
      String title, String description, double value, VoidCallback onTap, double heightFactor, String currency,
      {bool showBottomPadding = true, bool showRedirectionButton = true}) {
    return IntrinsicHeight(
      child: Row(
        spacing: 5.r,
        children: [
          SizedBox(
            width: 15.r,
            child: Stack(
              children: [
                Center(
                  child: VerticalDivider(
                    width: 1.r,
                  ),
                ),
                Center(
                  heightFactor: heightFactor,
                  child: Container(
                    width: 10.r,
                    height: 10.r,
                    decoration: const BoxDecoration(color: Color(0xFFFFB522), shape: BoxShape.circle),
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(bottom: showBottomPadding ? 16.r : 0),
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: ColorConstants.borderColor, width: 1.r)),
                child: _buildCardContent(
                  Text(
                    value.toPrice(currency),
                    style: context.textBodySmall(fontWeight: FontWeight.w500),
                  ),
                  title,
                  description,
                  action: showRedirectionButton
                      ? IconButton(
                          onPressed: onTap,
                          icon: Icon(
                            Icons.arrow_forward_outlined,
                            size: 20.r,
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMinimumPaymentAmount(PaymentReportState state) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r), border: Border.all(color: ColorConstants.borderColor, width: 1.r)),
      child: _buildCardContent(
          ClipRRect(
            borderRadius: BorderRadius.circular(9999),
            child: Stack(
              children: [
                LinearProgressIndicator(
                  color: state.paymentSummary!.availablePayment < state.minimumPaymentDetails!.minimumAmount
                      ? const Color(0xFFFFB522)
                      : const Color(0xFF1AAA55),
                  backgroundColor: Colors.grey[400],
                  value: state.paymentSummary!.availablePayment / state.minimumPaymentDetails!.minimumAmount,
                  minHeight: 28.r,
                ),
                Container(
                  padding: EdgeInsets.only(left: 12.r),
                  alignment: Alignment.centerLeft,
                  height: 28.r,
                  child: Text(
                    state.paymentSummary!.availablePayment.toPrice(state.currency),
                    style: Theme.of(context)
                        .textTheme
                        .labelLarge!
                        .copyWith(fontWeight: FontWeight.w500, color: Colors.white),
                  ),
                )
              ],
            ),
          ),
          'Minimum payment amount (${state.minimumPaymentDetails!.minimumAmount.toPrice(state.minimumPaymentDetails!.currency)})',
          'Minimum payment for ${state.country!.name.toTitleCase()} bank account is ${state.country!.localCurrencyCode} ${state.minimumPaymentDetails!.minimumAmount.toCommaSeparated()}, International bank is ${state.country!.internationalCurrencyCode} ${state.country!.internationalMinimumPayment.toCommaSeparated()}'),
    );
  }

  Widget _buildCardContent(Widget data, String description, String dialogContents, {Widget? action}) {
    final lines = description.split('\n');
    return Padding(
      padding: EdgeInsets.all(12.r),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                data,
                ...List.generate(lines.length, (i) {
                  final isLast = i == lines.length - 1;
                  return Row(
                    spacing: 4.r,
                    children: [
                      Text(
                        lines[i],
                        style: Theme.of(context)
                            .textTheme
                            .labelLarge!
                            .copyWith(fontWeight: FontWeight.w500, color: const Color(0xFF767676)),
                      ),
                      if (isLast)
                        GestureDetector(
                          onTap: () {
                            showDescription(context, description, dialogContents);
                          },
                          child: Icon(
                            Icons.help_outline,
                            size: 16.r,
                            color: const Color(0xFF767676),
                          ),
                        ),
                    ],
                  );
                }),
              ],
            ),
          ),
          if (action != null) action
        ],
      ),
    );
  }

  List<List<dynamic>> _convertToCsv(List<PaymentReportData> reportData, String currency) {
    List<List<dynamic>> result = [
      ['Invoice', 'Approved Month', 'Reward (${currency.currencySymbol})', 'VAT', 'WHT', 'Paid Amount', 'Paid Date']
    ];

    for (var report in reportData) {
      result.add([
        report.invoiceNumber,
        DateFormat(yearMonthFormat).parse(report.rewardApprovedMonthPeriod!.from).toMonthAndYear(),
        report.totalAmount.toCommaSeparated(),
        report.vat.toCommaSeparated(),
        report.wht.toCommaSeparated(),
        report.paidAmount.toCommaSeparated(),
        DateFormat(fullDateFormat).parse(report.paymentDate).toMonthAndYear()
      ]);
    }
    return result;
  }
}
