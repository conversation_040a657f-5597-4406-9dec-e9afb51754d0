import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_details_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_details_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_process_summary.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/widget/no_data.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/common_tab.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

import '../../../../shared/constants/color_constants.dart';

class PaymentDetailsReportPage extends StatefulWidget {
  final PaymentProcessType processType;
  const PaymentDetailsReportPage(this.processType, {super.key});

  @override
  State<PaymentDetailsReportPage> createState() => _PaymentDetailsReportPageState();
}

class _PaymentDetailsReportPageState extends BasePageState<PaymentDetailsReportPage, PaymentDetailsReportCubit>
    with ReportMixin, SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;

  @override
  void initState() {
    _initData();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedIndex = _tabController.index;
      });
    });
    super.initState();
  }

  void _initData() {
    if (cubit.state.isPullToRefresh) {
      _loadPaymentDetailsData();
    } else {
      doLoadingAction(() async {
        await _loadPaymentDetailsData();
      });
    }
  }

  Future<void> _loadPaymentDetailsData() async {
    await cubit.findPaymentProcessStageDetails(widget.processType);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: Text(widget.processType.title),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<PaymentDetailsReportCubit, PaymentDetailsReportState>(builder: (_, state) {
      if (state != PaymentDetailsReportState()) {
        return PullToRefreshWrapper(
          onRefresh: () => cubit.pullToRefresh(widget.processType),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.all(16.r),
                child: buildTotalRewardCard(context, state.currency, _getTotalReward(state)),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.r).copyWith(bottom: 16.r),
                  child: _buildTable(state),
                ),
              ),
            ],
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  Widget _buildTable(PaymentDetailsReportState state) {
    if (widget.processType == PaymentProcessType.REWARD_APPROVED ||
        widget.processType == PaymentProcessType.REWARD_TO_BE_PAID) {
      return _buildCampaignRewardTable(state.currency, state.paymentProcessSummary!.content);
    }
    return _buildTab(state.currency, state.underProcessing!.content!);
  }

  Widget _buildCampaignRewardTable(String currency, List<CampaignReward> campaignRewards) {
    if (campaignRewards.isEmpty) {
      return Container(
        width: double.infinity,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: ColorConstants.borderColor,
              width: 1.r,
            )),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: const NoDataReportWidget(),
        ),
      );
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: ColorConstants.borderColor,
            width: 1.r,
          )),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: buildCampaignRewardTable(context, currency, campaignRewards)),
    );
  }

  Widget _buildTab(String currency, Content content) {
    List<Widget> tableContent = [
      _buildTabTableContent(context, currency, content.rewardPaid!.campaignRewards),
      _buildTabTableContent(context, currency, content.rewardBeingProcessPayment!.campaignRewards)
    ];
    return Container(
      padding: EdgeInsets.only(top: 16.r),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: ColorConstants.borderColor,
            width: 1.r,
          )),
      child: Column(
        children: [
          TabBar(
            controller: _tabController,
            tabAlignment: TabAlignment.center,
            isScrollable: true,
            labelStyle: context.textLabelLarge(),
            tabs: [
              CommonTab('Paid(${content.rewardPaid!.totalReward.toPrice(currency)})'),
              CommonTab('Under Processing(${content.rewardBeingProcessPayment!.totalReward.toPrice(currency)})'),
            ],
          ),
          Expanded(
            child: tableContent[_selectedIndex],
          ),
        ],
      ),
    );
  }

  Widget _buildTabTableContent(BuildContext context, String currency, List<CampaignReward> campaignRewards) {
    if (campaignRewards.isEmpty) {
      return const NoDataReportWidget();
    }
    return buildCampaignRewardTable(context, currency, campaignRewards);
  }

  double _getTotalReward(PaymentDetailsReportState state) {
    if (widget.processType == PaymentProcessType.REWARD_BEING_PROCESSED_PAYMENT) {
      return state.underProcessing!.totalReward;
    }
    return state.paymentProcessSummary!.totalReward;
  }
}
