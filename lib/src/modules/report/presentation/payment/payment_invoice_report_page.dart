import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_invoice_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_invoice_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/invoice_detail_response.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class PaymentInvoiceReportPage extends StatefulWidget {
  final String invoiceId;
  const PaymentInvoiceReportPage(this.invoiceId, {super.key});

  @override
  State<PaymentInvoiceReportPage> createState() => _PaymentInvoiceReportPageState();
}

class _PaymentInvoiceReportPageState extends BasePageState<PaymentInvoiceReportPage, PaymentInvoiceReportCubit>
    with ReportMixin {
  @override
  void initState() {
    _initData();
    super.initState();
  }

  void _initData() {
    if (cubit.state.isPullToRefresh) {
      _loadInvoiceData();
    } else {
      doLoadingAction(() async {
        await _loadInvoiceData();
      });
    }
  }

  Future<void> _loadInvoiceData() async {
    await cubit.findInvoiceReport(widget.invoiceId);
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        centerTitle: false,
        title: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            widget.invoiceId,
            style: context.textBodySmall(fontSize: 18.r),
          ),
          Text(
            'Invoice',
            style: context.textLabelMedium(),
          )
        ]),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<PaymentInvoiceReportCubit, PaymentInvoiceReportState>(builder: (_, state) {
      if (state != PaymentInvoiceReportState()) {
        return PullToRefreshWrapper(
          onRefresh: () => cubit.pullToRefresh(widget.invoiceId),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                spacing: 16.r,
                children: [
                  buildTotalRewardCard(context, state.currency, state.invoiceDetailResponse!.totalReward),
                  _buildCampaignRewardTable(state.currency, state.invoiceDetailResponse!.rewards),
                ],
              ),
            ),
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  Widget _buildCampaignRewardTable(String currency, List<InvoiceCampaignReward> campaignRewards) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: ColorConstants.borderColor,
            width: 1.r,
          )),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: _buildTable(context, currency, campaignRewards),
      ),
    );
  }

  Widget _buildTable(BuildContext context, String currency, List<InvoiceCampaignReward> campaignRewards) {
    if (campaignRewards.isEmpty) {
      return const SizedBox.shrink();
    }
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
        columnSpacing: 12.r,
        columns: [
          buildDataColumn(context, 'Campaign'),
          buildDataColumn(context, 'Conversions'),
          buildDataColumn(context, 'Reward (${currency.currencySymbol})'),
          buildDataColumn(context, 'Month'),
        ],
        rows: campaignRewards.map((e) {
          return DataRow(cells: [
            buildDataCell(context, e.campaignName, color: const Color(0xFFEF6507), onTap: () {
              FilterCubit filterCubit = Modular.get<FilterCubit>();
              filterCubit.selectRewardMonth(e.rewardMonth);
              Modular.to
                  .pushNamed('/report/campaign/details', arguments: Item(name: e.campaignName, value: e.campaignId));
            }),
            buildDataCell(context, e.quantity.toCommaSeparated(), alignment: Alignment.centerRight),
            buildDataCell(context, e.amount.toCommaSeparated(), alignment: Alignment.centerRight),
            buildDataCell(
              context,
              DateFormat(yearMonthFormat).parse(e.rewardMonth).toMonthAndYear(),
            ),
          ]);
        }).toList(),
      ),
    );
  }
}
