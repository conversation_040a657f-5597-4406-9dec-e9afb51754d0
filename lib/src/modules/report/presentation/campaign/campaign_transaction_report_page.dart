import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_transaction_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_transaction_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/campaign/campaign_detail_report_response.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class CampaignTransactionReportPage extends StatefulWidget {
  final int campaignId;
  final CampaignDetailReportData detailReportData;
  const CampaignTransactionReportPage(this.campaignId, this.detailReportData, {super.key});

  @override
  State<CampaignTransactionReportPage> createState() => _CampaignTransactionReportPageState();
}

class _CampaignTransactionReportPageState
    extends BasePageState<CampaignTransactionReportPage, CampaignTransactionReportCubit> with ReportMixin {
  @override
  void initState() {
    doLoadingAction(() async {
      await cubit.findConversions(
          widget.campaignId,
          widget.detailReportData.verificationId,
          widget.detailReportData.conversionTime!,
          widget.detailReportData.confirmationTime,
          widget.detailReportData.status);
    });
    super.initState();
  }

  /// Refresh data for pull-to-refresh
  Future<void> _refreshData() async {
    // Use pullToRefresh method which clears cache before fetching new data
    await cubit.pullToRefresh(
        widget.campaignId,
        widget.detailReportData.verificationId,
        widget.detailReportData.conversionTime!,
        widget.detailReportData.confirmationTime,
        widget.detailReportData.status);
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        centerTitle: false,
        title: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            widget.detailReportData.verificationId,
            style: context.textBodySmall(fontSize: 18.r),
          ),
          Text(
            'Transaction ID',
            style: context.textLabelMedium(),
          )
        ]),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return PullToRefreshWrapper(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: BlocBuilder<CampaignTransactionReportCubit, CampaignTransactionReportState>(
              bloc: cubit,
              builder: (_, state) {
                if (state.reportData.isNotEmpty) {
                  return _buildDataTable(state);
                }
                return const SizedBox.shrink();
              }),
        ),
      ),
    );
  }

  Widget _buildDataTable(CampaignTransactionReportState state) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: const Color(0x1F000000),
              width: 1.r,
            )),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: DataTable(
            headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
            columnSpacing: 16.r,
            columns: [
              buildDataColumn(context, 'Category ID'),
              buildDataColumn(context, 'Product ID'),
              buildDataColumn(context, 'Quantity'),
              buildDataColumn(context, 'Unit Price'),
              buildDataColumn(context, 'Reward (${NumberFormat().simpleCurrencySymbol(state.currency)})'),
            ],
            rows: state.reportData.map(
              (data) {
                return DataRow(cells: [
                  buildDataCell(context, data.categoryId),
                  buildDataCell(context, data.productId),
                  buildDataCell(context, data.quantity.toCommaSeparated()),
                  buildDataCell(context, data.unitPrice.toCommaSeparated()),
                  buildDataCell(context, data.reward.toCommaSeparated()),
                ]);
              },
            ).toList(),
          ),
        ),
      ),
    );
  }
}
