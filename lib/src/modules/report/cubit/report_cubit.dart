import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_state.dart';
import 'package:koc_app/src/modules/report/data/model/conversion/conversion_summary_response.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class ReportCubit extends BaseCubit<ReportState> with FilterMixin {
  final ReportRepository reportRepository;

  ReportCubit(this.reportRepository) : super(ReportState());

  DateTime addMonths(DateTime date, int months) {
    int newMonth = date.month + months;
    int newYear = date.year + (newMonth > 12 ? newMonth ~/ 12 : 0);
    newMonth = newMonth > 12 ? newMonth % 12 : newMonth;

    int day = date.day;
    int lastDayOfNewMonth = DateTime(newYear, newMonth + 1, 0).day;
    if (day > lastDayOfNewMonth) {
      day = lastDayOfNewMonth;
    }

    return DateTime(newYear, newMonth, day);
  }

  Future<void> findReportData() async {
    try {
      DateTimeRange range = getTimeRange(ReportPeriod.LAST_12_MONTHS, null, null);
      int? siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      String? countryCode = await commonCubit.sharedPreferencesService.getCountryCode();

      if (siteId == null) {
        emit(state.copyWith(errorMessage: 'Site ID is not available. Please select a site.'));
        return;
      }

      if (countryCode == null) {
        emit(state.copyWith(errorMessage: 'Country code is not available.'));
        return;
      }

      List<dynamic> results = await Future.wait([
        reportRepository.findConversionSummary(siteId),
        reportRepository.findTopTenCampaignsClickCount(
          range.start.toZonedIso8601(countryCode.toCountry),
          range.end.toZonedIso8601(countryCode.toCountry),
          siteId,
        ),
        reportRepository.findPaymentSummary(),
        reportRepository.findMinimumPaymentDetails(),
      ]);
      Map<DateTime, int> currentOneYearClickCount = {};
      Map<DateTime, int> currentOneYearConversionCount = {};
      DateTime date = DateTime(2024, 3, 1);
      for (int i = 0; i < 12; i++) {
        DateTime addedDate = addMonths(date, i);
        currentOneYearClickCount[addedDate] = Random().nextInt(1000) + 1000;
        currentOneYearConversionCount[addedDate] = Random().nextInt(100) + 100;
      }

      ConversionSummaryResponse conversionSummaryResponse = results[0];

      emit(state.copyWith(
          currentOneYearClickCount: currentOneYearClickCount,
          currentOneYearConversionCount: currentOneYearConversionCount,
          thisMonthOccurredConversionCount: conversionSummaryResponse.countConversionOccurredThisMonth,
          lastMonthApprovedReward: conversionSummaryResponse.rewardApprovedLastMonth,
          topTenCampaignsClickCount: results[1].data,
          paymentSummary: results[2],
          minimumPaymentDetails: results[3],
          currency: conversionSummaryResponse.currencyCode,
          country: countryCode.toCountry,
          selectedSiteId: siteId));
    } on DioException catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes report data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      if (siteId != null) {
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/reports/summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/reports/performance-chart');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/reports/conversion-summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/reports/top-campaigns');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/payment-summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/minimum-payment-details');
        
        await findReportData();
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }
}
