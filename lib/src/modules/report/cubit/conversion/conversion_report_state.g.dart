// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversion_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConversionReportStateImpl _$$ConversionReportStateImplFromJson(
        Map<String, dynamic> json) =>
    _$ConversionReportStateImpl(
      showReport: json['showReport'] as bool? ?? false,
      currency: json['currency'] as String? ?? '',
      reportData: json['reportData'] == null
          ? null
          : ConversionCampaignSummaryResponse.fromJson(
              json['reportData'] as Map<String, dynamic>),
      errorMessage: json['errorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$$ConversionReportStateImplToJson(
        _$ConversionReportStateImpl instance) =>
    <String, dynamic>{
      'showReport': instance.showReport,
      'currency': instance.currency,
      'reportData': instance.reportData,
      'errorMessage': instance.errorMessage,
    };
