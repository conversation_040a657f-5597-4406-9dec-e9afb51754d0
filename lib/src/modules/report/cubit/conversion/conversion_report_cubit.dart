import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/conversion/conversion_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/conversion/conversion_campaign_summary_response.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class ConversionReportCubit extends BaseCubit<ConversionReportState> {
  final ReportRepository reportRepository;
  ConversionReportCubit(this.reportRepository) : super(ConversionReportState());

  void hideReport() {
    emit(state.copyWith(showReport: false));
  }

  Future<void> findConversions(DateTime fromDate, DateTime toDate, ReportQueryPeriodBase periodBase, int page,
      int limit, ConversionStatus? status, Item? campaign, Item? site) async {
    try {
      String? countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      int? siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      final results = await Future.wait([
        commonCubit.sharedPreferencesService.getPublisherCurrency(),
        reportRepository.findConversionCampaignSummary(
          fromDate.toZonedIso8601(countryCode!.toCountry),
          toDate.toZonedIso8601(countryCode.toCountry),
          periodBase,
          page,
          limit,
          campaign != null ? [campaign.value] : null,
          site?.value ?? siteId,
        )
      ]);

      emit(state.copyWith(
          currency: results[0].toString(),
          reportData: results[1] as ConversionCampaignSummaryResponse,
          showReport: true));
    } on DioException catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<bool> requestCsvDownload(DateTime fromDate, DateTime toDate, ReportQueryPeriodBase periodBase,
      ConversionStatus? status, Item? campaign, Item? site) async {
    try {
      String? country = await commonCubit.sharedPreferencesService.getCountryCode();
      int? siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      await reportRepository.requestDownloadCsv(
          (fromDate.toZonedIso8601(country!.toCountry)).replaceAllMapped(
              RegExp(r'(.*)(:\d{2})$'), (match) => '${match.group(1)}${match.group(2)!.substring(1)}'),
          (toDate.toZonedIso8601(country.toCountry)).replaceAllMapped(
              RegExp(r'(.*)(:\d{2})$'), (match) => '${match.group(1)}${match.group(2)!.substring(1)}'),
          periodBase,
          status,
          campaign,
          site?.value ?? siteId);
      return true;
    } on DioException catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes conversion data while bypassing cache to ensure fresh data
  Future<void> pullToRefresh(DateTime fromDate, DateTime toDate, ReportQueryPeriodBase periodBase, int page, int limit,
      ConversionStatus? status, Item? campaign, Item? site) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/conversion-summary');

      await findConversions(fromDate, toDate, periodBase, page, limit, status, campaign, site);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
