import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/conversion/conversion_campaign_summary_response.dart';

part 'conversion_report_state.freezed.dart';
part 'conversion_report_state.g.dart';

@freezed
class ConversionReportState extends BaseCubitState
    with _$ConversionReportState {
  factory ConversionReportState({
    @Default(false) bool showReport,
    @Default('') String currency,
    ConversionCampaignSummaryResponse? reportData,
    @Default('') String errorMessage,
  }) = _ConversionReportState;

  factory ConversionReportState.fromJson(Map<String, Object?> json) =>
      _$ConversionReportStateFromJson(json);
}
