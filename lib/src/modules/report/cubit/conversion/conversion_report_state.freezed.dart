// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversion_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ConversionReportState _$ConversionReportStateFromJson(
    Map<String, dynamic> json) {
  return _ConversionReportState.fromJson(json);
}

/// @nodoc
mixin _$ConversionReportState {
  bool get showReport => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  ConversionCampaignSummaryResponse? get reportData =>
      throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this ConversionReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConversionReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConversionReportStateCopyWith<ConversionReportState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversionReportStateCopyWith<$Res> {
  factory $ConversionReportStateCopyWith(ConversionReportState value,
          $Res Function(ConversionReportState) then) =
      _$ConversionReportStateCopyWithImpl<$Res, ConversionReportState>;
  @useResult
  $Res call(
      {bool showReport,
      String currency,
      ConversionCampaignSummaryResponse? reportData,
      String errorMessage});

  $ConversionCampaignSummaryResponseCopyWith<$Res>? get reportData;
}

/// @nodoc
class _$ConversionReportStateCopyWithImpl<$Res,
        $Val extends ConversionReportState>
    implements $ConversionReportStateCopyWith<$Res> {
  _$ConversionReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConversionReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showReport = null,
    Object? currency = null,
    Object? reportData = freezed,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      showReport: null == showReport
          ? _value.showReport
          : showReport // ignore: cast_nullable_to_non_nullable
              as bool,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      reportData: freezed == reportData
          ? _value.reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as ConversionCampaignSummaryResponse?,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of ConversionReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ConversionCampaignSummaryResponseCopyWith<$Res>? get reportData {
    if (_value.reportData == null) {
      return null;
    }

    return $ConversionCampaignSummaryResponseCopyWith<$Res>(_value.reportData!,
        (value) {
      return _then(_value.copyWith(reportData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ConversionReportStateImplCopyWith<$Res>
    implements $ConversionReportStateCopyWith<$Res> {
  factory _$$ConversionReportStateImplCopyWith(
          _$ConversionReportStateImpl value,
          $Res Function(_$ConversionReportStateImpl) then) =
      __$$ConversionReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showReport,
      String currency,
      ConversionCampaignSummaryResponse? reportData,
      String errorMessage});

  @override
  $ConversionCampaignSummaryResponseCopyWith<$Res>? get reportData;
}

/// @nodoc
class __$$ConversionReportStateImplCopyWithImpl<$Res>
    extends _$ConversionReportStateCopyWithImpl<$Res,
        _$ConversionReportStateImpl>
    implements _$$ConversionReportStateImplCopyWith<$Res> {
  __$$ConversionReportStateImplCopyWithImpl(_$ConversionReportStateImpl _value,
      $Res Function(_$ConversionReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConversionReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showReport = null,
    Object? currency = null,
    Object? reportData = freezed,
    Object? errorMessage = null,
  }) {
    return _then(_$ConversionReportStateImpl(
      showReport: null == showReport
          ? _value.showReport
          : showReport // ignore: cast_nullable_to_non_nullable
              as bool,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      reportData: freezed == reportData
          ? _value.reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as ConversionCampaignSummaryResponse?,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ConversionReportStateImpl implements _ConversionReportState {
  _$ConversionReportStateImpl(
      {this.showReport = false,
      this.currency = '',
      this.reportData,
      this.errorMessage = ''});

  factory _$ConversionReportStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConversionReportStateImplFromJson(json);

  @override
  @JsonKey()
  final bool showReport;
  @override
  @JsonKey()
  final String currency;
  @override
  final ConversionCampaignSummaryResponse? reportData;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'ConversionReportState(showReport: $showReport, currency: $currency, reportData: $reportData, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversionReportStateImpl &&
            (identical(other.showReport, showReport) ||
                other.showReport == showReport) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.reportData, reportData) ||
                other.reportData == reportData) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, showReport, currency, reportData, errorMessage);

  /// Create a copy of ConversionReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversionReportStateImplCopyWith<_$ConversionReportStateImpl>
      get copyWith => __$$ConversionReportStateImplCopyWithImpl<
          _$ConversionReportStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConversionReportStateImplToJson(
      this,
    );
  }
}

abstract class _ConversionReportState implements ConversionReportState {
  factory _ConversionReportState(
      {final bool showReport,
      final String currency,
      final ConversionCampaignSummaryResponse? reportData,
      final String errorMessage}) = _$ConversionReportStateImpl;

  factory _ConversionReportState.fromJson(Map<String, dynamic> json) =
      _$ConversionReportStateImpl.fromJson;

  @override
  bool get showReport;
  @override
  String get currency;
  @override
  ConversionCampaignSummaryResponse? get reportData;
  @override
  String get errorMessage;

  /// Create a copy of ConversionReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConversionReportStateImplCopyWith<_$ConversionReportStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
