// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_transaction_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignTransactionReportState _$CampaignTransactionReportStateFromJson(
    Map<String, dynamic> json) {
  return _CampaignTransactionReportState.fromJson(json);
}

/// @nodoc
mixin _$CampaignTransactionReportState {
  String get currency => throw _privateConstructorUsedError;
  List<CampaignTransactionReportData> get reportData =>
      throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this CampaignTransactionReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignTransactionReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignTransactionReportStateCopyWith<CampaignTransactionReportState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignTransactionReportStateCopyWith<$Res> {
  factory $CampaignTransactionReportStateCopyWith(
          CampaignTransactionReportState value,
          $Res Function(CampaignTransactionReportState) then) =
      _$CampaignTransactionReportStateCopyWithImpl<$Res,
          CampaignTransactionReportState>;
  @useResult
  $Res call(
      {String currency,
      List<CampaignTransactionReportData> reportData,
      String errorMessage});
}

/// @nodoc
class _$CampaignTransactionReportStateCopyWithImpl<$Res,
        $Val extends CampaignTransactionReportState>
    implements $CampaignTransactionReportStateCopyWith<$Res> {
  _$CampaignTransactionReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignTransactionReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      reportData: null == reportData
          ? _value.reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<CampaignTransactionReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignTransactionReportStateImplCopyWith<$Res>
    implements $CampaignTransactionReportStateCopyWith<$Res> {
  factory _$$CampaignTransactionReportStateImplCopyWith(
          _$CampaignTransactionReportStateImpl value,
          $Res Function(_$CampaignTransactionReportStateImpl) then) =
      __$$CampaignTransactionReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String currency,
      List<CampaignTransactionReportData> reportData,
      String errorMessage});
}

/// @nodoc
class __$$CampaignTransactionReportStateImplCopyWithImpl<$Res>
    extends _$CampaignTransactionReportStateCopyWithImpl<$Res,
        _$CampaignTransactionReportStateImpl>
    implements _$$CampaignTransactionReportStateImplCopyWith<$Res> {
  __$$CampaignTransactionReportStateImplCopyWithImpl(
      _$CampaignTransactionReportStateImpl _value,
      $Res Function(_$CampaignTransactionReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignTransactionReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_$CampaignTransactionReportStateImpl(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      reportData: null == reportData
          ? _value._reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<CampaignTransactionReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignTransactionReportStateImpl
    implements _CampaignTransactionReportState {
  _$CampaignTransactionReportStateImpl(
      {this.currency = '',
      final List<CampaignTransactionReportData> reportData = const [],
      this.errorMessage = ''})
      : _reportData = reportData;

  factory _$CampaignTransactionReportStateImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CampaignTransactionReportStateImplFromJson(json);

  @override
  @JsonKey()
  final String currency;
  final List<CampaignTransactionReportData> _reportData;
  @override
  @JsonKey()
  List<CampaignTransactionReportData> get reportData {
    if (_reportData is EqualUnmodifiableListView) return _reportData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reportData);
  }

  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'CampaignTransactionReportState(currency: $currency, reportData: $reportData, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignTransactionReportStateImpl &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            const DeepCollectionEquality()
                .equals(other._reportData, _reportData) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, currency,
      const DeepCollectionEquality().hash(_reportData), errorMessage);

  /// Create a copy of CampaignTransactionReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignTransactionReportStateImplCopyWith<
          _$CampaignTransactionReportStateImpl>
      get copyWith => __$$CampaignTransactionReportStateImplCopyWithImpl<
          _$CampaignTransactionReportStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignTransactionReportStateImplToJson(
      this,
    );
  }
}

abstract class _CampaignTransactionReportState
    implements CampaignTransactionReportState {
  factory _CampaignTransactionReportState(
      {final String currency,
      final List<CampaignTransactionReportData> reportData,
      final String errorMessage}) = _$CampaignTransactionReportStateImpl;

  factory _CampaignTransactionReportState.fromJson(Map<String, dynamic> json) =
      _$CampaignTransactionReportStateImpl.fromJson;

  @override
  String get currency;
  @override
  List<CampaignTransactionReportData> get reportData;
  @override
  String get errorMessage;

  /// Create a copy of CampaignTransactionReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignTransactionReportStateImplCopyWith<
          _$CampaignTransactionReportStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
