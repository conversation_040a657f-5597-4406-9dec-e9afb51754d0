// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_transaction_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignTransactionReportStateImpl
    _$$CampaignTransactionReportStateImplFromJson(Map<String, dynamic> json) =>
        _$CampaignTransactionReportStateImpl(
          currency: json['currency'] as String? ?? '',
          reportData: (json['reportData'] as List<dynamic>?)
                  ?.map((e) => CampaignTransactionReportData.fromJson(
                      e as Map<String, dynamic>))
                  .toList() ??
              const [],
          errorMessage: json['errorMessage'] as String? ?? '',
        );

Map<String, dynamic> _$$CampaignTransactionReportStateImplToJson(
        _$CampaignTransactionReportStateImpl instance) =>
    <String, dynamic>{
      'currency': instance.currency,
      'reportData': instance.reportData,
      'errorMessage': instance.errorMessage,
    };
