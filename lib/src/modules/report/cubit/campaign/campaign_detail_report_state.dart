import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/campaign/campaign_detail_report_response.dart';

part 'campaign_detail_report_state.freezed.dart';
part 'campaign_detail_report_state.g.dart';

@freezed
class CampaignDetailReportState extends BaseCubitState
    with _$CampaignDetailReportState {
  factory CampaignDetailReportState({
    @Default('') String currency,
    @Default([]) List<CampaignDetailReportData> reportData,
    @Default('') String errorMessage,
  }) = _CampaignDetailReportState;

  factory CampaignDetailReportState.fromJson(Map<String, Object?> json) =>
      _$CampaignDetailReportStateFromJson(json);
}
