// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_detail_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignDetailReportState _$CampaignDetailReportStateFromJson(
    Map<String, dynamic> json) {
  return _CampaignDetailReportState.fromJson(json);
}

/// @nodoc
mixin _$CampaignDetailReportState {
  String get currency => throw _privateConstructorUsedError;
  List<CampaignDetailReportData> get reportData =>
      throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this CampaignDetailReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignDetailReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignDetailReportStateCopyWith<CampaignDetailReportState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignDetailReportStateCopyWith<$Res> {
  factory $CampaignDetailReportStateCopyWith(CampaignDetailReportState value,
          $Res Function(CampaignDetailReportState) then) =
      _$CampaignDetailReportStateCopyWithImpl<$Res, CampaignDetailReportState>;
  @useResult
  $Res call(
      {String currency,
      List<CampaignDetailReportData> reportData,
      String errorMessage});
}

/// @nodoc
class _$CampaignDetailReportStateCopyWithImpl<$Res,
        $Val extends CampaignDetailReportState>
    implements $CampaignDetailReportStateCopyWith<$Res> {
  _$CampaignDetailReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignDetailReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      reportData: null == reportData
          ? _value.reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<CampaignDetailReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignDetailReportStateImplCopyWith<$Res>
    implements $CampaignDetailReportStateCopyWith<$Res> {
  factory _$$CampaignDetailReportStateImplCopyWith(
          _$CampaignDetailReportStateImpl value,
          $Res Function(_$CampaignDetailReportStateImpl) then) =
      __$$CampaignDetailReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String currency,
      List<CampaignDetailReportData> reportData,
      String errorMessage});
}

/// @nodoc
class __$$CampaignDetailReportStateImplCopyWithImpl<$Res>
    extends _$CampaignDetailReportStateCopyWithImpl<$Res,
        _$CampaignDetailReportStateImpl>
    implements _$$CampaignDetailReportStateImplCopyWith<$Res> {
  __$$CampaignDetailReportStateImplCopyWithImpl(
      _$CampaignDetailReportStateImpl _value,
      $Res Function(_$CampaignDetailReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignDetailReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_$CampaignDetailReportStateImpl(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      reportData: null == reportData
          ? _value._reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<CampaignDetailReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignDetailReportStateImpl implements _CampaignDetailReportState {
  _$CampaignDetailReportStateImpl(
      {this.currency = '',
      final List<CampaignDetailReportData> reportData = const [],
      this.errorMessage = ''})
      : _reportData = reportData;

  factory _$CampaignDetailReportStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignDetailReportStateImplFromJson(json);

  @override
  @JsonKey()
  final String currency;
  final List<CampaignDetailReportData> _reportData;
  @override
  @JsonKey()
  List<CampaignDetailReportData> get reportData {
    if (_reportData is EqualUnmodifiableListView) return _reportData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reportData);
  }

  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'CampaignDetailReportState(currency: $currency, reportData: $reportData, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignDetailReportStateImpl &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            const DeepCollectionEquality()
                .equals(other._reportData, _reportData) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, currency,
      const DeepCollectionEquality().hash(_reportData), errorMessage);

  /// Create a copy of CampaignDetailReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignDetailReportStateImplCopyWith<_$CampaignDetailReportStateImpl>
      get copyWith => __$$CampaignDetailReportStateImplCopyWithImpl<
          _$CampaignDetailReportStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignDetailReportStateImplToJson(
      this,
    );
  }
}

abstract class _CampaignDetailReportState implements CampaignDetailReportState {
  factory _CampaignDetailReportState(
      {final String currency,
      final List<CampaignDetailReportData> reportData,
      final String errorMessage}) = _$CampaignDetailReportStateImpl;

  factory _CampaignDetailReportState.fromJson(Map<String, dynamic> json) =
      _$CampaignDetailReportStateImpl.fromJson;

  @override
  String get currency;
  @override
  List<CampaignDetailReportData> get reportData;
  @override
  String get errorMessage;

  /// Create a copy of CampaignDetailReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignDetailReportStateImplCopyWith<_$CampaignDetailReportStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
