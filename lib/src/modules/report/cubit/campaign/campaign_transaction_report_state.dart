import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/campaign_transaction_report_data.dart';

part 'campaign_transaction_report_state.freezed.dart';
part 'campaign_transaction_report_state.g.dart';

@freezed
class CampaignTransactionReportState extends BaseCubitState
    with _$CampaignTransactionReportState {
  factory CampaignTransactionReportState({
    @Default('') String currency,
    @Default([]) List<CampaignTransactionReportData> reportData,
    @Default('') String errorMessage,
  }) = _CampaignTransactionReportState;

  factory CampaignTransactionReportState.fromJson(Map<String, Object?> json) =>
      _$CampaignTransactionReportStateFromJson(json);
}
