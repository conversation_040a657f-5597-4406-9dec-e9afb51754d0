import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/campaign/campaign_report_data.dart';

part 'campaign_report_state.freezed.dart';
part 'campaign_report_state.g.dart';

@freezed
class CampaignReportState extends BaseCubitState with _$CampaignReportState {
  factory CampaignReportState({
    @Default(false) bool showReport,
    @Default(CampaignChartTitle.TOP_10) CampaignChartTitle chartTitle,
    @Default(CampaignChartValue.CLICKS) CampaignChartValue chartValue,
    @Default('') String currency,
    @Default([]) List<CampaignReportData> reportData,
    @Default('') String errorMessage,
  }) = _CampaignReportState;

  factory CampaignReportState.fromJson(Map<String, Object?> json) => _$CampaignReportStateFromJson(json);
}

enum CampaignChartTitle {
  TOP_10('Top 10'),
  TOP_15('Top 15');

  final String value;
  const CampaignChartTitle(this.value);
}

enum CampaignChartValue {
  CLICKS('Clicks'),
  CONVERSIONS('Conversions');

  final String value;
  const CampaignChartValue(this.value);
}
