import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_transaction_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class CampaignTransactionReportCubit extends BaseCubit<CampaignTransactionReportState> {
  final ReportRepository reportRepository;
  CampaignTransactionReportCubit(this.reportRepository) : super(CampaignTransactionReportState());

  Future<void> findConversions(
    int campaignId,
    String transactionId,
    DateTime conversionTime,
    DateTime? confirmationTime,
    ConversionStatus status,
  ) async {
    try {
      String? countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      List<dynamic> results = await Future.wait([
        commonCubit.sharedPreferencesService.getPublisherCurrency(),
        reportRepository.findConversionCampaignTransactionReportData(
            campaignId,
            transactionId,
            conversionTime.toZonedIso8601(countryCode!.toCountry),
            confirmationTime?.toZonedIso8601(countryCode.toCountry),
            status)
      ]);

      emit(state.copyWith(
        currency: results[0].toString(),
        reportData: results[1],
      ));
    } on DioException catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes campaign transaction data while bypassing cache to ensure fresh data
  Future<void> pullToRefresh(
    int campaignId,
    String transactionId,
    DateTime conversionTime,
    DateTime? confirmationTime,
    ConversionStatus status,
  ) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/conversion/transaction');

      await findConversions(campaignId, transactionId, conversionTime, confirmationTime, status);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
