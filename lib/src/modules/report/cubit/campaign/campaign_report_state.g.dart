// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignReportStateImpl _$$CampaignReportStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignReportStateImpl(
      showReport: json['showReport'] as bool? ?? false,
      chartTitle: $enumDecodeNullable(
              _$CampaignChartTitleEnumMap, json['chartTitle']) ??
          CampaignChartTitle.TOP_10,
      chartValue: $enumDecodeNullable(
              _$CampaignChartValueEnumMap, json['chartValue']) ??
          CampaignChartValue.CLICKS,
      currency: json['currency'] as String? ?? '',
      reportData: (json['reportData'] as List<dynamic>?)
              ?.map(
                  (e) => CampaignReportData.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      errorMessage: json['errorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$$CampaignReportStateImplToJson(
        _$CampaignReportStateImpl instance) =>
    <String, dynamic>{
      'showReport': instance.showReport,
      'chartTitle': _$CampaignChartTitleEnumMap[instance.chartTitle]!,
      'chartValue': _$CampaignChartValueEnumMap[instance.chartValue]!,
      'currency': instance.currency,
      'reportData': instance.reportData,
      'errorMessage': instance.errorMessage,
    };

const _$CampaignChartTitleEnumMap = {
  CampaignChartTitle.TOP_10: 'TOP_10',
  CampaignChartTitle.TOP_15: 'TOP_15',
};

const _$CampaignChartValueEnumMap = {
  CampaignChartValue.CLICKS: 'CLICKS',
  CampaignChartValue.CONVERSIONS: 'CONVERSIONS',
};
