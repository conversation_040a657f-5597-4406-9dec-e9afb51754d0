// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignReportState _$CampaignReportStateFromJson(Map<String, dynamic> json) {
  return _CampaignReportState.fromJson(json);
}

/// @nodoc
mixin _$CampaignReportState {
  bool get showReport => throw _privateConstructorUsedError;
  CampaignChartTitle get chartTitle => throw _privateConstructorUsedError;
  CampaignChartValue get chartValue => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  List<CampaignReportData> get reportData => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this CampaignReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignReportStateCopyWith<CampaignReportState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignReportStateCopyWith<$Res> {
  factory $CampaignReportStateCopyWith(
          CampaignReportState value, $Res Function(CampaignReportState) then) =
      _$CampaignReportStateCopyWithImpl<$Res, CampaignReportState>;
  @useResult
  $Res call(
      {bool showReport,
      CampaignChartTitle chartTitle,
      CampaignChartValue chartValue,
      String currency,
      List<CampaignReportData> reportData,
      String errorMessage});
}

/// @nodoc
class _$CampaignReportStateCopyWithImpl<$Res, $Val extends CampaignReportState>
    implements $CampaignReportStateCopyWith<$Res> {
  _$CampaignReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showReport = null,
    Object? chartTitle = null,
    Object? chartValue = null,
    Object? currency = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      showReport: null == showReport
          ? _value.showReport
          : showReport // ignore: cast_nullable_to_non_nullable
              as bool,
      chartTitle: null == chartTitle
          ? _value.chartTitle
          : chartTitle // ignore: cast_nullable_to_non_nullable
              as CampaignChartTitle,
      chartValue: null == chartValue
          ? _value.chartValue
          : chartValue // ignore: cast_nullable_to_non_nullable
              as CampaignChartValue,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      reportData: null == reportData
          ? _value.reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<CampaignReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignReportStateImplCopyWith<$Res>
    implements $CampaignReportStateCopyWith<$Res> {
  factory _$$CampaignReportStateImplCopyWith(_$CampaignReportStateImpl value,
          $Res Function(_$CampaignReportStateImpl) then) =
      __$$CampaignReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showReport,
      CampaignChartTitle chartTitle,
      CampaignChartValue chartValue,
      String currency,
      List<CampaignReportData> reportData,
      String errorMessage});
}

/// @nodoc
class __$$CampaignReportStateImplCopyWithImpl<$Res>
    extends _$CampaignReportStateCopyWithImpl<$Res, _$CampaignReportStateImpl>
    implements _$$CampaignReportStateImplCopyWith<$Res> {
  __$$CampaignReportStateImplCopyWithImpl(_$CampaignReportStateImpl _value,
      $Res Function(_$CampaignReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showReport = null,
    Object? chartTitle = null,
    Object? chartValue = null,
    Object? currency = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_$CampaignReportStateImpl(
      showReport: null == showReport
          ? _value.showReport
          : showReport // ignore: cast_nullable_to_non_nullable
              as bool,
      chartTitle: null == chartTitle
          ? _value.chartTitle
          : chartTitle // ignore: cast_nullable_to_non_nullable
              as CampaignChartTitle,
      chartValue: null == chartValue
          ? _value.chartValue
          : chartValue // ignore: cast_nullable_to_non_nullable
              as CampaignChartValue,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      reportData: null == reportData
          ? _value._reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<CampaignReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignReportStateImpl implements _CampaignReportState {
  _$CampaignReportStateImpl(
      {this.showReport = false,
      this.chartTitle = CampaignChartTitle.TOP_10,
      this.chartValue = CampaignChartValue.CLICKS,
      this.currency = '',
      final List<CampaignReportData> reportData = const [],
      this.errorMessage = ''})
      : _reportData = reportData;

  factory _$CampaignReportStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignReportStateImplFromJson(json);

  @override
  @JsonKey()
  final bool showReport;
  @override
  @JsonKey()
  final CampaignChartTitle chartTitle;
  @override
  @JsonKey()
  final CampaignChartValue chartValue;
  @override
  @JsonKey()
  final String currency;
  final List<CampaignReportData> _reportData;
  @override
  @JsonKey()
  List<CampaignReportData> get reportData {
    if (_reportData is EqualUnmodifiableListView) return _reportData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reportData);
  }

  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'CampaignReportState(showReport: $showReport, chartTitle: $chartTitle, chartValue: $chartValue, currency: $currency, reportData: $reportData, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignReportStateImpl &&
            (identical(other.showReport, showReport) ||
                other.showReport == showReport) &&
            (identical(other.chartTitle, chartTitle) ||
                other.chartTitle == chartTitle) &&
            (identical(other.chartValue, chartValue) ||
                other.chartValue == chartValue) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            const DeepCollectionEquality()
                .equals(other._reportData, _reportData) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showReport,
      chartTitle,
      chartValue,
      currency,
      const DeepCollectionEquality().hash(_reportData),
      errorMessage);

  /// Create a copy of CampaignReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignReportStateImplCopyWith<_$CampaignReportStateImpl> get copyWith =>
      __$$CampaignReportStateImplCopyWithImpl<_$CampaignReportStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignReportStateImplToJson(
      this,
    );
  }
}

abstract class _CampaignReportState implements CampaignReportState {
  factory _CampaignReportState(
      {final bool showReport,
      final CampaignChartTitle chartTitle,
      final CampaignChartValue chartValue,
      final String currency,
      final List<CampaignReportData> reportData,
      final String errorMessage}) = _$CampaignReportStateImpl;

  factory _CampaignReportState.fromJson(Map<String, dynamic> json) =
      _$CampaignReportStateImpl.fromJson;

  @override
  bool get showReport;
  @override
  CampaignChartTitle get chartTitle;
  @override
  CampaignChartValue get chartValue;
  @override
  String get currency;
  @override
  List<CampaignReportData> get reportData;
  @override
  String get errorMessage;

  /// Create a copy of CampaignReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignReportStateImplCopyWith<_$CampaignReportStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
