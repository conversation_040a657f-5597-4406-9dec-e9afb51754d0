import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/minimum_payment_details.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_summary.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class PaymentReportCubit extends BaseCubit<PaymentReportState> {
  final ReportRepository reportRepository;
  PaymentReportCubit(this.reportRepository) : super(PaymentReportState());

  Future<void> findPayments(DateTime fromMonth, DateTime toMonth, String? invoiceNumber) async {
    try {
      final results = await Future.wait([
        reportRepository.findPaymentSummary(),
        reportRepository.findPayments(fromMonth.toYearMonth(), toMonth.toYearMonth(), invoiceNumber),
        reportRepository.findMinimumPaymentDetails(),
        commonCubit.sharedPreferencesService.getPublisherCurrency(),
        commonCubit.sharedPreferencesService.getCountryCode(),
      ]);
      PaymentSummary summary = results[0] as PaymentSummary;
      List<PaymentReportData> payments = results[1] as List<PaymentReportData>;
      emit(state.copyWith(
          paymentSummary: summary,
          payments: payments,
          minimumPaymentDetails: results[2] as MinimumPaymentDetails,
          currency: results[3] as String,
          country: (results[4] as String).toCountry));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  void toggleAllPayments() {
    List<PaymentReportData> reportData = state.payments;
    if (reportData.where((e) => e.isCheck).length == reportData.length) {
      emit(state.copyWith(payments: reportData.map((e) => e.copyWith(isCheck: false)).toList()));
    } else {
      emit(state.copyWith(payments: reportData.map((e) => e.copyWith(isCheck: true)).toList()));
    }
  }

  void togglePayment(String invoiceNumber) {
    List<PaymentReportData> payments = List.from(state.payments);
    int index = payments.indexWhere((e) => e.invoiceNumber == invoiceNumber);
    PaymentReportData targetData = payments.firstWhere((e) => e.invoiceNumber == invoiceNumber);
    payments[index] = targetData.copyWith(isCheck: !targetData.isCheck);
    emit(state.copyWith(payments: payments));
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes payment data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/payment-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/minimum-payment-details');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/payments');

      final now = DateTime.now();
      final fromMonth = DateTime(now.year - 1, now.month);
      final toMonth = now;

      await _refreshPaymentDataForPullToRefresh(fromMonth, toMonth, null);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  /// Specialized refresh method for payment data during pull-to-refresh
  /// This method doesn't trigger loading states to avoid multiple loading indicators
  Future<void> _refreshPaymentDataForPullToRefresh(DateTime fromMonth, DateTime toMonth, String? invoiceNumber) async {
    try {
      final results = await Future.wait([
        reportRepository.findPaymentSummary(),
        reportRepository.findPayments(fromMonth.toYearMonth(), toMonth.toYearMonth(), invoiceNumber),
        reportRepository.findMinimumPaymentDetails(),
        commonCubit.sharedPreferencesService.getPublisherCurrency(),
        commonCubit.sharedPreferencesService.getCountryCode(),
      ]);

      PaymentSummary summary = results[0] as PaymentSummary;
      List<PaymentReportData> payments = results[1] as List<PaymentReportData>;

      emit(state.copyWith(
          paymentSummary: summary,
          payments: payments,
          minimumPaymentDetails: results[2] as MinimumPaymentDetails,
          currency: results[3] as String,
          country: (results[4] as String).toCountry));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
