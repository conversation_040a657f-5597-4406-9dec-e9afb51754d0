// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_details_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaymentDetailsReportState _$PaymentDetailsReportStateFromJson(
    Map<String, dynamic> json) {
  return _PaymentDetailsReportState.fromJson(json);
}

/// @nodoc
mixin _$PaymentDetailsReportState {
  String get currency => throw _privateConstructorUsedError;
  PaymentProcessSummary? get paymentProcessSummary =>
      throw _privateConstructorUsedError;
  PaymentInProcessState? get underProcessing =>
      throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  bool get isPullToRefresh => throw _privateConstructorUsedError;

  /// Serializes this PaymentDetailsReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentDetailsReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentDetailsReportStateCopyWith<PaymentDetailsReportState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentDetailsReportStateCopyWith<$Res> {
  factory $PaymentDetailsReportStateCopyWith(PaymentDetailsReportState value,
          $Res Function(PaymentDetailsReportState) then) =
      _$PaymentDetailsReportStateCopyWithImpl<$Res, PaymentDetailsReportState>;
  @useResult
  $Res call(
      {String currency,
      PaymentProcessSummary? paymentProcessSummary,
      PaymentInProcessState? underProcessing,
      String errorMessage,
      bool isPullToRefresh});

  $PaymentProcessSummaryCopyWith<$Res>? get paymentProcessSummary;
  $PaymentInProcessStateCopyWith<$Res>? get underProcessing;
}

/// @nodoc
class _$PaymentDetailsReportStateCopyWithImpl<$Res,
        $Val extends PaymentDetailsReportState>
    implements $PaymentDetailsReportStateCopyWith<$Res> {
  _$PaymentDetailsReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentDetailsReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? paymentProcessSummary = freezed,
    Object? underProcessing = freezed,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
  }) {
    return _then(_value.copyWith(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      paymentProcessSummary: freezed == paymentProcessSummary
          ? _value.paymentProcessSummary
          : paymentProcessSummary // ignore: cast_nullable_to_non_nullable
              as PaymentProcessSummary?,
      underProcessing: freezed == underProcessing
          ? _value.underProcessing
          : underProcessing // ignore: cast_nullable_to_non_nullable
              as PaymentInProcessState?,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of PaymentDetailsReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentProcessSummaryCopyWith<$Res>? get paymentProcessSummary {
    if (_value.paymentProcessSummary == null) {
      return null;
    }

    return $PaymentProcessSummaryCopyWith<$Res>(_value.paymentProcessSummary!,
        (value) {
      return _then(_value.copyWith(paymentProcessSummary: value) as $Val);
    });
  }

  /// Create a copy of PaymentDetailsReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentInProcessStateCopyWith<$Res>? get underProcessing {
    if (_value.underProcessing == null) {
      return null;
    }

    return $PaymentInProcessStateCopyWith<$Res>(_value.underProcessing!,
        (value) {
      return _then(_value.copyWith(underProcessing: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PaymentDetailsReportStateImplCopyWith<$Res>
    implements $PaymentDetailsReportStateCopyWith<$Res> {
  factory _$$PaymentDetailsReportStateImplCopyWith(
          _$PaymentDetailsReportStateImpl value,
          $Res Function(_$PaymentDetailsReportStateImpl) then) =
      __$$PaymentDetailsReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String currency,
      PaymentProcessSummary? paymentProcessSummary,
      PaymentInProcessState? underProcessing,
      String errorMessage,
      bool isPullToRefresh});

  @override
  $PaymentProcessSummaryCopyWith<$Res>? get paymentProcessSummary;
  @override
  $PaymentInProcessStateCopyWith<$Res>? get underProcessing;
}

/// @nodoc
class __$$PaymentDetailsReportStateImplCopyWithImpl<$Res>
    extends _$PaymentDetailsReportStateCopyWithImpl<$Res,
        _$PaymentDetailsReportStateImpl>
    implements _$$PaymentDetailsReportStateImplCopyWith<$Res> {
  __$$PaymentDetailsReportStateImplCopyWithImpl(
      _$PaymentDetailsReportStateImpl _value,
      $Res Function(_$PaymentDetailsReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentDetailsReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? paymentProcessSummary = freezed,
    Object? underProcessing = freezed,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
  }) {
    return _then(_$PaymentDetailsReportStateImpl(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      paymentProcessSummary: freezed == paymentProcessSummary
          ? _value.paymentProcessSummary
          : paymentProcessSummary // ignore: cast_nullable_to_non_nullable
              as PaymentProcessSummary?,
      underProcessing: freezed == underProcessing
          ? _value.underProcessing
          : underProcessing // ignore: cast_nullable_to_non_nullable
              as PaymentInProcessState?,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentDetailsReportStateImpl implements _PaymentDetailsReportState {
  _$PaymentDetailsReportStateImpl(
      {this.currency = '',
      this.paymentProcessSummary,
      this.underProcessing,
      this.errorMessage = '',
      this.isPullToRefresh = false});

  factory _$PaymentDetailsReportStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentDetailsReportStateImplFromJson(json);

  @override
  @JsonKey()
  final String currency;
  @override
  final PaymentProcessSummary? paymentProcessSummary;
  @override
  final PaymentInProcessState? underProcessing;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool isPullToRefresh;

  @override
  String toString() {
    return 'PaymentDetailsReportState(currency: $currency, paymentProcessSummary: $paymentProcessSummary, underProcessing: $underProcessing, errorMessage: $errorMessage, isPullToRefresh: $isPullToRefresh)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentDetailsReportStateImpl &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.paymentProcessSummary, paymentProcessSummary) ||
                other.paymentProcessSummary == paymentProcessSummary) &&
            (identical(other.underProcessing, underProcessing) ||
                other.underProcessing == underProcessing) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isPullToRefresh, isPullToRefresh) ||
                other.isPullToRefresh == isPullToRefresh));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, currency, paymentProcessSummary,
      underProcessing, errorMessage, isPullToRefresh);

  /// Create a copy of PaymentDetailsReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentDetailsReportStateImplCopyWith<_$PaymentDetailsReportStateImpl>
      get copyWith => __$$PaymentDetailsReportStateImplCopyWithImpl<
          _$PaymentDetailsReportStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentDetailsReportStateImplToJson(
      this,
    );
  }
}

abstract class _PaymentDetailsReportState implements PaymentDetailsReportState {
  factory _PaymentDetailsReportState(
      {final String currency,
      final PaymentProcessSummary? paymentProcessSummary,
      final PaymentInProcessState? underProcessing,
      final String errorMessage,
      final bool isPullToRefresh}) = _$PaymentDetailsReportStateImpl;

  factory _PaymentDetailsReportState.fromJson(Map<String, dynamic> json) =
      _$PaymentDetailsReportStateImpl.fromJson;

  @override
  String get currency;
  @override
  PaymentProcessSummary? get paymentProcessSummary;
  @override
  PaymentInProcessState? get underProcessing;
  @override
  String get errorMessage;
  @override
  bool get isPullToRefresh;

  /// Create a copy of PaymentDetailsReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentDetailsReportStateImplCopyWith<_$PaymentDetailsReportStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
