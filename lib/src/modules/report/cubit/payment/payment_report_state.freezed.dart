// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaymentReportState _$PaymentReportStateFromJson(Map<String, dynamic> json) {
  return _PaymentReportState.fromJson(json);
}

/// @nodoc
mixin _$PaymentReportState {
  String get currency => throw _privateConstructorUsedError;
  PaymentSummary? get paymentSummary => throw _privateConstructorUsedError;
  List<PaymentReportData> get payments => throw _privateConstructorUsedError;
  MinimumPaymentDetails? get minimumPaymentDetails =>
      throw _privateConstructorUsedError;
  Country? get country => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  bool get isPullToRefresh => throw _privateConstructorUsedError;

  /// Serializes this PaymentReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentReportStateCopyWith<PaymentReportState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentReportStateCopyWith<$Res> {
  factory $PaymentReportStateCopyWith(
          PaymentReportState value, $Res Function(PaymentReportState) then) =
      _$PaymentReportStateCopyWithImpl<$Res, PaymentReportState>;
  @useResult
  $Res call(
      {String currency,
      PaymentSummary? paymentSummary,
      List<PaymentReportData> payments,
      MinimumPaymentDetails? minimumPaymentDetails,
      Country? country,
      String errorMessage,
      bool isPullToRefresh});

  $PaymentSummaryCopyWith<$Res>? get paymentSummary;
  $MinimumPaymentDetailsCopyWith<$Res>? get minimumPaymentDetails;
}

/// @nodoc
class _$PaymentReportStateCopyWithImpl<$Res, $Val extends PaymentReportState>
    implements $PaymentReportStateCopyWith<$Res> {
  _$PaymentReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? paymentSummary = freezed,
    Object? payments = null,
    Object? minimumPaymentDetails = freezed,
    Object? country = freezed,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
  }) {
    return _then(_value.copyWith(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      paymentSummary: freezed == paymentSummary
          ? _value.paymentSummary
          : paymentSummary // ignore: cast_nullable_to_non_nullable
              as PaymentSummary?,
      payments: null == payments
          ? _value.payments
          : payments // ignore: cast_nullable_to_non_nullable
              as List<PaymentReportData>,
      minimumPaymentDetails: freezed == minimumPaymentDetails
          ? _value.minimumPaymentDetails
          : minimumPaymentDetails // ignore: cast_nullable_to_non_nullable
              as MinimumPaymentDetails?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country?,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of PaymentReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentSummaryCopyWith<$Res>? get paymentSummary {
    if (_value.paymentSummary == null) {
      return null;
    }

    return $PaymentSummaryCopyWith<$Res>(_value.paymentSummary!, (value) {
      return _then(_value.copyWith(paymentSummary: value) as $Val);
    });
  }

  /// Create a copy of PaymentReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MinimumPaymentDetailsCopyWith<$Res>? get minimumPaymentDetails {
    if (_value.minimumPaymentDetails == null) {
      return null;
    }

    return $MinimumPaymentDetailsCopyWith<$Res>(_value.minimumPaymentDetails!,
        (value) {
      return _then(_value.copyWith(minimumPaymentDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PaymentReportStateImplCopyWith<$Res>
    implements $PaymentReportStateCopyWith<$Res> {
  factory _$$PaymentReportStateImplCopyWith(_$PaymentReportStateImpl value,
          $Res Function(_$PaymentReportStateImpl) then) =
      __$$PaymentReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String currency,
      PaymentSummary? paymentSummary,
      List<PaymentReportData> payments,
      MinimumPaymentDetails? minimumPaymentDetails,
      Country? country,
      String errorMessage,
      bool isPullToRefresh});

  @override
  $PaymentSummaryCopyWith<$Res>? get paymentSummary;
  @override
  $MinimumPaymentDetailsCopyWith<$Res>? get minimumPaymentDetails;
}

/// @nodoc
class __$$PaymentReportStateImplCopyWithImpl<$Res>
    extends _$PaymentReportStateCopyWithImpl<$Res, _$PaymentReportStateImpl>
    implements _$$PaymentReportStateImplCopyWith<$Res> {
  __$$PaymentReportStateImplCopyWithImpl(_$PaymentReportStateImpl _value,
      $Res Function(_$PaymentReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? paymentSummary = freezed,
    Object? payments = null,
    Object? minimumPaymentDetails = freezed,
    Object? country = freezed,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
  }) {
    return _then(_$PaymentReportStateImpl(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      paymentSummary: freezed == paymentSummary
          ? _value.paymentSummary
          : paymentSummary // ignore: cast_nullable_to_non_nullable
              as PaymentSummary?,
      payments: null == payments
          ? _value._payments
          : payments // ignore: cast_nullable_to_non_nullable
              as List<PaymentReportData>,
      minimumPaymentDetails: freezed == minimumPaymentDetails
          ? _value.minimumPaymentDetails
          : minimumPaymentDetails // ignore: cast_nullable_to_non_nullable
              as MinimumPaymentDetails?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country?,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentReportStateImpl implements _PaymentReportState {
  _$PaymentReportStateImpl(
      {this.currency = '',
      this.paymentSummary,
      final List<PaymentReportData> payments = const [],
      this.minimumPaymentDetails,
      this.country,
      this.errorMessage = '',
      this.isPullToRefresh = false})
      : _payments = payments;

  factory _$PaymentReportStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentReportStateImplFromJson(json);

  @override
  @JsonKey()
  final String currency;
  @override
  final PaymentSummary? paymentSummary;
  final List<PaymentReportData> _payments;
  @override
  @JsonKey()
  List<PaymentReportData> get payments {
    if (_payments is EqualUnmodifiableListView) return _payments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_payments);
  }

  @override
  final MinimumPaymentDetails? minimumPaymentDetails;
  @override
  final Country? country;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool isPullToRefresh;

  @override
  String toString() {
    return 'PaymentReportState(currency: $currency, paymentSummary: $paymentSummary, payments: $payments, minimumPaymentDetails: $minimumPaymentDetails, country: $country, errorMessage: $errorMessage, isPullToRefresh: $isPullToRefresh)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentReportStateImpl &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.paymentSummary, paymentSummary) ||
                other.paymentSummary == paymentSummary) &&
            const DeepCollectionEquality().equals(other._payments, _payments) &&
            (identical(other.minimumPaymentDetails, minimumPaymentDetails) ||
                other.minimumPaymentDetails == minimumPaymentDetails) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isPullToRefresh, isPullToRefresh) ||
                other.isPullToRefresh == isPullToRefresh));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      currency,
      paymentSummary,
      const DeepCollectionEquality().hash(_payments),
      minimumPaymentDetails,
      country,
      errorMessage,
      isPullToRefresh);

  /// Create a copy of PaymentReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentReportStateImplCopyWith<_$PaymentReportStateImpl> get copyWith =>
      __$$PaymentReportStateImplCopyWithImpl<_$PaymentReportStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentReportStateImplToJson(
      this,
    );
  }
}

abstract class _PaymentReportState implements PaymentReportState {
  factory _PaymentReportState(
      {final String currency,
      final PaymentSummary? paymentSummary,
      final List<PaymentReportData> payments,
      final MinimumPaymentDetails? minimumPaymentDetails,
      final Country? country,
      final String errorMessage,
      final bool isPullToRefresh}) = _$PaymentReportStateImpl;

  factory _PaymentReportState.fromJson(Map<String, dynamic> json) =
      _$PaymentReportStateImpl.fromJson;

  @override
  String get currency;
  @override
  PaymentSummary? get paymentSummary;
  @override
  List<PaymentReportData> get payments;
  @override
  MinimumPaymentDetails? get minimumPaymentDetails;
  @override
  Country? get country;
  @override
  String get errorMessage;
  @override
  bool get isPullToRefresh;

  /// Create a copy of PaymentReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentReportStateImplCopyWith<_$PaymentReportStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
