import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_process_summary.dart';

part 'payment_details_report_state.freezed.dart';
part 'payment_details_report_state.g.dart';

@freezed
class PaymentDetailsReportState extends BaseCubitState with _$PaymentDetailsReportState {
  factory PaymentDetailsReportState({
    @Default('') String currency,
    PaymentProcessSummary? paymentProcessSummary,
    PaymentInProcessState? underProcessing,
    @Default('') String errorMessage,
    @Default(false) bool isPullToRefresh,
  }) = _PaymentDetailsReportState;

  factory PaymentDetailsReportState.fromJson(Map<String, Object?> json) => _$PaymentDetailsReportStateFromJson(json);
}
