// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_invoice_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentInvoiceReportStateImpl _$$PaymentInvoiceReportStateImplFromJson(
        Map<String, dynamic> json) =>
    _$PaymentInvoiceReportStateImpl(
      invoiceDetailResponse: json['invoiceDetailResponse'] == null
          ? null
          : InvoiceDetailResponse.fromJson(
              json['invoiceDetailResponse'] as Map<String, dynamic>),
      currency: json['currency'] as String? ?? '',
      errorMessage: json['errorMessage'] as String? ?? '',
      isPullToRefresh: json['isPullToRefresh'] as bool? ?? false,
    );

Map<String, dynamic> _$$PaymentInvoiceReportStateImplToJson(
        _$PaymentInvoiceReportStateImpl instance) =>
    <String, dynamic>{
      'invoiceDetailResponse': instance.invoiceDetailResponse,
      'currency': instance.currency,
      'errorMessage': instance.errorMessage,
      'isPullToRefresh': instance.isPullToRefresh,
    };
