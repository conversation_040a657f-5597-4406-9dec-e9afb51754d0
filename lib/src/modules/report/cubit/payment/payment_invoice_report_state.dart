import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/invoice_detail_response.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_process_summary.dart';

part 'payment_invoice_report_state.freezed.dart';
part 'payment_invoice_report_state.g.dart';

@freezed
class PaymentInvoiceReportState extends BaseCubitState with _$PaymentInvoiceReportState {
  factory PaymentInvoiceReportState({
    InvoiceDetailResponse? invoiceDetailResponse,
    @Default('') String currency,
    @Default('') String errorMessage,
    @Default(false) bool isPullToRefresh,
  }) = _PaymentInvoiceReportState;

  factory PaymentInvoiceReportState.fromJson(Map<String, Object?> json) => _$PaymentInvoiceReportStateFromJson(json);
}
