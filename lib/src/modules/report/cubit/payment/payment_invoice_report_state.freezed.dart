// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_invoice_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaymentInvoiceReportState _$PaymentInvoiceReportStateFromJson(
    Map<String, dynamic> json) {
  return _PaymentInvoiceReportState.fromJson(json);
}

/// @nodoc
mixin _$PaymentInvoiceReportState {
  InvoiceDetailResponse? get invoiceDetailResponse =>
      throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  bool get isPullToRefresh => throw _privateConstructorUsedError;

  /// Serializes this PaymentInvoiceReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentInvoiceReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentInvoiceReportStateCopyWith<PaymentInvoiceReportState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentInvoiceReportStateCopyWith<$Res> {
  factory $PaymentInvoiceReportStateCopyWith(PaymentInvoiceReportState value,
          $Res Function(PaymentInvoiceReportState) then) =
      _$PaymentInvoiceReportStateCopyWithImpl<$Res, PaymentInvoiceReportState>;
  @useResult
  $Res call(
      {InvoiceDetailResponse? invoiceDetailResponse,
      String currency,
      String errorMessage,
      bool isPullToRefresh});

  $InvoiceDetailResponseCopyWith<$Res>? get invoiceDetailResponse;
}

/// @nodoc
class _$PaymentInvoiceReportStateCopyWithImpl<$Res,
        $Val extends PaymentInvoiceReportState>
    implements $PaymentInvoiceReportStateCopyWith<$Res> {
  _$PaymentInvoiceReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentInvoiceReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? invoiceDetailResponse = freezed,
    Object? currency = null,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
  }) {
    return _then(_value.copyWith(
      invoiceDetailResponse: freezed == invoiceDetailResponse
          ? _value.invoiceDetailResponse
          : invoiceDetailResponse // ignore: cast_nullable_to_non_nullable
              as InvoiceDetailResponse?,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of PaymentInvoiceReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InvoiceDetailResponseCopyWith<$Res>? get invoiceDetailResponse {
    if (_value.invoiceDetailResponse == null) {
      return null;
    }

    return $InvoiceDetailResponseCopyWith<$Res>(_value.invoiceDetailResponse!,
        (value) {
      return _then(_value.copyWith(invoiceDetailResponse: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PaymentInvoiceReportStateImplCopyWith<$Res>
    implements $PaymentInvoiceReportStateCopyWith<$Res> {
  factory _$$PaymentInvoiceReportStateImplCopyWith(
          _$PaymentInvoiceReportStateImpl value,
          $Res Function(_$PaymentInvoiceReportStateImpl) then) =
      __$$PaymentInvoiceReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {InvoiceDetailResponse? invoiceDetailResponse,
      String currency,
      String errorMessage,
      bool isPullToRefresh});

  @override
  $InvoiceDetailResponseCopyWith<$Res>? get invoiceDetailResponse;
}

/// @nodoc
class __$$PaymentInvoiceReportStateImplCopyWithImpl<$Res>
    extends _$PaymentInvoiceReportStateCopyWithImpl<$Res,
        _$PaymentInvoiceReportStateImpl>
    implements _$$PaymentInvoiceReportStateImplCopyWith<$Res> {
  __$$PaymentInvoiceReportStateImplCopyWithImpl(
      _$PaymentInvoiceReportStateImpl _value,
      $Res Function(_$PaymentInvoiceReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentInvoiceReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? invoiceDetailResponse = freezed,
    Object? currency = null,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
  }) {
    return _then(_$PaymentInvoiceReportStateImpl(
      invoiceDetailResponse: freezed == invoiceDetailResponse
          ? _value.invoiceDetailResponse
          : invoiceDetailResponse // ignore: cast_nullable_to_non_nullable
              as InvoiceDetailResponse?,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentInvoiceReportStateImpl implements _PaymentInvoiceReportState {
  _$PaymentInvoiceReportStateImpl(
      {this.invoiceDetailResponse,
      this.currency = '',
      this.errorMessage = '',
      this.isPullToRefresh = false});

  factory _$PaymentInvoiceReportStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentInvoiceReportStateImplFromJson(json);

  @override
  final InvoiceDetailResponse? invoiceDetailResponse;
  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool isPullToRefresh;

  @override
  String toString() {
    return 'PaymentInvoiceReportState(invoiceDetailResponse: $invoiceDetailResponse, currency: $currency, errorMessage: $errorMessage, isPullToRefresh: $isPullToRefresh)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentInvoiceReportStateImpl &&
            (identical(other.invoiceDetailResponse, invoiceDetailResponse) ||
                other.invoiceDetailResponse == invoiceDetailResponse) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isPullToRefresh, isPullToRefresh) ||
                other.isPullToRefresh == isPullToRefresh));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, invoiceDetailResponse, currency,
      errorMessage, isPullToRefresh);

  /// Create a copy of PaymentInvoiceReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentInvoiceReportStateImplCopyWith<_$PaymentInvoiceReportStateImpl>
      get copyWith => __$$PaymentInvoiceReportStateImplCopyWithImpl<
          _$PaymentInvoiceReportStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentInvoiceReportStateImplToJson(
      this,
    );
  }
}

abstract class _PaymentInvoiceReportState implements PaymentInvoiceReportState {
  factory _PaymentInvoiceReportState(
      {final InvoiceDetailResponse? invoiceDetailResponse,
      final String currency,
      final String errorMessage,
      final bool isPullToRefresh}) = _$PaymentInvoiceReportStateImpl;

  factory _PaymentInvoiceReportState.fromJson(Map<String, dynamic> json) =
      _$PaymentInvoiceReportStateImpl.fromJson;

  @override
  InvoiceDetailResponse? get invoiceDetailResponse;
  @override
  String get currency;
  @override
  String get errorMessage;
  @override
  bool get isPullToRefresh;

  /// Create a copy of PaymentInvoiceReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentInvoiceReportStateImplCopyWith<_$PaymentInvoiceReportStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
