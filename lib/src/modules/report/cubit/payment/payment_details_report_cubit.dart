import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_details_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_process_summary.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class PaymentDetailsReportCubit extends BaseCubit<PaymentDetailsReportState> {
  final ReportRepository reportRepository;
  PaymentDetailsReportCubit(this.reportRepository) : super(PaymentDetailsReportState());

  Future<void> findPaymentProcessStageDetails(PaymentProcessType type) async {
    try {
      final results = await Future.wait([
        reportRepository.findPaymentProcessStageDetails(type),
        commonCubit.sharedPreferencesService.getPublisherCurrency()
      ]);
      if (type == PaymentProcessType.REWARD_BEING_PROCESSED_PAYMENT) {
        emit(state.copyWith(
          currency: results[1] as String,
          underProcessing: PaymentInProcessState.fromJson(Map<String, dynamic>.from(results[0])),
        ));
      } else {
        emit(state.copyWith(
          currency: results[1] as String,
          paymentProcessSummary: PaymentProcessSummary.fromJson(Map<String, dynamic>.from(results[0])),
        ));
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes payment details data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh(PaymentProcessType processType) async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/payment-process-stage-details');

      await _refreshPaymentDetailsForPullToRefresh(processType);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  /// Specialized refresh method for payment details data during pull-to-refresh
  /// This method doesn't trigger loading states to avoid multiple loading indicators
  Future<void> _refreshPaymentDetailsForPullToRefresh(PaymentProcessType processType) async {
    try {
      final results = await Future.wait([
        reportRepository.findPaymentProcessStageDetails(processType),
        commonCubit.sharedPreferencesService.getPublisherCurrency()
      ]);

      if (processType == PaymentProcessType.REWARD_BEING_PROCESSED_PAYMENT) {
        emit(state.copyWith(
          currency: results[1] as String,
          underProcessing: PaymentInProcessState.fromJson(Map<String, dynamic>.from(results[0])),
        ));
      } else {
        emit(state.copyWith(
          currency: results[1] as String,
          paymentProcessSummary: PaymentProcessSummary.fromJson(Map<String, dynamic>.from(results[0])),
        ));
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
