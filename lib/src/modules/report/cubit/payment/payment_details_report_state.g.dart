// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_details_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentDetailsReportStateImpl _$$PaymentDetailsReportStateImplFromJson(
        Map<String, dynamic> json) =>
    _$PaymentDetailsReportStateImpl(
      currency: json['currency'] as String? ?? '',
      paymentProcessSummary: json['paymentProcessSummary'] == null
          ? null
          : PaymentProcessSummary.fromJson(
              json['paymentProcessSummary'] as Map<String, dynamic>),
      underProcessing: json['underProcessing'] == null
          ? null
          : PaymentInProcessState.fromJson(
              json['underProcessing'] as Map<String, dynamic>),
      errorMessage: json['errorMessage'] as String? ?? '',
      isPullToRefresh: json['isPullToRefresh'] as bool? ?? false,
    );

Map<String, dynamic> _$$PaymentDetailsReportStateImplToJson(
        _$PaymentDetailsReportStateImpl instance) =>
    <String, dynamic>{
      'currency': instance.currency,
      'paymentProcessSummary': instance.paymentProcessSummary,
      'underProcessing': instance.underProcessing,
      'errorMessage': instance.errorMessage,
      'isPullToRefresh': instance.isPullToRefresh,
    };
