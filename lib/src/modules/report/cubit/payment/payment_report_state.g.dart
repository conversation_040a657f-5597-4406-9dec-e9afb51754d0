// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentReportStateImpl _$$PaymentReportStateImplFromJson(
        Map<String, dynamic> json) =>
    _$PaymentReportStateImpl(
      currency: json['currency'] as String? ?? '',
      paymentSummary: json['paymentSummary'] == null
          ? null
          : PaymentSummary.fromJson(
              json['paymentSummary'] as Map<String, dynamic>),
      payments: (json['payments'] as List<dynamic>?)
              ?.map(
                  (e) => PaymentReportData.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      minimumPaymentDetails: json['minimumPaymentDetails'] == null
          ? null
          : MinimumPaymentDetails.fromJson(
              json['minimumPaymentDetails'] as Map<String, dynamic>),
      country: $enumDecodeNullable(_$CountryEnumMap, json['country']),
      errorMessage: json['errorMessage'] as String? ?? '',
      isPullToRefresh: json['isPullToRefresh'] as bool? ?? false,
    );

Map<String, dynamic> _$$PaymentReportStateImplToJson(
        _$PaymentReportStateImpl instance) =>
    <String, dynamic>{
      'currency': instance.currency,
      'paymentSummary': instance.paymentSummary,
      'payments': instance.payments,
      'minimumPaymentDetails': instance.minimumPaymentDetails,
      'country': _$CountryEnumMap[instance.country],
      'errorMessage': instance.errorMessage,
      'isPullToRefresh': instance.isPullToRefresh,
    };

const _$CountryEnumMap = {
  Country.INDONESIA: 'INDONESIA',
  Country.MALAYSIA: 'MALAYSIA',
  Country.SINGAPORE: 'SINGAPORE',
  Country.THAILAND: 'THAILAND',
  Country.GLOBAL: 'GLOBAL',
};
