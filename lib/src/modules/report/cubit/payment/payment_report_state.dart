import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/minimum_payment_details.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_summary.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

part 'payment_report_state.freezed.dart';
part 'payment_report_state.g.dart';

@freezed
class PaymentReportState extends BaseCubitState with _$PaymentReportState {
  factory PaymentReportState({
    @Default('') String currency,
    PaymentSummary? paymentSummary,
    @Default([]) List<PaymentReportData> payments,
    MinimumPaymentDetails? minimumPaymentDetails,
    Country? country,
    @Default('') String errorMessage,
    @Default(false) bool isPullToRefresh,
  }) = _PaymentReportState;

  factory PaymentReportState.fromJson(Map<String, Object?> json) => _$PaymentReportStateFromJson(json);
}
