import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/performance_daily_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';

part 'performance_daily_report_state.freezed.dart';
part 'performance_daily_report_state.g.dart';

@freezed
class PerformanceDailyReportState extends BaseCubitState
    with _$PerformanceDailyReportState {
  factory PerformanceDailyReportState({
    @Default(false) bool showReport,
    @Default('') String currency,
    @Default(PerformanceChartTitle.CLICKS) PerformanceChartTitle left,
    @Default(PerformanceChartTitle.CONVERSIONS) PerformanceChartTitle right,
    @Default([]) List<PerformanceDailyReportData> reportData,
    @Default('') String errorMessage,
  }) = _PerformanceDailyReportState;

  factory PerformanceDailyReportState.fromJson(Map<String, Object?> json) =>
      _$PerformanceDailyReportStateFromJson(json);
}
