// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'performance_monthly_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PerformanceMonthlyReportState _$PerformanceMonthlyReportStateFromJson(
    Map<String, dynamic> json) {
  return _PerformanceMonthlyReportState.fromJson(json);
}

/// @nodoc
mixin _$PerformanceMonthlyReportState {
  bool get showReport => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  PerformanceChartTitle get left => throw _privateConstructorUsedError;
  PerformanceChartTitle get right => throw _privateConstructorUsedError;
  List<PerformanceMonthlyReportData> get reportData =>
      throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this PerformanceMonthlyReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PerformanceMonthlyReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PerformanceMonthlyReportStateCopyWith<PerformanceMonthlyReportState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PerformanceMonthlyReportStateCopyWith<$Res> {
  factory $PerformanceMonthlyReportStateCopyWith(
          PerformanceMonthlyReportState value,
          $Res Function(PerformanceMonthlyReportState) then) =
      _$PerformanceMonthlyReportStateCopyWithImpl<$Res,
          PerformanceMonthlyReportState>;
  @useResult
  $Res call(
      {bool showReport,
      String currency,
      PerformanceChartTitle left,
      PerformanceChartTitle right,
      List<PerformanceMonthlyReportData> reportData,
      String errorMessage});
}

/// @nodoc
class _$PerformanceMonthlyReportStateCopyWithImpl<$Res,
        $Val extends PerformanceMonthlyReportState>
    implements $PerformanceMonthlyReportStateCopyWith<$Res> {
  _$PerformanceMonthlyReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PerformanceMonthlyReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showReport = null,
    Object? currency = null,
    Object? left = null,
    Object? right = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      showReport: null == showReport
          ? _value.showReport
          : showReport // ignore: cast_nullable_to_non_nullable
              as bool,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      left: null == left
          ? _value.left
          : left // ignore: cast_nullable_to_non_nullable
              as PerformanceChartTitle,
      right: null == right
          ? _value.right
          : right // ignore: cast_nullable_to_non_nullable
              as PerformanceChartTitle,
      reportData: null == reportData
          ? _value.reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<PerformanceMonthlyReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PerformanceMonthlyReportStateImplCopyWith<$Res>
    implements $PerformanceMonthlyReportStateCopyWith<$Res> {
  factory _$$PerformanceMonthlyReportStateImplCopyWith(
          _$PerformanceMonthlyReportStateImpl value,
          $Res Function(_$PerformanceMonthlyReportStateImpl) then) =
      __$$PerformanceMonthlyReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showReport,
      String currency,
      PerformanceChartTitle left,
      PerformanceChartTitle right,
      List<PerformanceMonthlyReportData> reportData,
      String errorMessage});
}

/// @nodoc
class __$$PerformanceMonthlyReportStateImplCopyWithImpl<$Res>
    extends _$PerformanceMonthlyReportStateCopyWithImpl<$Res,
        _$PerformanceMonthlyReportStateImpl>
    implements _$$PerformanceMonthlyReportStateImplCopyWith<$Res> {
  __$$PerformanceMonthlyReportStateImplCopyWithImpl(
      _$PerformanceMonthlyReportStateImpl _value,
      $Res Function(_$PerformanceMonthlyReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PerformanceMonthlyReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showReport = null,
    Object? currency = null,
    Object? left = null,
    Object? right = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_$PerformanceMonthlyReportStateImpl(
      showReport: null == showReport
          ? _value.showReport
          : showReport // ignore: cast_nullable_to_non_nullable
              as bool,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      left: null == left
          ? _value.left
          : left // ignore: cast_nullable_to_non_nullable
              as PerformanceChartTitle,
      right: null == right
          ? _value.right
          : right // ignore: cast_nullable_to_non_nullable
              as PerformanceChartTitle,
      reportData: null == reportData
          ? _value._reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<PerformanceMonthlyReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PerformanceMonthlyReportStateImpl
    implements _PerformanceMonthlyReportState {
  _$PerformanceMonthlyReportStateImpl(
      {this.showReport = false,
      this.currency = '',
      this.left = PerformanceChartTitle.CLICKS,
      this.right = PerformanceChartTitle.CONVERSIONS,
      final List<PerformanceMonthlyReportData> reportData = const [],
      this.errorMessage = ''})
      : _reportData = reportData;

  factory _$PerformanceMonthlyReportStateImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$PerformanceMonthlyReportStateImplFromJson(json);

  @override
  @JsonKey()
  final bool showReport;
  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final PerformanceChartTitle left;
  @override
  @JsonKey()
  final PerformanceChartTitle right;
  final List<PerformanceMonthlyReportData> _reportData;
  @override
  @JsonKey()
  List<PerformanceMonthlyReportData> get reportData {
    if (_reportData is EqualUnmodifiableListView) return _reportData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reportData);
  }

  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'PerformanceMonthlyReportState(showReport: $showReport, currency: $currency, left: $left, right: $right, reportData: $reportData, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceMonthlyReportStateImpl &&
            (identical(other.showReport, showReport) ||
                other.showReport == showReport) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.left, left) || other.left == left) &&
            (identical(other.right, right) || other.right == right) &&
            const DeepCollectionEquality()
                .equals(other._reportData, _reportData) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, showReport, currency, left,
      right, const DeepCollectionEquality().hash(_reportData), errorMessage);

  /// Create a copy of PerformanceMonthlyReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PerformanceMonthlyReportStateImplCopyWith<
          _$PerformanceMonthlyReportStateImpl>
      get copyWith => __$$PerformanceMonthlyReportStateImplCopyWithImpl<
          _$PerformanceMonthlyReportStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PerformanceMonthlyReportStateImplToJson(
      this,
    );
  }
}

abstract class _PerformanceMonthlyReportState
    implements PerformanceMonthlyReportState {
  factory _PerformanceMonthlyReportState(
      {final bool showReport,
      final String currency,
      final PerformanceChartTitle left,
      final PerformanceChartTitle right,
      final List<PerformanceMonthlyReportData> reportData,
      final String errorMessage}) = _$PerformanceMonthlyReportStateImpl;

  factory _PerformanceMonthlyReportState.fromJson(Map<String, dynamic> json) =
      _$PerformanceMonthlyReportStateImpl.fromJson;

  @override
  bool get showReport;
  @override
  String get currency;
  @override
  PerformanceChartTitle get left;
  @override
  PerformanceChartTitle get right;
  @override
  List<PerformanceMonthlyReportData> get reportData;
  @override
  String get errorMessage;

  /// Create a copy of PerformanceMonthlyReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PerformanceMonthlyReportStateImplCopyWith<
          _$PerformanceMonthlyReportStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
