import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/performance/performance_monthly_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class PerformanceMonthlyReportCubit extends BaseCubit<PerformanceMonthlyReportState> {
  final ReportRepository reportRepository;
  PerformanceMonthlyReportCubit(this.reportRepository) : super(PerformanceMonthlyReportState());

  void hideReport() {
    emit(state.copyWith(showReport: false));
  }

  Future<void> findConversions(DateTime fromMonth, DateTime toMonth, ReportQueryPeriodBase dateType,
      ConversionStatus? status, Item? campaign, Item? site) async {
    try {
      String? countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      int? siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      List<dynamic> results = await Future.wait([
        commonCubit.sharedPreferencesService.getPublisherCurrency(),
        reportRepository.findPerformanceMonthlyReportData(fromMonth.toZonedIso8601(countryCode!.toCountry),
            toMonth.toZonedIso8601(countryCode.toCountry), dateType, status, campaign?.value, site?.value ?? siteId)
      ]);

      emit(state.copyWith(currency: results[0], showReport: true, reportData: results[1]));
    } on DioException catch (e) {
      handleError(e, (message) => emit(state.copyWith(showReport: true, reportData: [], errorMessage: message)));
    }
  }

  void updateChartTitles(PerformanceChartTitle left, PerformanceChartTitle right) {
    emit(state.copyWith(left: left, right: right));
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes performance monthly data while bypassing cache to ensure fresh data
  Future<void> pullToRefresh(DateTime fromMonth, DateTime toMonth, ReportQueryPeriodBase dateType,
      ConversionStatus? status, Item? campaign, Item? site) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/monthly');

      await findConversions(fromMonth, toMonth, dateType, status, campaign, site);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
