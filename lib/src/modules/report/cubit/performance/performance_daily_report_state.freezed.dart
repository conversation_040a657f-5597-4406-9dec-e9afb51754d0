// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'performance_daily_report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PerformanceDailyReportState _$PerformanceDailyReportStateFromJson(
    Map<String, dynamic> json) {
  return _PerformanceDailyReportState.fromJson(json);
}

/// @nodoc
mixin _$PerformanceDailyReportState {
  bool get showReport => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  PerformanceChartTitle get left => throw _privateConstructorUsedError;
  PerformanceChartTitle get right => throw _privateConstructorUsedError;
  List<PerformanceDailyReportData> get reportData =>
      throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this PerformanceDailyReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PerformanceDailyReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PerformanceDailyReportStateCopyWith<PerformanceDailyReportState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PerformanceDailyReportStateCopyWith<$Res> {
  factory $PerformanceDailyReportStateCopyWith(
          PerformanceDailyReportState value,
          $Res Function(PerformanceDailyReportState) then) =
      _$PerformanceDailyReportStateCopyWithImpl<$Res,
          PerformanceDailyReportState>;
  @useResult
  $Res call(
      {bool showReport,
      String currency,
      PerformanceChartTitle left,
      PerformanceChartTitle right,
      List<PerformanceDailyReportData> reportData,
      String errorMessage});
}

/// @nodoc
class _$PerformanceDailyReportStateCopyWithImpl<$Res,
        $Val extends PerformanceDailyReportState>
    implements $PerformanceDailyReportStateCopyWith<$Res> {
  _$PerformanceDailyReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PerformanceDailyReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showReport = null,
    Object? currency = null,
    Object? left = null,
    Object? right = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      showReport: null == showReport
          ? _value.showReport
          : showReport // ignore: cast_nullable_to_non_nullable
              as bool,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      left: null == left
          ? _value.left
          : left // ignore: cast_nullable_to_non_nullable
              as PerformanceChartTitle,
      right: null == right
          ? _value.right
          : right // ignore: cast_nullable_to_non_nullable
              as PerformanceChartTitle,
      reportData: null == reportData
          ? _value.reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<PerformanceDailyReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PerformanceDailyReportStateImplCopyWith<$Res>
    implements $PerformanceDailyReportStateCopyWith<$Res> {
  factory _$$PerformanceDailyReportStateImplCopyWith(
          _$PerformanceDailyReportStateImpl value,
          $Res Function(_$PerformanceDailyReportStateImpl) then) =
      __$$PerformanceDailyReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showReport,
      String currency,
      PerformanceChartTitle left,
      PerformanceChartTitle right,
      List<PerformanceDailyReportData> reportData,
      String errorMessage});
}

/// @nodoc
class __$$PerformanceDailyReportStateImplCopyWithImpl<$Res>
    extends _$PerformanceDailyReportStateCopyWithImpl<$Res,
        _$PerformanceDailyReportStateImpl>
    implements _$$PerformanceDailyReportStateImplCopyWith<$Res> {
  __$$PerformanceDailyReportStateImplCopyWithImpl(
      _$PerformanceDailyReportStateImpl _value,
      $Res Function(_$PerformanceDailyReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PerformanceDailyReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showReport = null,
    Object? currency = null,
    Object? left = null,
    Object? right = null,
    Object? reportData = null,
    Object? errorMessage = null,
  }) {
    return _then(_$PerformanceDailyReportStateImpl(
      showReport: null == showReport
          ? _value.showReport
          : showReport // ignore: cast_nullable_to_non_nullable
              as bool,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      left: null == left
          ? _value.left
          : left // ignore: cast_nullable_to_non_nullable
              as PerformanceChartTitle,
      right: null == right
          ? _value.right
          : right // ignore: cast_nullable_to_non_nullable
              as PerformanceChartTitle,
      reportData: null == reportData
          ? _value._reportData
          : reportData // ignore: cast_nullable_to_non_nullable
              as List<PerformanceDailyReportData>,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PerformanceDailyReportStateImpl
    implements _PerformanceDailyReportState {
  _$PerformanceDailyReportStateImpl(
      {this.showReport = false,
      this.currency = '',
      this.left = PerformanceChartTitle.CLICKS,
      this.right = PerformanceChartTitle.CONVERSIONS,
      final List<PerformanceDailyReportData> reportData = const [],
      this.errorMessage = ''})
      : _reportData = reportData;

  factory _$PerformanceDailyReportStateImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$PerformanceDailyReportStateImplFromJson(json);

  @override
  @JsonKey()
  final bool showReport;
  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final PerformanceChartTitle left;
  @override
  @JsonKey()
  final PerformanceChartTitle right;
  final List<PerformanceDailyReportData> _reportData;
  @override
  @JsonKey()
  List<PerformanceDailyReportData> get reportData {
    if (_reportData is EqualUnmodifiableListView) return _reportData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reportData);
  }

  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'PerformanceDailyReportState(showReport: $showReport, currency: $currency, left: $left, right: $right, reportData: $reportData, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceDailyReportStateImpl &&
            (identical(other.showReport, showReport) ||
                other.showReport == showReport) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.left, left) || other.left == left) &&
            (identical(other.right, right) || other.right == right) &&
            const DeepCollectionEquality()
                .equals(other._reportData, _reportData) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, showReport, currency, left,
      right, const DeepCollectionEquality().hash(_reportData), errorMessage);

  /// Create a copy of PerformanceDailyReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PerformanceDailyReportStateImplCopyWith<_$PerformanceDailyReportStateImpl>
      get copyWith => __$$PerformanceDailyReportStateImplCopyWithImpl<
          _$PerformanceDailyReportStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PerformanceDailyReportStateImplToJson(
      this,
    );
  }
}

abstract class _PerformanceDailyReportState
    implements PerformanceDailyReportState {
  factory _PerformanceDailyReportState(
      {final bool showReport,
      final String currency,
      final PerformanceChartTitle left,
      final PerformanceChartTitle right,
      final List<PerformanceDailyReportData> reportData,
      final String errorMessage}) = _$PerformanceDailyReportStateImpl;

  factory _PerformanceDailyReportState.fromJson(Map<String, dynamic> json) =
      _$PerformanceDailyReportStateImpl.fromJson;

  @override
  bool get showReport;
  @override
  String get currency;
  @override
  PerformanceChartTitle get left;
  @override
  PerformanceChartTitle get right;
  @override
  List<PerformanceDailyReportData> get reportData;
  @override
  String get errorMessage;

  /// Create a copy of PerformanceDailyReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PerformanceDailyReportStateImplCopyWith<_$PerformanceDailyReportStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
