// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance_daily_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PerformanceDailyReportStateImpl _$$PerformanceDailyReportStateImplFromJson(
        Map<String, dynamic> json) =>
    _$PerformanceDailyReportStateImpl(
      showReport: json['showReport'] as bool? ?? false,
      currency: json['currency'] as String? ?? '',
      left: $enumDecodeNullable(_$PerformanceChartTitleEnumMap, json['left']) ??
          PerformanceChartTitle.CLICKS,
      right:
          $enumDecodeNullable(_$PerformanceChartTitleEnumMap, json['right']) ??
              PerformanceChartTitle.CONVERSIONS,
      reportData: (json['reportData'] as List<dynamic>?)
              ?.map((e) => PerformanceDailyReportData.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          const [],
      errorMessage: json['errorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$$PerformanceDailyReportStateImplToJson(
        _$PerformanceDailyReportStateImpl instance) =>
    <String, dynamic>{
      'showReport': instance.showReport,
      'currency': instance.currency,
      'left': _$PerformanceChartTitleEnumMap[instance.left]!,
      'right': _$PerformanceChartTitleEnumMap[instance.right]!,
      'reportData': instance.reportData,
      'errorMessage': instance.errorMessage,
    };

const _$PerformanceChartTitleEnumMap = {
  PerformanceChartTitle.CLICKS: 'CLICKS',
  PerformanceChartTitle.CONVERSIONS: 'CONVERSIONS',
  PerformanceChartTitle.REWARD: 'REWARD',
  PerformanceChartTitle.CVR: 'CVR',
  PerformanceChartTitle.EPC: 'EPC',
};
