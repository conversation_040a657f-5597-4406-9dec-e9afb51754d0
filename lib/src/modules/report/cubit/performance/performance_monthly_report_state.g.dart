// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance_monthly_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PerformanceMonthlyReportStateImpl
    _$$PerformanceMonthlyReportStateImplFromJson(Map<String, dynamic> json) =>
        _$PerformanceMonthlyReportStateImpl(
          showReport: json['showReport'] as bool? ?? false,
          currency: json['currency'] as String? ?? '',
          left: $enumDecodeNullable(
                  _$PerformanceChartTitleEnumMap, json['left']) ??
              PerformanceChartTitle.CLICKS,
          right: $enumDecodeNullable(
                  _$PerformanceChartTitleEnumMap, json['right']) ??
              PerformanceChartTitle.CONVERSIONS,
          reportData: (json['reportData'] as List<dynamic>?)
                  ?.map((e) => PerformanceMonthlyReportData.fromJson(
                      e as Map<String, dynamic>))
                  .toList() ??
              const [],
          errorMessage: json['errorMessage'] as String? ?? '',
        );

Map<String, dynamic> _$$PerformanceMonthlyReportStateImplToJson(
        _$PerformanceMonthlyReportStateImpl instance) =>
    <String, dynamic>{
      'showReport': instance.showReport,
      'currency': instance.currency,
      'left': _$PerformanceChartTitleEnumMap[instance.left]!,
      'right': _$PerformanceChartTitleEnumMap[instance.right]!,
      'reportData': instance.reportData,
      'errorMessage': instance.errorMessage,
    };

const _$PerformanceChartTitleEnumMap = {
  PerformanceChartTitle.CLICKS: 'CLICKS',
  PerformanceChartTitle.CONVERSIONS: 'CONVERSIONS',
  PerformanceChartTitle.REWARD: 'REWARD',
  PerformanceChartTitle.CVR: 'CVR',
  PerformanceChartTitle.EPC: 'EPC',
};
