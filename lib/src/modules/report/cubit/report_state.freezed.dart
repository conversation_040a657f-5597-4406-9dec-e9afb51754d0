// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'report_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReportState _$ReportStateFromJson(Map<String, dynamic> json) {
  return _PerformanceReportState.fromJson(json);
}

/// @nodoc
mixin _$ReportState {
  Map<DateTime, int> get currentOneYearClickCount =>
      throw _privateConstructorUsedError;
  Map<DateTime, int> get currentOneYearConversionCount =>
      throw _privateConstructorUsedError;
  int get thisMonthOccurredConversionCount =>
      throw _privateConstructorUsedError;
  double get lastMonthApprovedReward => throw _privateConstructorUsedError;
  List<CampaignNameAndClicks> get topTenCampaignsClickCount =>
      throw _privateConstructorUsedError;
  PaymentSummary? get paymentSummary => throw _privateConstructorUsedError;
  MinimumPaymentDetails? get minimumPaymentDetails =>
      throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  bool get isPullToRefresh => throw _privateConstructorUsedError;
  Country? get country => throw _privateConstructorUsedError;
  int get selectedSiteId => throw _privateConstructorUsedError;

  /// Serializes this ReportState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReportStateCopyWith<ReportState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReportStateCopyWith<$Res> {
  factory $ReportStateCopyWith(
          ReportState value, $Res Function(ReportState) then) =
      _$ReportStateCopyWithImpl<$Res, ReportState>;
  @useResult
  $Res call(
      {Map<DateTime, int> currentOneYearClickCount,
      Map<DateTime, int> currentOneYearConversionCount,
      int thisMonthOccurredConversionCount,
      double lastMonthApprovedReward,
      List<CampaignNameAndClicks> topTenCampaignsClickCount,
      PaymentSummary? paymentSummary,
      MinimumPaymentDetails? minimumPaymentDetails,
      String currency,
      String errorMessage,
      bool isPullToRefresh,
      Country? country,
      int selectedSiteId});

  $PaymentSummaryCopyWith<$Res>? get paymentSummary;
  $MinimumPaymentDetailsCopyWith<$Res>? get minimumPaymentDetails;
}

/// @nodoc
class _$ReportStateCopyWithImpl<$Res, $Val extends ReportState>
    implements $ReportStateCopyWith<$Res> {
  _$ReportStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentOneYearClickCount = null,
    Object? currentOneYearConversionCount = null,
    Object? thisMonthOccurredConversionCount = null,
    Object? lastMonthApprovedReward = null,
    Object? topTenCampaignsClickCount = null,
    Object? paymentSummary = freezed,
    Object? minimumPaymentDetails = freezed,
    Object? currency = null,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
    Object? country = freezed,
    Object? selectedSiteId = null,
  }) {
    return _then(_value.copyWith(
      currentOneYearClickCount: null == currentOneYearClickCount
          ? _value.currentOneYearClickCount
          : currentOneYearClickCount // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      currentOneYearConversionCount: null == currentOneYearConversionCount
          ? _value.currentOneYearConversionCount
          : currentOneYearConversionCount // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      thisMonthOccurredConversionCount: null == thisMonthOccurredConversionCount
          ? _value.thisMonthOccurredConversionCount
          : thisMonthOccurredConversionCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastMonthApprovedReward: null == lastMonthApprovedReward
          ? _value.lastMonthApprovedReward
          : lastMonthApprovedReward // ignore: cast_nullable_to_non_nullable
              as double,
      topTenCampaignsClickCount: null == topTenCampaignsClickCount
          ? _value.topTenCampaignsClickCount
          : topTenCampaignsClickCount // ignore: cast_nullable_to_non_nullable
              as List<CampaignNameAndClicks>,
      paymentSummary: freezed == paymentSummary
          ? _value.paymentSummary
          : paymentSummary // ignore: cast_nullable_to_non_nullable
              as PaymentSummary?,
      minimumPaymentDetails: freezed == minimumPaymentDetails
          ? _value.minimumPaymentDetails
          : minimumPaymentDetails // ignore: cast_nullable_to_non_nullable
              as MinimumPaymentDetails?,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country?,
      selectedSiteId: null == selectedSiteId
          ? _value.selectedSiteId
          : selectedSiteId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of ReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentSummaryCopyWith<$Res>? get paymentSummary {
    if (_value.paymentSummary == null) {
      return null;
    }

    return $PaymentSummaryCopyWith<$Res>(_value.paymentSummary!, (value) {
      return _then(_value.copyWith(paymentSummary: value) as $Val);
    });
  }

  /// Create a copy of ReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MinimumPaymentDetailsCopyWith<$Res>? get minimumPaymentDetails {
    if (_value.minimumPaymentDetails == null) {
      return null;
    }

    return $MinimumPaymentDetailsCopyWith<$Res>(_value.minimumPaymentDetails!,
        (value) {
      return _then(_value.copyWith(minimumPaymentDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PerformanceReportStateImplCopyWith<$Res>
    implements $ReportStateCopyWith<$Res> {
  factory _$$PerformanceReportStateImplCopyWith(
          _$PerformanceReportStateImpl value,
          $Res Function(_$PerformanceReportStateImpl) then) =
      __$$PerformanceReportStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Map<DateTime, int> currentOneYearClickCount,
      Map<DateTime, int> currentOneYearConversionCount,
      int thisMonthOccurredConversionCount,
      double lastMonthApprovedReward,
      List<CampaignNameAndClicks> topTenCampaignsClickCount,
      PaymentSummary? paymentSummary,
      MinimumPaymentDetails? minimumPaymentDetails,
      String currency,
      String errorMessage,
      bool isPullToRefresh,
      Country? country,
      int selectedSiteId});

  @override
  $PaymentSummaryCopyWith<$Res>? get paymentSummary;
  @override
  $MinimumPaymentDetailsCopyWith<$Res>? get minimumPaymentDetails;
}

/// @nodoc
class __$$PerformanceReportStateImplCopyWithImpl<$Res>
    extends _$ReportStateCopyWithImpl<$Res, _$PerformanceReportStateImpl>
    implements _$$PerformanceReportStateImplCopyWith<$Res> {
  __$$PerformanceReportStateImplCopyWithImpl(
      _$PerformanceReportStateImpl _value,
      $Res Function(_$PerformanceReportStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentOneYearClickCount = null,
    Object? currentOneYearConversionCount = null,
    Object? thisMonthOccurredConversionCount = null,
    Object? lastMonthApprovedReward = null,
    Object? topTenCampaignsClickCount = null,
    Object? paymentSummary = freezed,
    Object? minimumPaymentDetails = freezed,
    Object? currency = null,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
    Object? country = freezed,
    Object? selectedSiteId = null,
  }) {
    return _then(_$PerformanceReportStateImpl(
      currentOneYearClickCount: null == currentOneYearClickCount
          ? _value._currentOneYearClickCount
          : currentOneYearClickCount // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      currentOneYearConversionCount: null == currentOneYearConversionCount
          ? _value._currentOneYearConversionCount
          : currentOneYearConversionCount // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      thisMonthOccurredConversionCount: null == thisMonthOccurredConversionCount
          ? _value.thisMonthOccurredConversionCount
          : thisMonthOccurredConversionCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastMonthApprovedReward: null == lastMonthApprovedReward
          ? _value.lastMonthApprovedReward
          : lastMonthApprovedReward // ignore: cast_nullable_to_non_nullable
              as double,
      topTenCampaignsClickCount: null == topTenCampaignsClickCount
          ? _value._topTenCampaignsClickCount
          : topTenCampaignsClickCount // ignore: cast_nullable_to_non_nullable
              as List<CampaignNameAndClicks>,
      paymentSummary: freezed == paymentSummary
          ? _value.paymentSummary
          : paymentSummary // ignore: cast_nullable_to_non_nullable
              as PaymentSummary?,
      minimumPaymentDetails: freezed == minimumPaymentDetails
          ? _value.minimumPaymentDetails
          : minimumPaymentDetails // ignore: cast_nullable_to_non_nullable
              as MinimumPaymentDetails?,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country?,
      selectedSiteId: null == selectedSiteId
          ? _value.selectedSiteId
          : selectedSiteId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PerformanceReportStateImpl implements _PerformanceReportState {
  _$PerformanceReportStateImpl(
      {final Map<DateTime, int> currentOneYearClickCount = const {},
      final Map<DateTime, int> currentOneYearConversionCount = const {},
      this.thisMonthOccurredConversionCount = 0,
      this.lastMonthApprovedReward = 0,
      final List<CampaignNameAndClicks> topTenCampaignsClickCount = const [],
      this.paymentSummary,
      this.minimumPaymentDetails,
      this.currency = '',
      this.errorMessage = '',
      this.isPullToRefresh = false,
      this.country,
      this.selectedSiteId = 0})
      : _currentOneYearClickCount = currentOneYearClickCount,
        _currentOneYearConversionCount = currentOneYearConversionCount,
        _topTenCampaignsClickCount = topTenCampaignsClickCount;

  factory _$PerformanceReportStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$PerformanceReportStateImplFromJson(json);

  final Map<DateTime, int> _currentOneYearClickCount;
  @override
  @JsonKey()
  Map<DateTime, int> get currentOneYearClickCount {
    if (_currentOneYearClickCount is EqualUnmodifiableMapView)
      return _currentOneYearClickCount;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_currentOneYearClickCount);
  }

  final Map<DateTime, int> _currentOneYearConversionCount;
  @override
  @JsonKey()
  Map<DateTime, int> get currentOneYearConversionCount {
    if (_currentOneYearConversionCount is EqualUnmodifiableMapView)
      return _currentOneYearConversionCount;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_currentOneYearConversionCount);
  }

  @override
  @JsonKey()
  final int thisMonthOccurredConversionCount;
  @override
  @JsonKey()
  final double lastMonthApprovedReward;
  final List<CampaignNameAndClicks> _topTenCampaignsClickCount;
  @override
  @JsonKey()
  List<CampaignNameAndClicks> get topTenCampaignsClickCount {
    if (_topTenCampaignsClickCount is EqualUnmodifiableListView)
      return _topTenCampaignsClickCount;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topTenCampaignsClickCount);
  }

  @override
  final PaymentSummary? paymentSummary;
  @override
  final MinimumPaymentDetails? minimumPaymentDetails;
  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool isPullToRefresh;
  @override
  final Country? country;
  @override
  @JsonKey()
  final int selectedSiteId;

  @override
  String toString() {
    return 'ReportState(currentOneYearClickCount: $currentOneYearClickCount, currentOneYearConversionCount: $currentOneYearConversionCount, thisMonthOccurredConversionCount: $thisMonthOccurredConversionCount, lastMonthApprovedReward: $lastMonthApprovedReward, topTenCampaignsClickCount: $topTenCampaignsClickCount, paymentSummary: $paymentSummary, minimumPaymentDetails: $minimumPaymentDetails, currency: $currency, errorMessage: $errorMessage, isPullToRefresh: $isPullToRefresh, country: $country, selectedSiteId: $selectedSiteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceReportStateImpl &&
            const DeepCollectionEquality().equals(
                other._currentOneYearClickCount, _currentOneYearClickCount) &&
            const DeepCollectionEquality().equals(
                other._currentOneYearConversionCount,
                _currentOneYearConversionCount) &&
            (identical(other.thisMonthOccurredConversionCount,
                    thisMonthOccurredConversionCount) ||
                other.thisMonthOccurredConversionCount ==
                    thisMonthOccurredConversionCount) &&
            (identical(
                    other.lastMonthApprovedReward, lastMonthApprovedReward) ||
                other.lastMonthApprovedReward == lastMonthApprovedReward) &&
            const DeepCollectionEquality().equals(
                other._topTenCampaignsClickCount, _topTenCampaignsClickCount) &&
            (identical(other.paymentSummary, paymentSummary) ||
                other.paymentSummary == paymentSummary) &&
            (identical(other.minimumPaymentDetails, minimumPaymentDetails) ||
                other.minimumPaymentDetails == minimumPaymentDetails) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isPullToRefresh, isPullToRefresh) ||
                other.isPullToRefresh == isPullToRefresh) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.selectedSiteId, selectedSiteId) ||
                other.selectedSiteId == selectedSiteId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_currentOneYearClickCount),
      const DeepCollectionEquality().hash(_currentOneYearConversionCount),
      thisMonthOccurredConversionCount,
      lastMonthApprovedReward,
      const DeepCollectionEquality().hash(_topTenCampaignsClickCount),
      paymentSummary,
      minimumPaymentDetails,
      currency,
      errorMessage,
      isPullToRefresh,
      country,
      selectedSiteId);

  /// Create a copy of ReportState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PerformanceReportStateImplCopyWith<_$PerformanceReportStateImpl>
      get copyWith => __$$PerformanceReportStateImplCopyWithImpl<
          _$PerformanceReportStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PerformanceReportStateImplToJson(
      this,
    );
  }
}

abstract class _PerformanceReportState implements ReportState {
  factory _PerformanceReportState(
      {final Map<DateTime, int> currentOneYearClickCount,
      final Map<DateTime, int> currentOneYearConversionCount,
      final int thisMonthOccurredConversionCount,
      final double lastMonthApprovedReward,
      final List<CampaignNameAndClicks> topTenCampaignsClickCount,
      final PaymentSummary? paymentSummary,
      final MinimumPaymentDetails? minimumPaymentDetails,
      final String currency,
      final String errorMessage,
      final bool isPullToRefresh,
      final Country? country,
      final int selectedSiteId}) = _$PerformanceReportStateImpl;

  factory _PerformanceReportState.fromJson(Map<String, dynamic> json) =
      _$PerformanceReportStateImpl.fromJson;

  @override
  Map<DateTime, int> get currentOneYearClickCount;
  @override
  Map<DateTime, int> get currentOneYearConversionCount;
  @override
  int get thisMonthOccurredConversionCount;
  @override
  double get lastMonthApprovedReward;
  @override
  List<CampaignNameAndClicks> get topTenCampaignsClickCount;
  @override
  PaymentSummary? get paymentSummary;
  @override
  MinimumPaymentDetails? get minimumPaymentDetails;
  @override
  String get currency;
  @override
  String get errorMessage;
  @override
  bool get isPullToRefresh;
  @override
  Country? get country;
  @override
  int get selectedSiteId;

  /// Create a copy of ReportState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PerformanceReportStateImplCopyWith<_$PerformanceReportStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
