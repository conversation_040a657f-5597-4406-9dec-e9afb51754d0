import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/campaign/campaign_module.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_detail_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_transaction_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/conversion/conversion_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_details_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_invoice_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/performance/performance_daily_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/performance/performance_monthly_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_cubit.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/modules/report/presentation/campaign/campaign_detail_report_page.dart';
import 'package:koc_app/src/modules/report/presentation/campaign/campaign_report_page.dart';
import 'package:koc_app/src/modules/report/presentation/campaign/campaign_transaction_report_page.dart';
import 'package:koc_app/src/modules/report/presentation/conversion/conversion_report_page.dart';
import 'package:koc_app/src/modules/report/presentation/payment/payment_details_report_page.dart';
import 'package:koc_app/src/modules/report/presentation/payment/payment_invoice_report_page.dart';
import 'package:koc_app/src/modules/report/presentation/payment/payment_report_page.dart';
import 'package:koc_app/src/modules/report/presentation/performance/performance_daily_report_page.dart';
import 'package:koc_app/src/modules/report/presentation/performance/performance_monthly_report_page.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';

class ReportModule extends Module {
  @override
  List<Module> get imports => [
        SharedModule(),
        ReportSharedModule(),
        CampaignSharedModule(),
      ];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.add(PerformanceMonthlyReportCubit.new);
    i.add(PerformanceDailyReportCubit.new);
    i.add(ConversionReportCubit.new);
    i.add(CampaignDetailReportCubit.new);
    i.add(CampaignTransactionReportCubit.new);
    i.add(CampaignReportCubit.new);
    i.add(PaymentReportCubit.new);
    i.add(PaymentDetailsReportCubit.new);
    i.add(PaymentInvoiceReportCubit.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.child('/campaign', child: (i) => const CampaignReportPage());
    r.child('/campaign/details',
        child: (i) => CampaignDetailReportPage(r.args.data));
    r.child('/campaign/transaction',
        child: (i) =>
            CampaignTransactionReportPage(r.args.data[0], r.args.data[1]));
    r.child('/performance-monthly',
        child: (i) => const PerformanceMonthlyReportPage());
    r.child('/performance-daily',
        child: (i) => PerformanceDailyReportPage(r.args.data));
    r.child('/conversion', child: (i) => const ConversionReportPage());

    r.child('/payment', child: (i) => const PaymentReportPage());
    r.child('/payment/details',
        child: (i) => PaymentDetailsReportPage(r.args.data));
    r.child('/payment/invoice',
        child: (i) => PaymentInvoiceReportPage(r.args.data));
  }
}

class ReportSharedModule extends Module {
  @override
  List<Module> get imports => [SharedModule()];

  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);
    i.addLazySingleton(ReportRepository.new);
    i.addLazySingleton(ReportCubit.new);
  }
}
