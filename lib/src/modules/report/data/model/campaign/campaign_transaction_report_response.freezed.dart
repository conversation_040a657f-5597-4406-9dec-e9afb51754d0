// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_transaction_report_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignTransactionReportResponse _$CampaignTransactionReportResponseFromJson(
    Map<String, dynamic> json) {
  return _CampaignTransactionReportResponse.fromJson(json);
}

/// @nodoc
mixin _$CampaignTransactionReportResponse {
  int get totalItems => throw _privateConstructorUsedError;

  /// Serializes this CampaignTransactionReportResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignTransactionReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignTransactionReportResponseCopyWith<CampaignTransactionReportResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignTransactionReportResponseCopyWith<$Res> {
  factory $CampaignTransactionReportResponseCopyWith(
          CampaignTransactionReportResponse value,
          $Res Function(CampaignTransactionReportResponse) then) =
      _$CampaignTransactionReportResponseCopyWithImpl<$Res,
          CampaignTransactionReportResponse>;
  @useResult
  $Res call({int totalItems});
}

/// @nodoc
class _$CampaignTransactionReportResponseCopyWithImpl<$Res,
        $Val extends CampaignTransactionReportResponse>
    implements $CampaignTransactionReportResponseCopyWith<$Res> {
  _$CampaignTransactionReportResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignTransactionReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalItems = null,
  }) {
    return _then(_value.copyWith(
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignTransactionReportResponseImplCopyWith<$Res>
    implements $CampaignTransactionReportResponseCopyWith<$Res> {
  factory _$$CampaignTransactionReportResponseImplCopyWith(
          _$CampaignTransactionReportResponseImpl value,
          $Res Function(_$CampaignTransactionReportResponseImpl) then) =
      __$$CampaignTransactionReportResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int totalItems});
}

/// @nodoc
class __$$CampaignTransactionReportResponseImplCopyWithImpl<$Res>
    extends _$CampaignTransactionReportResponseCopyWithImpl<$Res,
        _$CampaignTransactionReportResponseImpl>
    implements _$$CampaignTransactionReportResponseImplCopyWith<$Res> {
  __$$CampaignTransactionReportResponseImplCopyWithImpl(
      _$CampaignTransactionReportResponseImpl _value,
      $Res Function(_$CampaignTransactionReportResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignTransactionReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalItems = null,
  }) {
    return _then(_$CampaignTransactionReportResponseImpl(
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignTransactionReportResponseImpl
    implements _CampaignTransactionReportResponse {
  _$CampaignTransactionReportResponseImpl({this.totalItems = 0});

  factory _$CampaignTransactionReportResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CampaignTransactionReportResponseImplFromJson(json);

  @override
  @JsonKey()
  final int totalItems;

  @override
  String toString() {
    return 'CampaignTransactionReportResponse(totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignTransactionReportResponseImpl &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalItems);

  /// Create a copy of CampaignTransactionReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignTransactionReportResponseImplCopyWith<
          _$CampaignTransactionReportResponseImpl>
      get copyWith => __$$CampaignTransactionReportResponseImplCopyWithImpl<
          _$CampaignTransactionReportResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignTransactionReportResponseImplToJson(
      this,
    );
  }
}

abstract class _CampaignTransactionReportResponse
    implements CampaignTransactionReportResponse {
  factory _CampaignTransactionReportResponse({final int totalItems}) =
      _$CampaignTransactionReportResponseImpl;

  factory _CampaignTransactionReportResponse.fromJson(
          Map<String, dynamic> json) =
      _$CampaignTransactionReportResponseImpl.fromJson;

  @override
  int get totalItems;

  /// Create a copy of CampaignTransactionReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignTransactionReportResponseImplCopyWith<
          _$CampaignTransactionReportResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
