// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_top_ten_click_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignTopTenClickResponse _$CampaignTopTenClickResponseFromJson(
    Map<String, dynamic> json) {
  return _CampaignTopTenClickResponse.fromJson(json);
}

/// @nodoc
mixin _$CampaignTopTenClickResponse {
  List<CampaignNameAndClicks> get data => throw _privateConstructorUsedError;

  /// Serializes this CampaignTopTenClickResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignTopTenClickResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignTopTenClickResponseCopyWith<CampaignTopTenClickResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignTopTenClickResponseCopyWith<$Res> {
  factory $CampaignTopTenClickResponseCopyWith(
          CampaignTopTenClickResponse value,
          $Res Function(CampaignTopTenClickResponse) then) =
      _$CampaignTopTenClickResponseCopyWithImpl<$Res,
          CampaignTopTenClickResponse>;
  @useResult
  $Res call({List<CampaignNameAndClicks> data});
}

/// @nodoc
class _$CampaignTopTenClickResponseCopyWithImpl<$Res,
        $Val extends CampaignTopTenClickResponse>
    implements $CampaignTopTenClickResponseCopyWith<$Res> {
  _$CampaignTopTenClickResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignTopTenClickResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<CampaignNameAndClicks>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignTopTenClickResponseImplCopyWith<$Res>
    implements $CampaignTopTenClickResponseCopyWith<$Res> {
  factory _$$CampaignTopTenClickResponseImplCopyWith(
          _$CampaignTopTenClickResponseImpl value,
          $Res Function(_$CampaignTopTenClickResponseImpl) then) =
      __$$CampaignTopTenClickResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CampaignNameAndClicks> data});
}

/// @nodoc
class __$$CampaignTopTenClickResponseImplCopyWithImpl<$Res>
    extends _$CampaignTopTenClickResponseCopyWithImpl<$Res,
        _$CampaignTopTenClickResponseImpl>
    implements _$$CampaignTopTenClickResponseImplCopyWith<$Res> {
  __$$CampaignTopTenClickResponseImplCopyWithImpl(
      _$CampaignTopTenClickResponseImpl _value,
      $Res Function(_$CampaignTopTenClickResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignTopTenClickResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$CampaignTopTenClickResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<CampaignNameAndClicks>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignTopTenClickResponseImpl
    implements _CampaignTopTenClickResponse {
  _$CampaignTopTenClickResponseImpl(
      {final List<CampaignNameAndClicks> data = const []})
      : _data = data;

  factory _$CampaignTopTenClickResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CampaignTopTenClickResponseImplFromJson(json);

  final List<CampaignNameAndClicks> _data;
  @override
  @JsonKey()
  List<CampaignNameAndClicks> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'CampaignTopTenClickResponse(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignTopTenClickResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of CampaignTopTenClickResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignTopTenClickResponseImplCopyWith<_$CampaignTopTenClickResponseImpl>
      get copyWith => __$$CampaignTopTenClickResponseImplCopyWithImpl<
          _$CampaignTopTenClickResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignTopTenClickResponseImplToJson(
      this,
    );
  }
}

abstract class _CampaignTopTenClickResponse
    implements CampaignTopTenClickResponse {
  factory _CampaignTopTenClickResponse(
          {final List<CampaignNameAndClicks> data}) =
      _$CampaignTopTenClickResponseImpl;

  factory _CampaignTopTenClickResponse.fromJson(Map<String, dynamic> json) =
      _$CampaignTopTenClickResponseImpl.fromJson;

  @override
  List<CampaignNameAndClicks> get data;

  /// Create a copy of CampaignTopTenClickResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignTopTenClickResponseImplCopyWith<_$CampaignTopTenClickResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CampaignNameAndClicks _$CampaignNameAndClicksFromJson(
    Map<String, dynamic> json) {
  return _CampaignNameAndClicks.fromJson(json);
}

/// @nodoc
mixin _$CampaignNameAndClicks {
  String get campaignName => throw _privateConstructorUsedError;
  int get clicks => throw _privateConstructorUsedError;

  /// Serializes this CampaignNameAndClicks to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignNameAndClicks
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignNameAndClicksCopyWith<CampaignNameAndClicks> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignNameAndClicksCopyWith<$Res> {
  factory $CampaignNameAndClicksCopyWith(CampaignNameAndClicks value,
          $Res Function(CampaignNameAndClicks) then) =
      _$CampaignNameAndClicksCopyWithImpl<$Res, CampaignNameAndClicks>;
  @useResult
  $Res call({String campaignName, int clicks});
}

/// @nodoc
class _$CampaignNameAndClicksCopyWithImpl<$Res,
        $Val extends CampaignNameAndClicks>
    implements $CampaignNameAndClicksCopyWith<$Res> {
  _$CampaignNameAndClicksCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignNameAndClicks
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignName = null,
    Object? clicks = null,
  }) {
    return _then(_value.copyWith(
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignNameAndClicksImplCopyWith<$Res>
    implements $CampaignNameAndClicksCopyWith<$Res> {
  factory _$$CampaignNameAndClicksImplCopyWith(
          _$CampaignNameAndClicksImpl value,
          $Res Function(_$CampaignNameAndClicksImpl) then) =
      __$$CampaignNameAndClicksImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String campaignName, int clicks});
}

/// @nodoc
class __$$CampaignNameAndClicksImplCopyWithImpl<$Res>
    extends _$CampaignNameAndClicksCopyWithImpl<$Res,
        _$CampaignNameAndClicksImpl>
    implements _$$CampaignNameAndClicksImplCopyWith<$Res> {
  __$$CampaignNameAndClicksImplCopyWithImpl(_$CampaignNameAndClicksImpl _value,
      $Res Function(_$CampaignNameAndClicksImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignNameAndClicks
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignName = null,
    Object? clicks = null,
  }) {
    return _then(_$CampaignNameAndClicksImpl(
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignNameAndClicksImpl implements _CampaignNameAndClicks {
  const _$CampaignNameAndClicksImpl({this.campaignName = '', this.clicks = 0});

  factory _$CampaignNameAndClicksImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignNameAndClicksImplFromJson(json);

  @override
  @JsonKey()
  final String campaignName;
  @override
  @JsonKey()
  final int clicks;

  @override
  String toString() {
    return 'CampaignNameAndClicks(campaignName: $campaignName, clicks: $clicks)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignNameAndClicksImpl &&
            (identical(other.campaignName, campaignName) ||
                other.campaignName == campaignName) &&
            (identical(other.clicks, clicks) || other.clicks == clicks));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, campaignName, clicks);

  /// Create a copy of CampaignNameAndClicks
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignNameAndClicksImplCopyWith<_$CampaignNameAndClicksImpl>
      get copyWith => __$$CampaignNameAndClicksImplCopyWithImpl<
          _$CampaignNameAndClicksImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignNameAndClicksImplToJson(
      this,
    );
  }
}

abstract class _CampaignNameAndClicks implements CampaignNameAndClicks {
  const factory _CampaignNameAndClicks(
      {final String campaignName,
      final int clicks}) = _$CampaignNameAndClicksImpl;

  factory _CampaignNameAndClicks.fromJson(Map<String, dynamic> json) =
      _$CampaignNameAndClicksImpl.fromJson;

  @override
  String get campaignName;
  @override
  int get clicks;

  /// Create a copy of CampaignNameAndClicks
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignNameAndClicksImplCopyWith<_$CampaignNameAndClicksImpl>
      get copyWith => throw _privateConstructorUsedError;
}
