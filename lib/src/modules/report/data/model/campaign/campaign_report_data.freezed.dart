// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_report_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignReportData _$CampaignReportDataFromJson(Map<String, dynamic> json) {
  return _CampaignReportData.fromJson(json);
}

/// @nodoc
mixin _$CampaignReportData {
  int get campaignId => throw _privateConstructorUsedError;
  String get campaignName => throw _privateConstructorUsedError;
  int get clicks => throw _privateConstructorUsedError;
  int get conversions => throw _privateConstructorUsedError;

  /// Serializes this CampaignReportData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignReportDataCopyWith<CampaignReportData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignReportDataCopyWith<$Res> {
  factory $CampaignReportDataCopyWith(
          CampaignReportData value, $Res Function(CampaignReportData) then) =
      _$CampaignReportDataCopyWithImpl<$Res, CampaignReportData>;
  @useResult
  $Res call({int campaignId, String campaignName, int clicks, int conversions});
}

/// @nodoc
class _$CampaignReportDataCopyWithImpl<$Res, $Val extends CampaignReportData>
    implements $CampaignReportDataCopyWith<$Res> {
  _$CampaignReportDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignId = null,
    Object? campaignName = null,
    Object? clicks = null,
    Object? conversions = null,
  }) {
    return _then(_value.copyWith(
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignReportDataImplCopyWith<$Res>
    implements $CampaignReportDataCopyWith<$Res> {
  factory _$$CampaignReportDataImplCopyWith(_$CampaignReportDataImpl value,
          $Res Function(_$CampaignReportDataImpl) then) =
      __$$CampaignReportDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int campaignId, String campaignName, int clicks, int conversions});
}

/// @nodoc
class __$$CampaignReportDataImplCopyWithImpl<$Res>
    extends _$CampaignReportDataCopyWithImpl<$Res, _$CampaignReportDataImpl>
    implements _$$CampaignReportDataImplCopyWith<$Res> {
  __$$CampaignReportDataImplCopyWithImpl(_$CampaignReportDataImpl _value,
      $Res Function(_$CampaignReportDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignId = null,
    Object? campaignName = null,
    Object? clicks = null,
    Object? conversions = null,
  }) {
    return _then(_$CampaignReportDataImpl(
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignReportDataImpl implements _CampaignReportData {
  _$CampaignReportDataImpl(
      {this.campaignId = 0,
      this.campaignName = '',
      this.clicks = 0,
      this.conversions = 0});

  factory _$CampaignReportDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignReportDataImplFromJson(json);

  @override
  @JsonKey()
  final int campaignId;
  @override
  @JsonKey()
  final String campaignName;
  @override
  @JsonKey()
  final int clicks;
  @override
  @JsonKey()
  final int conversions;

  @override
  String toString() {
    return 'CampaignReportData(campaignId: $campaignId, campaignName: $campaignName, clicks: $clicks, conversions: $conversions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignReportDataImpl &&
            (identical(other.campaignId, campaignId) ||
                other.campaignId == campaignId) &&
            (identical(other.campaignName, campaignName) ||
                other.campaignName == campaignName) &&
            (identical(other.clicks, clicks) || other.clicks == clicks) &&
            (identical(other.conversions, conversions) ||
                other.conversions == conversions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, campaignId, campaignName, clicks, conversions);

  /// Create a copy of CampaignReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignReportDataImplCopyWith<_$CampaignReportDataImpl> get copyWith =>
      __$$CampaignReportDataImplCopyWithImpl<_$CampaignReportDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignReportDataImplToJson(
      this,
    );
  }
}

abstract class _CampaignReportData implements CampaignReportData {
  factory _CampaignReportData(
      {final int campaignId,
      final String campaignName,
      final int clicks,
      final int conversions}) = _$CampaignReportDataImpl;

  factory _CampaignReportData.fromJson(Map<String, dynamic> json) =
      _$CampaignReportDataImpl.fromJson;

  @override
  int get campaignId;
  @override
  String get campaignName;
  @override
  int get clicks;
  @override
  int get conversions;

  /// Create a copy of CampaignReportData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignReportDataImplCopyWith<_$CampaignReportDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
