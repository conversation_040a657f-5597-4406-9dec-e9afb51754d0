import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';

part 'campaign_detail_report_response.freezed.dart';
part 'campaign_detail_report_response.g.dart';

@freezed
class CampaignDetailReportResponse with _$CampaignDetailReportResponse {
  factory CampaignDetailReportResponse({
    @Default(0) int totalItems,
    @Default([]) List<CampaignDetailReportData> content,
  }) = _CampaignDetailReportResponse;

  factory CampaignDetailReportResponse.fromJson(Map<String, Object?> json) =>
      _$CampaignDetailReportResponseFromJson(json);
}

@freezed
class CampaignDetailReportData with _$CampaignDetailReportData {
  factory CampaignDetailReportData(
          {@Default('') String verificationId,
          String? customerType,
          DateTime? clickTime,
          DateTime? conversionTime,
          DateTime? confirmationTime,
          @Default(0) double reward,
          @Default(false) bool hasProduct,
          @Default(ConversionStatus.APPROVED) ConversionStatus status}) =
      _CampaignDetailReportData;

  factory CampaignDetailReportData.fromJson(Map<String, dynamic> json) =>
      _$CampaignDetailReportDataFromJson(json);
}
