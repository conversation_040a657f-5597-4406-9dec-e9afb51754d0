import 'package:freezed_annotation/freezed_annotation.dart';

part 'campaign_report_data.freezed.dart';
part 'campaign_report_data.g.dart';

@freezed
class CampaignReportData with _$CampaignReportData {
  factory CampaignReportData({
    @Default(0) int campaignId,
    @Default('') String campaignName,
    @Default(0) int clicks,
    @Default(0) int conversions,
  }) = _CampaignReportData;

  factory CampaignReportData.fromJson(Map<String, Object?> json) => _$CampaignReportDataFromJson(json);
}
