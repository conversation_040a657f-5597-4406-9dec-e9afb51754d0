// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_detail_report_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignDetailReportResponse _$CampaignDetailReportResponseFromJson(
    Map<String, dynamic> json) {
  return _CampaignDetailReportResponse.fromJson(json);
}

/// @nodoc
mixin _$CampaignDetailReportResponse {
  int get totalItems => throw _privateConstructorUsedError;
  List<CampaignDetailReportData> get content =>
      throw _privateConstructorUsedError;

  /// Serializes this CampaignDetailReportResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignDetailReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignDetailReportResponseCopyWith<CampaignDetailReportResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignDetailReportResponseCopyWith<$Res> {
  factory $CampaignDetailReportResponseCopyWith(
          CampaignDetailReportResponse value,
          $Res Function(CampaignDetailReportResponse) then) =
      _$CampaignDetailReportResponseCopyWithImpl<$Res,
          CampaignDetailReportResponse>;
  @useResult
  $Res call({int totalItems, List<CampaignDetailReportData> content});
}

/// @nodoc
class _$CampaignDetailReportResponseCopyWithImpl<$Res,
        $Val extends CampaignDetailReportResponse>
    implements $CampaignDetailReportResponseCopyWith<$Res> {
  _$CampaignDetailReportResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignDetailReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalItems = null,
    Object? content = null,
  }) {
    return _then(_value.copyWith(
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as List<CampaignDetailReportData>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignDetailReportResponseImplCopyWith<$Res>
    implements $CampaignDetailReportResponseCopyWith<$Res> {
  factory _$$CampaignDetailReportResponseImplCopyWith(
          _$CampaignDetailReportResponseImpl value,
          $Res Function(_$CampaignDetailReportResponseImpl) then) =
      __$$CampaignDetailReportResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int totalItems, List<CampaignDetailReportData> content});
}

/// @nodoc
class __$$CampaignDetailReportResponseImplCopyWithImpl<$Res>
    extends _$CampaignDetailReportResponseCopyWithImpl<$Res,
        _$CampaignDetailReportResponseImpl>
    implements _$$CampaignDetailReportResponseImplCopyWith<$Res> {
  __$$CampaignDetailReportResponseImplCopyWithImpl(
      _$CampaignDetailReportResponseImpl _value,
      $Res Function(_$CampaignDetailReportResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignDetailReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalItems = null,
    Object? content = null,
  }) {
    return _then(_$CampaignDetailReportResponseImpl(
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      content: null == content
          ? _value._content
          : content // ignore: cast_nullable_to_non_nullable
              as List<CampaignDetailReportData>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignDetailReportResponseImpl
    implements _CampaignDetailReportResponse {
  _$CampaignDetailReportResponseImpl(
      {this.totalItems = 0,
      final List<CampaignDetailReportData> content = const []})
      : _content = content;

  factory _$CampaignDetailReportResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CampaignDetailReportResponseImplFromJson(json);

  @override
  @JsonKey()
  final int totalItems;
  final List<CampaignDetailReportData> _content;
  @override
  @JsonKey()
  List<CampaignDetailReportData> get content {
    if (_content is EqualUnmodifiableListView) return _content;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_content);
  }

  @override
  String toString() {
    return 'CampaignDetailReportResponse(totalItems: $totalItems, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignDetailReportResponseImpl &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems) &&
            const DeepCollectionEquality().equals(other._content, _content));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, totalItems, const DeepCollectionEquality().hash(_content));

  /// Create a copy of CampaignDetailReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignDetailReportResponseImplCopyWith<
          _$CampaignDetailReportResponseImpl>
      get copyWith => __$$CampaignDetailReportResponseImplCopyWithImpl<
          _$CampaignDetailReportResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignDetailReportResponseImplToJson(
      this,
    );
  }
}

abstract class _CampaignDetailReportResponse
    implements CampaignDetailReportResponse {
  factory _CampaignDetailReportResponse(
          {final int totalItems,
          final List<CampaignDetailReportData> content}) =
      _$CampaignDetailReportResponseImpl;

  factory _CampaignDetailReportResponse.fromJson(Map<String, dynamic> json) =
      _$CampaignDetailReportResponseImpl.fromJson;

  @override
  int get totalItems;
  @override
  List<CampaignDetailReportData> get content;

  /// Create a copy of CampaignDetailReportResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignDetailReportResponseImplCopyWith<
          _$CampaignDetailReportResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CampaignDetailReportData _$CampaignDetailReportDataFromJson(
    Map<String, dynamic> json) {
  return _CampaignDetailReportData.fromJson(json);
}

/// @nodoc
mixin _$CampaignDetailReportData {
  String get verificationId => throw _privateConstructorUsedError;
  String? get customerType => throw _privateConstructorUsedError;
  DateTime? get clickTime => throw _privateConstructorUsedError;
  DateTime? get conversionTime => throw _privateConstructorUsedError;
  DateTime? get confirmationTime => throw _privateConstructorUsedError;
  double get reward => throw _privateConstructorUsedError;
  bool get hasProduct => throw _privateConstructorUsedError;
  ConversionStatus get status => throw _privateConstructorUsedError;

  /// Serializes this CampaignDetailReportData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignDetailReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignDetailReportDataCopyWith<CampaignDetailReportData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignDetailReportDataCopyWith<$Res> {
  factory $CampaignDetailReportDataCopyWith(CampaignDetailReportData value,
          $Res Function(CampaignDetailReportData) then) =
      _$CampaignDetailReportDataCopyWithImpl<$Res, CampaignDetailReportData>;
  @useResult
  $Res call(
      {String verificationId,
      String? customerType,
      DateTime? clickTime,
      DateTime? conversionTime,
      DateTime? confirmationTime,
      double reward,
      bool hasProduct,
      ConversionStatus status});
}

/// @nodoc
class _$CampaignDetailReportDataCopyWithImpl<$Res,
        $Val extends CampaignDetailReportData>
    implements $CampaignDetailReportDataCopyWith<$Res> {
  _$CampaignDetailReportDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignDetailReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? verificationId = null,
    Object? customerType = freezed,
    Object? clickTime = freezed,
    Object? conversionTime = freezed,
    Object? confirmationTime = freezed,
    Object? reward = null,
    Object? hasProduct = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      verificationId: null == verificationId
          ? _value.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      customerType: freezed == customerType
          ? _value.customerType
          : customerType // ignore: cast_nullable_to_non_nullable
              as String?,
      clickTime: freezed == clickTime
          ? _value.clickTime
          : clickTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      conversionTime: freezed == conversionTime
          ? _value.conversionTime
          : conversionTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      confirmationTime: freezed == confirmationTime
          ? _value.confirmationTime
          : confirmationTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      hasProduct: null == hasProduct
          ? _value.hasProduct
          : hasProduct // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConversionStatus,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignDetailReportDataImplCopyWith<$Res>
    implements $CampaignDetailReportDataCopyWith<$Res> {
  factory _$$CampaignDetailReportDataImplCopyWith(
          _$CampaignDetailReportDataImpl value,
          $Res Function(_$CampaignDetailReportDataImpl) then) =
      __$$CampaignDetailReportDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String verificationId,
      String? customerType,
      DateTime? clickTime,
      DateTime? conversionTime,
      DateTime? confirmationTime,
      double reward,
      bool hasProduct,
      ConversionStatus status});
}

/// @nodoc
class __$$CampaignDetailReportDataImplCopyWithImpl<$Res>
    extends _$CampaignDetailReportDataCopyWithImpl<$Res,
        _$CampaignDetailReportDataImpl>
    implements _$$CampaignDetailReportDataImplCopyWith<$Res> {
  __$$CampaignDetailReportDataImplCopyWithImpl(
      _$CampaignDetailReportDataImpl _value,
      $Res Function(_$CampaignDetailReportDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignDetailReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? verificationId = null,
    Object? customerType = freezed,
    Object? clickTime = freezed,
    Object? conversionTime = freezed,
    Object? confirmationTime = freezed,
    Object? reward = null,
    Object? hasProduct = null,
    Object? status = null,
  }) {
    return _then(_$CampaignDetailReportDataImpl(
      verificationId: null == verificationId
          ? _value.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      customerType: freezed == customerType
          ? _value.customerType
          : customerType // ignore: cast_nullable_to_non_nullable
              as String?,
      clickTime: freezed == clickTime
          ? _value.clickTime
          : clickTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      conversionTime: freezed == conversionTime
          ? _value.conversionTime
          : conversionTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      confirmationTime: freezed == confirmationTime
          ? _value.confirmationTime
          : confirmationTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      hasProduct: null == hasProduct
          ? _value.hasProduct
          : hasProduct // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConversionStatus,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignDetailReportDataImpl implements _CampaignDetailReportData {
  _$CampaignDetailReportDataImpl(
      {this.verificationId = '',
      this.customerType,
      this.clickTime,
      this.conversionTime,
      this.confirmationTime,
      this.reward = 0,
      this.hasProduct = false,
      this.status = ConversionStatus.APPROVED});

  factory _$CampaignDetailReportDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignDetailReportDataImplFromJson(json);

  @override
  @JsonKey()
  final String verificationId;
  @override
  final String? customerType;
  @override
  final DateTime? clickTime;
  @override
  final DateTime? conversionTime;
  @override
  final DateTime? confirmationTime;
  @override
  @JsonKey()
  final double reward;
  @override
  @JsonKey()
  final bool hasProduct;
  @override
  @JsonKey()
  final ConversionStatus status;

  @override
  String toString() {
    return 'CampaignDetailReportData(verificationId: $verificationId, customerType: $customerType, clickTime: $clickTime, conversionTime: $conversionTime, confirmationTime: $confirmationTime, reward: $reward, hasProduct: $hasProduct, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignDetailReportDataImpl &&
            (identical(other.verificationId, verificationId) ||
                other.verificationId == verificationId) &&
            (identical(other.customerType, customerType) ||
                other.customerType == customerType) &&
            (identical(other.clickTime, clickTime) ||
                other.clickTime == clickTime) &&
            (identical(other.conversionTime, conversionTime) ||
                other.conversionTime == conversionTime) &&
            (identical(other.confirmationTime, confirmationTime) ||
                other.confirmationTime == confirmationTime) &&
            (identical(other.reward, reward) || other.reward == reward) &&
            (identical(other.hasProduct, hasProduct) ||
                other.hasProduct == hasProduct) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, verificationId, customerType,
      clickTime, conversionTime, confirmationTime, reward, hasProduct, status);

  /// Create a copy of CampaignDetailReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignDetailReportDataImplCopyWith<_$CampaignDetailReportDataImpl>
      get copyWith => __$$CampaignDetailReportDataImplCopyWithImpl<
          _$CampaignDetailReportDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignDetailReportDataImplToJson(
      this,
    );
  }
}

abstract class _CampaignDetailReportData implements CampaignDetailReportData {
  factory _CampaignDetailReportData(
      {final String verificationId,
      final String? customerType,
      final DateTime? clickTime,
      final DateTime? conversionTime,
      final DateTime? confirmationTime,
      final double reward,
      final bool hasProduct,
      final ConversionStatus status}) = _$CampaignDetailReportDataImpl;

  factory _CampaignDetailReportData.fromJson(Map<String, dynamic> json) =
      _$CampaignDetailReportDataImpl.fromJson;

  @override
  String get verificationId;
  @override
  String? get customerType;
  @override
  DateTime? get clickTime;
  @override
  DateTime? get conversionTime;
  @override
  DateTime? get confirmationTime;
  @override
  double get reward;
  @override
  bool get hasProduct;
  @override
  ConversionStatus get status;

  /// Create a copy of CampaignDetailReportData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignDetailReportDataImplCopyWith<_$CampaignDetailReportDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
