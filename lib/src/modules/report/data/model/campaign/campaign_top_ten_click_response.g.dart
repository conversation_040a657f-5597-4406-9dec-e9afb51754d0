// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_top_ten_click_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignTopTenClickResponseImpl _$$CampaignTopTenClickResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignTopTenClickResponseImpl(
      data: (json['data'] as List<dynamic>?)
              ?.map((e) =>
                  CampaignNameAndClicks.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CampaignTopTenClickResponseImplToJson(
        _$CampaignTopTenClickResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

_$CampaignNameAndClicksImpl _$$CampaignNameAndClicksImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignNameAndClicksImpl(
      campaignName: json['campaignName'] as String? ?? '',
      clicks: (json['clicks'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CampaignNameAndClicksImplToJson(
        _$CampaignNameAndClicksImpl instance) =>
    <String, dynamic>{
      'campaignName': instance.campaignName,
      'clicks': instance.clicks,
    };
