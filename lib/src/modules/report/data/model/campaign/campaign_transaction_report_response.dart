import 'package:freezed_annotation/freezed_annotation.dart';

part 'campaign_transaction_report_response.freezed.dart';
part 'campaign_transaction_report_response.g.dart';

@freezed
class CampaignTransactionReportResponse
    with _$CampaignTransactionReportResponse {
  factory CampaignTransactionReportResponse({
    @Default(0) int totalItems,
  }) = _CampaignTransactionReportResponse;

  factory CampaignTransactionReportResponse.fromJson(
          Map<String, dynamic> json) =>
      _$CampaignTransactionReportResponseFromJson(json);
}
