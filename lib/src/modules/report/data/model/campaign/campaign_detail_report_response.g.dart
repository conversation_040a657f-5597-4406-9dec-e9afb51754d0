// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_detail_report_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignDetailReportResponseImpl _$$CampaignDetailReportResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignDetailReportResponseImpl(
      totalItems: (json['totalItems'] as num?)?.toInt() ?? 0,
      content: (json['content'] as List<dynamic>?)
              ?.map((e) =>
                  CampaignDetailReportData.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CampaignDetailReportResponseImplToJson(
        _$CampaignDetailReportResponseImpl instance) =>
    <String, dynamic>{
      'totalItems': instance.totalItems,
      'content': instance.content,
    };

_$CampaignDetailReportDataImpl _$$CampaignDetailReportDataImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignDetailReportDataImpl(
      verificationId: json['verificationId'] as String? ?? '',
      customerType: json['customerType'] as String?,
      clickTime: json['clickTime'] == null
          ? null
          : DateTime.parse(json['clickTime'] as String),
      conversionTime: json['conversionTime'] == null
          ? null
          : DateTime.parse(json['conversionTime'] as String),
      confirmationTime: json['confirmationTime'] == null
          ? null
          : DateTime.parse(json['confirmationTime'] as String),
      reward: (json['reward'] as num?)?.toDouble() ?? 0,
      hasProduct: json['hasProduct'] as bool? ?? false,
      status: $enumDecodeNullable(_$ConversionStatusEnumMap, json['status']) ??
          ConversionStatus.APPROVED,
    );

Map<String, dynamic> _$$CampaignDetailReportDataImplToJson(
        _$CampaignDetailReportDataImpl instance) =>
    <String, dynamic>{
      'verificationId': instance.verificationId,
      'customerType': instance.customerType,
      'clickTime': instance.clickTime?.toIso8601String(),
      'conversionTime': instance.conversionTime?.toIso8601String(),
      'confirmationTime': instance.confirmationTime?.toIso8601String(),
      'reward': instance.reward,
      'hasProduct': instance.hasProduct,
      'status': _$ConversionStatusEnumMap[instance.status]!,
    };

const _$ConversionStatusEnumMap = {
  ConversionStatus.PENDING: 'PENDING',
  ConversionStatus.APPROVED: 'APPROVED',
  ConversionStatus.REJECTED: 'REJECTED',
};
