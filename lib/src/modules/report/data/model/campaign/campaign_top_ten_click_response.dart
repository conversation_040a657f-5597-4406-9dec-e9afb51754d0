import 'package:freezed_annotation/freezed_annotation.dart';

part 'campaign_top_ten_click_response.freezed.dart';
part 'campaign_top_ten_click_response.g.dart';

@freezed
class CampaignTopTenClickResponse with _$CampaignTopTenClickResponse {
  factory CampaignTopTenClickResponse({
    @Default([]) List<CampaignNameAndClicks> data,
  }) = _CampaignTopTenClickResponse;

  factory CampaignTopTenClickResponse.fromJson(Map<String, dynamic> json) =>
      _$CampaignTopTenClickResponseFromJson(json);
}

@freezed
class CampaignNameAndClicks with _$CampaignNameAndClicks {
  const factory CampaignNameAndClicks({
    @Default('') String campaignName,
    @Default(0) int clicks,
  }) = _CampaignNameAndClicks;

  factory CampaignNameAndClicks.fromJson(Map<String, dynamic> json) => _$CampaignNameAndClicksFromJson(json);
}
