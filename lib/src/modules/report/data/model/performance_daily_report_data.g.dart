// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance_daily_report_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PerformanceDailyReportDataImpl _$$PerformanceDailyReportDataImplFromJson(
        Map<String, dynamic> json) =>
    _$PerformanceDailyReportDataImpl(
      date: json['date'] as String?,
      clicks: (json['clicks'] as num?)?.toInt() ?? 0,
      conversions: (json['conversions'] as num?)?.toInt() ?? 0,
      conversionRate: (json['conversionRate'] as num?)?.toDouble() ?? 0,
      reward: (json['reward'] as num?)?.toDouble() ?? 0,
      earningsPerClick: (json['earningsPerClick'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$$PerformanceDailyReportDataImplToJson(
        _$PerformanceDailyReportDataImpl instance) =>
    <String, dynamic>{
      'date': instance.date,
      'clicks': instance.clicks,
      'conversions': instance.conversions,
      'conversionRate': instance.conversionRate,
      'reward': instance.reward,
      'earningsPerClick': instance.earningsPerClick,
    };
