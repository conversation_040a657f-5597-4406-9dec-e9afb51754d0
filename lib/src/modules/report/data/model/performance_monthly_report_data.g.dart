// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance_monthly_report_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PerformanceMonthlyReportDataImpl _$$PerformanceMonthlyReportDataImplFromJson(
        Map<String, dynamic> json) =>
    _$PerformanceMonthlyReportDataImpl(
      month: json['month'] as String?,
      clicks: (json['clicks'] as num?)?.toInt() ?? 0,
      conversions: (json['conversions'] as num?)?.toInt() ?? 0,
      conversionRate: (json['conversionRate'] as num?)?.toDouble() ?? 0,
      reward: (json['reward'] as num?)?.toDouble() ?? 0,
      earningsPerClick: (json['earningsPerClick'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$$PerformanceMonthlyReportDataImplToJson(
        _$PerformanceMonthlyReportDataImpl instance) =>
    <String, dynamic>{
      'month': instance.month,
      'clicks': instance.clicks,
      'conversions': instance.conversions,
      'conversionRate': instance.conversionRate,
      'reward': instance.reward,
      'earningsPerClick': instance.earningsPerClick,
    };
