import 'package:freezed_annotation/freezed_annotation.dart';

part 'performance_monthly_report_data.freezed.dart';
part 'performance_monthly_report_data.g.dart';

@freezed
class PerformanceMonthlyReportData with _$PerformanceMonthlyReportData {
  factory PerformanceMonthlyReportData({
    String? month,
    @Default(0) int clicks,
    @Default(0) int conversions,
    @Default(0) double conversionRate,
    @Default(0) double reward,
    @Default(0) double earningsPerClick,
  }) = _PerformanceMonthlyReportData;

  factory PerformanceMonthlyReportData.fromJson(Map<String, Object?> json) =>
      _$PerformanceMonthlyReportDataFromJson(json);
}
