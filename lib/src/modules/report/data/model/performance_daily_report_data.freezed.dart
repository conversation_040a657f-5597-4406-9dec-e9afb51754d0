// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'performance_daily_report_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PerformanceDailyReportData _$PerformanceDailyReportDataFromJson(
    Map<String, dynamic> json) {
  return _PerformanceDailyReportData.fromJson(json);
}

/// @nodoc
mixin _$PerformanceDailyReportData {
  String? get date => throw _privateConstructorUsedError;
  int get clicks => throw _privateConstructorUsedError;
  int get conversions => throw _privateConstructorUsedError;
  double get conversionRate => throw _privateConstructorUsedError;
  double get reward => throw _privateConstructorUsedError;
  double get earningsPerClick => throw _privateConstructorUsedError;

  /// Serializes this PerformanceDailyReportData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PerformanceDailyReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PerformanceDailyReportDataCopyWith<PerformanceDailyReportData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PerformanceDailyReportDataCopyWith<$Res> {
  factory $PerformanceDailyReportDataCopyWith(PerformanceDailyReportData value,
          $Res Function(PerformanceDailyReportData) then) =
      _$PerformanceDailyReportDataCopyWithImpl<$Res,
          PerformanceDailyReportData>;
  @useResult
  $Res call(
      {String? date,
      int clicks,
      int conversions,
      double conversionRate,
      double reward,
      double earningsPerClick});
}

/// @nodoc
class _$PerformanceDailyReportDataCopyWithImpl<$Res,
        $Val extends PerformanceDailyReportData>
    implements $PerformanceDailyReportDataCopyWith<$Res> {
  _$PerformanceDailyReportDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PerformanceDailyReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? clicks = null,
    Object? conversions = null,
    Object? conversionRate = null,
    Object? reward = null,
    Object? earningsPerClick = null,
  }) {
    return _then(_value.copyWith(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
      conversionRate: null == conversionRate
          ? _value.conversionRate
          : conversionRate // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      earningsPerClick: null == earningsPerClick
          ? _value.earningsPerClick
          : earningsPerClick // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PerformanceDailyReportDataImplCopyWith<$Res>
    implements $PerformanceDailyReportDataCopyWith<$Res> {
  factory _$$PerformanceDailyReportDataImplCopyWith(
          _$PerformanceDailyReportDataImpl value,
          $Res Function(_$PerformanceDailyReportDataImpl) then) =
      __$$PerformanceDailyReportDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? date,
      int clicks,
      int conversions,
      double conversionRate,
      double reward,
      double earningsPerClick});
}

/// @nodoc
class __$$PerformanceDailyReportDataImplCopyWithImpl<$Res>
    extends _$PerformanceDailyReportDataCopyWithImpl<$Res,
        _$PerformanceDailyReportDataImpl>
    implements _$$PerformanceDailyReportDataImplCopyWith<$Res> {
  __$$PerformanceDailyReportDataImplCopyWithImpl(
      _$PerformanceDailyReportDataImpl _value,
      $Res Function(_$PerformanceDailyReportDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PerformanceDailyReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? clicks = null,
    Object? conversions = null,
    Object? conversionRate = null,
    Object? reward = null,
    Object? earningsPerClick = null,
  }) {
    return _then(_$PerformanceDailyReportDataImpl(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
      conversionRate: null == conversionRate
          ? _value.conversionRate
          : conversionRate // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      earningsPerClick: null == earningsPerClick
          ? _value.earningsPerClick
          : earningsPerClick // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PerformanceDailyReportDataImpl implements _PerformanceDailyReportData {
  _$PerformanceDailyReportDataImpl(
      {this.date,
      this.clicks = 0,
      this.conversions = 0,
      this.conversionRate = 0,
      this.reward = 0,
      this.earningsPerClick = 0});

  factory _$PerformanceDailyReportDataImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$PerformanceDailyReportDataImplFromJson(json);

  @override
  final String? date;
  @override
  @JsonKey()
  final int clicks;
  @override
  @JsonKey()
  final int conversions;
  @override
  @JsonKey()
  final double conversionRate;
  @override
  @JsonKey()
  final double reward;
  @override
  @JsonKey()
  final double earningsPerClick;

  @override
  String toString() {
    return 'PerformanceDailyReportData(date: $date, clicks: $clicks, conversions: $conversions, conversionRate: $conversionRate, reward: $reward, earningsPerClick: $earningsPerClick)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceDailyReportDataImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.clicks, clicks) || other.clicks == clicks) &&
            (identical(other.conversions, conversions) ||
                other.conversions == conversions) &&
            (identical(other.conversionRate, conversionRate) ||
                other.conversionRate == conversionRate) &&
            (identical(other.reward, reward) || other.reward == reward) &&
            (identical(other.earningsPerClick, earningsPerClick) ||
                other.earningsPerClick == earningsPerClick));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, date, clicks, conversions,
      conversionRate, reward, earningsPerClick);

  /// Create a copy of PerformanceDailyReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PerformanceDailyReportDataImplCopyWith<_$PerformanceDailyReportDataImpl>
      get copyWith => __$$PerformanceDailyReportDataImplCopyWithImpl<
          _$PerformanceDailyReportDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PerformanceDailyReportDataImplToJson(
      this,
    );
  }
}

abstract class _PerformanceDailyReportData
    implements PerformanceDailyReportData {
  factory _PerformanceDailyReportData(
      {final String? date,
      final int clicks,
      final int conversions,
      final double conversionRate,
      final double reward,
      final double earningsPerClick}) = _$PerformanceDailyReportDataImpl;

  factory _PerformanceDailyReportData.fromJson(Map<String, dynamic> json) =
      _$PerformanceDailyReportDataImpl.fromJson;

  @override
  String? get date;
  @override
  int get clicks;
  @override
  int get conversions;
  @override
  double get conversionRate;
  @override
  double get reward;
  @override
  double get earningsPerClick;

  /// Create a copy of PerformanceDailyReportData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PerformanceDailyReportDataImplCopyWith<_$PerformanceDailyReportDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
