import 'package:freezed_annotation/freezed_annotation.dart';

part 'performance_daily_report_data.freezed.dart';
part 'performance_daily_report_data.g.dart';

@freezed
class PerformanceDailyReportData with _$PerformanceDailyReportData {
  factory PerformanceDailyReportData({
    String? date,
    @Default(0) int clicks,
    @Default(0) int conversions,
    @Default(0) double conversionRate,
    @Default(0) double reward,
    @Default(0) double earningsPerClick,
  }) = _PerformanceDailyReportData;

  factory PerformanceDailyReportData.fromJson(Map<String, Object?> json) =>
      _$PerformanceDailyReportDataFromJson(json);
}
