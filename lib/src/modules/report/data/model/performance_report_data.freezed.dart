// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'performance_report_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PerformanceReportData _$PerformanceReportDataFromJson(
    Map<String, dynamic> json) {
  return _PerformanceReportData.fromJson(json);
}

/// @nodoc
mixin _$PerformanceReportData {
  DateTime? get date => throw _privateConstructorUsedError;
  int get clicks => throw _privateConstructorUsedError;
  int get conversions => throw _privateConstructorUsedError;
  double get reward => throw _privateConstructorUsedError;
  double get epc => throw _privateConstructorUsedError;

  /// Serializes this PerformanceReportData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PerformanceReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PerformanceReportDataCopyWith<PerformanceReportData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PerformanceReportDataCopyWith<$Res> {
  factory $PerformanceReportDataCopyWith(PerformanceReportData value,
          $Res Function(PerformanceReportData) then) =
      _$PerformanceReportDataCopyWithImpl<$Res, PerformanceReportData>;
  @useResult
  $Res call(
      {DateTime? date, int clicks, int conversions, double reward, double epc});
}

/// @nodoc
class _$PerformanceReportDataCopyWithImpl<$Res,
        $Val extends PerformanceReportData>
    implements $PerformanceReportDataCopyWith<$Res> {
  _$PerformanceReportDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PerformanceReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? clicks = null,
    Object? conversions = null,
    Object? reward = null,
    Object? epc = null,
  }) {
    return _then(_value.copyWith(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      epc: null == epc
          ? _value.epc
          : epc // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PerformanceReportDataImplCopyWith<$Res>
    implements $PerformanceReportDataCopyWith<$Res> {
  factory _$$PerformanceReportDataImplCopyWith(
          _$PerformanceReportDataImpl value,
          $Res Function(_$PerformanceReportDataImpl) then) =
      __$$PerformanceReportDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime? date, int clicks, int conversions, double reward, double epc});
}

/// @nodoc
class __$$PerformanceReportDataImplCopyWithImpl<$Res>
    extends _$PerformanceReportDataCopyWithImpl<$Res,
        _$PerformanceReportDataImpl>
    implements _$$PerformanceReportDataImplCopyWith<$Res> {
  __$$PerformanceReportDataImplCopyWithImpl(_$PerformanceReportDataImpl _value,
      $Res Function(_$PerformanceReportDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PerformanceReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? clicks = null,
    Object? conversions = null,
    Object? reward = null,
    Object? epc = null,
  }) {
    return _then(_$PerformanceReportDataImpl(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      epc: null == epc
          ? _value.epc
          : epc // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PerformanceReportDataImpl implements _PerformanceReportData {
  _$PerformanceReportDataImpl(
      {this.date,
      this.clicks = 0,
      this.conversions = 0,
      this.reward = 0,
      this.epc = 0});

  factory _$PerformanceReportDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$PerformanceReportDataImplFromJson(json);

  @override
  final DateTime? date;
  @override
  @JsonKey()
  final int clicks;
  @override
  @JsonKey()
  final int conversions;
  @override
  @JsonKey()
  final double reward;
  @override
  @JsonKey()
  final double epc;

  @override
  String toString() {
    return 'PerformanceReportData(date: $date, clicks: $clicks, conversions: $conversions, reward: $reward, epc: $epc)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceReportDataImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.clicks, clicks) || other.clicks == clicks) &&
            (identical(other.conversions, conversions) ||
                other.conversions == conversions) &&
            (identical(other.reward, reward) || other.reward == reward) &&
            (identical(other.epc, epc) || other.epc == epc));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, date, clicks, conversions, reward, epc);

  /// Create a copy of PerformanceReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PerformanceReportDataImplCopyWith<_$PerformanceReportDataImpl>
      get copyWith => __$$PerformanceReportDataImplCopyWithImpl<
          _$PerformanceReportDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PerformanceReportDataImplToJson(
      this,
    );
  }
}

abstract class _PerformanceReportData implements PerformanceReportData {
  factory _PerformanceReportData(
      {final DateTime? date,
      final int clicks,
      final int conversions,
      final double reward,
      final double epc}) = _$PerformanceReportDataImpl;

  factory _PerformanceReportData.fromJson(Map<String, dynamic> json) =
      _$PerformanceReportDataImpl.fromJson;

  @override
  DateTime? get date;
  @override
  int get clicks;
  @override
  int get conversions;
  @override
  double get reward;
  @override
  double get epc;

  /// Create a copy of PerformanceReportData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PerformanceReportDataImplCopyWith<_$PerformanceReportDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}

PerformanceChartData _$PerformanceChartDataFromJson(Map<String, dynamic> json) {
  return _PerformanceChartData.fromJson(json);
}

/// @nodoc
mixin _$PerformanceChartData {
  String get date => throw _privateConstructorUsedError;
  int get clicks => throw _privateConstructorUsedError;
  int get conversions => throw _privateConstructorUsedError;

  /// Serializes this PerformanceChartData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PerformanceChartData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PerformanceChartDataCopyWith<PerformanceChartData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PerformanceChartDataCopyWith<$Res> {
  factory $PerformanceChartDataCopyWith(PerformanceChartData value,
          $Res Function(PerformanceChartData) then) =
      _$PerformanceChartDataCopyWithImpl<$Res, PerformanceChartData>;
  @useResult
  $Res call({String date, int clicks, int conversions});
}

/// @nodoc
class _$PerformanceChartDataCopyWithImpl<$Res,
        $Val extends PerformanceChartData>
    implements $PerformanceChartDataCopyWith<$Res> {
  _$PerformanceChartDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PerformanceChartData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? clicks = null,
    Object? conversions = null,
  }) {
    return _then(_value.copyWith(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PerformanceChartDataImplCopyWith<$Res>
    implements $PerformanceChartDataCopyWith<$Res> {
  factory _$$PerformanceChartDataImplCopyWith(_$PerformanceChartDataImpl value,
          $Res Function(_$PerformanceChartDataImpl) then) =
      __$$PerformanceChartDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String date, int clicks, int conversions});
}

/// @nodoc
class __$$PerformanceChartDataImplCopyWithImpl<$Res>
    extends _$PerformanceChartDataCopyWithImpl<$Res, _$PerformanceChartDataImpl>
    implements _$$PerformanceChartDataImplCopyWith<$Res> {
  __$$PerformanceChartDataImplCopyWithImpl(_$PerformanceChartDataImpl _value,
      $Res Function(_$PerformanceChartDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PerformanceChartData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? clicks = null,
    Object? conversions = null,
  }) {
    return _then(_$PerformanceChartDataImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PerformanceChartDataImpl implements _PerformanceChartData {
  _$PerformanceChartDataImpl(
      {required this.date, required this.clicks, required this.conversions});

  factory _$PerformanceChartDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$PerformanceChartDataImplFromJson(json);

  @override
  final String date;
  @override
  final int clicks;
  @override
  final int conversions;

  @override
  String toString() {
    return 'PerformanceChartData(date: $date, clicks: $clicks, conversions: $conversions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceChartDataImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.clicks, clicks) || other.clicks == clicks) &&
            (identical(other.conversions, conversions) ||
                other.conversions == conversions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, date, clicks, conversions);

  /// Create a copy of PerformanceChartData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PerformanceChartDataImplCopyWith<_$PerformanceChartDataImpl>
      get copyWith =>
          __$$PerformanceChartDataImplCopyWithImpl<_$PerformanceChartDataImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PerformanceChartDataImplToJson(
      this,
    );
  }
}

abstract class _PerformanceChartData implements PerformanceChartData {
  factory _PerformanceChartData(
      {required final String date,
      required final int clicks,
      required final int conversions}) = _$PerformanceChartDataImpl;

  factory _PerformanceChartData.fromJson(Map<String, dynamic> json) =
      _$PerformanceChartDataImpl.fromJson;

  @override
  String get date;
  @override
  int get clicks;
  @override
  int get conversions;

  /// Create a copy of PerformanceChartData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PerformanceChartDataImplCopyWith<_$PerformanceChartDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FindReportSummaryRequest _$FindReportSummaryRequestFromJson(
    Map<String, dynamic> json) {
  return _FindReportSummaryRequest.fromJson(json);
}

/// @nodoc
mixin _$FindReportSummaryRequest {
  DateTime get fromDate => throw _privateConstructorUsedError;
  DateTime get toDate => throw _privateConstructorUsedError;
  DateTime get compareFromDate => throw _privateConstructorUsedError;
  DateTime get compareToDate => throw _privateConstructorUsedError;

  /// Serializes this FindReportSummaryRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FindReportSummaryRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FindReportSummaryRequestCopyWith<FindReportSummaryRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FindReportSummaryRequestCopyWith<$Res> {
  factory $FindReportSummaryRequestCopyWith(FindReportSummaryRequest value,
          $Res Function(FindReportSummaryRequest) then) =
      _$FindReportSummaryRequestCopyWithImpl<$Res, FindReportSummaryRequest>;
  @useResult
  $Res call(
      {DateTime fromDate,
      DateTime toDate,
      DateTime compareFromDate,
      DateTime compareToDate});
}

/// @nodoc
class _$FindReportSummaryRequestCopyWithImpl<$Res,
        $Val extends FindReportSummaryRequest>
    implements $FindReportSummaryRequestCopyWith<$Res> {
  _$FindReportSummaryRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FindReportSummaryRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromDate = null,
    Object? toDate = null,
    Object? compareFromDate = null,
    Object? compareToDate = null,
  }) {
    return _then(_value.copyWith(
      fromDate: null == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      toDate: null == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      compareFromDate: null == compareFromDate
          ? _value.compareFromDate
          : compareFromDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      compareToDate: null == compareToDate
          ? _value.compareToDate
          : compareToDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FindReportSummaryRequestImplCopyWith<$Res>
    implements $FindReportSummaryRequestCopyWith<$Res> {
  factory _$$FindReportSummaryRequestImplCopyWith(
          _$FindReportSummaryRequestImpl value,
          $Res Function(_$FindReportSummaryRequestImpl) then) =
      __$$FindReportSummaryRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime fromDate,
      DateTime toDate,
      DateTime compareFromDate,
      DateTime compareToDate});
}

/// @nodoc
class __$$FindReportSummaryRequestImplCopyWithImpl<$Res>
    extends _$FindReportSummaryRequestCopyWithImpl<$Res,
        _$FindReportSummaryRequestImpl>
    implements _$$FindReportSummaryRequestImplCopyWith<$Res> {
  __$$FindReportSummaryRequestImplCopyWithImpl(
      _$FindReportSummaryRequestImpl _value,
      $Res Function(_$FindReportSummaryRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of FindReportSummaryRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromDate = null,
    Object? toDate = null,
    Object? compareFromDate = null,
    Object? compareToDate = null,
  }) {
    return _then(_$FindReportSummaryRequestImpl(
      fromDate: null == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      toDate: null == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      compareFromDate: null == compareFromDate
          ? _value.compareFromDate
          : compareFromDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      compareToDate: null == compareToDate
          ? _value.compareToDate
          : compareToDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FindReportSummaryRequestImpl implements _FindReportSummaryRequest {
  _$FindReportSummaryRequestImpl(
      {required this.fromDate,
      required this.toDate,
      required this.compareFromDate,
      required this.compareToDate});

  factory _$FindReportSummaryRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$FindReportSummaryRequestImplFromJson(json);

  @override
  final DateTime fromDate;
  @override
  final DateTime toDate;
  @override
  final DateTime compareFromDate;
  @override
  final DateTime compareToDate;

  @override
  String toString() {
    return 'FindReportSummaryRequest(fromDate: $fromDate, toDate: $toDate, compareFromDate: $compareFromDate, compareToDate: $compareToDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FindReportSummaryRequestImpl &&
            (identical(other.fromDate, fromDate) ||
                other.fromDate == fromDate) &&
            (identical(other.toDate, toDate) || other.toDate == toDate) &&
            (identical(other.compareFromDate, compareFromDate) ||
                other.compareFromDate == compareFromDate) &&
            (identical(other.compareToDate, compareToDate) ||
                other.compareToDate == compareToDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, fromDate, toDate, compareFromDate, compareToDate);

  /// Create a copy of FindReportSummaryRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FindReportSummaryRequestImplCopyWith<_$FindReportSummaryRequestImpl>
      get copyWith => __$$FindReportSummaryRequestImplCopyWithImpl<
          _$FindReportSummaryRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FindReportSummaryRequestImplToJson(
      this,
    );
  }
}

abstract class _FindReportSummaryRequest implements FindReportSummaryRequest {
  factory _FindReportSummaryRequest(
      {required final DateTime fromDate,
      required final DateTime toDate,
      required final DateTime compareFromDate,
      required final DateTime compareToDate}) = _$FindReportSummaryRequestImpl;

  factory _FindReportSummaryRequest.fromJson(Map<String, dynamic> json) =
      _$FindReportSummaryRequestImpl.fromJson;

  @override
  DateTime get fromDate;
  @override
  DateTime get toDate;
  @override
  DateTime get compareFromDate;
  @override
  DateTime get compareToDate;

  /// Create a copy of FindReportSummaryRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FindReportSummaryRequestImplCopyWith<_$FindReportSummaryRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FindPerformanceChartRequest _$FindPerformanceChartRequestFromJson(
    Map<String, dynamic> json) {
  return _FindPerformanceChartRequest.fromJson(json);
}

/// @nodoc
mixin _$FindPerformanceChartRequest {
  int get siteId => throw _privateConstructorUsedError;
  DateTime get fromDate => throw _privateConstructorUsedError;
  DateTime get toDate => throw _privateConstructorUsedError;
  ReportQueryPeriodBase get periodBase => throw _privateConstructorUsedError;

  /// Serializes this FindPerformanceChartRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FindPerformanceChartRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FindPerformanceChartRequestCopyWith<FindPerformanceChartRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FindPerformanceChartRequestCopyWith<$Res> {
  factory $FindPerformanceChartRequestCopyWith(
          FindPerformanceChartRequest value,
          $Res Function(FindPerformanceChartRequest) then) =
      _$FindPerformanceChartRequestCopyWithImpl<$Res,
          FindPerformanceChartRequest>;
  @useResult
  $Res call(
      {int siteId,
      DateTime fromDate,
      DateTime toDate,
      ReportQueryPeriodBase periodBase});
}

/// @nodoc
class _$FindPerformanceChartRequestCopyWithImpl<$Res,
        $Val extends FindPerformanceChartRequest>
    implements $FindPerformanceChartRequestCopyWith<$Res> {
  _$FindPerformanceChartRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FindPerformanceChartRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? siteId = null,
    Object? fromDate = null,
    Object? toDate = null,
    Object? periodBase = null,
  }) {
    return _then(_value.copyWith(
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      fromDate: null == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      toDate: null == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      periodBase: null == periodBase
          ? _value.periodBase
          : periodBase // ignore: cast_nullable_to_non_nullable
              as ReportQueryPeriodBase,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FindPerformanceChartRequestImplCopyWith<$Res>
    implements $FindPerformanceChartRequestCopyWith<$Res> {
  factory _$$FindPerformanceChartRequestImplCopyWith(
          _$FindPerformanceChartRequestImpl value,
          $Res Function(_$FindPerformanceChartRequestImpl) then) =
      __$$FindPerformanceChartRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int siteId,
      DateTime fromDate,
      DateTime toDate,
      ReportQueryPeriodBase periodBase});
}

/// @nodoc
class __$$FindPerformanceChartRequestImplCopyWithImpl<$Res>
    extends _$FindPerformanceChartRequestCopyWithImpl<$Res,
        _$FindPerformanceChartRequestImpl>
    implements _$$FindPerformanceChartRequestImplCopyWith<$Res> {
  __$$FindPerformanceChartRequestImplCopyWithImpl(
      _$FindPerformanceChartRequestImpl _value,
      $Res Function(_$FindPerformanceChartRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of FindPerformanceChartRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? siteId = null,
    Object? fromDate = null,
    Object? toDate = null,
    Object? periodBase = null,
  }) {
    return _then(_$FindPerformanceChartRequestImpl(
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      fromDate: null == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      toDate: null == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      periodBase: null == periodBase
          ? _value.periodBase
          : periodBase // ignore: cast_nullable_to_non_nullable
              as ReportQueryPeriodBase,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FindPerformanceChartRequestImpl
    implements _FindPerformanceChartRequest {
  _$FindPerformanceChartRequestImpl(
      {required this.siteId,
      required this.fromDate,
      required this.toDate,
      required this.periodBase});

  factory _$FindPerformanceChartRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$FindPerformanceChartRequestImplFromJson(json);

  @override
  final int siteId;
  @override
  final DateTime fromDate;
  @override
  final DateTime toDate;
  @override
  final ReportQueryPeriodBase periodBase;

  @override
  String toString() {
    return 'FindPerformanceChartRequest(siteId: $siteId, fromDate: $fromDate, toDate: $toDate, periodBase: $periodBase)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FindPerformanceChartRequestImpl &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.fromDate, fromDate) ||
                other.fromDate == fromDate) &&
            (identical(other.toDate, toDate) || other.toDate == toDate) &&
            (identical(other.periodBase, periodBase) ||
                other.periodBase == periodBase));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, siteId, fromDate, toDate, periodBase);

  /// Create a copy of FindPerformanceChartRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FindPerformanceChartRequestImplCopyWith<_$FindPerformanceChartRequestImpl>
      get copyWith => __$$FindPerformanceChartRequestImplCopyWithImpl<
          _$FindPerformanceChartRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FindPerformanceChartRequestImplToJson(
      this,
    );
  }
}

abstract class _FindPerformanceChartRequest
    implements FindPerformanceChartRequest {
  factory _FindPerformanceChartRequest(
          {required final int siteId,
          required final DateTime fromDate,
          required final DateTime toDate,
          required final ReportQueryPeriodBase periodBase}) =
      _$FindPerformanceChartRequestImpl;

  factory _FindPerformanceChartRequest.fromJson(Map<String, dynamic> json) =
      _$FindPerformanceChartRequestImpl.fromJson;

  @override
  int get siteId;
  @override
  DateTime get fromDate;
  @override
  DateTime get toDate;
  @override
  ReportQueryPeriodBase get periodBase;

  /// Create a copy of FindPerformanceChartRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FindPerformanceChartRequestImplCopyWith<_$FindPerformanceChartRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ReportSummary _$ReportSummaryFromJson(Map<String, dynamic> json) {
  return _ReportSummary.fromJson(json);
}

/// @nodoc
mixin _$ReportSummary {
  Metric get earnings => throw _privateConstructorUsedError;
  Metric get clicks => throw _privateConstructorUsedError;
  Metric get conversions => throw _privateConstructorUsedError;
  Metric get earningsPerClick => throw _privateConstructorUsedError;

  /// Serializes this ReportSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReportSummaryCopyWith<ReportSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReportSummaryCopyWith<$Res> {
  factory $ReportSummaryCopyWith(
          ReportSummary value, $Res Function(ReportSummary) then) =
      _$ReportSummaryCopyWithImpl<$Res, ReportSummary>;
  @useResult
  $Res call(
      {Metric earnings,
      Metric clicks,
      Metric conversions,
      Metric earningsPerClick});

  $MetricCopyWith<$Res> get earnings;
  $MetricCopyWith<$Res> get clicks;
  $MetricCopyWith<$Res> get conversions;
  $MetricCopyWith<$Res> get earningsPerClick;
}

/// @nodoc
class _$ReportSummaryCopyWithImpl<$Res, $Val extends ReportSummary>
    implements $ReportSummaryCopyWith<$Res> {
  _$ReportSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? earnings = null,
    Object? clicks = null,
    Object? conversions = null,
    Object? earningsPerClick = null,
  }) {
    return _then(_value.copyWith(
      earnings: null == earnings
          ? _value.earnings
          : earnings // ignore: cast_nullable_to_non_nullable
              as Metric,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as Metric,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as Metric,
      earningsPerClick: null == earningsPerClick
          ? _value.earningsPerClick
          : earningsPerClick // ignore: cast_nullable_to_non_nullable
              as Metric,
    ) as $Val);
  }

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MetricCopyWith<$Res> get earnings {
    return $MetricCopyWith<$Res>(_value.earnings, (value) {
      return _then(_value.copyWith(earnings: value) as $Val);
    });
  }

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MetricCopyWith<$Res> get clicks {
    return $MetricCopyWith<$Res>(_value.clicks, (value) {
      return _then(_value.copyWith(clicks: value) as $Val);
    });
  }

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MetricCopyWith<$Res> get conversions {
    return $MetricCopyWith<$Res>(_value.conversions, (value) {
      return _then(_value.copyWith(conversions: value) as $Val);
    });
  }

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MetricCopyWith<$Res> get earningsPerClick {
    return $MetricCopyWith<$Res>(_value.earningsPerClick, (value) {
      return _then(_value.copyWith(earningsPerClick: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ReportSummaryImplCopyWith<$Res>
    implements $ReportSummaryCopyWith<$Res> {
  factory _$$ReportSummaryImplCopyWith(
          _$ReportSummaryImpl value, $Res Function(_$ReportSummaryImpl) then) =
      __$$ReportSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Metric earnings,
      Metric clicks,
      Metric conversions,
      Metric earningsPerClick});

  @override
  $MetricCopyWith<$Res> get earnings;
  @override
  $MetricCopyWith<$Res> get clicks;
  @override
  $MetricCopyWith<$Res> get conversions;
  @override
  $MetricCopyWith<$Res> get earningsPerClick;
}

/// @nodoc
class __$$ReportSummaryImplCopyWithImpl<$Res>
    extends _$ReportSummaryCopyWithImpl<$Res, _$ReportSummaryImpl>
    implements _$$ReportSummaryImplCopyWith<$Res> {
  __$$ReportSummaryImplCopyWithImpl(
      _$ReportSummaryImpl _value, $Res Function(_$ReportSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? earnings = null,
    Object? clicks = null,
    Object? conversions = null,
    Object? earningsPerClick = null,
  }) {
    return _then(_$ReportSummaryImpl(
      earnings: null == earnings
          ? _value.earnings
          : earnings // ignore: cast_nullable_to_non_nullable
              as Metric,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as Metric,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as Metric,
      earningsPerClick: null == earningsPerClick
          ? _value.earningsPerClick
          : earningsPerClick // ignore: cast_nullable_to_non_nullable
              as Metric,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReportSummaryImpl implements _ReportSummary {
  const _$ReportSummaryImpl(
      {this.earnings = const Metric(),
      this.clicks = const Metric(),
      this.conversions = const Metric(),
      this.earningsPerClick = const Metric()});

  factory _$ReportSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReportSummaryImplFromJson(json);

  @override
  @JsonKey()
  final Metric earnings;
  @override
  @JsonKey()
  final Metric clicks;
  @override
  @JsonKey()
  final Metric conversions;
  @override
  @JsonKey()
  final Metric earningsPerClick;

  @override
  String toString() {
    return 'ReportSummary(earnings: $earnings, clicks: $clicks, conversions: $conversions, earningsPerClick: $earningsPerClick)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReportSummaryImpl &&
            (identical(other.earnings, earnings) ||
                other.earnings == earnings) &&
            (identical(other.clicks, clicks) || other.clicks == clicks) &&
            (identical(other.conversions, conversions) ||
                other.conversions == conversions) &&
            (identical(other.earningsPerClick, earningsPerClick) ||
                other.earningsPerClick == earningsPerClick));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, earnings, clicks, conversions, earningsPerClick);

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReportSummaryImplCopyWith<_$ReportSummaryImpl> get copyWith =>
      __$$ReportSummaryImplCopyWithImpl<_$ReportSummaryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReportSummaryImplToJson(
      this,
    );
  }
}

abstract class _ReportSummary implements ReportSummary {
  const factory _ReportSummary(
      {final Metric earnings,
      final Metric clicks,
      final Metric conversions,
      final Metric earningsPerClick}) = _$ReportSummaryImpl;

  factory _ReportSummary.fromJson(Map<String, dynamic> json) =
      _$ReportSummaryImpl.fromJson;

  @override
  Metric get earnings;
  @override
  Metric get clicks;
  @override
  Metric get conversions;
  @override
  Metric get earningsPerClick;

  /// Create a copy of ReportSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReportSummaryImplCopyWith<_$ReportSummaryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Metric _$MetricFromJson(Map<String, dynamic> json) {
  return _Metric.fromJson(json);
}

/// @nodoc
mixin _$Metric {
  double get current => throw _privateConstructorUsedError;
  double get changedPercentage => throw _privateConstructorUsedError;
  double get changedValue => throw _privateConstructorUsedError;

  /// Serializes this Metric to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Metric
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MetricCopyWith<Metric> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MetricCopyWith<$Res> {
  factory $MetricCopyWith(Metric value, $Res Function(Metric) then) =
      _$MetricCopyWithImpl<$Res, Metric>;
  @useResult
  $Res call({double current, double changedPercentage, double changedValue});
}

/// @nodoc
class _$MetricCopyWithImpl<$Res, $Val extends Metric>
    implements $MetricCopyWith<$Res> {
  _$MetricCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Metric
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = null,
    Object? changedPercentage = null,
    Object? changedValue = null,
  }) {
    return _then(_value.copyWith(
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as double,
      changedPercentage: null == changedPercentage
          ? _value.changedPercentage
          : changedPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      changedValue: null == changedValue
          ? _value.changedValue
          : changedValue // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MetricImplCopyWith<$Res> implements $MetricCopyWith<$Res> {
  factory _$$MetricImplCopyWith(
          _$MetricImpl value, $Res Function(_$MetricImpl) then) =
      __$$MetricImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double current, double changedPercentage, double changedValue});
}

/// @nodoc
class __$$MetricImplCopyWithImpl<$Res>
    extends _$MetricCopyWithImpl<$Res, _$MetricImpl>
    implements _$$MetricImplCopyWith<$Res> {
  __$$MetricImplCopyWithImpl(
      _$MetricImpl _value, $Res Function(_$MetricImpl) _then)
      : super(_value, _then);

  /// Create a copy of Metric
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = null,
    Object? changedPercentage = null,
    Object? changedValue = null,
  }) {
    return _then(_$MetricImpl(
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as double,
      changedPercentage: null == changedPercentage
          ? _value.changedPercentage
          : changedPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      changedValue: null == changedValue
          ? _value.changedValue
          : changedValue // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MetricImpl implements _Metric {
  const _$MetricImpl(
      {this.current = 0, this.changedPercentage = 0, this.changedValue = 0});

  factory _$MetricImpl.fromJson(Map<String, dynamic> json) =>
      _$$MetricImplFromJson(json);

  @override
  @JsonKey()
  final double current;
  @override
  @JsonKey()
  final double changedPercentage;
  @override
  @JsonKey()
  final double changedValue;

  @override
  String toString() {
    return 'Metric(current: $current, changedPercentage: $changedPercentage, changedValue: $changedValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MetricImpl &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.changedPercentage, changedPercentage) ||
                other.changedPercentage == changedPercentage) &&
            (identical(other.changedValue, changedValue) ||
                other.changedValue == changedValue));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, current, changedPercentage, changedValue);

  /// Create a copy of Metric
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MetricImplCopyWith<_$MetricImpl> get copyWith =>
      __$$MetricImplCopyWithImpl<_$MetricImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MetricImplToJson(
      this,
    );
  }
}

abstract class _Metric implements Metric {
  const factory _Metric(
      {final double current,
      final double changedPercentage,
      final double changedValue}) = _$MetricImpl;

  factory _Metric.fromJson(Map<String, dynamic> json) = _$MetricImpl.fromJson;

  @override
  double get current;
  @override
  double get changedPercentage;
  @override
  double get changedValue;

  /// Create a copy of Metric
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MetricImplCopyWith<_$MetricImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
