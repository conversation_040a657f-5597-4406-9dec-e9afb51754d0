import 'package:freezed_annotation/freezed_annotation.dart';

part 'conversion_campaign_summary_response.freezed.dart';
part 'conversion_campaign_summary_response.g.dart';

@freezed
class ConversionCampaignSummaryResponse
    with _$ConversionCampaignSummaryResponse {
  factory ConversionCampaignSummaryResponse({
    required int campaignCount,
    required int conversionCount,
    required double totalReward,
    required List<ConversionCampaignItem> campaignSummaries,
  }) = _ConversionCampaignSummaryResponse;

  factory ConversionCampaignSummaryResponse.fromJson(
          Map<String, Object?> json) =>
      _$ConversionCampaignSummaryResponseFromJson(json);
}

@freezed
class ConversionCampaignItem with _$ConversionCampaignItem {
  factory ConversionCampaignItem({
    required int campaignId,
    required String campaignName,
    required int conversionCount,
    required double totalReward,
  }) = _ConversionCampaignItem;

  factory ConversionCampaignItem.fromJson(Map<String, Object?> json) =>
      _$ConversionCampaignItemFromJson(json);
}
