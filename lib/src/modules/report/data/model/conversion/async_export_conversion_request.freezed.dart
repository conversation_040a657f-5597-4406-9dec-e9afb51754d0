// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'async_export_conversion_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AsyncExportConversionRequest _$AsyncExportConversionRequestFromJson(
    Map<String, dynamic> json) {
  return _AsyncExportConversionRequest.fromJson(json);
}

/// @nodoc
mixin _$AsyncExportConversionRequest {
  DateTime? get fromDate => throw _privateConstructorUsedError;
  DateTime? get toDate => throw _privateConstructorUsedError;
  ReportQueryPeriodBase get periodBase => throw _privateConstructorUsedError;
  List<ConversionStatus>? get conversionStatuses =>
      throw _privateConstructorUsedError;
  List<int>? get campaignIds => throw _privateConstructorUsedError;
  List<int>? get siteIds => throw _privateConstructorUsedError;
  String? get invoiceNumber => throw _privateConstructorUsedError;
  String? get locale => throw _privateConstructorUsedError;

  /// Serializes this AsyncExportConversionRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AsyncExportConversionRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AsyncExportConversionRequestCopyWith<AsyncExportConversionRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AsyncExportConversionRequestCopyWith<$Res> {
  factory $AsyncExportConversionRequestCopyWith(
          AsyncExportConversionRequest value,
          $Res Function(AsyncExportConversionRequest) then) =
      _$AsyncExportConversionRequestCopyWithImpl<$Res,
          AsyncExportConversionRequest>;
  @useResult
  $Res call(
      {DateTime? fromDate,
      DateTime? toDate,
      ReportQueryPeriodBase periodBase,
      List<ConversionStatus>? conversionStatuses,
      List<int>? campaignIds,
      List<int>? siteIds,
      String? invoiceNumber,
      String? locale});
}

/// @nodoc
class _$AsyncExportConversionRequestCopyWithImpl<$Res,
        $Val extends AsyncExportConversionRequest>
    implements $AsyncExportConversionRequestCopyWith<$Res> {
  _$AsyncExportConversionRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AsyncExportConversionRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromDate = freezed,
    Object? toDate = freezed,
    Object? periodBase = null,
    Object? conversionStatuses = freezed,
    Object? campaignIds = freezed,
    Object? siteIds = freezed,
    Object? invoiceNumber = freezed,
    Object? locale = freezed,
  }) {
    return _then(_value.copyWith(
      fromDate: freezed == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toDate: freezed == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      periodBase: null == periodBase
          ? _value.periodBase
          : periodBase // ignore: cast_nullable_to_non_nullable
              as ReportQueryPeriodBase,
      conversionStatuses: freezed == conversionStatuses
          ? _value.conversionStatuses
          : conversionStatuses // ignore: cast_nullable_to_non_nullable
              as List<ConversionStatus>?,
      campaignIds: freezed == campaignIds
          ? _value.campaignIds
          : campaignIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      siteIds: freezed == siteIds
          ? _value.siteIds
          : siteIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      invoiceNumber: freezed == invoiceNumber
          ? _value.invoiceNumber
          : invoiceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      locale: freezed == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AsyncExportConversionRequestImplCopyWith<$Res>
    implements $AsyncExportConversionRequestCopyWith<$Res> {
  factory _$$AsyncExportConversionRequestImplCopyWith(
          _$AsyncExportConversionRequestImpl value,
          $Res Function(_$AsyncExportConversionRequestImpl) then) =
      __$$AsyncExportConversionRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime? fromDate,
      DateTime? toDate,
      ReportQueryPeriodBase periodBase,
      List<ConversionStatus>? conversionStatuses,
      List<int>? campaignIds,
      List<int>? siteIds,
      String? invoiceNumber,
      String? locale});
}

/// @nodoc
class __$$AsyncExportConversionRequestImplCopyWithImpl<$Res>
    extends _$AsyncExportConversionRequestCopyWithImpl<$Res,
        _$AsyncExportConversionRequestImpl>
    implements _$$AsyncExportConversionRequestImplCopyWith<$Res> {
  __$$AsyncExportConversionRequestImplCopyWithImpl(
      _$AsyncExportConversionRequestImpl _value,
      $Res Function(_$AsyncExportConversionRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of AsyncExportConversionRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromDate = freezed,
    Object? toDate = freezed,
    Object? periodBase = null,
    Object? conversionStatuses = freezed,
    Object? campaignIds = freezed,
    Object? siteIds = freezed,
    Object? invoiceNumber = freezed,
    Object? locale = freezed,
  }) {
    return _then(_$AsyncExportConversionRequestImpl(
      fromDate: freezed == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toDate: freezed == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      periodBase: null == periodBase
          ? _value.periodBase
          : periodBase // ignore: cast_nullable_to_non_nullable
              as ReportQueryPeriodBase,
      conversionStatuses: freezed == conversionStatuses
          ? _value._conversionStatuses
          : conversionStatuses // ignore: cast_nullable_to_non_nullable
              as List<ConversionStatus>?,
      campaignIds: freezed == campaignIds
          ? _value._campaignIds
          : campaignIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      siteIds: freezed == siteIds
          ? _value._siteIds
          : siteIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      invoiceNumber: freezed == invoiceNumber
          ? _value.invoiceNumber
          : invoiceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      locale: freezed == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AsyncExportConversionRequestImpl
    implements _AsyncExportConversionRequest {
  _$AsyncExportConversionRequestImpl(
      {this.fromDate,
      this.toDate,
      this.periodBase = ReportQueryPeriodBase.CONVERSION_DATE,
      final List<ConversionStatus>? conversionStatuses,
      final List<int>? campaignIds,
      final List<int>? siteIds,
      this.invoiceNumber,
      this.locale})
      : _conversionStatuses = conversionStatuses,
        _campaignIds = campaignIds,
        _siteIds = siteIds;

  factory _$AsyncExportConversionRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AsyncExportConversionRequestImplFromJson(json);

  @override
  final DateTime? fromDate;
  @override
  final DateTime? toDate;
  @override
  @JsonKey()
  final ReportQueryPeriodBase periodBase;
  final List<ConversionStatus>? _conversionStatuses;
  @override
  List<ConversionStatus>? get conversionStatuses {
    final value = _conversionStatuses;
    if (value == null) return null;
    if (_conversionStatuses is EqualUnmodifiableListView)
      return _conversionStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _campaignIds;
  @override
  List<int>? get campaignIds {
    final value = _campaignIds;
    if (value == null) return null;
    if (_campaignIds is EqualUnmodifiableListView) return _campaignIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _siteIds;
  @override
  List<int>? get siteIds {
    final value = _siteIds;
    if (value == null) return null;
    if (_siteIds is EqualUnmodifiableListView) return _siteIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? invoiceNumber;
  @override
  final String? locale;

  @override
  String toString() {
    return 'AsyncExportConversionRequest(fromDate: $fromDate, toDate: $toDate, periodBase: $periodBase, conversionStatuses: $conversionStatuses, campaignIds: $campaignIds, siteIds: $siteIds, invoiceNumber: $invoiceNumber, locale: $locale)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AsyncExportConversionRequestImpl &&
            (identical(other.fromDate, fromDate) ||
                other.fromDate == fromDate) &&
            (identical(other.toDate, toDate) || other.toDate == toDate) &&
            (identical(other.periodBase, periodBase) ||
                other.periodBase == periodBase) &&
            const DeepCollectionEquality()
                .equals(other._conversionStatuses, _conversionStatuses) &&
            const DeepCollectionEquality()
                .equals(other._campaignIds, _campaignIds) &&
            const DeepCollectionEquality().equals(other._siteIds, _siteIds) &&
            (identical(other.invoiceNumber, invoiceNumber) ||
                other.invoiceNumber == invoiceNumber) &&
            (identical(other.locale, locale) || other.locale == locale));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      fromDate,
      toDate,
      periodBase,
      const DeepCollectionEquality().hash(_conversionStatuses),
      const DeepCollectionEquality().hash(_campaignIds),
      const DeepCollectionEquality().hash(_siteIds),
      invoiceNumber,
      locale);

  /// Create a copy of AsyncExportConversionRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AsyncExportConversionRequestImplCopyWith<
          _$AsyncExportConversionRequestImpl>
      get copyWith => __$$AsyncExportConversionRequestImplCopyWithImpl<
          _$AsyncExportConversionRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AsyncExportConversionRequestImplToJson(
      this,
    );
  }
}

abstract class _AsyncExportConversionRequest
    implements AsyncExportConversionRequest {
  factory _AsyncExportConversionRequest(
      {final DateTime? fromDate,
      final DateTime? toDate,
      final ReportQueryPeriodBase periodBase,
      final List<ConversionStatus>? conversionStatuses,
      final List<int>? campaignIds,
      final List<int>? siteIds,
      final String? invoiceNumber,
      final String? locale}) = _$AsyncExportConversionRequestImpl;

  factory _AsyncExportConversionRequest.fromJson(Map<String, dynamic> json) =
      _$AsyncExportConversionRequestImpl.fromJson;

  @override
  DateTime? get fromDate;
  @override
  DateTime? get toDate;
  @override
  ReportQueryPeriodBase get periodBase;
  @override
  List<ConversionStatus>? get conversionStatuses;
  @override
  List<int>? get campaignIds;
  @override
  List<int>? get siteIds;
  @override
  String? get invoiceNumber;
  @override
  String? get locale;

  /// Create a copy of AsyncExportConversionRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AsyncExportConversionRequestImplCopyWith<
          _$AsyncExportConversionRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
