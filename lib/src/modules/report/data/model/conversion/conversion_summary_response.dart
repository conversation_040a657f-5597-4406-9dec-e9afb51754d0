import 'package:freezed_annotation/freezed_annotation.dart';

part 'conversion_summary_response.freezed.dart';
part 'conversion_summary_response.g.dart';

@freezed
class ConversionSummaryResponse with _$ConversionSummaryResponse {
  factory ConversionSummaryResponse({
    @Default(0) int countConversionOccurredThisMonth,
    @Default(0) double rewardApprovedLastMonth,
    @Default('') String currencyCode,
  }) = _ConversionSummaryResponse;

  factory ConversionSummaryResponse.fromJson(Map<String, Object?> json) =>
      _$ConversionSummaryResponseFromJson(json);
}
