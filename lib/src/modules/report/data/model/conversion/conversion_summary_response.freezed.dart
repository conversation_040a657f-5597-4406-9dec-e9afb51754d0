// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversion_summary_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ConversionSummaryResponse _$ConversionSummaryResponseFromJson(
    Map<String, dynamic> json) {
  return _ConversionSummaryResponse.fromJson(json);
}

/// @nodoc
mixin _$ConversionSummaryResponse {
  int get countConversionOccurredThisMonth =>
      throw _privateConstructorUsedError;
  double get rewardApprovedLastMonth => throw _privateConstructorUsedError;
  String get currencyCode => throw _privateConstructorUsedError;

  /// Serializes this ConversionSummaryResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConversionSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConversionSummaryResponseCopyWith<ConversionSummaryResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversionSummaryResponseCopyWith<$Res> {
  factory $ConversionSummaryResponseCopyWith(ConversionSummaryResponse value,
          $Res Function(ConversionSummaryResponse) then) =
      _$ConversionSummaryResponseCopyWithImpl<$Res, ConversionSummaryResponse>;
  @useResult
  $Res call(
      {int countConversionOccurredThisMonth,
      double rewardApprovedLastMonth,
      String currencyCode});
}

/// @nodoc
class _$ConversionSummaryResponseCopyWithImpl<$Res,
        $Val extends ConversionSummaryResponse>
    implements $ConversionSummaryResponseCopyWith<$Res> {
  _$ConversionSummaryResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConversionSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countConversionOccurredThisMonth = null,
    Object? rewardApprovedLastMonth = null,
    Object? currencyCode = null,
  }) {
    return _then(_value.copyWith(
      countConversionOccurredThisMonth: null == countConversionOccurredThisMonth
          ? _value.countConversionOccurredThisMonth
          : countConversionOccurredThisMonth // ignore: cast_nullable_to_non_nullable
              as int,
      rewardApprovedLastMonth: null == rewardApprovedLastMonth
          ? _value.rewardApprovedLastMonth
          : rewardApprovedLastMonth // ignore: cast_nullable_to_non_nullable
              as double,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConversionSummaryResponseImplCopyWith<$Res>
    implements $ConversionSummaryResponseCopyWith<$Res> {
  factory _$$ConversionSummaryResponseImplCopyWith(
          _$ConversionSummaryResponseImpl value,
          $Res Function(_$ConversionSummaryResponseImpl) then) =
      __$$ConversionSummaryResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int countConversionOccurredThisMonth,
      double rewardApprovedLastMonth,
      String currencyCode});
}

/// @nodoc
class __$$ConversionSummaryResponseImplCopyWithImpl<$Res>
    extends _$ConversionSummaryResponseCopyWithImpl<$Res,
        _$ConversionSummaryResponseImpl>
    implements _$$ConversionSummaryResponseImplCopyWith<$Res> {
  __$$ConversionSummaryResponseImplCopyWithImpl(
      _$ConversionSummaryResponseImpl _value,
      $Res Function(_$ConversionSummaryResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConversionSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countConversionOccurredThisMonth = null,
    Object? rewardApprovedLastMonth = null,
    Object? currencyCode = null,
  }) {
    return _then(_$ConversionSummaryResponseImpl(
      countConversionOccurredThisMonth: null == countConversionOccurredThisMonth
          ? _value.countConversionOccurredThisMonth
          : countConversionOccurredThisMonth // ignore: cast_nullable_to_non_nullable
              as int,
      rewardApprovedLastMonth: null == rewardApprovedLastMonth
          ? _value.rewardApprovedLastMonth
          : rewardApprovedLastMonth // ignore: cast_nullable_to_non_nullable
              as double,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ConversionSummaryResponseImpl implements _ConversionSummaryResponse {
  _$ConversionSummaryResponseImpl(
      {this.countConversionOccurredThisMonth = 0,
      this.rewardApprovedLastMonth = 0,
      this.currencyCode = ''});

  factory _$ConversionSummaryResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConversionSummaryResponseImplFromJson(json);

  @override
  @JsonKey()
  final int countConversionOccurredThisMonth;
  @override
  @JsonKey()
  final double rewardApprovedLastMonth;
  @override
  @JsonKey()
  final String currencyCode;

  @override
  String toString() {
    return 'ConversionSummaryResponse(countConversionOccurredThisMonth: $countConversionOccurredThisMonth, rewardApprovedLastMonth: $rewardApprovedLastMonth, currencyCode: $currencyCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversionSummaryResponseImpl &&
            (identical(other.countConversionOccurredThisMonth,
                    countConversionOccurredThisMonth) ||
                other.countConversionOccurredThisMonth ==
                    countConversionOccurredThisMonth) &&
            (identical(
                    other.rewardApprovedLastMonth, rewardApprovedLastMonth) ||
                other.rewardApprovedLastMonth == rewardApprovedLastMonth) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, countConversionOccurredThisMonth,
      rewardApprovedLastMonth, currencyCode);

  /// Create a copy of ConversionSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversionSummaryResponseImplCopyWith<_$ConversionSummaryResponseImpl>
      get copyWith => __$$ConversionSummaryResponseImplCopyWithImpl<
          _$ConversionSummaryResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConversionSummaryResponseImplToJson(
      this,
    );
  }
}

abstract class _ConversionSummaryResponse implements ConversionSummaryResponse {
  factory _ConversionSummaryResponse(
      {final int countConversionOccurredThisMonth,
      final double rewardApprovedLastMonth,
      final String currencyCode}) = _$ConversionSummaryResponseImpl;

  factory _ConversionSummaryResponse.fromJson(Map<String, dynamic> json) =
      _$ConversionSummaryResponseImpl.fromJson;

  @override
  int get countConversionOccurredThisMonth;
  @override
  double get rewardApprovedLastMonth;
  @override
  String get currencyCode;

  /// Create a copy of ConversionSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConversionSummaryResponseImplCopyWith<_$ConversionSummaryResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
