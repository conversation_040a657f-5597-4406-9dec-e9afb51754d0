// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversion_campaign_summary_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConversionCampaignSummaryResponseImpl
    _$$ConversionCampaignSummaryResponseImplFromJson(
            Map<String, dynamic> json) =>
        _$ConversionCampaignSummaryResponseImpl(
          campaignCount: (json['campaignCount'] as num).toInt(),
          conversionCount: (json['conversionCount'] as num).toInt(),
          totalReward: (json['totalReward'] as num).toDouble(),
          campaignSummaries: (json['campaignSummaries'] as List<dynamic>)
              .map((e) =>
                  ConversionCampaignItem.fromJson(e as Map<String, dynamic>))
              .toList(),
        );

Map<String, dynamic> _$$ConversionCampaignSummaryResponseImplToJson(
        _$ConversionCampaignSummaryResponseImpl instance) =>
    <String, dynamic>{
      'campaignCount': instance.campaignCount,
      'conversionCount': instance.conversionCount,
      'totalReward': instance.totalReward,
      'campaignSummaries': instance.campaignSummaries,
    };

_$ConversionCampaignItemImpl _$$ConversionCampaignItemImplFromJson(
        Map<String, dynamic> json) =>
    _$ConversionCampaignItemImpl(
      campaignId: (json['campaignId'] as num).toInt(),
      campaignName: json['campaignName'] as String,
      conversionCount: (json['conversionCount'] as num).toInt(),
      totalReward: (json['totalReward'] as num).toDouble(),
    );

Map<String, dynamic> _$$ConversionCampaignItemImplToJson(
        _$ConversionCampaignItemImpl instance) =>
    <String, dynamic>{
      'campaignId': instance.campaignId,
      'campaignName': instance.campaignName,
      'conversionCount': instance.conversionCount,
      'totalReward': instance.totalReward,
    };
