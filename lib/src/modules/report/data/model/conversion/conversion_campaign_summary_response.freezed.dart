// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversion_campaign_summary_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ConversionCampaignSummaryResponse _$ConversionCampaignSummaryResponseFromJson(
    Map<String, dynamic> json) {
  return _ConversionCampaignSummaryResponse.fromJson(json);
}

/// @nodoc
mixin _$ConversionCampaignSummaryResponse {
  int get campaignCount => throw _privateConstructorUsedError;
  int get conversionCount => throw _privateConstructorUsedError;
  double get totalReward => throw _privateConstructorUsedError;
  List<ConversionCampaignItem> get campaignSummaries =>
      throw _privateConstructorUsedError;

  /// Serializes this ConversionCampaignSummaryResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConversionCampaignSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConversionCampaignSummaryResponseCopyWith<ConversionCampaignSummaryResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversionCampaignSummaryResponseCopyWith<$Res> {
  factory $ConversionCampaignSummaryResponseCopyWith(
          ConversionCampaignSummaryResponse value,
          $Res Function(ConversionCampaignSummaryResponse) then) =
      _$ConversionCampaignSummaryResponseCopyWithImpl<$Res,
          ConversionCampaignSummaryResponse>;
  @useResult
  $Res call(
      {int campaignCount,
      int conversionCount,
      double totalReward,
      List<ConversionCampaignItem> campaignSummaries});
}

/// @nodoc
class _$ConversionCampaignSummaryResponseCopyWithImpl<$Res,
        $Val extends ConversionCampaignSummaryResponse>
    implements $ConversionCampaignSummaryResponseCopyWith<$Res> {
  _$ConversionCampaignSummaryResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConversionCampaignSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignCount = null,
    Object? conversionCount = null,
    Object? totalReward = null,
    Object? campaignSummaries = null,
  }) {
    return _then(_value.copyWith(
      campaignCount: null == campaignCount
          ? _value.campaignCount
          : campaignCount // ignore: cast_nullable_to_non_nullable
              as int,
      conversionCount: null == conversionCount
          ? _value.conversionCount
          : conversionCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      campaignSummaries: null == campaignSummaries
          ? _value.campaignSummaries
          : campaignSummaries // ignore: cast_nullable_to_non_nullable
              as List<ConversionCampaignItem>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConversionCampaignSummaryResponseImplCopyWith<$Res>
    implements $ConversionCampaignSummaryResponseCopyWith<$Res> {
  factory _$$ConversionCampaignSummaryResponseImplCopyWith(
          _$ConversionCampaignSummaryResponseImpl value,
          $Res Function(_$ConversionCampaignSummaryResponseImpl) then) =
      __$$ConversionCampaignSummaryResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int campaignCount,
      int conversionCount,
      double totalReward,
      List<ConversionCampaignItem> campaignSummaries});
}

/// @nodoc
class __$$ConversionCampaignSummaryResponseImplCopyWithImpl<$Res>
    extends _$ConversionCampaignSummaryResponseCopyWithImpl<$Res,
        _$ConversionCampaignSummaryResponseImpl>
    implements _$$ConversionCampaignSummaryResponseImplCopyWith<$Res> {
  __$$ConversionCampaignSummaryResponseImplCopyWithImpl(
      _$ConversionCampaignSummaryResponseImpl _value,
      $Res Function(_$ConversionCampaignSummaryResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConversionCampaignSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignCount = null,
    Object? conversionCount = null,
    Object? totalReward = null,
    Object? campaignSummaries = null,
  }) {
    return _then(_$ConversionCampaignSummaryResponseImpl(
      campaignCount: null == campaignCount
          ? _value.campaignCount
          : campaignCount // ignore: cast_nullable_to_non_nullable
              as int,
      conversionCount: null == conversionCount
          ? _value.conversionCount
          : conversionCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      campaignSummaries: null == campaignSummaries
          ? _value._campaignSummaries
          : campaignSummaries // ignore: cast_nullable_to_non_nullable
              as List<ConversionCampaignItem>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ConversionCampaignSummaryResponseImpl
    implements _ConversionCampaignSummaryResponse {
  _$ConversionCampaignSummaryResponseImpl(
      {required this.campaignCount,
      required this.conversionCount,
      required this.totalReward,
      required final List<ConversionCampaignItem> campaignSummaries})
      : _campaignSummaries = campaignSummaries;

  factory _$ConversionCampaignSummaryResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ConversionCampaignSummaryResponseImplFromJson(json);

  @override
  final int campaignCount;
  @override
  final int conversionCount;
  @override
  final double totalReward;
  final List<ConversionCampaignItem> _campaignSummaries;
  @override
  List<ConversionCampaignItem> get campaignSummaries {
    if (_campaignSummaries is EqualUnmodifiableListView)
      return _campaignSummaries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaignSummaries);
  }

  @override
  String toString() {
    return 'ConversionCampaignSummaryResponse(campaignCount: $campaignCount, conversionCount: $conversionCount, totalReward: $totalReward, campaignSummaries: $campaignSummaries)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversionCampaignSummaryResponseImpl &&
            (identical(other.campaignCount, campaignCount) ||
                other.campaignCount == campaignCount) &&
            (identical(other.conversionCount, conversionCount) ||
                other.conversionCount == conversionCount) &&
            (identical(other.totalReward, totalReward) ||
                other.totalReward == totalReward) &&
            const DeepCollectionEquality()
                .equals(other._campaignSummaries, _campaignSummaries));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, campaignCount, conversionCount,
      totalReward, const DeepCollectionEquality().hash(_campaignSummaries));

  /// Create a copy of ConversionCampaignSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversionCampaignSummaryResponseImplCopyWith<
          _$ConversionCampaignSummaryResponseImpl>
      get copyWith => __$$ConversionCampaignSummaryResponseImplCopyWithImpl<
          _$ConversionCampaignSummaryResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConversionCampaignSummaryResponseImplToJson(
      this,
    );
  }
}

abstract class _ConversionCampaignSummaryResponse
    implements ConversionCampaignSummaryResponse {
  factory _ConversionCampaignSummaryResponse(
          {required final int campaignCount,
          required final int conversionCount,
          required final double totalReward,
          required final List<ConversionCampaignItem> campaignSummaries}) =
      _$ConversionCampaignSummaryResponseImpl;

  factory _ConversionCampaignSummaryResponse.fromJson(
          Map<String, dynamic> json) =
      _$ConversionCampaignSummaryResponseImpl.fromJson;

  @override
  int get campaignCount;
  @override
  int get conversionCount;
  @override
  double get totalReward;
  @override
  List<ConversionCampaignItem> get campaignSummaries;

  /// Create a copy of ConversionCampaignSummaryResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConversionCampaignSummaryResponseImplCopyWith<
          _$ConversionCampaignSummaryResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ConversionCampaignItem _$ConversionCampaignItemFromJson(
    Map<String, dynamic> json) {
  return _ConversionCampaignItem.fromJson(json);
}

/// @nodoc
mixin _$ConversionCampaignItem {
  int get campaignId => throw _privateConstructorUsedError;
  String get campaignName => throw _privateConstructorUsedError;
  int get conversionCount => throw _privateConstructorUsedError;
  double get totalReward => throw _privateConstructorUsedError;

  /// Serializes this ConversionCampaignItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConversionCampaignItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConversionCampaignItemCopyWith<ConversionCampaignItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversionCampaignItemCopyWith<$Res> {
  factory $ConversionCampaignItemCopyWith(ConversionCampaignItem value,
          $Res Function(ConversionCampaignItem) then) =
      _$ConversionCampaignItemCopyWithImpl<$Res, ConversionCampaignItem>;
  @useResult
  $Res call(
      {int campaignId,
      String campaignName,
      int conversionCount,
      double totalReward});
}

/// @nodoc
class _$ConversionCampaignItemCopyWithImpl<$Res,
        $Val extends ConversionCampaignItem>
    implements $ConversionCampaignItemCopyWith<$Res> {
  _$ConversionCampaignItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConversionCampaignItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignId = null,
    Object? campaignName = null,
    Object? conversionCount = null,
    Object? totalReward = null,
  }) {
    return _then(_value.copyWith(
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      conversionCount: null == conversionCount
          ? _value.conversionCount
          : conversionCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConversionCampaignItemImplCopyWith<$Res>
    implements $ConversionCampaignItemCopyWith<$Res> {
  factory _$$ConversionCampaignItemImplCopyWith(
          _$ConversionCampaignItemImpl value,
          $Res Function(_$ConversionCampaignItemImpl) then) =
      __$$ConversionCampaignItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int campaignId,
      String campaignName,
      int conversionCount,
      double totalReward});
}

/// @nodoc
class __$$ConversionCampaignItemImplCopyWithImpl<$Res>
    extends _$ConversionCampaignItemCopyWithImpl<$Res,
        _$ConversionCampaignItemImpl>
    implements _$$ConversionCampaignItemImplCopyWith<$Res> {
  __$$ConversionCampaignItemImplCopyWithImpl(
      _$ConversionCampaignItemImpl _value,
      $Res Function(_$ConversionCampaignItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConversionCampaignItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignId = null,
    Object? campaignName = null,
    Object? conversionCount = null,
    Object? totalReward = null,
  }) {
    return _then(_$ConversionCampaignItemImpl(
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      conversionCount: null == conversionCount
          ? _value.conversionCount
          : conversionCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ConversionCampaignItemImpl implements _ConversionCampaignItem {
  _$ConversionCampaignItemImpl(
      {required this.campaignId,
      required this.campaignName,
      required this.conversionCount,
      required this.totalReward});

  factory _$ConversionCampaignItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConversionCampaignItemImplFromJson(json);

  @override
  final int campaignId;
  @override
  final String campaignName;
  @override
  final int conversionCount;
  @override
  final double totalReward;

  @override
  String toString() {
    return 'ConversionCampaignItem(campaignId: $campaignId, campaignName: $campaignName, conversionCount: $conversionCount, totalReward: $totalReward)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversionCampaignItemImpl &&
            (identical(other.campaignId, campaignId) ||
                other.campaignId == campaignId) &&
            (identical(other.campaignName, campaignName) ||
                other.campaignName == campaignName) &&
            (identical(other.conversionCount, conversionCount) ||
                other.conversionCount == conversionCount) &&
            (identical(other.totalReward, totalReward) ||
                other.totalReward == totalReward));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, campaignId, campaignName, conversionCount, totalReward);

  /// Create a copy of ConversionCampaignItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversionCampaignItemImplCopyWith<_$ConversionCampaignItemImpl>
      get copyWith => __$$ConversionCampaignItemImplCopyWithImpl<
          _$ConversionCampaignItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConversionCampaignItemImplToJson(
      this,
    );
  }
}

abstract class _ConversionCampaignItem implements ConversionCampaignItem {
  factory _ConversionCampaignItem(
      {required final int campaignId,
      required final String campaignName,
      required final int conversionCount,
      required final double totalReward}) = _$ConversionCampaignItemImpl;

  factory _ConversionCampaignItem.fromJson(Map<String, dynamic> json) =
      _$ConversionCampaignItemImpl.fromJson;

  @override
  int get campaignId;
  @override
  String get campaignName;
  @override
  int get conversionCount;
  @override
  double get totalReward;

  /// Create a copy of ConversionCampaignItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConversionCampaignItemImplCopyWith<_$ConversionCampaignItemImpl>
      get copyWith => throw _privateConstructorUsedError;
}
