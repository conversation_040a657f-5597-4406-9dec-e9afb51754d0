// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversion_summary_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConversionSummaryResponseImpl _$$ConversionSummaryResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ConversionSummaryResponseImpl(
      countConversionOccurredThisMonth:
          (json['countConversionOccurredThisMonth'] as num?)?.toInt() ?? 0,
      rewardApprovedLastMonth:
          (json['rewardApprovedLastMonth'] as num?)?.toDouble() ?? 0,
      currencyCode: json['currencyCode'] as String? ?? '',
    );

Map<String, dynamic> _$$ConversionSummaryResponseImplToJson(
        _$ConversionSummaryResponseImpl instance) =>
    <String, dynamic>{
      'countConversionOccurredThisMonth':
          instance.countConversionOccurredThisMonth,
      'rewardApprovedLastMonth': instance.rewardApprovedLastMonth,
      'currencyCode': instance.currencyCode,
    };
