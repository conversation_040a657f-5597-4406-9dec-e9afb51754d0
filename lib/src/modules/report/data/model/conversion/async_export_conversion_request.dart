import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';

part 'async_export_conversion_request.freezed.dart';
part 'async_export_conversion_request.g.dart';

@freezed
class AsyncExportConversionRequest with _$AsyncExportConversionRequest {
  factory AsyncExportConversionRequest({
    DateTime? fromDate,
    DateTime? toDate,
    @Default(ReportQueryPeriodBase.CONVERSION_DATE)
    ReportQueryPeriodBase periodBase,
    List<ConversionStatus>? conversionStatuses,
    List<int>? campaignIds,
    List<int>? siteIds,
    String? invoiceNumber,
    String? locale,
  }) = _AsyncExportConversionRequest;

  factory AsyncExportConversionRequest.fromJson(Map<String, Object?> json) =>
      _$AsyncExportConversionRequestFromJson(json);
}
