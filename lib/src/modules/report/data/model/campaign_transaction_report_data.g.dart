// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_transaction_report_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignTransactionReportDataImpl
    _$$CampaignTransactionReportDataImplFromJson(Map<String, dynamic> json) =>
        _$CampaignTransactionReportDataImpl(
          categoryId: json['categoryId'] as String? ?? '',
          productId: json['productId'] as String? ?? '',
          quantity: (json['quantity'] as num?)?.toInt() ?? 0,
          unitPrice: (json['unitPrice'] as num?)?.toDouble() ?? 0,
          reward: (json['reward'] as num?)?.toDouble() ?? 0,
        );

Map<String, dynamic> _$$CampaignTransactionReportDataImplToJson(
        _$CampaignTransactionReportDataImpl instance) =>
    <String, dynamic>{
      'categoryId': instance.categoryId,
      'productId': instance.productId,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'reward': instance.reward,
    };
