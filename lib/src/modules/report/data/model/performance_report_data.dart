import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';

part 'performance_report_data.freezed.dart';
part 'performance_report_data.g.dart';

@freezed
class PerformanceReportData with _$PerformanceReportData {
  factory PerformanceReportData({
    DateTime? date,
    @Default(0) int clicks,
    @Default(0) int conversions,
    @Default(0) double reward,
    @Default(0) double epc,
  }) = _PerformanceReportData;

  factory PerformanceReportData.fromJson(Map<String, Object?> json) =>
      _$PerformanceReportDataFromJson(json);
}

@freezed
class PerformanceChartData with _$PerformanceChartData {
  factory PerformanceChartData({
    required String date,
    required int clicks,
    required int conversions,
  }) = _PerformanceChartData;

  factory PerformanceChartData.fromJson(Map<String, Object?> json) =>
      _$PerformanceChartDataFromJson(json);
}

@freezed
class FindReportSummaryRequest with _$FindReportSummaryRequest {
  factory FindReportSummaryRequest({
    required DateTime fromDate,
    required DateTime toDate,
    required DateTime compareFromDate,
    required DateTime compareToDate,
  }) = _FindReportSummaryRequest;

  factory FindReportSummaryRequest.fromJson(Map<String, Object?> json) =>
      _$FindReportSummaryRequestFromJson(json);
}

@freezed
class FindPerformanceChartRequest with _$FindPerformanceChartRequest {
  factory FindPerformanceChartRequest({
    required int siteId,
    required DateTime fromDate,
    required DateTime toDate,
    required ReportQueryPeriodBase periodBase,
  }) = _FindPerformanceChartRequest;

  factory FindPerformanceChartRequest.fromJson(Map<String, Object?> json) =>
      _$FindPerformanceChartRequestFromJson(json);
}

@freezed
class ReportSummary with _$ReportSummary {
  const factory ReportSummary({
    @Default(Metric()) Metric earnings,
    @Default(Metric()) Metric clicks,
    @Default(Metric()) Metric conversions,
    @Default(Metric()) Metric earningsPerClick,
  }) = _ReportSummary;

  factory ReportSummary.fromJson(Map<String, dynamic> json) =>
      _$ReportSummaryFromJson(json);
}

@freezed
class Metric with _$Metric {
  const factory Metric({
    @Default(0) double current,
    @Default(0) double changedPercentage,
    @Default(0) double changedValue,
  }) = _Metric;

  factory Metric.fromJson(Map<String, dynamic> json) => _$MetricFromJson(json);
}
