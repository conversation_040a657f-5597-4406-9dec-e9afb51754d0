import 'package:freezed_annotation/freezed_annotation.dart';

part 'campaign_transaction_report_data.freezed.dart';
part 'campaign_transaction_report_data.g.dart';

@freezed
class CampaignTransactionReportData with _$CampaignTransactionReportData {
  factory CampaignTransactionReportData({
    @Default('') String categoryId,
    @Default('') String productId,
    @Default(0) int quantity,
    @Default(0) double unitPrice,
    @Default(0) double reward,
  }) = _CampaignTransactionReportData;

  factory CampaignTransactionReportData.fromJson(Map<String, Object?> json) =>
      _$CampaignTransactionReportDataFromJson(json);
}
