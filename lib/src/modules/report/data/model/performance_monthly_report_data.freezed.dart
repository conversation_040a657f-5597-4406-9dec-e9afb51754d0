// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'performance_monthly_report_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PerformanceMonthlyReportData _$PerformanceMonthlyReportDataFromJson(
    Map<String, dynamic> json) {
  return _PerformanceMonthlyReportData.fromJson(json);
}

/// @nodoc
mixin _$PerformanceMonthlyReportData {
  String? get month => throw _privateConstructorUsedError;
  int get clicks => throw _privateConstructorUsedError;
  int get conversions => throw _privateConstructorUsedError;
  double get conversionRate => throw _privateConstructorUsedError;
  double get reward => throw _privateConstructorUsedError;
  double get earningsPerClick => throw _privateConstructorUsedError;

  /// Serializes this PerformanceMonthlyReportData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PerformanceMonthlyReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PerformanceMonthlyReportDataCopyWith<PerformanceMonthlyReportData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PerformanceMonthlyReportDataCopyWith<$Res> {
  factory $PerformanceMonthlyReportDataCopyWith(
          PerformanceMonthlyReportData value,
          $Res Function(PerformanceMonthlyReportData) then) =
      _$PerformanceMonthlyReportDataCopyWithImpl<$Res,
          PerformanceMonthlyReportData>;
  @useResult
  $Res call(
      {String? month,
      int clicks,
      int conversions,
      double conversionRate,
      double reward,
      double earningsPerClick});
}

/// @nodoc
class _$PerformanceMonthlyReportDataCopyWithImpl<$Res,
        $Val extends PerformanceMonthlyReportData>
    implements $PerformanceMonthlyReportDataCopyWith<$Res> {
  _$PerformanceMonthlyReportDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PerformanceMonthlyReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? month = freezed,
    Object? clicks = null,
    Object? conversions = null,
    Object? conversionRate = null,
    Object? reward = null,
    Object? earningsPerClick = null,
  }) {
    return _then(_value.copyWith(
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
      conversionRate: null == conversionRate
          ? _value.conversionRate
          : conversionRate // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      earningsPerClick: null == earningsPerClick
          ? _value.earningsPerClick
          : earningsPerClick // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PerformanceMonthlyReportDataImplCopyWith<$Res>
    implements $PerformanceMonthlyReportDataCopyWith<$Res> {
  factory _$$PerformanceMonthlyReportDataImplCopyWith(
          _$PerformanceMonthlyReportDataImpl value,
          $Res Function(_$PerformanceMonthlyReportDataImpl) then) =
      __$$PerformanceMonthlyReportDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? month,
      int clicks,
      int conversions,
      double conversionRate,
      double reward,
      double earningsPerClick});
}

/// @nodoc
class __$$PerformanceMonthlyReportDataImplCopyWithImpl<$Res>
    extends _$PerformanceMonthlyReportDataCopyWithImpl<$Res,
        _$PerformanceMonthlyReportDataImpl>
    implements _$$PerformanceMonthlyReportDataImplCopyWith<$Res> {
  __$$PerformanceMonthlyReportDataImplCopyWithImpl(
      _$PerformanceMonthlyReportDataImpl _value,
      $Res Function(_$PerformanceMonthlyReportDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PerformanceMonthlyReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? month = freezed,
    Object? clicks = null,
    Object? conversions = null,
    Object? conversionRate = null,
    Object? reward = null,
    Object? earningsPerClick = null,
  }) {
    return _then(_$PerformanceMonthlyReportDataImpl(
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as int,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as int,
      conversionRate: null == conversionRate
          ? _value.conversionRate
          : conversionRate // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      earningsPerClick: null == earningsPerClick
          ? _value.earningsPerClick
          : earningsPerClick // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PerformanceMonthlyReportDataImpl
    implements _PerformanceMonthlyReportData {
  _$PerformanceMonthlyReportDataImpl(
      {this.month,
      this.clicks = 0,
      this.conversions = 0,
      this.conversionRate = 0,
      this.reward = 0,
      this.earningsPerClick = 0});

  factory _$PerformanceMonthlyReportDataImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$PerformanceMonthlyReportDataImplFromJson(json);

  @override
  final String? month;
  @override
  @JsonKey()
  final int clicks;
  @override
  @JsonKey()
  final int conversions;
  @override
  @JsonKey()
  final double conversionRate;
  @override
  @JsonKey()
  final double reward;
  @override
  @JsonKey()
  final double earningsPerClick;

  @override
  String toString() {
    return 'PerformanceMonthlyReportData(month: $month, clicks: $clicks, conversions: $conversions, conversionRate: $conversionRate, reward: $reward, earningsPerClick: $earningsPerClick)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceMonthlyReportDataImpl &&
            (identical(other.month, month) || other.month == month) &&
            (identical(other.clicks, clicks) || other.clicks == clicks) &&
            (identical(other.conversions, conversions) ||
                other.conversions == conversions) &&
            (identical(other.conversionRate, conversionRate) ||
                other.conversionRate == conversionRate) &&
            (identical(other.reward, reward) || other.reward == reward) &&
            (identical(other.earningsPerClick, earningsPerClick) ||
                other.earningsPerClick == earningsPerClick));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, month, clicks, conversions,
      conversionRate, reward, earningsPerClick);

  /// Create a copy of PerformanceMonthlyReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PerformanceMonthlyReportDataImplCopyWith<
          _$PerformanceMonthlyReportDataImpl>
      get copyWith => __$$PerformanceMonthlyReportDataImplCopyWithImpl<
          _$PerformanceMonthlyReportDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PerformanceMonthlyReportDataImplToJson(
      this,
    );
  }
}

abstract class _PerformanceMonthlyReportData
    implements PerformanceMonthlyReportData {
  factory _PerformanceMonthlyReportData(
      {final String? month,
      final int clicks,
      final int conversions,
      final double conversionRate,
      final double reward,
      final double earningsPerClick}) = _$PerformanceMonthlyReportDataImpl;

  factory _PerformanceMonthlyReportData.fromJson(Map<String, dynamic> json) =
      _$PerformanceMonthlyReportDataImpl.fromJson;

  @override
  String? get month;
  @override
  int get clicks;
  @override
  int get conversions;
  @override
  double get conversionRate;
  @override
  double get reward;
  @override
  double get earningsPerClick;

  /// Create a copy of PerformanceMonthlyReportData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PerformanceMonthlyReportDataImplCopyWith<
          _$PerformanceMonthlyReportDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
