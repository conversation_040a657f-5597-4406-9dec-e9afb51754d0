// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invoice_detail_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

InvoiceDetailResponse _$InvoiceDetailResponseFromJson(
    Map<String, dynamic> json) {
  return _InvoiceDetailResponse.fromJson(json);
}

/// @nodoc
mixin _$InvoiceDetailResponse {
  String get invoiceId => throw _privateConstructorUsedError;
  double get totalReward => throw _privateConstructorUsedError;
  List<InvoiceCampaignReward> get rewards => throw _privateConstructorUsedError;

  /// Serializes this InvoiceDetailResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InvoiceDetailResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InvoiceDetailResponseCopyWith<InvoiceDetailResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvoiceDetailResponseCopyWith<$Res> {
  factory $InvoiceDetailResponseCopyWith(InvoiceDetailResponse value,
          $Res Function(InvoiceDetailResponse) then) =
      _$InvoiceDetailResponseCopyWithImpl<$Res, InvoiceDetailResponse>;
  @useResult
  $Res call(
      {String invoiceId,
      double totalReward,
      List<InvoiceCampaignReward> rewards});
}

/// @nodoc
class _$InvoiceDetailResponseCopyWithImpl<$Res,
        $Val extends InvoiceDetailResponse>
    implements $InvoiceDetailResponseCopyWith<$Res> {
  _$InvoiceDetailResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InvoiceDetailResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? invoiceId = null,
    Object? totalReward = null,
    Object? rewards = null,
  }) {
    return _then(_value.copyWith(
      invoiceId: null == invoiceId
          ? _value.invoiceId
          : invoiceId // ignore: cast_nullable_to_non_nullable
              as String,
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      rewards: null == rewards
          ? _value.rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as List<InvoiceCampaignReward>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InvoiceDetailResponseImplCopyWith<$Res>
    implements $InvoiceDetailResponseCopyWith<$Res> {
  factory _$$InvoiceDetailResponseImplCopyWith(
          _$InvoiceDetailResponseImpl value,
          $Res Function(_$InvoiceDetailResponseImpl) then) =
      __$$InvoiceDetailResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String invoiceId,
      double totalReward,
      List<InvoiceCampaignReward> rewards});
}

/// @nodoc
class __$$InvoiceDetailResponseImplCopyWithImpl<$Res>
    extends _$InvoiceDetailResponseCopyWithImpl<$Res,
        _$InvoiceDetailResponseImpl>
    implements _$$InvoiceDetailResponseImplCopyWith<$Res> {
  __$$InvoiceDetailResponseImplCopyWithImpl(_$InvoiceDetailResponseImpl _value,
      $Res Function(_$InvoiceDetailResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceDetailResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? invoiceId = null,
    Object? totalReward = null,
    Object? rewards = null,
  }) {
    return _then(_$InvoiceDetailResponseImpl(
      invoiceId: null == invoiceId
          ? _value.invoiceId
          : invoiceId // ignore: cast_nullable_to_non_nullable
              as String,
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      rewards: null == rewards
          ? _value._rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as List<InvoiceCampaignReward>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InvoiceDetailResponseImpl implements _InvoiceDetailResponse {
  _$InvoiceDetailResponseImpl(
      {this.invoiceId = '',
      this.totalReward = 0,
      final List<InvoiceCampaignReward> rewards = const []})
      : _rewards = rewards;

  factory _$InvoiceDetailResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$InvoiceDetailResponseImplFromJson(json);

  @override
  @JsonKey()
  final String invoiceId;
  @override
  @JsonKey()
  final double totalReward;
  final List<InvoiceCampaignReward> _rewards;
  @override
  @JsonKey()
  List<InvoiceCampaignReward> get rewards {
    if (_rewards is EqualUnmodifiableListView) return _rewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_rewards);
  }

  @override
  String toString() {
    return 'InvoiceDetailResponse(invoiceId: $invoiceId, totalReward: $totalReward, rewards: $rewards)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvoiceDetailResponseImpl &&
            (identical(other.invoiceId, invoiceId) ||
                other.invoiceId == invoiceId) &&
            (identical(other.totalReward, totalReward) ||
                other.totalReward == totalReward) &&
            const DeepCollectionEquality().equals(other._rewards, _rewards));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, invoiceId, totalReward,
      const DeepCollectionEquality().hash(_rewards));

  /// Create a copy of InvoiceDetailResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InvoiceDetailResponseImplCopyWith<_$InvoiceDetailResponseImpl>
      get copyWith => __$$InvoiceDetailResponseImplCopyWithImpl<
          _$InvoiceDetailResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InvoiceDetailResponseImplToJson(
      this,
    );
  }
}

abstract class _InvoiceDetailResponse implements InvoiceDetailResponse {
  factory _InvoiceDetailResponse(
      {final String invoiceId,
      final double totalReward,
      final List<InvoiceCampaignReward> rewards}) = _$InvoiceDetailResponseImpl;

  factory _InvoiceDetailResponse.fromJson(Map<String, dynamic> json) =
      _$InvoiceDetailResponseImpl.fromJson;

  @override
  String get invoiceId;
  @override
  double get totalReward;
  @override
  List<InvoiceCampaignReward> get rewards;

  /// Create a copy of InvoiceDetailResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InvoiceDetailResponseImplCopyWith<_$InvoiceDetailResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

InvoiceCampaignReward _$InvoiceCampaignRewardFromJson(
    Map<String, dynamic> json) {
  return _InvoiceCampaignReward.fromJson(json);
}

/// @nodoc
mixin _$InvoiceCampaignReward {
  int get campaignId => throw _privateConstructorUsedError;
  String get campaignName => throw _privateConstructorUsedError;
  String get rewardMonth => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;

  /// Serializes this InvoiceCampaignReward to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InvoiceCampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InvoiceCampaignRewardCopyWith<InvoiceCampaignReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvoiceCampaignRewardCopyWith<$Res> {
  factory $InvoiceCampaignRewardCopyWith(InvoiceCampaignReward value,
          $Res Function(InvoiceCampaignReward) then) =
      _$InvoiceCampaignRewardCopyWithImpl<$Res, InvoiceCampaignReward>;
  @useResult
  $Res call(
      {int campaignId,
      String campaignName,
      String rewardMonth,
      double amount,
      int quantity});
}

/// @nodoc
class _$InvoiceCampaignRewardCopyWithImpl<$Res,
        $Val extends InvoiceCampaignReward>
    implements $InvoiceCampaignRewardCopyWith<$Res> {
  _$InvoiceCampaignRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InvoiceCampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignId = null,
    Object? campaignName = null,
    Object? rewardMonth = null,
    Object? amount = null,
    Object? quantity = null,
  }) {
    return _then(_value.copyWith(
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      rewardMonth: null == rewardMonth
          ? _value.rewardMonth
          : rewardMonth // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InvoiceCampaignRewardImplCopyWith<$Res>
    implements $InvoiceCampaignRewardCopyWith<$Res> {
  factory _$$InvoiceCampaignRewardImplCopyWith(
          _$InvoiceCampaignRewardImpl value,
          $Res Function(_$InvoiceCampaignRewardImpl) then) =
      __$$InvoiceCampaignRewardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int campaignId,
      String campaignName,
      String rewardMonth,
      double amount,
      int quantity});
}

/// @nodoc
class __$$InvoiceCampaignRewardImplCopyWithImpl<$Res>
    extends _$InvoiceCampaignRewardCopyWithImpl<$Res,
        _$InvoiceCampaignRewardImpl>
    implements _$$InvoiceCampaignRewardImplCopyWith<$Res> {
  __$$InvoiceCampaignRewardImplCopyWithImpl(_$InvoiceCampaignRewardImpl _value,
      $Res Function(_$InvoiceCampaignRewardImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceCampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignId = null,
    Object? campaignName = null,
    Object? rewardMonth = null,
    Object? amount = null,
    Object? quantity = null,
  }) {
    return _then(_$InvoiceCampaignRewardImpl(
      campaignId: null == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      rewardMonth: null == rewardMonth
          ? _value.rewardMonth
          : rewardMonth // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InvoiceCampaignRewardImpl implements _InvoiceCampaignReward {
  _$InvoiceCampaignRewardImpl(
      {this.campaignId = 0,
      this.campaignName = '',
      this.rewardMonth = '',
      this.amount = 0,
      this.quantity = 0});

  factory _$InvoiceCampaignRewardImpl.fromJson(Map<String, dynamic> json) =>
      _$$InvoiceCampaignRewardImplFromJson(json);

  @override
  @JsonKey()
  final int campaignId;
  @override
  @JsonKey()
  final String campaignName;
  @override
  @JsonKey()
  final String rewardMonth;
  @override
  @JsonKey()
  final double amount;
  @override
  @JsonKey()
  final int quantity;

  @override
  String toString() {
    return 'InvoiceCampaignReward(campaignId: $campaignId, campaignName: $campaignName, rewardMonth: $rewardMonth, amount: $amount, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvoiceCampaignRewardImpl &&
            (identical(other.campaignId, campaignId) ||
                other.campaignId == campaignId) &&
            (identical(other.campaignName, campaignName) ||
                other.campaignName == campaignName) &&
            (identical(other.rewardMonth, rewardMonth) ||
                other.rewardMonth == rewardMonth) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, campaignId, campaignName, rewardMonth, amount, quantity);

  /// Create a copy of InvoiceCampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InvoiceCampaignRewardImplCopyWith<_$InvoiceCampaignRewardImpl>
      get copyWith => __$$InvoiceCampaignRewardImplCopyWithImpl<
          _$InvoiceCampaignRewardImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InvoiceCampaignRewardImplToJson(
      this,
    );
  }
}

abstract class _InvoiceCampaignReward implements InvoiceCampaignReward {
  factory _InvoiceCampaignReward(
      {final int campaignId,
      final String campaignName,
      final String rewardMonth,
      final double amount,
      final int quantity}) = _$InvoiceCampaignRewardImpl;

  factory _InvoiceCampaignReward.fromJson(Map<String, dynamic> json) =
      _$InvoiceCampaignRewardImpl.fromJson;

  @override
  int get campaignId;
  @override
  String get campaignName;
  @override
  String get rewardMonth;
  @override
  double get amount;
  @override
  int get quantity;

  /// Create a copy of InvoiceCampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InvoiceCampaignRewardImplCopyWith<_$InvoiceCampaignRewardImpl>
      get copyWith => throw _privateConstructorUsedError;
}
