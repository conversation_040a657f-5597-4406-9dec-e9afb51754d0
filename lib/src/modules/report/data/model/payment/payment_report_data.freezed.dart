// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_report_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaymentReportData _$PaymentReportDataFromJson(Map<String, dynamic> json) {
  return _PaymentReportData.fromJson(json);
}

/// @nodoc
mixin _$PaymentReportData {
  bool get isCheck => throw _privateConstructorUsedError;
  String get invoiceNumber => throw _privateConstructorUsedError;
  DatePeriod? get rewardApprovedMonthPeriod =>
      throw _privateConstructorUsedError;
  double get totalAmount => throw _privateConstructorUsedError;
  double get vat => throw _privateConstructorUsedError;
  double get wht => throw _privateConstructorUsedError;
  double get paidAmount => throw _privateConstructorUsedError;
  String get paymentDate => throw _privateConstructorUsedError;

  /// Serializes this PaymentReportData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentReportDataCopyWith<PaymentReportData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentReportDataCopyWith<$Res> {
  factory $PaymentReportDataCopyWith(
          PaymentReportData value, $Res Function(PaymentReportData) then) =
      _$PaymentReportDataCopyWithImpl<$Res, PaymentReportData>;
  @useResult
  $Res call(
      {bool isCheck,
      String invoiceNumber,
      DatePeriod? rewardApprovedMonthPeriod,
      double totalAmount,
      double vat,
      double wht,
      double paidAmount,
      String paymentDate});

  $DatePeriodCopyWith<$Res>? get rewardApprovedMonthPeriod;
}

/// @nodoc
class _$PaymentReportDataCopyWithImpl<$Res, $Val extends PaymentReportData>
    implements $PaymentReportDataCopyWith<$Res> {
  _$PaymentReportDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCheck = null,
    Object? invoiceNumber = null,
    Object? rewardApprovedMonthPeriod = freezed,
    Object? totalAmount = null,
    Object? vat = null,
    Object? wht = null,
    Object? paidAmount = null,
    Object? paymentDate = null,
  }) {
    return _then(_value.copyWith(
      isCheck: null == isCheck
          ? _value.isCheck
          : isCheck // ignore: cast_nullable_to_non_nullable
              as bool,
      invoiceNumber: null == invoiceNumber
          ? _value.invoiceNumber
          : invoiceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      rewardApprovedMonthPeriod: freezed == rewardApprovedMonthPeriod
          ? _value.rewardApprovedMonthPeriod
          : rewardApprovedMonthPeriod // ignore: cast_nullable_to_non_nullable
              as DatePeriod?,
      totalAmount: null == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double,
      vat: null == vat
          ? _value.vat
          : vat // ignore: cast_nullable_to_non_nullable
              as double,
      wht: null == wht
          ? _value.wht
          : wht // ignore: cast_nullable_to_non_nullable
              as double,
      paidAmount: null == paidAmount
          ? _value.paidAmount
          : paidAmount // ignore: cast_nullable_to_non_nullable
              as double,
      paymentDate: null == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of PaymentReportData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DatePeriodCopyWith<$Res>? get rewardApprovedMonthPeriod {
    if (_value.rewardApprovedMonthPeriod == null) {
      return null;
    }

    return $DatePeriodCopyWith<$Res>(_value.rewardApprovedMonthPeriod!,
        (value) {
      return _then(_value.copyWith(rewardApprovedMonthPeriod: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PaymentReportDataImplCopyWith<$Res>
    implements $PaymentReportDataCopyWith<$Res> {
  factory _$$PaymentReportDataImplCopyWith(_$PaymentReportDataImpl value,
          $Res Function(_$PaymentReportDataImpl) then) =
      __$$PaymentReportDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isCheck,
      String invoiceNumber,
      DatePeriod? rewardApprovedMonthPeriod,
      double totalAmount,
      double vat,
      double wht,
      double paidAmount,
      String paymentDate});

  @override
  $DatePeriodCopyWith<$Res>? get rewardApprovedMonthPeriod;
}

/// @nodoc
class __$$PaymentReportDataImplCopyWithImpl<$Res>
    extends _$PaymentReportDataCopyWithImpl<$Res, _$PaymentReportDataImpl>
    implements _$$PaymentReportDataImplCopyWith<$Res> {
  __$$PaymentReportDataImplCopyWithImpl(_$PaymentReportDataImpl _value,
      $Res Function(_$PaymentReportDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCheck = null,
    Object? invoiceNumber = null,
    Object? rewardApprovedMonthPeriod = freezed,
    Object? totalAmount = null,
    Object? vat = null,
    Object? wht = null,
    Object? paidAmount = null,
    Object? paymentDate = null,
  }) {
    return _then(_$PaymentReportDataImpl(
      isCheck: null == isCheck
          ? _value.isCheck
          : isCheck // ignore: cast_nullable_to_non_nullable
              as bool,
      invoiceNumber: null == invoiceNumber
          ? _value.invoiceNumber
          : invoiceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      rewardApprovedMonthPeriod: freezed == rewardApprovedMonthPeriod
          ? _value.rewardApprovedMonthPeriod
          : rewardApprovedMonthPeriod // ignore: cast_nullable_to_non_nullable
              as DatePeriod?,
      totalAmount: null == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double,
      vat: null == vat
          ? _value.vat
          : vat // ignore: cast_nullable_to_non_nullable
              as double,
      wht: null == wht
          ? _value.wht
          : wht // ignore: cast_nullable_to_non_nullable
              as double,
      paidAmount: null == paidAmount
          ? _value.paidAmount
          : paidAmount // ignore: cast_nullable_to_non_nullable
              as double,
      paymentDate: null == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentReportDataImpl implements _PaymentReportData {
  _$PaymentReportDataImpl(
      {this.isCheck = false,
      this.invoiceNumber = '',
      this.rewardApprovedMonthPeriod,
      this.totalAmount = 0,
      this.vat = 0,
      this.wht = 0,
      this.paidAmount = 0,
      this.paymentDate = ''});

  factory _$PaymentReportDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentReportDataImplFromJson(json);

  @override
  @JsonKey()
  final bool isCheck;
  @override
  @JsonKey()
  final String invoiceNumber;
  @override
  final DatePeriod? rewardApprovedMonthPeriod;
  @override
  @JsonKey()
  final double totalAmount;
  @override
  @JsonKey()
  final double vat;
  @override
  @JsonKey()
  final double wht;
  @override
  @JsonKey()
  final double paidAmount;
  @override
  @JsonKey()
  final String paymentDate;

  @override
  String toString() {
    return 'PaymentReportData(isCheck: $isCheck, invoiceNumber: $invoiceNumber, rewardApprovedMonthPeriod: $rewardApprovedMonthPeriod, totalAmount: $totalAmount, vat: $vat, wht: $wht, paidAmount: $paidAmount, paymentDate: $paymentDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentReportDataImpl &&
            (identical(other.isCheck, isCheck) || other.isCheck == isCheck) &&
            (identical(other.invoiceNumber, invoiceNumber) ||
                other.invoiceNumber == invoiceNumber) &&
            (identical(other.rewardApprovedMonthPeriod,
                    rewardApprovedMonthPeriod) ||
                other.rewardApprovedMonthPeriod == rewardApprovedMonthPeriod) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(other.vat, vat) || other.vat == vat) &&
            (identical(other.wht, wht) || other.wht == wht) &&
            (identical(other.paidAmount, paidAmount) ||
                other.paidAmount == paidAmount) &&
            (identical(other.paymentDate, paymentDate) ||
                other.paymentDate == paymentDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isCheck,
      invoiceNumber,
      rewardApprovedMonthPeriod,
      totalAmount,
      vat,
      wht,
      paidAmount,
      paymentDate);

  /// Create a copy of PaymentReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentReportDataImplCopyWith<_$PaymentReportDataImpl> get copyWith =>
      __$$PaymentReportDataImplCopyWithImpl<_$PaymentReportDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentReportDataImplToJson(
      this,
    );
  }
}

abstract class _PaymentReportData implements PaymentReportData {
  factory _PaymentReportData(
      {final bool isCheck,
      final String invoiceNumber,
      final DatePeriod? rewardApprovedMonthPeriod,
      final double totalAmount,
      final double vat,
      final double wht,
      final double paidAmount,
      final String paymentDate}) = _$PaymentReportDataImpl;

  factory _PaymentReportData.fromJson(Map<String, dynamic> json) =
      _$PaymentReportDataImpl.fromJson;

  @override
  bool get isCheck;
  @override
  String get invoiceNumber;
  @override
  DatePeriod? get rewardApprovedMonthPeriod;
  @override
  double get totalAmount;
  @override
  double get vat;
  @override
  double get wht;
  @override
  double get paidAmount;
  @override
  String get paymentDate;

  /// Create a copy of PaymentReportData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentReportDataImplCopyWith<_$PaymentReportDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
