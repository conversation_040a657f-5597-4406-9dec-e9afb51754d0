// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice_detail_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InvoiceDetailResponseImpl _$$InvoiceDetailResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$InvoiceDetailResponseImpl(
      invoiceId: json['invoiceId'] as String? ?? '',
      totalReward: (json['totalReward'] as num?)?.toDouble() ?? 0,
      rewards: (json['rewards'] as List<dynamic>?)
              ?.map((e) =>
                  InvoiceCampaignReward.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$InvoiceDetailResponseImplToJson(
        _$InvoiceDetailResponseImpl instance) =>
    <String, dynamic>{
      'invoiceId': instance.invoiceId,
      'totalReward': instance.totalReward,
      'rewards': instance.rewards,
    };

_$InvoiceCampaignRewardImpl _$$InvoiceCampaignRewardImplFromJson(
        Map<String, dynamic> json) =>
    _$InvoiceCampaignRewardImpl(
      campaignId: (json['campaignId'] as num?)?.toInt() ?? 0,
      campaignName: json['campaignName'] as String? ?? '',
      rewardMonth: json['rewardMonth'] as String? ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0,
      quantity: (json['quantity'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$InvoiceCampaignRewardImplToJson(
        _$InvoiceCampaignRewardImpl instance) =>
    <String, dynamic>{
      'campaignId': instance.campaignId,
      'campaignName': instance.campaignName,
      'rewardMonth': instance.rewardMonth,
      'amount': instance.amount,
      'quantity': instance.quantity,
    };
