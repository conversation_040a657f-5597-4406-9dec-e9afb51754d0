// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_process_summary.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaymentProcessSummary _$PaymentProcessSummaryFromJson(
    Map<String, dynamic> json) {
  return _PaymentProcessSummary.fromJson(json);
}

/// @nodoc
mixin _$PaymentProcessSummary {
  double get totalReward => throw _privateConstructorUsedError;
  List<CampaignReward> get content => throw _privateConstructorUsedError;

  /// Serializes this PaymentProcessSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentProcessSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentProcessSummaryCopyWith<PaymentProcessSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentProcessSummaryCopyWith<$Res> {
  factory $PaymentProcessSummaryCopyWith(PaymentProcessSummary value,
          $Res Function(PaymentProcessSummary) then) =
      _$PaymentProcessSummaryCopyWithImpl<$Res, PaymentProcessSummary>;
  @useResult
  $Res call({double totalReward, List<CampaignReward> content});
}

/// @nodoc
class _$PaymentProcessSummaryCopyWithImpl<$Res,
        $Val extends PaymentProcessSummary>
    implements $PaymentProcessSummaryCopyWith<$Res> {
  _$PaymentProcessSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentProcessSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalReward = null,
    Object? content = null,
  }) {
    return _then(_value.copyWith(
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as List<CampaignReward>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentProcessSummaryImplCopyWith<$Res>
    implements $PaymentProcessSummaryCopyWith<$Res> {
  factory _$$PaymentProcessSummaryImplCopyWith(
          _$PaymentProcessSummaryImpl value,
          $Res Function(_$PaymentProcessSummaryImpl) then) =
      __$$PaymentProcessSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double totalReward, List<CampaignReward> content});
}

/// @nodoc
class __$$PaymentProcessSummaryImplCopyWithImpl<$Res>
    extends _$PaymentProcessSummaryCopyWithImpl<$Res,
        _$PaymentProcessSummaryImpl>
    implements _$$PaymentProcessSummaryImplCopyWith<$Res> {
  __$$PaymentProcessSummaryImplCopyWithImpl(_$PaymentProcessSummaryImpl _value,
      $Res Function(_$PaymentProcessSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentProcessSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalReward = null,
    Object? content = null,
  }) {
    return _then(_$PaymentProcessSummaryImpl(
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      content: null == content
          ? _value._content
          : content // ignore: cast_nullable_to_non_nullable
              as List<CampaignReward>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentProcessSummaryImpl implements _PaymentProcessSummary {
  _$PaymentProcessSummaryImpl(
      {this.totalReward = 0, final List<CampaignReward> content = const []})
      : _content = content;

  factory _$PaymentProcessSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentProcessSummaryImplFromJson(json);

  @override
  @JsonKey()
  final double totalReward;
  final List<CampaignReward> _content;
  @override
  @JsonKey()
  List<CampaignReward> get content {
    if (_content is EqualUnmodifiableListView) return _content;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_content);
  }

  @override
  String toString() {
    return 'PaymentProcessSummary(totalReward: $totalReward, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentProcessSummaryImpl &&
            (identical(other.totalReward, totalReward) ||
                other.totalReward == totalReward) &&
            const DeepCollectionEquality().equals(other._content, _content));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, totalReward, const DeepCollectionEquality().hash(_content));

  /// Create a copy of PaymentProcessSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentProcessSummaryImplCopyWith<_$PaymentProcessSummaryImpl>
      get copyWith => __$$PaymentProcessSummaryImplCopyWithImpl<
          _$PaymentProcessSummaryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentProcessSummaryImplToJson(
      this,
    );
  }
}

abstract class _PaymentProcessSummary implements PaymentProcessSummary {
  factory _PaymentProcessSummary(
      {final double totalReward,
      final List<CampaignReward> content}) = _$PaymentProcessSummaryImpl;

  factory _PaymentProcessSummary.fromJson(Map<String, dynamic> json) =
      _$PaymentProcessSummaryImpl.fromJson;

  @override
  double get totalReward;
  @override
  List<CampaignReward> get content;

  /// Create a copy of PaymentProcessSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentProcessSummaryImplCopyWith<_$PaymentProcessSummaryImpl>
      get copyWith => throw _privateConstructorUsedError;
}

PaymentInProcessState _$PaymentInProcessStateFromJson(
    Map<String, dynamic> json) {
  return _PaymentInProcessState.fromJson(json);
}

/// @nodoc
mixin _$PaymentInProcessState {
  double get totalReward => throw _privateConstructorUsedError;
  Content? get content => throw _privateConstructorUsedError;

  /// Serializes this PaymentInProcessState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentInProcessState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentInProcessStateCopyWith<PaymentInProcessState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentInProcessStateCopyWith<$Res> {
  factory $PaymentInProcessStateCopyWith(PaymentInProcessState value,
          $Res Function(PaymentInProcessState) then) =
      _$PaymentInProcessStateCopyWithImpl<$Res, PaymentInProcessState>;
  @useResult
  $Res call({double totalReward, Content? content});

  $ContentCopyWith<$Res>? get content;
}

/// @nodoc
class _$PaymentInProcessStateCopyWithImpl<$Res,
        $Val extends PaymentInProcessState>
    implements $PaymentInProcessStateCopyWith<$Res> {
  _$PaymentInProcessStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentInProcessState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalReward = null,
    Object? content = freezed,
  }) {
    return _then(_value.copyWith(
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as Content?,
    ) as $Val);
  }

  /// Create a copy of PaymentInProcessState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContentCopyWith<$Res>? get content {
    if (_value.content == null) {
      return null;
    }

    return $ContentCopyWith<$Res>(_value.content!, (value) {
      return _then(_value.copyWith(content: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PaymentInProcessStateImplCopyWith<$Res>
    implements $PaymentInProcessStateCopyWith<$Res> {
  factory _$$PaymentInProcessStateImplCopyWith(
          _$PaymentInProcessStateImpl value,
          $Res Function(_$PaymentInProcessStateImpl) then) =
      __$$PaymentInProcessStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double totalReward, Content? content});

  @override
  $ContentCopyWith<$Res>? get content;
}

/// @nodoc
class __$$PaymentInProcessStateImplCopyWithImpl<$Res>
    extends _$PaymentInProcessStateCopyWithImpl<$Res,
        _$PaymentInProcessStateImpl>
    implements _$$PaymentInProcessStateImplCopyWith<$Res> {
  __$$PaymentInProcessStateImplCopyWithImpl(_$PaymentInProcessStateImpl _value,
      $Res Function(_$PaymentInProcessStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentInProcessState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalReward = null,
    Object? content = freezed,
  }) {
    return _then(_$PaymentInProcessStateImpl(
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as Content?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentInProcessStateImpl implements _PaymentInProcessState {
  _$PaymentInProcessStateImpl({this.totalReward = 0, this.content});

  factory _$PaymentInProcessStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentInProcessStateImplFromJson(json);

  @override
  @JsonKey()
  final double totalReward;
  @override
  final Content? content;

  @override
  String toString() {
    return 'PaymentInProcessState(totalReward: $totalReward, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentInProcessStateImpl &&
            (identical(other.totalReward, totalReward) ||
                other.totalReward == totalReward) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalReward, content);

  /// Create a copy of PaymentInProcessState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentInProcessStateImplCopyWith<_$PaymentInProcessStateImpl>
      get copyWith => __$$PaymentInProcessStateImplCopyWithImpl<
          _$PaymentInProcessStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentInProcessStateImplToJson(
      this,
    );
  }
}

abstract class _PaymentInProcessState implements PaymentInProcessState {
  factory _PaymentInProcessState(
      {final double totalReward,
      final Content? content}) = _$PaymentInProcessStateImpl;

  factory _PaymentInProcessState.fromJson(Map<String, dynamic> json) =
      _$PaymentInProcessStateImpl.fromJson;

  @override
  double get totalReward;
  @override
  Content? get content;

  /// Create a copy of PaymentInProcessState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentInProcessStateImplCopyWith<_$PaymentInProcessStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

Content _$ContentFromJson(Map<String, dynamic> json) {
  return _Content.fromJson(json);
}

/// @nodoc
mixin _$Content {
  ProcessDetails? get rewardPaid => throw _privateConstructorUsedError;
  ProcessDetails? get rewardBeingProcessPayment =>
      throw _privateConstructorUsedError;

  /// Serializes this Content to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Content
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContentCopyWith<Content> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentCopyWith<$Res> {
  factory $ContentCopyWith(Content value, $Res Function(Content) then) =
      _$ContentCopyWithImpl<$Res, Content>;
  @useResult
  $Res call(
      {ProcessDetails? rewardPaid, ProcessDetails? rewardBeingProcessPayment});

  $ProcessDetailsCopyWith<$Res>? get rewardPaid;
  $ProcessDetailsCopyWith<$Res>? get rewardBeingProcessPayment;
}

/// @nodoc
class _$ContentCopyWithImpl<$Res, $Val extends Content>
    implements $ContentCopyWith<$Res> {
  _$ContentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Content
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardPaid = freezed,
    Object? rewardBeingProcessPayment = freezed,
  }) {
    return _then(_value.copyWith(
      rewardPaid: freezed == rewardPaid
          ? _value.rewardPaid
          : rewardPaid // ignore: cast_nullable_to_non_nullable
              as ProcessDetails?,
      rewardBeingProcessPayment: freezed == rewardBeingProcessPayment
          ? _value.rewardBeingProcessPayment
          : rewardBeingProcessPayment // ignore: cast_nullable_to_non_nullable
              as ProcessDetails?,
    ) as $Val);
  }

  /// Create a copy of Content
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProcessDetailsCopyWith<$Res>? get rewardPaid {
    if (_value.rewardPaid == null) {
      return null;
    }

    return $ProcessDetailsCopyWith<$Res>(_value.rewardPaid!, (value) {
      return _then(_value.copyWith(rewardPaid: value) as $Val);
    });
  }

  /// Create a copy of Content
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProcessDetailsCopyWith<$Res>? get rewardBeingProcessPayment {
    if (_value.rewardBeingProcessPayment == null) {
      return null;
    }

    return $ProcessDetailsCopyWith<$Res>(_value.rewardBeingProcessPayment!,
        (value) {
      return _then(_value.copyWith(rewardBeingProcessPayment: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ContentImplCopyWith<$Res> implements $ContentCopyWith<$Res> {
  factory _$$ContentImplCopyWith(
          _$ContentImpl value, $Res Function(_$ContentImpl) then) =
      __$$ContentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ProcessDetails? rewardPaid, ProcessDetails? rewardBeingProcessPayment});

  @override
  $ProcessDetailsCopyWith<$Res>? get rewardPaid;
  @override
  $ProcessDetailsCopyWith<$Res>? get rewardBeingProcessPayment;
}

/// @nodoc
class __$$ContentImplCopyWithImpl<$Res>
    extends _$ContentCopyWithImpl<$Res, _$ContentImpl>
    implements _$$ContentImplCopyWith<$Res> {
  __$$ContentImplCopyWithImpl(
      _$ContentImpl _value, $Res Function(_$ContentImpl) _then)
      : super(_value, _then);

  /// Create a copy of Content
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardPaid = freezed,
    Object? rewardBeingProcessPayment = freezed,
  }) {
    return _then(_$ContentImpl(
      rewardPaid: freezed == rewardPaid
          ? _value.rewardPaid
          : rewardPaid // ignore: cast_nullable_to_non_nullable
              as ProcessDetails?,
      rewardBeingProcessPayment: freezed == rewardBeingProcessPayment
          ? _value.rewardBeingProcessPayment
          : rewardBeingProcessPayment // ignore: cast_nullable_to_non_nullable
              as ProcessDetails?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentImpl implements _Content {
  _$ContentImpl({this.rewardPaid, this.rewardBeingProcessPayment});

  factory _$ContentImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentImplFromJson(json);

  @override
  final ProcessDetails? rewardPaid;
  @override
  final ProcessDetails? rewardBeingProcessPayment;

  @override
  String toString() {
    return 'Content(rewardPaid: $rewardPaid, rewardBeingProcessPayment: $rewardBeingProcessPayment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentImpl &&
            (identical(other.rewardPaid, rewardPaid) ||
                other.rewardPaid == rewardPaid) &&
            (identical(other.rewardBeingProcessPayment,
                    rewardBeingProcessPayment) ||
                other.rewardBeingProcessPayment == rewardBeingProcessPayment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, rewardPaid, rewardBeingProcessPayment);

  /// Create a copy of Content
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentImplCopyWith<_$ContentImpl> get copyWith =>
      __$$ContentImplCopyWithImpl<_$ContentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentImplToJson(
      this,
    );
  }
}

abstract class _Content implements Content {
  factory _Content(
      {final ProcessDetails? rewardPaid,
      final ProcessDetails? rewardBeingProcessPayment}) = _$ContentImpl;

  factory _Content.fromJson(Map<String, dynamic> json) = _$ContentImpl.fromJson;

  @override
  ProcessDetails? get rewardPaid;
  @override
  ProcessDetails? get rewardBeingProcessPayment;

  /// Create a copy of Content
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContentImplCopyWith<_$ContentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProcessDetails _$ProcessDetailsFromJson(Map<String, dynamic> json) {
  return _ProcessDetails.fromJson(json);
}

/// @nodoc
mixin _$ProcessDetails {
  double get totalReward => throw _privateConstructorUsedError;
  List<CampaignReward> get campaignRewards =>
      throw _privateConstructorUsedError;

  /// Serializes this ProcessDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProcessDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProcessDetailsCopyWith<ProcessDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProcessDetailsCopyWith<$Res> {
  factory $ProcessDetailsCopyWith(
          ProcessDetails value, $Res Function(ProcessDetails) then) =
      _$ProcessDetailsCopyWithImpl<$Res, ProcessDetails>;
  @useResult
  $Res call({double totalReward, List<CampaignReward> campaignRewards});
}

/// @nodoc
class _$ProcessDetailsCopyWithImpl<$Res, $Val extends ProcessDetails>
    implements $ProcessDetailsCopyWith<$Res> {
  _$ProcessDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProcessDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalReward = null,
    Object? campaignRewards = null,
  }) {
    return _then(_value.copyWith(
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      campaignRewards: null == campaignRewards
          ? _value.campaignRewards
          : campaignRewards // ignore: cast_nullable_to_non_nullable
              as List<CampaignReward>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProcessDetailsImplCopyWith<$Res>
    implements $ProcessDetailsCopyWith<$Res> {
  factory _$$ProcessDetailsImplCopyWith(_$ProcessDetailsImpl value,
          $Res Function(_$ProcessDetailsImpl) then) =
      __$$ProcessDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double totalReward, List<CampaignReward> campaignRewards});
}

/// @nodoc
class __$$ProcessDetailsImplCopyWithImpl<$Res>
    extends _$ProcessDetailsCopyWithImpl<$Res, _$ProcessDetailsImpl>
    implements _$$ProcessDetailsImplCopyWith<$Res> {
  __$$ProcessDetailsImplCopyWithImpl(
      _$ProcessDetailsImpl _value, $Res Function(_$ProcessDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProcessDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalReward = null,
    Object? campaignRewards = null,
  }) {
    return _then(_$ProcessDetailsImpl(
      totalReward: null == totalReward
          ? _value.totalReward
          : totalReward // ignore: cast_nullable_to_non_nullable
              as double,
      campaignRewards: null == campaignRewards
          ? _value._campaignRewards
          : campaignRewards // ignore: cast_nullable_to_non_nullable
              as List<CampaignReward>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProcessDetailsImpl implements _ProcessDetails {
  _$ProcessDetailsImpl(
      {this.totalReward = 0,
      final List<CampaignReward> campaignRewards = const []})
      : _campaignRewards = campaignRewards;

  factory _$ProcessDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProcessDetailsImplFromJson(json);

  @override
  @JsonKey()
  final double totalReward;
  final List<CampaignReward> _campaignRewards;
  @override
  @JsonKey()
  List<CampaignReward> get campaignRewards {
    if (_campaignRewards is EqualUnmodifiableListView) return _campaignRewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaignRewards);
  }

  @override
  String toString() {
    return 'ProcessDetails(totalReward: $totalReward, campaignRewards: $campaignRewards)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProcessDetailsImpl &&
            (identical(other.totalReward, totalReward) ||
                other.totalReward == totalReward) &&
            const DeepCollectionEquality()
                .equals(other._campaignRewards, _campaignRewards));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalReward,
      const DeepCollectionEquality().hash(_campaignRewards));

  /// Create a copy of ProcessDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProcessDetailsImplCopyWith<_$ProcessDetailsImpl> get copyWith =>
      __$$ProcessDetailsImplCopyWithImpl<_$ProcessDetailsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProcessDetailsImplToJson(
      this,
    );
  }
}

abstract class _ProcessDetails implements ProcessDetails {
  factory _ProcessDetails(
      {final double totalReward,
      final List<CampaignReward> campaignRewards}) = _$ProcessDetailsImpl;

  factory _ProcessDetails.fromJson(Map<String, dynamic> json) =
      _$ProcessDetailsImpl.fromJson;

  @override
  double get totalReward;
  @override
  List<CampaignReward> get campaignRewards;

  /// Create a copy of ProcessDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProcessDetailsImplCopyWith<_$ProcessDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CampaignReward _$CampaignRewardFromJson(Map<String, dynamic> json) {
  return _CampaignReward.fromJson(json);
}

/// @nodoc
mixin _$CampaignReward {
  String get campaignName => throw _privateConstructorUsedError;
  double get reward => throw _privateConstructorUsedError;
  String get rewardMonth => throw _privateConstructorUsedError;

  /// Serializes this CampaignReward to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignRewardCopyWith<CampaignReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignRewardCopyWith<$Res> {
  factory $CampaignRewardCopyWith(
          CampaignReward value, $Res Function(CampaignReward) then) =
      _$CampaignRewardCopyWithImpl<$Res, CampaignReward>;
  @useResult
  $Res call({String campaignName, double reward, String rewardMonth});
}

/// @nodoc
class _$CampaignRewardCopyWithImpl<$Res, $Val extends CampaignReward>
    implements $CampaignRewardCopyWith<$Res> {
  _$CampaignRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignName = null,
    Object? reward = null,
    Object? rewardMonth = null,
  }) {
    return _then(_value.copyWith(
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      rewardMonth: null == rewardMonth
          ? _value.rewardMonth
          : rewardMonth // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignRewardImplCopyWith<$Res>
    implements $CampaignRewardCopyWith<$Res> {
  factory _$$CampaignRewardImplCopyWith(_$CampaignRewardImpl value,
          $Res Function(_$CampaignRewardImpl) then) =
      __$$CampaignRewardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String campaignName, double reward, String rewardMonth});
}

/// @nodoc
class __$$CampaignRewardImplCopyWithImpl<$Res>
    extends _$CampaignRewardCopyWithImpl<$Res, _$CampaignRewardImpl>
    implements _$$CampaignRewardImplCopyWith<$Res> {
  __$$CampaignRewardImplCopyWithImpl(
      _$CampaignRewardImpl _value, $Res Function(_$CampaignRewardImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaignName = null,
    Object? reward = null,
    Object? rewardMonth = null,
  }) {
    return _then(_$CampaignRewardImpl(
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
      rewardMonth: null == rewardMonth
          ? _value.rewardMonth
          : rewardMonth // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignRewardImpl implements _CampaignReward {
  _$CampaignRewardImpl(
      {this.campaignName = '', this.reward = 0, this.rewardMonth = ''});

  factory _$CampaignRewardImpl.fromJson(Map<String, dynamic> json) =>
      _$$CampaignRewardImplFromJson(json);

  @override
  @JsonKey()
  final String campaignName;
  @override
  @JsonKey()
  final double reward;
  @override
  @JsonKey()
  final String rewardMonth;

  @override
  String toString() {
    return 'CampaignReward(campaignName: $campaignName, reward: $reward, rewardMonth: $rewardMonth)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignRewardImpl &&
            (identical(other.campaignName, campaignName) ||
                other.campaignName == campaignName) &&
            (identical(other.reward, reward) || other.reward == reward) &&
            (identical(other.rewardMonth, rewardMonth) ||
                other.rewardMonth == rewardMonth));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, campaignName, reward, rewardMonth);

  /// Create a copy of CampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignRewardImplCopyWith<_$CampaignRewardImpl> get copyWith =>
      __$$CampaignRewardImplCopyWithImpl<_$CampaignRewardImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignRewardImplToJson(
      this,
    );
  }
}

abstract class _CampaignReward implements CampaignReward {
  factory _CampaignReward(
      {final String campaignName,
      final double reward,
      final String rewardMonth}) = _$CampaignRewardImpl;

  factory _CampaignReward.fromJson(Map<String, dynamic> json) =
      _$CampaignRewardImpl.fromJson;

  @override
  String get campaignName;
  @override
  double get reward;
  @override
  String get rewardMonth;

  /// Create a copy of CampaignReward
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignRewardImplCopyWith<_$CampaignRewardImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
