import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/shared/model/date_period.dart';

part 'payment_report_data.freezed.dart';
part 'payment_report_data.g.dart';

@freezed
class PaymentReportData with _$PaymentReportData {
  factory PaymentReportData({
    @Default(false) bool isCheck,
    @Default('') String invoiceNumber,
    DatePeriod? rewardApprovedMonthPeriod,
    @Default(0) double totalAmount,
    @Default(0) double vat,
    @Default(0) double wht,
    @Default(0) double paidAmount,
    @Default('') String paymentDate,
  }) = _PaymentReportData;

  factory PaymentReportData.fromJson(Map<String, Object?> json) =>
      _$PaymentReportDataFromJson(json);
}
