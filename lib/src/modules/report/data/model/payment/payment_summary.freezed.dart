// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_summary.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaymentSummary _$PaymentSummaryFromJson(Map<String, dynamic> json) {
  return _PaymentSummary.fromJson(json);
}

/// @nodoc
mixin _$PaymentSummary {
  double get lifetimeTotalPaidReward => throw _privateConstructorUsedError;
  double get availablePayment => throw _privateConstructorUsedError;
  double get rewardApproved => throw _privateConstructorUsedError;
  double get rewardProcessedPayment => throw _privateConstructorUsedError;
  double get rewardToBePaid => throw _privateConstructorUsedError;

  /// Serializes this PaymentSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentSummaryCopyWith<PaymentSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentSummaryCopyWith<$Res> {
  factory $PaymentSummaryCopyWith(
          PaymentSummary value, $Res Function(PaymentSummary) then) =
      _$PaymentSummaryCopyWithImpl<$Res, PaymentSummary>;
  @useResult
  $Res call(
      {double lifetimeTotalPaidReward,
      double availablePayment,
      double rewardApproved,
      double rewardProcessedPayment,
      double rewardToBePaid});
}

/// @nodoc
class _$PaymentSummaryCopyWithImpl<$Res, $Val extends PaymentSummary>
    implements $PaymentSummaryCopyWith<$Res> {
  _$PaymentSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lifetimeTotalPaidReward = null,
    Object? availablePayment = null,
    Object? rewardApproved = null,
    Object? rewardProcessedPayment = null,
    Object? rewardToBePaid = null,
  }) {
    return _then(_value.copyWith(
      lifetimeTotalPaidReward: null == lifetimeTotalPaidReward
          ? _value.lifetimeTotalPaidReward
          : lifetimeTotalPaidReward // ignore: cast_nullable_to_non_nullable
              as double,
      availablePayment: null == availablePayment
          ? _value.availablePayment
          : availablePayment // ignore: cast_nullable_to_non_nullable
              as double,
      rewardApproved: null == rewardApproved
          ? _value.rewardApproved
          : rewardApproved // ignore: cast_nullable_to_non_nullable
              as double,
      rewardProcessedPayment: null == rewardProcessedPayment
          ? _value.rewardProcessedPayment
          : rewardProcessedPayment // ignore: cast_nullable_to_non_nullable
              as double,
      rewardToBePaid: null == rewardToBePaid
          ? _value.rewardToBePaid
          : rewardToBePaid // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentSummaryImplCopyWith<$Res>
    implements $PaymentSummaryCopyWith<$Res> {
  factory _$$PaymentSummaryImplCopyWith(_$PaymentSummaryImpl value,
          $Res Function(_$PaymentSummaryImpl) then) =
      __$$PaymentSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double lifetimeTotalPaidReward,
      double availablePayment,
      double rewardApproved,
      double rewardProcessedPayment,
      double rewardToBePaid});
}

/// @nodoc
class __$$PaymentSummaryImplCopyWithImpl<$Res>
    extends _$PaymentSummaryCopyWithImpl<$Res, _$PaymentSummaryImpl>
    implements _$$PaymentSummaryImplCopyWith<$Res> {
  __$$PaymentSummaryImplCopyWithImpl(
      _$PaymentSummaryImpl _value, $Res Function(_$PaymentSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lifetimeTotalPaidReward = null,
    Object? availablePayment = null,
    Object? rewardApproved = null,
    Object? rewardProcessedPayment = null,
    Object? rewardToBePaid = null,
  }) {
    return _then(_$PaymentSummaryImpl(
      lifetimeTotalPaidReward: null == lifetimeTotalPaidReward
          ? _value.lifetimeTotalPaidReward
          : lifetimeTotalPaidReward // ignore: cast_nullable_to_non_nullable
              as double,
      availablePayment: null == availablePayment
          ? _value.availablePayment
          : availablePayment // ignore: cast_nullable_to_non_nullable
              as double,
      rewardApproved: null == rewardApproved
          ? _value.rewardApproved
          : rewardApproved // ignore: cast_nullable_to_non_nullable
              as double,
      rewardProcessedPayment: null == rewardProcessedPayment
          ? _value.rewardProcessedPayment
          : rewardProcessedPayment // ignore: cast_nullable_to_non_nullable
              as double,
      rewardToBePaid: null == rewardToBePaid
          ? _value.rewardToBePaid
          : rewardToBePaid // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentSummaryImpl implements _PaymentSummary {
  _$PaymentSummaryImpl(
      {this.lifetimeTotalPaidReward = 0,
      this.availablePayment = 0,
      this.rewardApproved = 0,
      this.rewardProcessedPayment = 0,
      this.rewardToBePaid = 0});

  factory _$PaymentSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentSummaryImplFromJson(json);

  @override
  @JsonKey()
  final double lifetimeTotalPaidReward;
  @override
  @JsonKey()
  final double availablePayment;
  @override
  @JsonKey()
  final double rewardApproved;
  @override
  @JsonKey()
  final double rewardProcessedPayment;
  @override
  @JsonKey()
  final double rewardToBePaid;

  @override
  String toString() {
    return 'PaymentSummary(lifetimeTotalPaidReward: $lifetimeTotalPaidReward, availablePayment: $availablePayment, rewardApproved: $rewardApproved, rewardProcessedPayment: $rewardProcessedPayment, rewardToBePaid: $rewardToBePaid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentSummaryImpl &&
            (identical(
                    other.lifetimeTotalPaidReward, lifetimeTotalPaidReward) ||
                other.lifetimeTotalPaidReward == lifetimeTotalPaidReward) &&
            (identical(other.availablePayment, availablePayment) ||
                other.availablePayment == availablePayment) &&
            (identical(other.rewardApproved, rewardApproved) ||
                other.rewardApproved == rewardApproved) &&
            (identical(other.rewardProcessedPayment, rewardProcessedPayment) ||
                other.rewardProcessedPayment == rewardProcessedPayment) &&
            (identical(other.rewardToBePaid, rewardToBePaid) ||
                other.rewardToBePaid == rewardToBePaid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lifetimeTotalPaidReward,
      availablePayment, rewardApproved, rewardProcessedPayment, rewardToBePaid);

  /// Create a copy of PaymentSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentSummaryImplCopyWith<_$PaymentSummaryImpl> get copyWith =>
      __$$PaymentSummaryImplCopyWithImpl<_$PaymentSummaryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentSummaryImplToJson(
      this,
    );
  }
}

abstract class _PaymentSummary implements PaymentSummary {
  factory _PaymentSummary(
      {final double lifetimeTotalPaidReward,
      final double availablePayment,
      final double rewardApproved,
      final double rewardProcessedPayment,
      final double rewardToBePaid}) = _$PaymentSummaryImpl;

  factory _PaymentSummary.fromJson(Map<String, dynamic> json) =
      _$PaymentSummaryImpl.fromJson;

  @override
  double get lifetimeTotalPaidReward;
  @override
  double get availablePayment;
  @override
  double get rewardApproved;
  @override
  double get rewardProcessedPayment;
  @override
  double get rewardToBePaid;

  /// Create a copy of PaymentSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentSummaryImplCopyWith<_$PaymentSummaryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
