import 'package:freezed_annotation/freezed_annotation.dart';

part 'minimum_payment_details.freezed.dart';
part 'minimum_payment_details.g.dart';

@freezed
class MinimumPaymentDetails with _$MinimumPaymentDetails {
  factory MinimumPaymentDetails({
    @Default(0) double minimumAmount,
    @Default('MYR') String currency,
  }) = _MinimumPaymentDetails;

  factory MinimumPaymentDetails.fromJson(Map<String, Object?> json) => _$MinimumPaymentDetailsFromJson(json);
}
