// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'minimum_payment_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MinimumPaymentDetails _$MinimumPaymentDetailsFromJson(
    Map<String, dynamic> json) {
  return _MinimumPaymentDetails.fromJson(json);
}

/// @nodoc
mixin _$MinimumPaymentDetails {
  double get minimumAmount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;

  /// Serializes this MinimumPaymentDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MinimumPaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MinimumPaymentDetailsCopyWith<MinimumPaymentDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MinimumPaymentDetailsCopyWith<$Res> {
  factory $MinimumPaymentDetailsCopyWith(MinimumPaymentDetails value,
          $Res Function(MinimumPaymentDetails) then) =
      _$MinimumPaymentDetailsCopyWithImpl<$Res, MinimumPaymentDetails>;
  @useResult
  $Res call({double minimumAmount, String currency});
}

/// @nodoc
class _$MinimumPaymentDetailsCopyWithImpl<$Res,
        $Val extends MinimumPaymentDetails>
    implements $MinimumPaymentDetailsCopyWith<$Res> {
  _$MinimumPaymentDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MinimumPaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimumAmount = null,
    Object? currency = null,
  }) {
    return _then(_value.copyWith(
      minimumAmount: null == minimumAmount
          ? _value.minimumAmount
          : minimumAmount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MinimumPaymentDetailsImplCopyWith<$Res>
    implements $MinimumPaymentDetailsCopyWith<$Res> {
  factory _$$MinimumPaymentDetailsImplCopyWith(
          _$MinimumPaymentDetailsImpl value,
          $Res Function(_$MinimumPaymentDetailsImpl) then) =
      __$$MinimumPaymentDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double minimumAmount, String currency});
}

/// @nodoc
class __$$MinimumPaymentDetailsImplCopyWithImpl<$Res>
    extends _$MinimumPaymentDetailsCopyWithImpl<$Res,
        _$MinimumPaymentDetailsImpl>
    implements _$$MinimumPaymentDetailsImplCopyWith<$Res> {
  __$$MinimumPaymentDetailsImplCopyWithImpl(_$MinimumPaymentDetailsImpl _value,
      $Res Function(_$MinimumPaymentDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of MinimumPaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimumAmount = null,
    Object? currency = null,
  }) {
    return _then(_$MinimumPaymentDetailsImpl(
      minimumAmount: null == minimumAmount
          ? _value.minimumAmount
          : minimumAmount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MinimumPaymentDetailsImpl implements _MinimumPaymentDetails {
  _$MinimumPaymentDetailsImpl({this.minimumAmount = 0, this.currency = 'MYR'});

  factory _$MinimumPaymentDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$MinimumPaymentDetailsImplFromJson(json);

  @override
  @JsonKey()
  final double minimumAmount;
  @override
  @JsonKey()
  final String currency;

  @override
  String toString() {
    return 'MinimumPaymentDetails(minimumAmount: $minimumAmount, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MinimumPaymentDetailsImpl &&
            (identical(other.minimumAmount, minimumAmount) ||
                other.minimumAmount == minimumAmount) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, minimumAmount, currency);

  /// Create a copy of MinimumPaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MinimumPaymentDetailsImplCopyWith<_$MinimumPaymentDetailsImpl>
      get copyWith => __$$MinimumPaymentDetailsImplCopyWithImpl<
          _$MinimumPaymentDetailsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MinimumPaymentDetailsImplToJson(
      this,
    );
  }
}

abstract class _MinimumPaymentDetails implements MinimumPaymentDetails {
  factory _MinimumPaymentDetails(
      {final double minimumAmount,
      final String currency}) = _$MinimumPaymentDetailsImpl;

  factory _MinimumPaymentDetails.fromJson(Map<String, dynamic> json) =
      _$MinimumPaymentDetailsImpl.fromJson;

  @override
  double get minimumAmount;
  @override
  String get currency;

  /// Create a copy of MinimumPaymentDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MinimumPaymentDetailsImplCopyWith<_$MinimumPaymentDetailsImpl>
      get copyWith => throw _privateConstructorUsedError;
}
