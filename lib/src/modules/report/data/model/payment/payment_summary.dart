import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_summary.freezed.dart';
part 'payment_summary.g.dart';

@freezed
class PaymentSummary with _$PaymentSummary {
  factory PaymentSummary({
    @Default(0) double lifetimeTotalPaidReward,
    @Default(0) double availablePayment,
    @Default(0) double rewardApproved,
    @Default(0) double rewardProcessedPayment,
    @Default(0) double rewardToBePaid,
  }) = _PaymentSummary;

  factory PaymentSummary.fromJson(Map<String, Object?> json) =>
      _$PaymentSummaryFromJson(json);
}
