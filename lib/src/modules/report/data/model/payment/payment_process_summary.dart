// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_process_summary.freezed.dart';
part 'payment_process_summary.g.dart';

@freezed
class PaymentProcessSummary with _$PaymentProcessSummary {
  factory PaymentProcessSummary({
    @Default(0) double totalReward,
    @Default([]) List<CampaignReward> content,
  }) = _PaymentProcessSummary;

  factory PaymentProcessSummary.fromJson(Map<String, Object?> json) => _$PaymentProcessSummaryFromJson(json);
}

@freezed
class PaymentInProcessState with _$PaymentInProcessState {
  factory PaymentInProcessState({
    @Default(0) double totalReward,
    Content? content,
  }) = _PaymentInProcessState;

  factory PaymentInProcessState.fromJson(Map<String, Object?> json) => _$PaymentInProcessStateFromJson(json);
}

@freezed
class Content with _$Content {
  factory Content({
    ProcessDetails? rewardPaid,
    ProcessDetails? rewardBeingProcessPayment,
  }) = _Content;

  factory Content.fromJson(Map<String, dynamic> json) => _$ContentFromJson(json);
}

@freezed
class ProcessDetails with _$ProcessDetails {
  factory ProcessDetails({
    @Default(0) double totalReward,
    @Default([]) List<CampaignReward> campaignRewards,
  }) = _ProcessDetails;

  factory ProcessDetails.fromJson(Map<String, dynamic> json) => _$ProcessDetailsFromJson(json);
}

@freezed
class CampaignReward with _$CampaignReward {
  factory CampaignReward({
    @Default('') String campaignName,
    @Default(0) double reward,
    @Default('') String rewardMonth,
  }) = _CampaignReward;

  factory CampaignReward.fromJson(Map<String, dynamic> json) => _$CampaignRewardFromJson(json);
}

enum PaymentProcessType {
  REWARD_APPROVED('Reward approved'),
  REWARD_BEING_PROCESSED_PAYMENT('Payment in progress'),
  REWARD_TO_BE_PAID('Payment held until requirement met');

  final String title;
  const PaymentProcessType(this.title);
}
