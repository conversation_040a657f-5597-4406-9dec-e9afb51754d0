// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_process_summary.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentProcessSummaryImpl _$$PaymentProcessSummaryImplFromJson(
        Map<String, dynamic> json) =>
    _$PaymentProcessSummaryImpl(
      totalReward: (json['totalReward'] as num?)?.toDouble() ?? 0,
      content: (json['content'] as List<dynamic>?)
              ?.map((e) => CampaignReward.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$PaymentProcessSummaryImplToJson(
        _$PaymentProcessSummaryImpl instance) =>
    <String, dynamic>{
      'totalReward': instance.totalReward,
      'content': instance.content,
    };

_$PaymentInProcessStateImpl _$$PaymentInProcessStateImplFromJson(
        Map<String, dynamic> json) =>
    _$PaymentInProcessStateImpl(
      totalReward: (json['totalReward'] as num?)?.toDouble() ?? 0,
      content: json['content'] == null
          ? null
          : Content.fromJson(json['content'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$PaymentInProcessStateImplToJson(
        _$PaymentInProcessStateImpl instance) =>
    <String, dynamic>{
      'totalReward': instance.totalReward,
      'content': instance.content,
    };

_$ContentImpl _$$ContentImplFromJson(Map<String, dynamic> json) =>
    _$ContentImpl(
      rewardPaid: json['rewardPaid'] == null
          ? null
          : ProcessDetails.fromJson(json['rewardPaid'] as Map<String, dynamic>),
      rewardBeingProcessPayment: json['rewardBeingProcessPayment'] == null
          ? null
          : ProcessDetails.fromJson(
              json['rewardBeingProcessPayment'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ContentImplToJson(_$ContentImpl instance) =>
    <String, dynamic>{
      'rewardPaid': instance.rewardPaid,
      'rewardBeingProcessPayment': instance.rewardBeingProcessPayment,
    };

_$ProcessDetailsImpl _$$ProcessDetailsImplFromJson(Map<String, dynamic> json) =>
    _$ProcessDetailsImpl(
      totalReward: (json['totalReward'] as num?)?.toDouble() ?? 0,
      campaignRewards: (json['campaignRewards'] as List<dynamic>?)
              ?.map((e) => CampaignReward.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$ProcessDetailsImplToJson(
        _$ProcessDetailsImpl instance) =>
    <String, dynamic>{
      'totalReward': instance.totalReward,
      'campaignRewards': instance.campaignRewards,
    };

_$CampaignRewardImpl _$$CampaignRewardImplFromJson(Map<String, dynamic> json) =>
    _$CampaignRewardImpl(
      campaignName: json['campaignName'] as String? ?? '',
      reward: (json['reward'] as num?)?.toDouble() ?? 0,
      rewardMonth: json['rewardMonth'] as String? ?? '',
    );

Map<String, dynamic> _$$CampaignRewardImplToJson(
        _$CampaignRewardImpl instance) =>
    <String, dynamic>{
      'campaignName': instance.campaignName,
      'reward': instance.reward,
      'rewardMonth': instance.rewardMonth,
    };
