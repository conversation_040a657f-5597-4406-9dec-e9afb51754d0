// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_summary.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentSummaryImpl _$$PaymentSummaryImplFromJson(Map<String, dynamic> json) =>
    _$PaymentSummaryImpl(
      lifetimeTotalPaidReward:
          (json['lifetimeTotalPaidReward'] as num?)?.toDouble() ?? 0,
      availablePayment: (json['availablePayment'] as num?)?.toDouble() ?? 0,
      rewardApproved: (json['rewardApproved'] as num?)?.toDouble() ?? 0,
      rewardProcessedPayment:
          (json['rewardProcessedPayment'] as num?)?.toDouble() ?? 0,
      rewardToBePaid: (json['rewardToBePaid'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$$PaymentSummaryImplToJson(
        _$PaymentSummaryImpl instance) =>
    <String, dynamic>{
      'lifetimeTotalPaidReward': instance.lifetimeTotalPaidReward,
      'availablePayment': instance.availablePayment,
      'rewardApproved': instance.rewardApproved,
      'rewardProcessedPayment': instance.rewardProcessedPayment,
      'rewardToBePaid': instance.rewardToBePaid,
    };
