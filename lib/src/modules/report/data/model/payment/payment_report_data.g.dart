// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_report_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentReportDataImpl _$$PaymentReportDataImplFromJson(
        Map<String, dynamic> json) =>
    _$PaymentReportDataImpl(
      isCheck: json['isCheck'] as bool? ?? false,
      invoiceNumber: json['invoiceNumber'] as String? ?? '',
      rewardApprovedMonthPeriod: json['rewardApprovedMonthPeriod'] == null
          ? null
          : DatePeriod.fromJson(
              json['rewardApprovedMonthPeriod'] as Map<String, dynamic>),
      totalAmount: (json['totalAmount'] as num?)?.toDouble() ?? 0,
      vat: (json['vat'] as num?)?.toDouble() ?? 0,
      wht: (json['wht'] as num?)?.toDouble() ?? 0,
      paidAmount: (json['paidAmount'] as num?)?.toDouble() ?? 0,
      paymentDate: json['paymentDate'] as String? ?? '',
    );

Map<String, dynamic> _$$PaymentReportDataImplToJson(
        _$PaymentReportDataImpl instance) =>
    <String, dynamic>{
      'isCheck': instance.isCheck,
      'invoiceNumber': instance.invoiceNumber,
      'rewardApprovedMonthPeriod': instance.rewardApprovedMonthPeriod,
      'totalAmount': instance.totalAmount,
      'vat': instance.vat,
      'wht': instance.wht,
      'paidAmount': instance.paidAmount,
      'paymentDate': instance.paymentDate,
    };
