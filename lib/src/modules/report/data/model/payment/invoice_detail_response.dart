import 'package:freezed_annotation/freezed_annotation.dart';

part 'invoice_detail_response.freezed.dart';
part 'invoice_detail_response.g.dart';

@freezed
class InvoiceDetailResponse with _$InvoiceDetailResponse {
  factory InvoiceDetailResponse({
    @Default('') String invoiceId,
    @Default(0) double totalReward,
    @Default([]) List<InvoiceCampaignReward> rewards,
  }) = _InvoiceDetailResponse;

  factory InvoiceDetailResponse.fromJson(Map<String, Object?> json) => _$InvoiceDetailResponseFromJson(json);
}

@freezed
class InvoiceCampaignReward with _$InvoiceCampaignReward {
  factory InvoiceCampaignReward({
    @Default(0) int campaignId,
    @Default('') String campaignName,
    @Default('') String rewardMonth,
    @Default(0) double amount,
    @Default(0) int quantity,
  }) = _InvoiceCampaignReward;

  factory InvoiceCampaignReward.fromJson(Map<String, Object?> json) => _$InvoiceCampaignRewardFromJson(json);
}
