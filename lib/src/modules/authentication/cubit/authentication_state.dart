import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/authentication/data/models/auth_token_info.dart';
import 'package:koc_app/src/modules/authentication/data/models/user.dart';
import 'package:koc_app/src/modules/shared/model/country_selector_item.dart';

part 'authentication_state.freezed.dart';
part 'authentication_state.g.dart';

@freezed
class AuthenticationState extends BaseCubitState with _$AuthenticationState {
  factory AuthenticationState(
      {SignInInfo? signInInfo,
      UserExistCheck? userExistCheck,
      AuthTokenInfo? authTokenInfo,
      CountrySelectorItem? country,
      @Default('') String route,
      @Default('') String email,
      @Default(false) bool isValidEmail,
      @Default(false) bool isValidPassword,
      @Default(false) bool obscurePassword,
      @Default(false) bool isLoadingButton,
      @Default(AccountLoginStatus.notRegistered) AccountLoginStatus loginStatus,
      @Default(0) int userId,
      @Default('') String oAuth2AccessToken,
      @Default('') String accessToken,
      @Default('') String refreshToken,
      @Default('') String errorMessage}) = _AuthenticationState;

  factory AuthenticationState.fromJson(Map<String, Object?> json) => _$AuthenticationStateFromJson(json);
}

@freezed
class SignInInfo with _$SignInInfo {
  factory SignInInfo({
    @Default('') String name,
    @Default('') String profilePictureUrl,
    @Default(false) bool hasPassword,
  }) = _SignInInfo;

  factory SignInInfo.fromJson(Map<String, Object?> json) => _$SignInInfoFromJson(json);
}

enum AccountLoginStatus {
  notRegistered,
  success,
  failure,
}
