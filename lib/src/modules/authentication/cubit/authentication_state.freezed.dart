// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'authentication_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AuthenticationState _$AuthenticationStateFromJson(Map<String, dynamic> json) {
  return _AuthenticationState.fromJson(json);
}

/// @nodoc
mixin _$AuthenticationState {
  SignInInfo? get signInInfo => throw _privateConstructorUsedError;
  UserExistCheck? get userExistCheck => throw _privateConstructorUsedError;
  AuthTokenInfo? get authTokenInfo => throw _privateConstructorUsedError;
  CountrySelectorItem? get country => throw _privateConstructorUsedError;
  String get route => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  bool get isValidEmail => throw _privateConstructorUsedError;
  bool get isValidPassword => throw _privateConstructorUsedError;
  bool get obscurePassword => throw _privateConstructorUsedError;
  bool get isLoadingButton => throw _privateConstructorUsedError;
  AccountLoginStatus get loginStatus => throw _privateConstructorUsedError;
  int get userId => throw _privateConstructorUsedError;
  String get oAuth2AccessToken => throw _privateConstructorUsedError;
  String get accessToken => throw _privateConstructorUsedError;
  String get refreshToken => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this AuthenticationState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthenticationStateCopyWith<AuthenticationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthenticationStateCopyWith<$Res> {
  factory $AuthenticationStateCopyWith(
          AuthenticationState value, $Res Function(AuthenticationState) then) =
      _$AuthenticationStateCopyWithImpl<$Res, AuthenticationState>;
  @useResult
  $Res call(
      {SignInInfo? signInInfo,
      UserExistCheck? userExistCheck,
      AuthTokenInfo? authTokenInfo,
      CountrySelectorItem? country,
      String route,
      String email,
      bool isValidEmail,
      bool isValidPassword,
      bool obscurePassword,
      bool isLoadingButton,
      AccountLoginStatus loginStatus,
      int userId,
      String oAuth2AccessToken,
      String accessToken,
      String refreshToken,
      String errorMessage});

  $SignInInfoCopyWith<$Res>? get signInInfo;
  $UserExistCheckCopyWith<$Res>? get userExistCheck;
  $AuthTokenInfoCopyWith<$Res>? get authTokenInfo;
  $CountrySelectorItemCopyWith<$Res>? get country;
}

/// @nodoc
class _$AuthenticationStateCopyWithImpl<$Res, $Val extends AuthenticationState>
    implements $AuthenticationStateCopyWith<$Res> {
  _$AuthenticationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? signInInfo = freezed,
    Object? userExistCheck = freezed,
    Object? authTokenInfo = freezed,
    Object? country = freezed,
    Object? route = null,
    Object? email = null,
    Object? isValidEmail = null,
    Object? isValidPassword = null,
    Object? obscurePassword = null,
    Object? isLoadingButton = null,
    Object? loginStatus = null,
    Object? userId = null,
    Object? oAuth2AccessToken = null,
    Object? accessToken = null,
    Object? refreshToken = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      signInInfo: freezed == signInInfo
          ? _value.signInInfo
          : signInInfo // ignore: cast_nullable_to_non_nullable
              as SignInInfo?,
      userExistCheck: freezed == userExistCheck
          ? _value.userExistCheck
          : userExistCheck // ignore: cast_nullable_to_non_nullable
              as UserExistCheck?,
      authTokenInfo: freezed == authTokenInfo
          ? _value.authTokenInfo
          : authTokenInfo // ignore: cast_nullable_to_non_nullable
              as AuthTokenInfo?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountrySelectorItem?,
      route: null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      isValidEmail: null == isValidEmail
          ? _value.isValidEmail
          : isValidEmail // ignore: cast_nullable_to_non_nullable
              as bool,
      isValidPassword: null == isValidPassword
          ? _value.isValidPassword
          : isValidPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      obscurePassword: null == obscurePassword
          ? _value.obscurePassword
          : obscurePassword // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingButton: null == isLoadingButton
          ? _value.isLoadingButton
          : isLoadingButton // ignore: cast_nullable_to_non_nullable
              as bool,
      loginStatus: null == loginStatus
          ? _value.loginStatus
          : loginStatus // ignore: cast_nullable_to_non_nullable
              as AccountLoginStatus,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      oAuth2AccessToken: null == oAuth2AccessToken
          ? _value.oAuth2AccessToken
          : oAuth2AccessToken // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SignInInfoCopyWith<$Res>? get signInInfo {
    if (_value.signInInfo == null) {
      return null;
    }

    return $SignInInfoCopyWith<$Res>(_value.signInInfo!, (value) {
      return _then(_value.copyWith(signInInfo: value) as $Val);
    });
  }

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserExistCheckCopyWith<$Res>? get userExistCheck {
    if (_value.userExistCheck == null) {
      return null;
    }

    return $UserExistCheckCopyWith<$Res>(_value.userExistCheck!, (value) {
      return _then(_value.copyWith(userExistCheck: value) as $Val);
    });
  }

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AuthTokenInfoCopyWith<$Res>? get authTokenInfo {
    if (_value.authTokenInfo == null) {
      return null;
    }

    return $AuthTokenInfoCopyWith<$Res>(_value.authTokenInfo!, (value) {
      return _then(_value.copyWith(authTokenInfo: value) as $Val);
    });
  }

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountrySelectorItemCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountrySelectorItemCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AuthenticationStateImplCopyWith<$Res>
    implements $AuthenticationStateCopyWith<$Res> {
  factory _$$AuthenticationStateImplCopyWith(_$AuthenticationStateImpl value,
          $Res Function(_$AuthenticationStateImpl) then) =
      __$$AuthenticationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SignInInfo? signInInfo,
      UserExistCheck? userExistCheck,
      AuthTokenInfo? authTokenInfo,
      CountrySelectorItem? country,
      String route,
      String email,
      bool isValidEmail,
      bool isValidPassword,
      bool obscurePassword,
      bool isLoadingButton,
      AccountLoginStatus loginStatus,
      int userId,
      String oAuth2AccessToken,
      String accessToken,
      String refreshToken,
      String errorMessage});

  @override
  $SignInInfoCopyWith<$Res>? get signInInfo;
  @override
  $UserExistCheckCopyWith<$Res>? get userExistCheck;
  @override
  $AuthTokenInfoCopyWith<$Res>? get authTokenInfo;
  @override
  $CountrySelectorItemCopyWith<$Res>? get country;
}

/// @nodoc
class __$$AuthenticationStateImplCopyWithImpl<$Res>
    extends _$AuthenticationStateCopyWithImpl<$Res, _$AuthenticationStateImpl>
    implements _$$AuthenticationStateImplCopyWith<$Res> {
  __$$AuthenticationStateImplCopyWithImpl(_$AuthenticationStateImpl _value,
      $Res Function(_$AuthenticationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? signInInfo = freezed,
    Object? userExistCheck = freezed,
    Object? authTokenInfo = freezed,
    Object? country = freezed,
    Object? route = null,
    Object? email = null,
    Object? isValidEmail = null,
    Object? isValidPassword = null,
    Object? obscurePassword = null,
    Object? isLoadingButton = null,
    Object? loginStatus = null,
    Object? userId = null,
    Object? oAuth2AccessToken = null,
    Object? accessToken = null,
    Object? refreshToken = null,
    Object? errorMessage = null,
  }) {
    return _then(_$AuthenticationStateImpl(
      signInInfo: freezed == signInInfo
          ? _value.signInInfo
          : signInInfo // ignore: cast_nullable_to_non_nullable
              as SignInInfo?,
      userExistCheck: freezed == userExistCheck
          ? _value.userExistCheck
          : userExistCheck // ignore: cast_nullable_to_non_nullable
              as UserExistCheck?,
      authTokenInfo: freezed == authTokenInfo
          ? _value.authTokenInfo
          : authTokenInfo // ignore: cast_nullable_to_non_nullable
              as AuthTokenInfo?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountrySelectorItem?,
      route: null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      isValidEmail: null == isValidEmail
          ? _value.isValidEmail
          : isValidEmail // ignore: cast_nullable_to_non_nullable
              as bool,
      isValidPassword: null == isValidPassword
          ? _value.isValidPassword
          : isValidPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      obscurePassword: null == obscurePassword
          ? _value.obscurePassword
          : obscurePassword // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingButton: null == isLoadingButton
          ? _value.isLoadingButton
          : isLoadingButton // ignore: cast_nullable_to_non_nullable
              as bool,
      loginStatus: null == loginStatus
          ? _value.loginStatus
          : loginStatus // ignore: cast_nullable_to_non_nullable
              as AccountLoginStatus,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      oAuth2AccessToken: null == oAuth2AccessToken
          ? _value.oAuth2AccessToken
          : oAuth2AccessToken // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthenticationStateImpl implements _AuthenticationState {
  _$AuthenticationStateImpl(
      {this.signInInfo,
      this.userExistCheck,
      this.authTokenInfo,
      this.country,
      this.route = '',
      this.email = '',
      this.isValidEmail = false,
      this.isValidPassword = false,
      this.obscurePassword = false,
      this.isLoadingButton = false,
      this.loginStatus = AccountLoginStatus.notRegistered,
      this.userId = 0,
      this.oAuth2AccessToken = '',
      this.accessToken = '',
      this.refreshToken = '',
      this.errorMessage = ''});

  factory _$AuthenticationStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthenticationStateImplFromJson(json);

  @override
  final SignInInfo? signInInfo;
  @override
  final UserExistCheck? userExistCheck;
  @override
  final AuthTokenInfo? authTokenInfo;
  @override
  final CountrySelectorItem? country;
  @override
  @JsonKey()
  final String route;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final bool isValidEmail;
  @override
  @JsonKey()
  final bool isValidPassword;
  @override
  @JsonKey()
  final bool obscurePassword;
  @override
  @JsonKey()
  final bool isLoadingButton;
  @override
  @JsonKey()
  final AccountLoginStatus loginStatus;
  @override
  @JsonKey()
  final int userId;
  @override
  @JsonKey()
  final String oAuth2AccessToken;
  @override
  @JsonKey()
  final String accessToken;
  @override
  @JsonKey()
  final String refreshToken;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'AuthenticationState(signInInfo: $signInInfo, userExistCheck: $userExistCheck, authTokenInfo: $authTokenInfo, country: $country, route: $route, email: $email, isValidEmail: $isValidEmail, isValidPassword: $isValidPassword, obscurePassword: $obscurePassword, isLoadingButton: $isLoadingButton, loginStatus: $loginStatus, userId: $userId, oAuth2AccessToken: $oAuth2AccessToken, accessToken: $accessToken, refreshToken: $refreshToken, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticationStateImpl &&
            (identical(other.signInInfo, signInInfo) ||
                other.signInInfo == signInInfo) &&
            (identical(other.userExistCheck, userExistCheck) ||
                other.userExistCheck == userExistCheck) &&
            (identical(other.authTokenInfo, authTokenInfo) ||
                other.authTokenInfo == authTokenInfo) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.isValidEmail, isValidEmail) ||
                other.isValidEmail == isValidEmail) &&
            (identical(other.isValidPassword, isValidPassword) ||
                other.isValidPassword == isValidPassword) &&
            (identical(other.obscurePassword, obscurePassword) ||
                other.obscurePassword == obscurePassword) &&
            (identical(other.isLoadingButton, isLoadingButton) ||
                other.isLoadingButton == isLoadingButton) &&
            (identical(other.loginStatus, loginStatus) ||
                other.loginStatus == loginStatus) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.oAuth2AccessToken, oAuth2AccessToken) ||
                other.oAuth2AccessToken == oAuth2AccessToken) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      signInInfo,
      userExistCheck,
      authTokenInfo,
      country,
      route,
      email,
      isValidEmail,
      isValidPassword,
      obscurePassword,
      isLoadingButton,
      loginStatus,
      userId,
      oAuth2AccessToken,
      accessToken,
      refreshToken,
      errorMessage);

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticationStateImplCopyWith<_$AuthenticationStateImpl> get copyWith =>
      __$$AuthenticationStateImplCopyWithImpl<_$AuthenticationStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthenticationStateImplToJson(
      this,
    );
  }
}

abstract class _AuthenticationState implements AuthenticationState {
  factory _AuthenticationState(
      {final SignInInfo? signInInfo,
      final UserExistCheck? userExistCheck,
      final AuthTokenInfo? authTokenInfo,
      final CountrySelectorItem? country,
      final String route,
      final String email,
      final bool isValidEmail,
      final bool isValidPassword,
      final bool obscurePassword,
      final bool isLoadingButton,
      final AccountLoginStatus loginStatus,
      final int userId,
      final String oAuth2AccessToken,
      final String accessToken,
      final String refreshToken,
      final String errorMessage}) = _$AuthenticationStateImpl;

  factory _AuthenticationState.fromJson(Map<String, dynamic> json) =
      _$AuthenticationStateImpl.fromJson;

  @override
  SignInInfo? get signInInfo;
  @override
  UserExistCheck? get userExistCheck;
  @override
  AuthTokenInfo? get authTokenInfo;
  @override
  CountrySelectorItem? get country;
  @override
  String get route;
  @override
  String get email;
  @override
  bool get isValidEmail;
  @override
  bool get isValidPassword;
  @override
  bool get obscurePassword;
  @override
  bool get isLoadingButton;
  @override
  AccountLoginStatus get loginStatus;
  @override
  int get userId;
  @override
  String get oAuth2AccessToken;
  @override
  String get accessToken;
  @override
  String get refreshToken;
  @override
  String get errorMessage;

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthenticationStateImplCopyWith<_$AuthenticationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SignInInfo _$SignInInfoFromJson(Map<String, dynamic> json) {
  return _SignInInfo.fromJson(json);
}

/// @nodoc
mixin _$SignInInfo {
  String get name => throw _privateConstructorUsedError;
  String get profilePictureUrl => throw _privateConstructorUsedError;
  bool get hasPassword => throw _privateConstructorUsedError;

  /// Serializes this SignInInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SignInInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SignInInfoCopyWith<SignInInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignInInfoCopyWith<$Res> {
  factory $SignInInfoCopyWith(
          SignInInfo value, $Res Function(SignInInfo) then) =
      _$SignInInfoCopyWithImpl<$Res, SignInInfo>;
  @useResult
  $Res call({String name, String profilePictureUrl, bool hasPassword});
}

/// @nodoc
class _$SignInInfoCopyWithImpl<$Res, $Val extends SignInInfo>
    implements $SignInInfoCopyWith<$Res> {
  _$SignInInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SignInInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? hasPassword = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      profilePictureUrl: null == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String,
      hasPassword: null == hasPassword
          ? _value.hasPassword
          : hasPassword // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SignInInfoImplCopyWith<$Res>
    implements $SignInInfoCopyWith<$Res> {
  factory _$$SignInInfoImplCopyWith(
          _$SignInInfoImpl value, $Res Function(_$SignInInfoImpl) then) =
      __$$SignInInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String profilePictureUrl, bool hasPassword});
}

/// @nodoc
class __$$SignInInfoImplCopyWithImpl<$Res>
    extends _$SignInInfoCopyWithImpl<$Res, _$SignInInfoImpl>
    implements _$$SignInInfoImplCopyWith<$Res> {
  __$$SignInInfoImplCopyWithImpl(
      _$SignInInfoImpl _value, $Res Function(_$SignInInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? profilePictureUrl = null,
    Object? hasPassword = null,
  }) {
    return _then(_$SignInInfoImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      profilePictureUrl: null == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String,
      hasPassword: null == hasPassword
          ? _value.hasPassword
          : hasPassword // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SignInInfoImpl implements _SignInInfo {
  _$SignInInfoImpl(
      {this.name = '', this.profilePictureUrl = '', this.hasPassword = false});

  factory _$SignInInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$SignInInfoImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String profilePictureUrl;
  @override
  @JsonKey()
  final bool hasPassword;

  @override
  String toString() {
    return 'SignInInfo(name: $name, profilePictureUrl: $profilePictureUrl, hasPassword: $hasPassword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignInInfoImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.hasPassword, hasPassword) ||
                other.hasPassword == hasPassword));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, profilePictureUrl, hasPassword);

  /// Create a copy of SignInInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignInInfoImplCopyWith<_$SignInInfoImpl> get copyWith =>
      __$$SignInInfoImplCopyWithImpl<_$SignInInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SignInInfoImplToJson(
      this,
    );
  }
}

abstract class _SignInInfo implements SignInInfo {
  factory _SignInInfo(
      {final String name,
      final String profilePictureUrl,
      final bool hasPassword}) = _$SignInInfoImpl;

  factory _SignInInfo.fromJson(Map<String, dynamic> json) =
      _$SignInInfoImpl.fromJson;

  @override
  String get name;
  @override
  String get profilePictureUrl;
  @override
  bool get hasPassword;

  /// Create a copy of SignInInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignInInfoImplCopyWith<_$SignInInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
