// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authentication_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthenticationStateImpl _$$AuthenticationStateImplFromJson(
        Map<String, dynamic> json) =>
    _$AuthenticationStateImpl(
      signInInfo: json['signInInfo'] == null
          ? null
          : SignInInfo.fromJson(json['signInInfo'] as Map<String, dynamic>),
      userExistCheck: json['userExistCheck'] == null
          ? null
          : UserExistCheck.fromJson(
              json['userExistCheck'] as Map<String, dynamic>),
      authTokenInfo: json['authTokenInfo'] == null
          ? null
          : AuthTokenInfo.fromJson(
              json['authTokenInfo'] as Map<String, dynamic>),
      country: json['country'] == null
          ? null
          : CountrySelectorItem.fromJson(
              json['country'] as Map<String, dynamic>),
      route: json['route'] as String? ?? '',
      email: json['email'] as String? ?? '',
      isValidEmail: json['isValidEmail'] as bool? ?? false,
      isValidPassword: json['isValidPassword'] as bool? ?? false,
      obscurePassword: json['obscurePassword'] as bool? ?? false,
      isLoadingButton: json['isLoadingButton'] as bool? ?? false,
      loginStatus: $enumDecodeNullable(
              _$AccountLoginStatusEnumMap, json['loginStatus']) ??
          AccountLoginStatus.notRegistered,
      userId: (json['userId'] as num?)?.toInt() ?? 0,
      oAuth2AccessToken: json['oAuth2AccessToken'] as String? ?? '',
      accessToken: json['accessToken'] as String? ?? '',
      refreshToken: json['refreshToken'] as String? ?? '',
      errorMessage: json['errorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$$AuthenticationStateImplToJson(
        _$AuthenticationStateImpl instance) =>
    <String, dynamic>{
      'signInInfo': instance.signInInfo,
      'userExistCheck': instance.userExistCheck,
      'authTokenInfo': instance.authTokenInfo,
      'country': instance.country,
      'route': instance.route,
      'email': instance.email,
      'isValidEmail': instance.isValidEmail,
      'isValidPassword': instance.isValidPassword,
      'obscurePassword': instance.obscurePassword,
      'isLoadingButton': instance.isLoadingButton,
      'loginStatus': _$AccountLoginStatusEnumMap[instance.loginStatus]!,
      'userId': instance.userId,
      'oAuth2AccessToken': instance.oAuth2AccessToken,
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
      'errorMessage': instance.errorMessage,
    };

const _$AccountLoginStatusEnumMap = {
  AccountLoginStatus.notRegistered: 'notRegistered',
  AccountLoginStatus.success: 'success',
  AccountLoginStatus.failure: 'failure',
};

_$SignInInfoImpl _$$SignInInfoImplFromJson(Map<String, dynamic> json) =>
    _$SignInInfoImpl(
      name: json['name'] as String? ?? '',
      profilePictureUrl: json['profilePictureUrl'] as String? ?? '',
      hasPassword: json['hasPassword'] as bool? ?? false,
    );

Map<String, dynamic> _$$SignInInfoImplToJson(_$SignInInfoImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'profilePictureUrl': instance.profilePictureUrl,
      'hasPassword': instance.hasPassword,
    };
