import 'dart:convert';
import 'dart:developer' as dev;

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:koc_app/src/shared/services/api_service.dart';

import '../../../../shared/services/secure_storage_helper.dart';
import '../../../../shared/services/shared_preferences_service.dart';
import '../models/auth_token_info.dart';
import '../models/otp.dart';
import '../models/sign_in_sign_up.dart';

class AuthenticationRepository {
  final ApiService apiService;
  final SharedPreferencesService _sharedPreferencesService;
  static final String? clientId = dotenv.env['CLIENT_ID'];
  static final storage = SecureStorageHelper();

  AuthenticationRepository(this.apiService, this._sharedPreferencesService);

  Future<dynamic> sendOtpRegister(SendCodeOtpRequest request) async {
    return await apiService.postDataWithoutJwt('/v1/auth/sign-up/send-otp', request);
  }

  Future<dynamic> sendOtpSignIn(String email, String countryCode) async {
    SendCodeOtpRequest request = SendCodeOtpRequest(email: email, countryCode: countryCode);
    return await apiService.postDataWithoutJwt('/v1/auth/sign-in/send-otp', request);
  }

  Future<dynamic> checkUserExistingBy(String email, String countryCode) async {
    Map<String, dynamic> params = {'email': email, 'countryCode': countryCode};
    return await apiService.getDataWithoutJwt('/v1/auth/check-email-registered', params: params);
  }

  Future<dynamic> loginWithEmail(String email) async {
    return await apiService.postDataWithoutJwt('/v1/auth/sign-in', email);
  }

  Future<String> loginWithOTP(String otp) async {
    await Future.delayed(const Duration(seconds: 1));
    return "jwtToken";
  }

  Future<dynamic> verifySignUpOtpCode(VerifySignUpOtpRequest request) async {
    return await apiService.postDataWithoutJwt('/v1/auth/sign-up/verify-otp', request);
  }

  Future<int> signUpByEmail(String email, String otp) async {
    await Future.delayed(const Duration(seconds: 1));
    if (otp == '123456') {
      return 10;
    }
    return 0;
  }

  Future<dynamic> verifySignInOtpCode(String email, String countryCode, String otp) async {
    VerifySignInOtpRequest request = VerifySignInOtpRequest(email: email, countryCode: countryCode, otp: otp);
    return await apiService.postDataWithoutJwt('/v1/auth/sign-in/verify-otp', request);
  }

  Future<dynamic> verifyEmailPassword(String email, String password, String countryCode) async {
    SignInRequest request =
        SignInRequest(username: email, password: password, countryCode: countryCode, clientId: clientId!);
    return await apiService.postDataWithoutJwt('/v1/auth/sign-in', request);
  }

  Future<dynamic> socialLogin(dynamic request) async {
    return await apiService.postDataWithoutJwt('/v1/auth/sign-in/social-network', request);
  }

  Future<void> logOut() async {
    try {
      await Future.wait([
        storage.deleteAll(),
        _sharedPreferencesService.clearAll(),
        apiService.clearAllCache(),
      ]);
    } catch (e) {
      dev.log('Error during logout: $e');
    }
  }
}
