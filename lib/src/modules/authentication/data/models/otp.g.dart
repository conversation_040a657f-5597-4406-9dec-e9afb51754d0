// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SendCodeOtpRequestImpl _$$SendCodeOtpRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$SendCodeOtpRequestImpl(
      email: json['email'] as String,
      countryCode: json['countryCode'] as String,
    );

Map<String, dynamic> _$$SendCodeOtpRequestImplToJson(
        _$SendCodeOtpRequestImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'countryCode': instance.countryCode,
    };

_$VerifySignInOtpRequestImpl _$$VerifySignInOtpRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$VerifySignInOtpRequestImpl(
      email: json['email'] as String,
      countryCode: json['countryCode'] as String,
      otp: json['otp'] as String,
    );

Map<String, dynamic> _$$VerifySignInOtpRequestImplToJson(
        _$VerifySignInOtpRequestImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'countryCode': instance.countryCode,
      'otp': instance.otp,
    };

_$VerifySignUpOtpRequestImpl _$$VerifySignUpOtpRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$VerifySignUpOtpRequestImpl(
      email: json['email'] as String,
      countryCode: json['countryCode'] as String,
      otp: json['otp'] as String,
    );

Map<String, dynamic> _$$VerifySignUpOtpRequestImplToJson(
        _$VerifySignUpOtpRequestImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'countryCode': instance.countryCode,
      'otp': instance.otp,
    };
