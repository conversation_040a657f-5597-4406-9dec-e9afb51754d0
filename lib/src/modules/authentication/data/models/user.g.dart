// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserHasPasswordImpl _$$UserHasPasswordImplFromJson(
        Map<String, dynamic> json) =>
    _$UserHasPasswordImpl(
      username: json['username'] as String,
      hasPassword: json['hasPassword'] as bool,
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
      profilePictureUrl: json['profilePictureUrl'] as String? ?? '',
    );

Map<String, dynamic> _$$UserHasPasswordImplToJson(
        _$UserHasPasswordImpl instance) =>
    <String, dynamic>{
      'username': instance.username,
      'hasPassword': instance.hasPassword,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'profilePictureUrl': instance.profilePictureUrl,
    };

_$UserExistCheckImpl _$$UserExistCheckImplFromJson(Map<String, dynamic> json) =>
    _$UserExistCheckImpl(
      isEmailRegistered: json['isEmailRegistered'] as bool? ?? false,
      isGlobal: json['isGlobal'] as bool? ?? false,
      users: (json['users'] as List<dynamic>?)
              ?.map((e) => UserHasPassword.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$UserExistCheckImplToJson(
        _$UserExistCheckImpl instance) =>
    <String, dynamic>{
      'isEmailRegistered': instance.isEmailRegistered,
      'isGlobal': instance.isGlobal,
      'users': instance.users,
    };

_$CheckEmailRequestImpl _$$CheckEmailRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CheckEmailRequestImpl(
      email: json['email'] as String,
      countryCode: json['countryCode'] as String,
    );

Map<String, dynamic> _$$CheckEmailRequestImplToJson(
        _$CheckEmailRequestImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'countryCode': instance.countryCode,
    };
