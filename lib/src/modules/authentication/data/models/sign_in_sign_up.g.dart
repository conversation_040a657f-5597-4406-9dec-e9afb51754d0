// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_in_sign_up.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SignInRequestImpl _$$SignInRequestImplFromJson(Map<String, dynamic> json) =>
    _$SignInRequestImpl(
      username: json['username'] as String,
      password: json['password'] as String,
      countryCode: json['countryCode'] as String,
      clientId: json['clientId'] as String,
    );

Map<String, dynamic> _$$SignInRequestImplToJson(_$SignInRequestImpl instance) =>
    <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
      'countryCode': instance.countryCode,
      'clientId': instance.clientId,
    };

_$SocialLoginRequestImpl _$$SocialLoginRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$SocialLoginRequestImpl(
      token: json['token'] as String,
      provider: json['provider'] as String,
      countryCode: json['countryCode'] as String,
      clientId: json['clientId'] as String,
    );

Map<String, dynamic> _$$SocialLoginRequestImplToJson(
        _$SocialLoginRequestImpl instance) =>
    <String, dynamic>{
      'token': instance.token,
      'provider': instance.provider,
      'countryCode': instance.countryCode,
      'clientId': instance.clientId,
    };

_$SocialSignInPayloadImpl _$$SocialSignInPayloadImplFromJson(
        Map<String, dynamic> json) =>
    _$SocialSignInPayloadImpl(
      accessToken: json['accessToken'] as String,
      countryCode: json['countryCode'] as String,
      socialNetworkType: json['socialNetworkType'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      siteName: json['siteName'] as String?,
      siteUrl: json['siteUrl'] as String?,
      socialNetwork: json['socialNetwork'] as String?,
      profilePictureUrl: json['profilePictureUrl'] as String?,
      tokenType: json['tokenType'] as String?,
      email: json['email'] as String?,
      isCheckExistingUser: json['isCheckExistingUser'] as bool? ?? true,
      totalFollowerLevel: json['totalFollowerLevel'] as String? ?? 'EMPTY',
    );

Map<String, dynamic> _$$SocialSignInPayloadImplToJson(
        _$SocialSignInPayloadImpl instance) =>
    <String, dynamic>{
      'accessToken': instance.accessToken,
      'countryCode': instance.countryCode,
      'socialNetworkType': instance.socialNetworkType,
      if (instance.firstName case final value?) 'firstName': value,
      if (instance.lastName case final value?) 'lastName': value,
      if (instance.siteName case final value?) 'siteName': value,
      if (instance.siteUrl case final value?) 'siteUrl': value,
      if (instance.socialNetwork case final value?) 'socialNetwork': value,
      if (instance.profilePictureUrl case final value?)
        'profilePictureUrl': value,
      if (instance.tokenType case final value?) 'tokenType': value,
      if (instance.email case final value?) 'email': value,
      'isCheckExistingUser': instance.isCheckExistingUser,
      'totalFollowerLevel': instance.totalFollowerLevel,
    };

_$SocialLoginResponseImpl _$$SocialLoginResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SocialLoginResponseImpl(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
      tokenType: json['tokenType'] as String,
      expiresIn: (json['expiresIn'] as num).toInt(),
      scope: json['scope'] as String?,
    );

Map<String, dynamic> _$$SocialLoginResponseImplToJson(
        _$SocialLoginResponseImpl instance) =>
    <String, dynamic>{
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
      'tokenType': instance.tokenType,
      'expiresIn': instance.expiresIn,
      'scope': instance.scope,
    };

_$GoogleSignInTokensImpl _$$GoogleSignInTokensImplFromJson(
        Map<String, dynamic> json) =>
    _$GoogleSignInTokensImpl(
      accessToken: json['accessToken'] as String?,
      idToken: json['idToken'] as String?,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      photoUrl: json['photoUrl'] as String?,
    );

Map<String, dynamic> _$$GoogleSignInTokensImplToJson(
        _$GoogleSignInTokensImpl instance) =>
    <String, dynamic>{
      'accessToken': instance.accessToken,
      'idToken': instance.idToken,
      'email': instance.email,
      'displayName': instance.displayName,
      'photoUrl': instance.photoUrl,
    };
