import 'package:freezed_annotation/freezed_annotation.dart';

part 'sign_in_sign_up.freezed.dart';
part 'sign_in_sign_up.g.dart';

@freezed
class SignInRequest with _$SignInRequest {
  const factory SignInRequest({
    required String username,
    required String password,
    required String countryCode,
    required String clientId,
  }) = _SignInRequest;
  factory SignInRequest.fromJson(Map<String, dynamic> json) => _$SignInRequestFromJson(json);
}

@freezed
class SocialLoginRequest with _$SocialLoginRequest {
  const factory SocialLoginRequest({
    required String token,
    required String provider,
    required String countryCode,
    required String clientId,
  }) = _SocialLoginRequest;
  factory SocialLoginRequest.fromJson(Map<String, dynamic> json) => _$SocialLoginRequestFromJson(json);
}

@freezed
class SocialSignInPayload with _$SocialSignInPayload {
  const factory SocialSignInPayload(
      {required String accessToken,
      required String countryCode,
      required String socialNetworkType,
      @Json<PERSON>ey(includeIfNull: false) String? firstName,
      @JsonKey(includeIfNull: false) String? lastName,
      @JsonKey(includeIfNull: false) String? siteName,
      @JsonKey(includeIfNull: false) String? siteUrl,
      @JsonKey(includeIfNull: false) String? socialNetwork,
      @JsonKey(includeIfNull: false) String? profilePictureUrl,
      @JsonKey(includeIfNull: false) String? tokenType,
      @JsonKey(includeIfNull: false) String? email,
      @Default(true) bool isCheckExistingUser,
      @Default('EMPTY') String totalFollowerLevel}) = _SocialSignInPayload;

  factory SocialSignInPayload.fromJson(Map<String, dynamic> json) => _$SocialSignInPayloadFromJson(json);
}

@freezed
class SocialLoginResponse with _$SocialLoginResponse {
  const factory SocialLoginResponse({
    required String accessToken,
    required String refreshToken,
    required String tokenType,
    required int expiresIn,
    String? scope,
  }) = _SocialLoginResponse;

  factory SocialLoginResponse.fromJson(Map<String, dynamic> json) => _$SocialLoginResponseFromJson(json);
}

@freezed
class GoogleSignInTokens with _$GoogleSignInTokens {
  const factory GoogleSignInTokens({
    String? accessToken,
    String? idToken,
    required String email,
    String? displayName,
    String? photoUrl,
  }) = _GoogleSignInTokens;

  factory GoogleSignInTokens.fromJson(Map<String, dynamic> json) => _$GoogleSignInTokensFromJson(json);
}

enum SocialNetworkType {
  facebook('FACEBOOK'),
  google('GOOGLE'),
  tiktok('TIKTOK');

  final String value;
  const SocialNetworkType(this.value);
}

class FacebookSignInResult {
  final bool success;
  final String? navigateTo;
  final String? errorMessage;
  final bool cancelled;

  const FacebookSignInResult({
    required this.success,
    this.navigateTo,
    this.errorMessage,
    this.cancelled = false,
  });
}
