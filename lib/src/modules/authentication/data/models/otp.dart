import 'package:freezed_annotation/freezed_annotation.dart';

part 'otp.freezed.dart';
part 'otp.g.dart';

@freezed
class SendCodeOtpRequest with _$SendCodeOtpRequest {
  const factory SendCodeOtpRequest({
    required String email,
    required String countryCode
  }) = _SendCodeOtpRequest;

  factory SendCodeOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$SendCodeOtpRequestFromJson(json);
}

@freezed
class VerifySignInOtpRequest with _$VerifySignInOtpRequest {
  const factory VerifySignInOtpRequest({
    required String email,
    required String countryCode,
    required String otp,
  }) = _VerifySignInOtpRequest;

  factory VerifySignInOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifySignInOtpRequestFromJson(json);
}

@freezed
class VerifySignUpOtpRequest with _$VerifySignUpOtpRequest {
  const factory VerifySignUpOtpRequest({
    required String email,
    required String countryCode,
    required String otp,
  }) = _VerifySignUpOtpRequest;

  factory VerifySignUpOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifySignUpOtpRequestFromJson(json);
}