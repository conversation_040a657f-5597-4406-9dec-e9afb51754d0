import 'dart:convert';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

/// **Facebook Authentication Route Guard**
///
/// Prevents direct access to Facebook authentication intermediate pages.
/// Users should only access these pages through the proper authentication flow.
///
/// **Protected Routes**:
/// - `/facebook-email-verification/` - Requires Facebook auth session
/// - `/facebook-verification-code/` - Requires email and registration status
///
/// **Validation Logic**:
/// - Checks if Facebook authentication data exists in storage
/// - Verifies required parameters are present for each route
/// - Redirects to main authentication page if validation fails
class FacebookAuthGuard extends RouteGuard {
  FacebookAuthGuard() : super(redirectTo: '/');

  @override
  Future<bool> canActivate(String path, ParallelRoute route) async {
    try {
      final sharedPreferencesService = Modular.get<SharedPreferencesService>();

      // For Facebook email verification page
      if (path.contains('/facebook-email-verification/')) {
        // Check if Facebook authentication session exists
        final facebookAuthData = await _getFacebookAuthData();
        return facebookAuthData != null;
      }

      // For Facebook verification code page
      if (path.contains('/facebook-verification-code/')) {
        // Check if email and registration status are set
        final email = await sharedPreferencesService.getEmail();
        final isRegistered = await sharedPreferencesService.getBoolFromInstance(
          InstanceConstants.isEmailRegisteredKey,
        );

        return email != null && email.isNotEmpty && isRegistered != null;
      }

      // Allow access to other routes
      return true;
    } catch (e) {
      // If any error occurs, deny access for security
      return false;
    }
  }

  /// **Helper method to check Facebook authentication data**
  ///
  /// Retrieves Facebook authentication data from secure storage to verify
  /// that a valid Facebook authentication session exists.
  ///
  /// **Returns**:
  /// - `Map<String, dynamic>` if valid Facebook data exists
  /// - `null` if no data found or data is invalid
  Future<Map<String, dynamic>?> _getFacebookAuthData() async {
    try {
      const storage = FlutterSecureStorage();
      final facebookAuthData = await storage.read(key: InstanceConstants.facebookAuthDataKey);

      if (facebookAuthData != null && facebookAuthData.isNotEmpty) {
        final data = jsonDecode(facebookAuthData);
        // Verify essential fields exist
        if (data['id'] != null && data['id'].toString().isNotEmpty) {
          return data;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
