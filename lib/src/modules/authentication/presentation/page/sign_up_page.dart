import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/presentation/widget/terms_and_privacy_widget.dart';
import 'package:koc_app/src/modules/shared/widget/otp_timer_field.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends BasePageState<SignUpPage, AuthenticationCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.only(top: 40.r, left: 16.r, right: 16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(),
          SizedBox(height: 28.r),
          Expanded(child: _buildMainContent()),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.only(top: 16.r),
      child: _buildOtpTimerField(),
    );
  }

  OtpTimerField _buildOtpTimerField() {
    return OtpTimerField(
        verifyTarget: cubit.state.email,
        btnName: 'Sign up',
        content: const TermsAndPrivacyWidget(),
        showLoading: () => commonCubit.showLoading(),
        hideLoading: () => commonCubit.hideLoading(),
        onTap: (value) async {
          commonCubit.showLoading();
          await cubit.verifySignUpOtpCode(value);
          if (cubit.state.route == '/survey/user') {
            await Modular.get<SharedPreferencesService>().setUserId(cubit.state.userId);
            await Modular.get<SharedPreferencesService>().setEmail(cubit.state.email);
            Modular.to.pushNamedAndRemoveUntil(cubit.state.route, (route) => false);
          } else if (mounted) {
            context.showSnackBar(cubit.state.errorMessage);
          }
          commonCubit.hideLoading();
        });
  }

  Text _buildTitle() {
    return Text(
      'createAnAccount'.tr(),
      style: Theme.of(context).textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.w700),
    );
  }
}
