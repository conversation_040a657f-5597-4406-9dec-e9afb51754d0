import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';

abstract class BaseSignInPageState<T extends StatefulWidget> extends BasePageState<T, AuthenticationCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.only(top: 40.r, bottom: 24.r, left: 16.r, right: 16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(),
          SizedBox(height: 42.r),
          _buildUserInfo(),
          Sized<PERSON><PERSON>(height: 32.r),
          buildFooter(),
        ],
      ),
    );
  }

  Widget buildFooter();

  Widget _buildUserInfo() {
    var profilePictureUrl = cubit.state.userExistCheck!.users[0].profilePictureUrl;
    var userName =
        '${cubit.state.userExistCheck!.users[0].firstName} ${cubit.state.userExistCheck!.users[0].lastName}'.trim();
    return Row(
      children: [
        if (profilePictureUrl.isNotEmpty)
          CachedImageWithPlaceholder(
            imageUrl: profilePictureUrl,
            width: 40.r,
            height: 40.r,
            fit: BoxFit.cover,
          ),
        if (profilePictureUrl.isEmpty)
          Container(
            width: 40.r,
            height: 40.r,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.orange,
            ),
            child: Center(
              child: Text(userName.substring(0, 1).toUpperCase(),
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      )),
            ),
          ),
        SizedBox(
          width: 16.r,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userName,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                'registered ${cubit.state.email}',
                style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF49454F)),
              )
            ],
          ),
        )
      ],
    );
  }

  Widget _buildTitle() {
    return Text(
      'Welcome back',
      style: Theme.of(context).textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.bold),
    );
  }
}
