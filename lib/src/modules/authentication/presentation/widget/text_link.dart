import 'package:flutter/material.dart';
import 'package:koc_app/src/shared/utils/intent_utils.dart';

class TextLink extends StatelessWidget {
  final String text;
  final String url;
  final TextStyle textStyle;

  const TextLink({required this.text, required this.url, required this.textStyle, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => IntentUtils.openBrowserURL(url: url),
      child: Text(
        text,
        style: textStyle.copyWith(
              color: const Color(0xFFEF6507),
            ),
      ),
    );
  }
}
