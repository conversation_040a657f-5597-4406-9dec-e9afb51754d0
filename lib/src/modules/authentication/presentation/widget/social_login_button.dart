import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';

enum SocialLoginType {
  facebook,
  google,
  twitter,
}

class SocialLoginConfig {
  final String imagePath;
  final String text;
  final Color buttonColor;
  final Color textColor;
  final Color borderColor;

  SocialLoginConfig(
      {required this.imagePath,
      required this.text,
      required this.buttonColor,
      required this.textColor,
      required this.borderColor});

  static SocialLoginConfig getConfig(SocialLoginType type) {
    switch (type) {
      case SocialLoginType.facebook:
        return SocialLoginConfig(
            imagePath: 'assets/images/facebook-button-logo.png',
            text: 'continueWithFacebook'.tr(),
            buttonColor: const Color(0xFF1877F2),
            textColor: Colors.white,
            borderColor: const Color(0xFF1877F2));
      case SocialLoginType.google:
        return SocialLoginConfig(
            imagePath: 'assets/images/google-button-logo.png',
            text: 'continueWithGoogle'.tr(),
            buttonColor: ColorConstants.borderColor,
            textColor: Colors.black,
            borderColor: ColorConstants.borderColor);
      default:
        throw Exception('Unknown social login type');
    }
  }
}

class SocialLoginButton extends StatelessWidget {
  final SocialLoginType loginType;
  final VoidCallback onPressed;
  final bool isLoading;

  const SocialLoginButton({required this.loginType, required this.onPressed, this.isLoading = false, super.key});

  @override
  Widget build(BuildContext context) {
    final config = SocialLoginConfig.getConfig(loginType);

    return SizedBox(
      width: double.infinity,
      height: 36.r,
      child: ElevatedButton(
        onPressed: isLoading ? () {} : onPressed,
        style: ElevatedButton.styleFrom(
          shadowColor: const Color.fromARGB(255, 255, 255, 255),
          backgroundColor: config.buttonColor,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(36.r / 2), side: BorderSide(color: config.borderColor)),
        ),
        child: Stack(
          children: [
            Positioned(
              top: 0,
              bottom: 0,
              child: Center(
                child: Image.asset(
                  config.imagePath,
                  width: 24.r,
                  height: 24.r,
                  fit: BoxFit.fitHeight,
                ),
              ),
            ),
            Center(
              child: Text(
                config.text,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(color: config.textColor),
              ),
            ),
            if (isLoading)
              Positioned(
                right: 12.r,
                top: 0,
                bottom: 0,
                child: Center(
                  child: SizedBox(
                    width: 16.r,
                    height: 16.r,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.r,
                      valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFEF6507)),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
