import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

import '../../../../shared/services/api_service.dart';

class SurveyRepository {
  final ApiService apiService;
  SurveyRepository(this.apiService);

  Future<dynamic> addPartner(AddPartnerRequest request) async {
    return await apiService.postDataWithoutJwt('/v1/auth/add-partner', request);
  }

  Future<PassionateInfo> getPassionateInfo() async {
    return await Future.delayed(
        const Duration(seconds: 1), () => const PassionateInfo());
  }

  Future<bool> saveSocialInfo(SocialInfo socialInfo) async {
    return true;
  }

  Future<bool> savePassionateInfo(PassionateInfo passionateInfo) async {
    return true;
  }

  Future<bool> saveUserInfo(UserInfo userInfo) async {
    return true;
  }
}
