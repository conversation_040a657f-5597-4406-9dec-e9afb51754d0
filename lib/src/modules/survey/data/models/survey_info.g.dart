// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'survey_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AddPartnerRequestImpl _$$AddPartnerRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$AddPartnerRequestImpl(
      email: json['email'] as String,
      countryCode: json['countryCode'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      siteName: json['siteName'] as String,
      siteUrl: json['siteUrl'] as String,
      socialMediaType: json['socialMediaType'] as String,
      socialMediaFollower: json['socialMediaFollower'] as String?,
    );

Map<String, dynamic> _$$AddPartnerRequestImplToJson(
        _$AddPartnerRequestImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'countryCode': instance.countryCode,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'siteName': instance.siteName,
      'siteUrl': instance.siteUrl,
      'socialMediaType': instance.socialMediaType,
      'socialMediaFollower': instance.socialMediaFollower,
    };

_$UserInfoImpl _$$UserInfoImplFromJson(Map<String, dynamic> json) =>
    _$UserInfoImpl(
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
    );

Map<String, dynamic> _$$UserInfoImplToJson(_$UserInfoImpl instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
    };

_$SocialInfoImpl _$$SocialInfoImplFromJson(Map<String, dynamic> json) =>
    _$SocialInfoImpl(
      id: (json['id'] as num?)?.toInt(),
      socialMediaType:
          $enumDecodeNullable(_$SocialTypeEnumMap, json['socialMediaType']) ??
              SocialType.OTHER,
      url: json['url'] as String? ?? '',
      name: json['name'] as String? ?? '',
      totalFollowerLevel: $enumDecodeNullable(
              _$FollowerNumberEnumMap, json['totalFollowerLevel']) ??
          FollowerNumber.KOC_LEVEL1,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$$SocialInfoImplToJson(_$SocialInfoImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'socialMediaType': _$SocialTypeEnumMap[instance.socialMediaType]!,
      'url': instance.url,
      'name': instance.name,
      'totalFollowerLevel':
          _$FollowerNumberEnumMap[instance.totalFollowerLevel]!,
      'errorMessage': instance.errorMessage,
    };

const _$SocialTypeEnumMap = {
  SocialType.YOUTUBE: 'YOUTUBE',
  SocialType.FACEBOOK: 'FACEBOOK',
  SocialType.TWITTER: 'TWITTER',
  SocialType.INSTAGRAM: 'INSTAGRAM',
  SocialType.TIKTOK: 'TIKTOK',
  SocialType.OTHER: 'OTHER',
};

const _$FollowerNumberEnumMap = {
  FollowerNumber.KOC_LEVEL1: 'KOC_LEVEL1',
  FollowerNumber.KOC_LEVEL2: 'KOC_LEVEL2',
  FollowerNumber.KOC_LEVEL3: 'KOC_LEVEL3',
  FollowerNumber.KOC_LEVEL4: 'KOC_LEVEL4',
  FollowerNumber.EMPTY: 'EMPTY',
};

_$PassionateInfoImpl _$$PassionateInfoImplFromJson(Map<String, dynamic> json) =>
    _$PassionateInfoImpl(
      selectedPassionateItems:
          (json['selectedPassionateItems'] as List<dynamic>?)
                  ?.map((e) => $enumDecode(_$PassionateItemEnumMap, e))
                  .toList() ??
              const <PassionateItem>[],
    );

Map<String, dynamic> _$$PassionateInfoImplToJson(
        _$PassionateInfoImpl instance) =>
    <String, dynamic>{
      'selectedPassionateItems': instance.selectedPassionateItems
          .map((e) => _$PassionateItemEnumMap[e]!)
          .toList(),
    };

const _$PassionateItemEnumMap = {
  PassionateItem.AUTOMOTIVE: 'AUTOMOTIVE',
  PassionateItem.E_COMMERCE: 'E_COMMERCE',
  PassionateItem.EDUCATION: 'EDUCATION',
  PassionateItem.ENTERTAINMENT: 'ENTERTAINMENT',
  PassionateItem.FINANCIAL: 'FINANCIAL',
  PassionateItem.GAMES: 'GAMES',
  PassionateItem.ONLINE_SERVICE: 'ONLINE_SERVICE',
  PassionateItem.TELECOMMUNICATION: 'TELECOMMUNICATION',
  PassionateItem.TRAVEL_LEISURE: 'TRAVEL_LEISURE',
};
