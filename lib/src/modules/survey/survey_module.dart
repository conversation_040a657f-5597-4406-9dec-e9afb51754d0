import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/authentication/authentication_module.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_cubit.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';
import 'package:koc_app/src/modules/survey/data/repository/survey_repository.dart';
import 'package:koc_app/src/modules/survey/presentation/page/passionate_info_survey_page.dart';
import 'package:koc_app/src/modules/survey/presentation/page/social_info_survey_page.dart';
import 'package:koc_app/src/modules/survey/presentation/page/user_info_survey_page.dart';

class SurveyModule extends Module {
  @override
  List<Module> get imports =>
      [SharedModule(), SurveySharedModule(), AuthenticationModule()];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addSingleton(SurveyRepository.new);
    i.addSingleton(SurveyCubit.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.child('/user', child: (_) => const UserInfoSurveyPage());
    r.child('/social',
        child: (_) => MultiBlocProvider(
              providers: [
                BlocProvider.value(value: Modular.get<SurveyTabCubit>()),
              ],
              child: const SocialInfoSurveyPage(),
            ));
    r.child('/passionate', child: (_) => const PassionateInfoSurveyPage());
  }
}

class SurveySharedModule extends Module {
  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);
    i.addLazySingleton(SurveyTabCubit.new);
  }
}
