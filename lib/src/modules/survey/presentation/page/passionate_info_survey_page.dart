import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_cubit.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/passionate_item_view.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_elevated_button.dart';

class PassionateInfoSurveyPage extends StatefulWidget {
  const PassionateInfoSurveyPage({super.key});

  @override
  State<PassionateInfoSurveyPage> createState() =>
      _PassionateInfoSurveyPageState();
}

class _PassionateInfoSurveyPageState
    extends BasePageState<PassionateInfoSurveyPage, SurveyCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          buildLogo(),
          SizedBox(
            height: 16.r,
          ),
          _buildProgressBar(),
          SizedBox(height: 16.r),
          _buildGreeting(),
          SizedBox(height: 10.r),
          _buildDescription(),
          SizedBox(height: 32.r),
          _buildPassionateItems(),
          _buildNextButton()
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return LinearProgressIndicator(
      value: 1,
      backgroundColor: const Color(0xFFFFB522).withValues(alpha: 0.3),
      color: const Color(0xFFFFB522),
    );
  }

  Widget _buildPassionateItems() {
    return BlocBuilder<SurveyCubit, SurveyState>(
        bloc: cubit,
        builder: (context, state) {
          List<PassionateItem> selectedPassionateItems = [];
          return PassionateItemView(
              selectedItems: selectedPassionateItems,
              onTap: (item) {
                if (selectedPassionateItems.contains(item)) {
                  selectedPassionateItems.remove(item);
                } else {
                  selectedPassionateItems.add(item);
                }
              });
        });
  }

  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What are you passionate about?',
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 4.r),
        Text('Select at least 3 for the best experience.',
            style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  Widget _buildGreeting() {
    return BlocBuilder<SurveyCubit, SurveyState>(
        bloc: cubit,
        builder: (context, state) {
          return Text(
            'Hi ${state.userInfo.firstName} ${state.userInfo.lastName},',
            style: Theme.of(context)
                .textTheme
                .bodyLarge!
                .copyWith(fontWeight: FontWeight.bold),
          );
        });
  }

  Widget _buildNextButton() {
    return BlocBuilder<SurveyCubit, SurveyState>(
      bloc: cubit,
      builder: (context, state) {
        return Expanded(
          child: Container(
            padding: EdgeInsets.only(bottom: 16.r),
            alignment: Alignment.bottomRight,
            child: CommonElevatedButton(
                'Next',
                [].length >= 3
                    ? () async {
                        commonCubit.showLoading();
                        final result = await cubit.saveState();
                        commonCubit.hideLoading();
                        if (result) {
                          Modular.to.navigate('/navigation');
                        } else if (context.mounted) {
                          context.showSnackBar(state.errorMessage);
                        }
                      }
                    : null),
          ),
        );
      },
    );
  }
}
