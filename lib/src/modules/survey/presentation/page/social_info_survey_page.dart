import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_cubit.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_state.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/social_info_tab_bar.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/social_info_tab_view.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

import '../../../../shared/validator/validators.dart';

class SocialInfoSurveyPage extends StatefulWidget {
  const SocialInfoSurveyPage({super.key});

  @override
  State<SocialInfoSurveyPage> createState() => _SocialInfoSurveyPageState();
}

class _SocialInfoSurveyPageState extends BasePageState<SocialInfoSurveyPage, SurveyCubit>
    with SingleTickerProviderStateMixin {
  final SharedPreferencesService _sharedPreferencesService = Modular.get<SharedPreferencesService>();

  @override
  void initState() {
    super.initState();
    _loadFacebookData();
  }

  Future<void> _loadFacebookData() async {
    Modular.get<SurveyTabCubit>().changeTab(0);

    final firstName = await _sharedPreferencesService.getStringFromInstance(InstanceConstants.firstNameKey);
    final lastName = await _sharedPreferencesService.getStringFromInstance(InstanceConstants.lastNameKey);
    final facebookId = await _sharedPreferencesService.getStringFromInstance(InstanceConstants.facebookIdKey);

    if (firstName != null && lastName != null && facebookId != null) {
      final socialName = '$firstName $lastName';
      final socialUrl = 'https://facebook.com/$facebookId';

      cubit.saveSocialInfo(SocialInfo(
        socialMediaType: SocialType.FACEBOOK,
        name: socialName,
        url: socialUrl,
        totalFollowerLevel: FollowerNumber.KOC_LEVEL1,
      ));

      Modular.get<SurveyTabCubit>().updateSocialMediaName(socialName);
      Modular.get<SurveyTabCubit>().updateSocialMediaUrl(socialUrl);
    }
  }

  void saveSocialInfo(SocialInfo socialInfo) {
    cubit.saveSocialInfo(socialInfo);
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
      bottomSheet: _buildButton(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            buildLogo(),
            SizedBox(height: 16.r),
            _buildProgressBar(),
            SizedBox(height: 16.r),
            _buildGreeting(),
            SizedBox(height: 8.r),
            _buildInstructions(),
            SizedBox(height: 16.r),
            _buildSocialInfoSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return LinearProgressIndicator(
      value: 0.66,
      backgroundColor: const Color(0xFFFFB522).withValues(alpha: 0.3),
      color: const Color(0xFFFFB522),
    );
  }

  Widget _buildGreeting() {
    return BlocBuilder<SurveyCubit, SurveyState>(
        bloc: cubit,
        builder: (context, state) {
          return Text(
            'Hi ${state.userInfo.firstName} ${state.userInfo.lastName},',
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.bold),
          );
        });
  }

  Widget _buildInstructions() {
    return Text(
      'Pick the best traffic sources for driving visitors to your affiliate links.',
      style: Theme.of(context).textTheme.bodySmall,
    );
  }

  Widget _buildSocialInfoSection() {
    return BlocBuilder<SurveyCubit, SurveyState>(
        bloc: cubit,
        builder: (context, state) {
          return SocialInfoTabBar(
            tabView: SocialInfoTabView(
              socialInfo: state.socialInfo,
              onEmitSocialInfo: saveSocialInfo,
              onFollowerNumber: (item) {
                cubit.saveSocialInfo(state.socialInfo.copyWith(totalFollowerLevel: item));
              },
              onClearForm: () => cubit.clearSocialInfo(),
            ),
          );
        });
  }

  Widget _buildButton() {
    return BlocBuilder<SurveyCubit, SurveyState>(
      bloc: cubit,
      builder: (context, state) {
        final currentTab = Modular.get<SurveyTabCubit>().state.currentTab;

        final bool isValid = currentTab == 0 ? _isSocialInfoValid(state.socialInfo) : _isWebsiteValid(state.socialInfo);

        return Container(
          padding: EdgeInsets.all(16.r),
          color: Colors.white,
          child: ConfirmationButtons(
            btnName: 'Next',
            onTap: () async {
              commonCubit.showLoading();
              final result = await cubit.saveState();
              if (result) {
                Modular.to.navigate('/navigation');
              } else if (context.mounted) {
                context.showSnackBar(state.errorMessage);
              }
              commonCubit.hideLoading();
            },
            isValid: isValid,
            showCancelButton: true,
            cancelButtonName: 'Back',
            onCancel: () => Modular.to.pop(),
          ),
        );
      },
    );
  }

  bool _isSocialInfoValid(SocialInfo socialInfo) {
    final hasBasicInfo =
        socialInfo.name.isNotEmpty && socialInfo.url.isNotEmpty && Validators.isValidSocialUrl(socialInfo.url);
    final hasValidFollowers =
        socialInfo.socialMediaType == SocialType.OTHER || socialInfo.totalFollowerLevel != FollowerNumber.EMPTY;

    return hasBasicInfo && hasValidFollowers;
  }

  bool _isWebsiteValid(SocialInfo socialInfo) {
    return socialInfo.socialMediaType == SocialType.OTHER && Validators.isValidUrl(socialInfo.url);
  }
}
