import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

class FollowerItemView extends StatelessWidget {
  final FollowerNumber? selectedFollowerNumber;
  final void Function(FollowerNumber) onTap;
  const FollowerItemView(
      {super.key, required this.selectedFollowerNumber, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        'How many followers do you have?',
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      Text('Where to find total followers',
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
              color: Colors.blue,
              decoration: TextDecoration.underline,
              decorationColor: Colors.blue)),
      SizedBox(
        height: 10.r,
      ),
      GridView.count(
        padding: const EdgeInsets.all(0),
        crossAxisCount: 2,
        crossAxisSpacing: 10.r,
        mainAxisSpacing: 10.r,
        shrinkWrap: true,
        childAspectRatio: _calculateGirdViewAspectRatio(),
        physics: const NeverScrollableScrollPhysics(),
        children: FollowerNumber.values.where((value) => value != FollowerNumber.EMPTY).map((value) {
          return GestureDetector(
            onTap: () => onTap(value),
            child: Container(
              decoration: BoxDecoration(
                color: selectedFollowerNumber == value
                    ? Colors.grey[300]
                    : Colors.white,
                border: Border.all(color: Colors.grey, width: 1.r),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.supervisor_account_outlined,
                      size: 30.r,
                    ),
                    Text(
                      value.value,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    ]);
  }

  double _calculateGirdViewAspectRatio() {
    const double baseAspectRatio = 9 / 20;
    const double baseChildAspectRatio = 2.5;

    final double currentAspectRatio =
        ScreenUtil().screenWidth / ScreenUtil().screenHeight;

    final double childAspectRatio =
        baseChildAspectRatio * (currentAspectRatio / baseAspectRatio);

    return childAspectRatio;
  }
}
