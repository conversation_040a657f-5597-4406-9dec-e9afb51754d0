import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';
import 'package:koc_app/src/shared/extensions.dart';

class SocialInfoTabBar extends StatefulWidget {
  final Widget tabView;

  const SocialInfoTabBar({super.key, required this.tabView});

  @override
  State<SocialInfoTabBar> createState() => _SocialInfoTabBarState();
}

class _SocialInfoTabBarState extends State<SocialInfoTabBar> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.index = ReadContext(context).read<SurveyTabCubit>().state.currentTab;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TabBar(
          isScrollable: false,
          physics: const NeverScrollableScrollPhysics(),
          controller: _tabController,
          tabAlignment: TabAlignment.fill,
          labelPadding: EdgeInsets.zero,
          labelStyle: context.textLabelLarge(),
          unselectedLabelStyle: context.textLabelLarge(),
          tabs: [
            SizedBox(height: 48.r, child: const Tab(text: 'Social media channel')),
            SizedBox(height: 48.r, child: const Tab(text: 'Website')),
          ],
          indicatorColor: const Color(0xFFEF8A13),
          onTap: (index) {
            ReadContext(context).read<SurveyTabCubit>().changeTab(index);
          },
        ),
        SizedBox(height: 16.r),
        widget.tabView
      ],
    );
  }
}
