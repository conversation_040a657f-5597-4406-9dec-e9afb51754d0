import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/utils/aspect_ratio_utils.dart';
import 'package:koc_app/src/shared/validator/validators.dart';

class SocialInfoTabView extends StatefulWidget {
  final SocialInfo socialInfo;
  final void Function(SocialInfo) onEmitSocialInfo;
  final void Function(FollowerNumber) onFollowerNumber;
  final void Function() onClearForm;

  const SocialInfoTabView({
    super.key,
    required this.socialInfo,
    required this.onEmitSocialInfo,
    required this.onFollowerNumber,
    required this.onClearForm,
  });

  @override
  State<SocialInfoTabView> createState() => _SocialInfoTabViewState();
}

class _SocialInfoTabViewState extends State<SocialInfoTabView> {
  int _initialIndex = 0;
  final TextEditingController _socialMediaNameController = TextEditingController();
  final TextEditingController _socialMediaUrlController = TextEditingController();
  final TextEditingController _websiteNameController = TextEditingController();
  final TextEditingController _websiteUrlController = TextEditingController();
  final FocusNode _socialMediaNameFocusNode = FocusNode();
  final FocusNode _websiteNameFocusNode = FocusNode();

  bool _isProfileNameTouched = false;
  bool _isSocialUrlTouched = false;
  bool _isWebsiteNameTouched = false;
  bool _isWebsiteUrlTouched = false;

  @override
  void initState() {
    super.initState();
    _initializeSocialInfo();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _socialMediaNameFocusNode.requestFocus();
    });
  }

  void _initializeSocialInfo() {
    if (widget.socialInfo != const SocialInfo()) {
      if (widget.socialInfo.socialMediaType != SocialType.OTHER) {
        _socialMediaNameController.text = widget.socialInfo.name;
        _socialMediaUrlController.text = widget.socialInfo.url;
      } else {
        _websiteNameController.text = widget.socialInfo.name;
        _websiteUrlController.text = widget.socialInfo.url;
      }
      context.read<SurveyTabCubit>().updateBySocialInfo(widget.socialInfo);
    }
  }

  @override
  void didUpdateWidget(SocialInfoTabView oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the socialInfo has changed
    if (widget.socialInfo != oldWidget.socialInfo) {
      _initializeSocialInfo();
    }
  }

  @override
  void dispose() {
    _socialMediaNameController.dispose();
    _socialMediaUrlController.dispose();
    _websiteNameController.dispose();
    _websiteUrlController.dispose();
    _socialMediaNameFocusNode.dispose();
    _websiteNameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SurveyTabCubit, SurveyTabState>(builder: (context, state) {
      if (_initialIndex != state.currentTab) {
        _clearFormForTab(_initialIndex);
        _initialIndex = state.currentTab;
      }

      if (state.currentTab == 0) {
        if (_socialMediaNameController.text.isEmpty && state.socialMediaName.isNotEmpty) {
          _socialMediaNameController.text = state.socialMediaName;
        }
        if (_socialMediaUrlController.text.isEmpty && state.socialMediaUrl.isNotEmpty) {
          _socialMediaUrlController.text = state.socialMediaUrl;
        }
        return socialMediaTab(state);
      } else {
        if (_websiteNameController.text.isEmpty && state.websiteName.isNotEmpty) {
          _websiteNameController.text = state.websiteName;
        }
        if (_websiteUrlController.text.isEmpty && state.websiteUrl.isNotEmpty) {
          _websiteUrlController.text = state.websiteUrl;
        }
        return websiteTab(state);
      }
    });
  }

  void _clearFormForTab(int previousTab) {
    if (previousTab == 0) {
      _socialMediaNameController.clear();
      _socialMediaUrlController.clear();
    } else {
      _websiteNameController.clear();
      _websiteUrlController.clear();
    }
    widget.onClearForm();
    _isProfileNameTouched = false;
    _isSocialUrlTouched = false;
    _isWebsiteNameTouched = false;
    _isWebsiteUrlTouched = false;
    context.read<SurveyTabCubit>().resetSocialInfo();
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required Function(String) onChanged,
    FocusNode? focusNode,
  }) {
    return SizedBox(
      height: 45.r,
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        onChanged: onChanged,
        style: Theme.of(context).textTheme.bodySmall,
        decoration: InputDecoration(
          labelText: label,
        ),
      ),
    );
  }

  Widget _buildErrorText(String message) {
    return Padding(
      padding: EdgeInsets.only(left: 16.r),
      child: Text(
        message,
        style: Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.red),
      ),
    );
  }

  Widget _buildHeaderText() {
    return RichText(
      text: TextSpan(
        style: context.textBodySmall(),
        children: const [
          TextSpan(text: 'We only connect with '),
          TextSpan(
            text: 'Facebook',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          TextSpan(text: ', '),
          TextSpan(
            text: 'X',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          TextSpan(text: ', '),
          TextSpan(
            text: 'Instagram',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          TextSpan(text: ', '),
          TextSpan(
            text: 'Tiktok',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          TextSpan(text: ', and '),
          TextSpan(
            text: 'Youtube',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          TextSpan(text: '.'),
        ],
      ),
    );
  }

  Widget _buildUrlExample(String example) {
    return Padding(
      padding: EdgeInsets.only(left: 16.r),
      child: Text(
        example,
        style: Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.grey),
      ),
    );
  }

  Widget socialMediaTab(SurveyTabState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeaderText(),
        SizedBox(height: 16.r),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTextField(
              controller: _socialMediaNameController,
              focusNode: _socialMediaNameFocusNode,
              label: 'Profile name',
              onChanged: (value) {
                _isProfileNameTouched = true;
                context.read<SurveyTabCubit>().updateSocialMediaName(value);
                widget.onEmitSocialInfo(widget.socialInfo.copyWith(name: value));
              },
            ),
            SizedBox(height: 8.r),
            if (state.socialMediaName.isEmpty && _isProfileNameTouched)
              _buildErrorText('Please enter your profile name'),
          ],
        ),
        SizedBox(height: 16.r),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTextField(
              controller: _socialMediaUrlController,
              label: 'Profile URL',
              onChanged: (value) async {
                _isSocialUrlTouched = true;
                context.read<SurveyTabCubit>().updateSocialMediaUrl(value);
                widget.onEmitSocialInfo(widget.socialInfo.copyWith(url: value));
              },
            ),
            SizedBox(height: 8.r),
            if (state.socialMediaUrl.isEmpty && _isSocialUrlTouched)
              _buildErrorText('Please use a URL that links to your own account'),
            if (state.socialMediaUrl.isNotEmpty && !Validators.isValidUrl(state.socialMediaUrl))
              _buildErrorText('Please enter a valid URL'),
            if (Validators.isValidUrl(state.socialMediaUrl) && !Validators.isValidSocialUrl(state.socialMediaUrl))
              _buildErrorText('Please enter a valid URL'),
            _buildUrlExample('e.g. https://www.facebook.com/your.username'),
          ],
        ),
        SizedBox(height: 24.r),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How many followers do you have?',
              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16.r),
            GridView.count(
              padding: const EdgeInsets.all(0),
              crossAxisCount: 2,
              crossAxisSpacing: 8.r,
              mainAxisSpacing: 8.r,
              shrinkWrap: true,
              childAspectRatio: AspectRatioUtils.calculateGridAspectRatio(),
              physics: const NeverScrollableScrollPhysics(),
              children: FollowerNumber.values.where((value) => value != FollowerNumber.EMPTY).map((value) {
                return GestureDetector(
                  onTap: () => widget.onFollowerNumber(value),
                  child: Container(
                    decoration: BoxDecoration(
                      color: widget.socialInfo.totalFollowerLevel == value
                          ? const Color(0xFFFFB522).withValues(alpha: 0.3)
                          : Colors.white,
                      border: Border.all(color: const Color(0xFFCACACA), width: 1.r),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Center(
                      child: Text(
                        value.value,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ],
    );
  }

  Widget websiteTab(SurveyTabState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTextField(
              controller: _websiteNameController,
              focusNode: _websiteNameFocusNode,
              label: 'Site name',
              onChanged: (value) {
                _isWebsiteNameTouched = true;
                context.read<SurveyTabCubit>().updateWebsiteName(value);
                widget.onEmitSocialInfo(widget.socialInfo.copyWith(socialMediaType: SocialType.OTHER, name: value));
              },
            ),
            SizedBox(height: 8.r),
            if (state.websiteName.isEmpty && _isWebsiteNameTouched) _buildErrorText('Please enter your website name'),
          ],
        ),
        SizedBox(height: 16.r),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTextField(
              controller: _websiteUrlController,
              label: 'Site URL',
              onChanged: (value) {
                _isWebsiteUrlTouched = true;
                context.read<SurveyTabCubit>().updateWebsiteUrl(value);
                widget.onEmitSocialInfo(widget.socialInfo.copyWith(socialMediaType: SocialType.OTHER, url: value));
              },
            ),
            SizedBox(height: 8.r),
            if (state.websiteUrl.isEmpty && _isWebsiteUrlTouched) _buildErrorText('Please enter your website url'),
            if (state.websiteUrl.isNotEmpty && !Validators.isValidUrl(state.websiteUrl))
              _buildErrorText('Please enter a valid URL'),
            _buildUrlExample('e.g. https://www.yoursite.com'),
          ],
        ),
      ],
    );
  }
}
