import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/utils/aspect_ratio_utils.dart';

class PassionateItemView extends StatelessWidget {
  final List<PassionateItem> selectedItems;
  final void Function(PassionateItem) onTap;
  const PassionateItemView(
      {super.key, required this.selectedItems, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      padding: const EdgeInsets.all(0),
      crossAxisCount: 2,
      crossAxisSpacing: 8.r,
      mainAxisSpacing: 8.r,
      childAspectRatio:
          AspectRatioUtils.calculateGridAspectRatio(baseChildAspectRatio: 3.5),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: PassionateItem.values.map((item) {
        return GestureDetector(
          onTap: () => onTap(item),
          child: Container(
            padding: EdgeInsets.only(left: 5.w, right: 5.w),
            decoration: BoxDecoration(
              color: selectedItems.contains(item)
                  ? const Color(0xFFFFB522).withValues(alpha: 0.3)
                  : Colors.white,
              border: Border.all(color: const Color(0xFFCACACA), width: 1.r),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      item.value,
                      style: Theme.of(context).textTheme.labelLarge,
                      maxLines: 1,
                      softWrap: false,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Icon(
                      item.icon,
                      size: 22.r,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
