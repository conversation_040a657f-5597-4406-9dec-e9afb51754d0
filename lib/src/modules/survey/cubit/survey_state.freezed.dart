// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'survey_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SurveyState _$SurveyStateFromJson(Map<String, dynamic> json) {
  return _SurveyState.fromJson(json);
}

/// @nodoc
mixin _$SurveyState {
  int get userId => throw _privateConstructorUsedError;
  UserInfo get userInfo => throw _privateConstructorUsedError;
  SocialInfo get socialInfo => throw _privateConstructorUsedError;
  String get socialNetworkType => throw _privateConstructorUsedError;
  String get accessToken => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this SurveyState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SurveyState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SurveyStateCopyWith<SurveyState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SurveyStateCopyWith<$Res> {
  factory $SurveyStateCopyWith(
          SurveyState value, $Res Function(SurveyState) then) =
      _$SurveyStateCopyWithImpl<$Res, SurveyState>;
  @useResult
  $Res call(
      {int userId,
      UserInfo userInfo,
      SocialInfo socialInfo,
      String socialNetworkType,
      String accessToken,
      String errorMessage});

  $UserInfoCopyWith<$Res> get userInfo;
  $SocialInfoCopyWith<$Res> get socialInfo;
}

/// @nodoc
class _$SurveyStateCopyWithImpl<$Res, $Val extends SurveyState>
    implements $SurveyStateCopyWith<$Res> {
  _$SurveyStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SurveyState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? userInfo = null,
    Object? socialInfo = null,
    Object? socialNetworkType = null,
    Object? accessToken = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      userInfo: null == userInfo
          ? _value.userInfo
          : userInfo // ignore: cast_nullable_to_non_nullable
              as UserInfo,
      socialInfo: null == socialInfo
          ? _value.socialInfo
          : socialInfo // ignore: cast_nullable_to_non_nullable
              as SocialInfo,
      socialNetworkType: null == socialNetworkType
          ? _value.socialNetworkType
          : socialNetworkType // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of SurveyState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<$Res> get userInfo {
    return $UserInfoCopyWith<$Res>(_value.userInfo, (value) {
      return _then(_value.copyWith(userInfo: value) as $Val);
    });
  }

  /// Create a copy of SurveyState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SocialInfoCopyWith<$Res> get socialInfo {
    return $SocialInfoCopyWith<$Res>(_value.socialInfo, (value) {
      return _then(_value.copyWith(socialInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SurveyStateImplCopyWith<$Res>
    implements $SurveyStateCopyWith<$Res> {
  factory _$$SurveyStateImplCopyWith(
          _$SurveyStateImpl value, $Res Function(_$SurveyStateImpl) then) =
      __$$SurveyStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int userId,
      UserInfo userInfo,
      SocialInfo socialInfo,
      String socialNetworkType,
      String accessToken,
      String errorMessage});

  @override
  $UserInfoCopyWith<$Res> get userInfo;
  @override
  $SocialInfoCopyWith<$Res> get socialInfo;
}

/// @nodoc
class __$$SurveyStateImplCopyWithImpl<$Res>
    extends _$SurveyStateCopyWithImpl<$Res, _$SurveyStateImpl>
    implements _$$SurveyStateImplCopyWith<$Res> {
  __$$SurveyStateImplCopyWithImpl(
      _$SurveyStateImpl _value, $Res Function(_$SurveyStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SurveyState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? userInfo = null,
    Object? socialInfo = null,
    Object? socialNetworkType = null,
    Object? accessToken = null,
    Object? errorMessage = null,
  }) {
    return _then(_$SurveyStateImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      userInfo: null == userInfo
          ? _value.userInfo
          : userInfo // ignore: cast_nullable_to_non_nullable
              as UserInfo,
      socialInfo: null == socialInfo
          ? _value.socialInfo
          : socialInfo // ignore: cast_nullable_to_non_nullable
              as SocialInfo,
      socialNetworkType: null == socialNetworkType
          ? _value.socialNetworkType
          : socialNetworkType // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SurveyStateImpl implements _SurveyState {
  _$SurveyStateImpl(
      {this.userId = 0,
      this.userInfo = const UserInfo(),
      this.socialInfo = const SocialInfo(),
      this.socialNetworkType = '',
      this.accessToken = '',
      this.errorMessage = ''});

  factory _$SurveyStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$SurveyStateImplFromJson(json);

  @override
  @JsonKey()
  final int userId;
  @override
  @JsonKey()
  final UserInfo userInfo;
  @override
  @JsonKey()
  final SocialInfo socialInfo;
  @override
  @JsonKey()
  final String socialNetworkType;
  @override
  @JsonKey()
  final String accessToken;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'SurveyState(userId: $userId, userInfo: $userInfo, socialInfo: $socialInfo, socialNetworkType: $socialNetworkType, accessToken: $accessToken, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SurveyStateImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userInfo, userInfo) ||
                other.userInfo == userInfo) &&
            (identical(other.socialInfo, socialInfo) ||
                other.socialInfo == socialInfo) &&
            (identical(other.socialNetworkType, socialNetworkType) ||
                other.socialNetworkType == socialNetworkType) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, userId, userInfo, socialInfo,
      socialNetworkType, accessToken, errorMessage);

  /// Create a copy of SurveyState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SurveyStateImplCopyWith<_$SurveyStateImpl> get copyWith =>
      __$$SurveyStateImplCopyWithImpl<_$SurveyStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SurveyStateImplToJson(
      this,
    );
  }
}

abstract class _SurveyState implements SurveyState {
  factory _SurveyState(
      {final int userId,
      final UserInfo userInfo,
      final SocialInfo socialInfo,
      final String socialNetworkType,
      final String accessToken,
      final String errorMessage}) = _$SurveyStateImpl;

  factory _SurveyState.fromJson(Map<String, dynamic> json) =
      _$SurveyStateImpl.fromJson;

  @override
  int get userId;
  @override
  UserInfo get userInfo;
  @override
  SocialInfo get socialInfo;
  @override
  String get socialNetworkType;
  @override
  String get accessToken;
  @override
  String get errorMessage;

  /// Create a copy of SurveyState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SurveyStateImplCopyWith<_$SurveyStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
