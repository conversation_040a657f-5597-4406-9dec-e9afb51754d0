// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'survey_tab_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SurveyTabState _$SurveyTabStateFromJson(Map<String, dynamic> json) {
  return _SurveyTabState.fromJson(json);
}

/// @nodoc
mixin _$SurveyTabState {
  int get currentTab => throw _privateConstructorUsedError;
  String get socialMediaName => throw _privateConstructorUsedError;
  String get socialMediaUrl => throw _privateConstructorUsedError;
  String get websiteName => throw _privateConstructorUsedError;
  String get websiteUrl => throw _privateConstructorUsedError;
  FollowerNumber get totalFollowerLevel => throw _privateConstructorUsedError;

  /// Serializes this SurveyTabState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SurveyTabState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SurveyTabStateCopyWith<SurveyTabState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SurveyTabStateCopyWith<$Res> {
  factory $SurveyTabStateCopyWith(
          SurveyTabState value, $Res Function(SurveyTabState) then) =
      _$SurveyTabStateCopyWithImpl<$Res, SurveyTabState>;
  @useResult
  $Res call(
      {int currentTab,
      String socialMediaName,
      String socialMediaUrl,
      String websiteName,
      String websiteUrl,
      FollowerNumber totalFollowerLevel});
}

/// @nodoc
class _$SurveyTabStateCopyWithImpl<$Res, $Val extends SurveyTabState>
    implements $SurveyTabStateCopyWith<$Res> {
  _$SurveyTabStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SurveyTabState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentTab = null,
    Object? socialMediaName = null,
    Object? socialMediaUrl = null,
    Object? websiteName = null,
    Object? websiteUrl = null,
    Object? totalFollowerLevel = null,
  }) {
    return _then(_value.copyWith(
      currentTab: null == currentTab
          ? _value.currentTab
          : currentTab // ignore: cast_nullable_to_non_nullable
              as int,
      socialMediaName: null == socialMediaName
          ? _value.socialMediaName
          : socialMediaName // ignore: cast_nullable_to_non_nullable
              as String,
      socialMediaUrl: null == socialMediaUrl
          ? _value.socialMediaUrl
          : socialMediaUrl // ignore: cast_nullable_to_non_nullable
              as String,
      websiteName: null == websiteName
          ? _value.websiteName
          : websiteName // ignore: cast_nullable_to_non_nullable
              as String,
      websiteUrl: null == websiteUrl
          ? _value.websiteUrl
          : websiteUrl // ignore: cast_nullable_to_non_nullable
              as String,
      totalFollowerLevel: null == totalFollowerLevel
          ? _value.totalFollowerLevel
          : totalFollowerLevel // ignore: cast_nullable_to_non_nullable
              as FollowerNumber,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SurveyTabStateImplCopyWith<$Res>
    implements $SurveyTabStateCopyWith<$Res> {
  factory _$$SurveyTabStateImplCopyWith(_$SurveyTabStateImpl value,
          $Res Function(_$SurveyTabStateImpl) then) =
      __$$SurveyTabStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentTab,
      String socialMediaName,
      String socialMediaUrl,
      String websiteName,
      String websiteUrl,
      FollowerNumber totalFollowerLevel});
}

/// @nodoc
class __$$SurveyTabStateImplCopyWithImpl<$Res>
    extends _$SurveyTabStateCopyWithImpl<$Res, _$SurveyTabStateImpl>
    implements _$$SurveyTabStateImplCopyWith<$Res> {
  __$$SurveyTabStateImplCopyWithImpl(
      _$SurveyTabStateImpl _value, $Res Function(_$SurveyTabStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SurveyTabState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentTab = null,
    Object? socialMediaName = null,
    Object? socialMediaUrl = null,
    Object? websiteName = null,
    Object? websiteUrl = null,
    Object? totalFollowerLevel = null,
  }) {
    return _then(_$SurveyTabStateImpl(
      currentTab: null == currentTab
          ? _value.currentTab
          : currentTab // ignore: cast_nullable_to_non_nullable
              as int,
      socialMediaName: null == socialMediaName
          ? _value.socialMediaName
          : socialMediaName // ignore: cast_nullable_to_non_nullable
              as String,
      socialMediaUrl: null == socialMediaUrl
          ? _value.socialMediaUrl
          : socialMediaUrl // ignore: cast_nullable_to_non_nullable
              as String,
      websiteName: null == websiteName
          ? _value.websiteName
          : websiteName // ignore: cast_nullable_to_non_nullable
              as String,
      websiteUrl: null == websiteUrl
          ? _value.websiteUrl
          : websiteUrl // ignore: cast_nullable_to_non_nullable
              as String,
      totalFollowerLevel: null == totalFollowerLevel
          ? _value.totalFollowerLevel
          : totalFollowerLevel // ignore: cast_nullable_to_non_nullable
              as FollowerNumber,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SurveyTabStateImpl implements _SurveyTabState {
  const _$SurveyTabStateImpl(
      {this.currentTab = 0,
      this.socialMediaName = '',
      this.socialMediaUrl = '',
      this.websiteName = '',
      this.websiteUrl = '',
      this.totalFollowerLevel = FollowerNumber.KOC_LEVEL1});

  factory _$SurveyTabStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$SurveyTabStateImplFromJson(json);

  @override
  @JsonKey()
  final int currentTab;
  @override
  @JsonKey()
  final String socialMediaName;
  @override
  @JsonKey()
  final String socialMediaUrl;
  @override
  @JsonKey()
  final String websiteName;
  @override
  @JsonKey()
  final String websiteUrl;
  @override
  @JsonKey()
  final FollowerNumber totalFollowerLevel;

  @override
  String toString() {
    return 'SurveyTabState(currentTab: $currentTab, socialMediaName: $socialMediaName, socialMediaUrl: $socialMediaUrl, websiteName: $websiteName, websiteUrl: $websiteUrl, totalFollowerLevel: $totalFollowerLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SurveyTabStateImpl &&
            (identical(other.currentTab, currentTab) ||
                other.currentTab == currentTab) &&
            (identical(other.socialMediaName, socialMediaName) ||
                other.socialMediaName == socialMediaName) &&
            (identical(other.socialMediaUrl, socialMediaUrl) ||
                other.socialMediaUrl == socialMediaUrl) &&
            (identical(other.websiteName, websiteName) ||
                other.websiteName == websiteName) &&
            (identical(other.websiteUrl, websiteUrl) ||
                other.websiteUrl == websiteUrl) &&
            (identical(other.totalFollowerLevel, totalFollowerLevel) ||
                other.totalFollowerLevel == totalFollowerLevel));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, currentTab, socialMediaName,
      socialMediaUrl, websiteName, websiteUrl, totalFollowerLevel);

  /// Create a copy of SurveyTabState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SurveyTabStateImplCopyWith<_$SurveyTabStateImpl> get copyWith =>
      __$$SurveyTabStateImplCopyWithImpl<_$SurveyTabStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SurveyTabStateImplToJson(
      this,
    );
  }
}

abstract class _SurveyTabState implements SurveyTabState {
  const factory _SurveyTabState(
      {final int currentTab,
      final String socialMediaName,
      final String socialMediaUrl,
      final String websiteName,
      final String websiteUrl,
      final FollowerNumber totalFollowerLevel}) = _$SurveyTabStateImpl;

  factory _SurveyTabState.fromJson(Map<String, dynamic> json) =
      _$SurveyTabStateImpl.fromJson;

  @override
  int get currentTab;
  @override
  String get socialMediaName;
  @override
  String get socialMediaUrl;
  @override
  String get websiteName;
  @override
  String get websiteUrl;
  @override
  FollowerNumber get totalFollowerLevel;

  /// Create a copy of SurveyTabState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SurveyTabStateImplCopyWith<_$SurveyTabStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
