// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'survey_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SurveyStateImpl _$$SurveyStateImplFromJson(Map<String, dynamic> json) =>
    _$SurveyStateImpl(
      userId: (json['userId'] as num?)?.toInt() ?? 0,
      userInfo: json['userInfo'] == null
          ? const UserInfo()
          : UserInfo.fromJson(json['userInfo'] as Map<String, dynamic>),
      socialInfo: json['socialInfo'] == null
          ? const SocialInfo()
          : SocialInfo.fromJson(json['socialInfo'] as Map<String, dynamic>),
      socialNetworkType: json['socialNetworkType'] as String? ?? '',
      accessToken: json['accessToken'] as String? ?? '',
      errorMessage: json['errorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$$SurveyStateImplToJson(_$SurveyStateImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userInfo': instance.userInfo,
      'socialInfo': instance.socialInfo,
      'socialNetworkType': instance.socialNetworkType,
      'accessToken': instance.accessToken,
      'errorMessage': instance.errorMessage,
    };
