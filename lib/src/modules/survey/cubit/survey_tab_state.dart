import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

part 'survey_tab_state.freezed.dart';
part 'survey_tab_state.g.dart';

@freezed
class SurveyTabState with _$SurveyTabState {
  const factory SurveyTabState({
    @Default(0) int currentTab,
    @Default('') String socialMediaName,
    @Default('') String socialMediaUrl,
    @Default('') String websiteName,
    @Default('') String websiteUrl,
    @Default(FollowerNumber.KOC_LEVEL1) FollowerNumber totalFollowerLevel,
  }) = _SurveyTabState;

  factory SurveyTabState.fromJson(Map<String, Object?> json) => _$SurveyTabStateFromJson(json);
}
