// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'survey_tab_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SurveyTabStateImpl _$$SurveyTabStateImplFromJson(Map<String, dynamic> json) =>
    _$SurveyTabStateImpl(
      currentTab: (json['currentTab'] as num?)?.toInt() ?? 0,
      socialMediaName: json['socialMediaName'] as String? ?? '',
      socialMediaUrl: json['socialMediaUrl'] as String? ?? '',
      websiteName: json['websiteName'] as String? ?? '',
      websiteUrl: json['websiteUrl'] as String? ?? '',
      totalFollowerLevel: $enumDecodeNullable(
              _$FollowerNumberEnumMap, json['totalFollowerLevel']) ??
          FollowerNumber.KOC_LEVEL1,
    );

Map<String, dynamic> _$$SurveyTabStateImplToJson(
        _$SurveyTabStateImpl instance) =>
    <String, dynamic>{
      'currentTab': instance.currentTab,
      'socialMediaName': instance.socialMediaName,
      'socialMediaUrl': instance.socialMediaUrl,
      'websiteName': instance.websiteName,
      'websiteUrl': instance.websiteUrl,
      'totalFollowerLevel':
          _$FollowerNumberEnumMap[instance.totalFollowerLevel]!,
    };

const _$FollowerNumberEnumMap = {
  FollowerNumber.KOC_LEVEL1: 'KOC_LEVEL1',
  FollowerNumber.KOC_LEVEL2: 'KOC_LEVEL2',
  FollowerNumber.KOC_LEVEL3: 'KOC_LEVEL3',
  FollowerNumber.KOC_LEVEL4: 'KOC_LEVEL4',
  FollowerNumber.EMPTY: 'EMPTY',
};
