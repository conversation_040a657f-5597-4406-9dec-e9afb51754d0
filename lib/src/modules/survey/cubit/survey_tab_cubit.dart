import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

class SurveyTabCubit extends Cubit<SurveyTabState> {
  SurveyTabCubit() : super(const SurveyTabState());

  void changeTab(int index) {
    emit(state.copyWith(currentTab: index));
  }

  void resetSocialInfo() {
    emit(state.copyWith(
        socialMediaName: '',
        socialMediaUrl: '',
        websiteName: '',
        websiteUrl: ''));
  }

  void updateSocialMediaName(String name) {
    emit(state.copyWith(socialMediaName: name));
  }

  void updateSocialMediaUrl(String url) async {
    emit(state.copyWith(socialMediaUrl: url));
  }

  void updateWebsiteName(String name) {
    emit(state.copyWith(websiteName: name));
  }

  void updateWebsiteUrl(String url) {
    emit(state.copyWith(websiteUrl: url));
  }

  void updateBySocialInfo(SocialInfo socialInfo) {
    if (socialInfo.socialMediaType == SocialType.OTHER) {
      emit(state.copyWith(
          websiteName: socialInfo.name, websiteUrl: socialInfo.url));
    } else {
      emit(state.copyWith(
          socialMediaName: socialInfo.name, socialMediaUrl: socialInfo.url));
    }
  }
}
