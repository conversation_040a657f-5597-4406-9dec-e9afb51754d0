import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

part 'survey_state.freezed.dart';

part 'survey_state.g.dart';

@freezed
class SurveyState extends BaseCubitState with _$SurveyState {
  factory SurveyState(
      {@Default(0) int userId,
      @Default(UserInfo()) UserInfo userInfo,
      @Default(SocialInfo()) SocialInfo socialInfo,
      @Default('') String socialNetworkType,
      @Default('') String accessToken,
      @Default('') String errorMessage}) = _SurveyState;

  factory SurveyState.fromJson(Map<String, Object?> json) =>
      _$SurveyStateFromJson(json);
}
