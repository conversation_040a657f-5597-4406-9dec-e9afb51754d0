import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class ForgotPasswordPage extends StatefulWidget {
  final String route;
  final Object? arguments;
  const ForgotPasswordPage(this.route, {this.arguments, super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Forgot password')),
      body: _buildBody(),
      bottomSheet: Container(
        color: Colors.white,
        padding: EdgeInsets.all(16.r),
        child: ConfirmationButtons(
          btnName: 'Continue',
          showCancelButton: true,
          onTap: () {
            Modular.to.pushNamed(widget.route, arguments: widget.arguments);
          },
          isValid: true,
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          spacing: 16.r,
          children: [
            Text(
              'Forgot password?',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            Text(
              'We will send you a message to help you reset your password',
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

}