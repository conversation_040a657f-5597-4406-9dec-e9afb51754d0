import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NoDataReportWidget extends StatelessWidget {
  const NoDataReportWidget({super.key, this.message, this.expandToFillSpace = false, this.ratio = 0.65});

  final String? message;
  final bool expandToFillSpace;
  final double ratio;


  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    Widget content = Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            SvgPicture.asset(
              'assets/images/scan_delete.svg',
              width: 80.r,
              height: 80.r,
            ),
            const SizedBox(height: 24),
            Text(
              message ?? 'There is no data available at the moment',
              textAlign: TextAlign.center,
              style: theme.textTheme.labelLarge,
            ),
          ],
        ),
      ),
    );

    if (expandToFillSpace) {
      return SizedBox(
        height: MediaQuery.of(context).size.height * ratio,
        child: content,
      );
    }

    return content;
  }
}
