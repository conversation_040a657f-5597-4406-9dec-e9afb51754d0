import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koc_app/src/modules/shared/cubit/pagination_state.dart';

class PaginationCubit extends Cubit<PaginationState> {
  PaginationCubit() : super(PaginationState());

  void setPageSize(int pageSize) {
    emit(state.copyWith(pageSize: pageSize));
  }

  void setCurrentPage(int currentPage) {
    emit(state.copyWith(currentPage: currentPage));
  }

  void setTotal(int total) {
    emit(state.copyWith(total: total));
  }
}
