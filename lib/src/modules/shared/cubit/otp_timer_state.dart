import 'package:freezed_annotation/freezed_annotation.dart';

part 'otp_timer_state.freezed.dart';
part 'otp_timer_state.g.dart';

@freezed
class OtpTimerState with _$OtpTimerState {
  static const int defaultRemainingSeconds = 300;

  factory OtpTimerState({
    @Default(300) int remainingSeconds,
    @Default(false) bool isValidOtp,
    @Default('') String errorMessage,
    @Default(false) bool isResendOtp,
  }) = _OtpTimerState;

  factory OtpTimerState.fromJson(Map<String, Object?> json) =>
      _$OtpTimerStateFromJson(json);
}
