import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koc_app/src/modules/shared/cubit/otp_timer_state.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

import '../../authentication/cubit/authentication_cubit.dart';

class OtpTimerCubit extends Cubit<OtpTimerState> {
  final AuthenticationCubit _authenticationCubit;
  final SharedPreferencesService _sharedPreferencesService;

  OtpTimerCubit(this._authenticationCubit, this._sharedPreferencesService) : super(OtpTimerState());
  Timer? _timer;

  Future<String?> getEmail() async {
    final result = await _sharedPreferencesService.getEmail();
    return result?.isNotEmpty == true ? result : null;
  }

  void startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.remainingSeconds > 0) {
        emit(state.copyWith(remainingSeconds: state.remainingSeconds - 1));
      } else {
        emit(state.copyWith(isResendOtp: false));
        timer.cancel();
      }
    });
  }

  void resetTimer() {
    _timer?.cancel();
    emit(state.copyWith(remainingSeconds: OtpTimerState.defaultRemainingSeconds, isResendOtp: true));
    startTimer();
  }

  void resetTimerForInitialOtp() {
    _timer?.cancel();
    emit(state.copyWith(remainingSeconds: OtpTimerState.defaultRemainingSeconds, isResendOtp: false));
    startTimer();
  }

  void updateValidation(bool isValid) {
    emit(state.copyWith(isValidOtp: isValid));
  }

  Future<bool> resendOtp() async {
    emit(state.copyWith(isResendOtp: true));

    final email = await getEmail();
    final countryCode = await _sharedPreferencesService.getCountryCode();
    if (email == null || email.isEmpty) {
      handleError(Exception('Email is null or empty'), (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
    if (countryCode == null) {
      return false;
    }
    try {
      bool? isEmailRegistered =
          await _sharedPreferencesService.getBoolFromInstance(InstanceConstants.isEmailRegisteredKey);

      if (isEmailRegistered!) {
        await _authenticationCubit.sendOtpSignIn(email, countryCode);
      } else {
        await _authenticationCubit.sendOtpRegister(email, countryCode);
      }

      _timer?.cancel();
      emit(state.copyWith(remainingSeconds: OtpTimerState.defaultRemainingSeconds));
      startTimer();
      return true;
    } catch (error) {
      emit(state.copyWith(isResendOtp: false, errorMessage: ''));
      handleError(error, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  void dispose() {
    _timer?.cancel();
  }
}
