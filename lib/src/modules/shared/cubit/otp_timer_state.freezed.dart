// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'otp_timer_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OtpTimerState _$OtpTimerStateFromJson(Map<String, dynamic> json) {
  return _OtpTimerState.fromJson(json);
}

/// @nodoc
mixin _$OtpTimerState {
  int get remainingSeconds => throw _privateConstructorUsedError;
  bool get isValidOtp => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  bool get isResendOtp => throw _privateConstructorUsedError;

  /// Serializes this OtpTimerState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtpTimerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtpTimerStateCopyWith<OtpTimerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtpTimerStateCopyWith<$Res> {
  factory $OtpTimerStateCopyWith(
          OtpTimerState value, $Res Function(OtpTimerState) then) =
      _$OtpTimerStateCopyWithImpl<$Res, OtpTimerState>;
  @useResult
  $Res call(
      {int remainingSeconds,
      bool isValidOtp,
      String errorMessage,
      bool isResendOtp});
}

/// @nodoc
class _$OtpTimerStateCopyWithImpl<$Res, $Val extends OtpTimerState>
    implements $OtpTimerStateCopyWith<$Res> {
  _$OtpTimerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtpTimerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remainingSeconds = null,
    Object? isValidOtp = null,
    Object? errorMessage = null,
    Object? isResendOtp = null,
  }) {
    return _then(_value.copyWith(
      remainingSeconds: null == remainingSeconds
          ? _value.remainingSeconds
          : remainingSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      isValidOtp: null == isValidOtp
          ? _value.isValidOtp
          : isValidOtp // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isResendOtp: null == isResendOtp
          ? _value.isResendOtp
          : isResendOtp // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OtpTimerStateImplCopyWith<$Res>
    implements $OtpTimerStateCopyWith<$Res> {
  factory _$$OtpTimerStateImplCopyWith(
          _$OtpTimerStateImpl value, $Res Function(_$OtpTimerStateImpl) then) =
      __$$OtpTimerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int remainingSeconds,
      bool isValidOtp,
      String errorMessage,
      bool isResendOtp});
}

/// @nodoc
class __$$OtpTimerStateImplCopyWithImpl<$Res>
    extends _$OtpTimerStateCopyWithImpl<$Res, _$OtpTimerStateImpl>
    implements _$$OtpTimerStateImplCopyWith<$Res> {
  __$$OtpTimerStateImplCopyWithImpl(
      _$OtpTimerStateImpl _value, $Res Function(_$OtpTimerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtpTimerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remainingSeconds = null,
    Object? isValidOtp = null,
    Object? errorMessage = null,
    Object? isResendOtp = null,
  }) {
    return _then(_$OtpTimerStateImpl(
      remainingSeconds: null == remainingSeconds
          ? _value.remainingSeconds
          : remainingSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      isValidOtp: null == isValidOtp
          ? _value.isValidOtp
          : isValidOtp // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isResendOtp: null == isResendOtp
          ? _value.isResendOtp
          : isResendOtp // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OtpTimerStateImpl implements _OtpTimerState {
  _$OtpTimerStateImpl(
      {this.remainingSeconds = 300,
      this.isValidOtp = false,
      this.errorMessage = '',
      this.isResendOtp = false});

  factory _$OtpTimerStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtpTimerStateImplFromJson(json);

  @override
  @JsonKey()
  final int remainingSeconds;
  @override
  @JsonKey()
  final bool isValidOtp;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool isResendOtp;

  @override
  String toString() {
    return 'OtpTimerState(remainingSeconds: $remainingSeconds, isValidOtp: $isValidOtp, errorMessage: $errorMessage, isResendOtp: $isResendOtp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtpTimerStateImpl &&
            (identical(other.remainingSeconds, remainingSeconds) ||
                other.remainingSeconds == remainingSeconds) &&
            (identical(other.isValidOtp, isValidOtp) ||
                other.isValidOtp == isValidOtp) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isResendOtp, isResendOtp) ||
                other.isResendOtp == isResendOtp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, remainingSeconds, isValidOtp, errorMessage, isResendOtp);

  /// Create a copy of OtpTimerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtpTimerStateImplCopyWith<_$OtpTimerStateImpl> get copyWith =>
      __$$OtpTimerStateImplCopyWithImpl<_$OtpTimerStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtpTimerStateImplToJson(
      this,
    );
  }
}

abstract class _OtpTimerState implements OtpTimerState {
  factory _OtpTimerState(
      {final int remainingSeconds,
      final bool isValidOtp,
      final String errorMessage,
      final bool isResendOtp}) = _$OtpTimerStateImpl;

  factory _OtpTimerState.fromJson(Map<String, dynamic> json) =
      _$OtpTimerStateImpl.fromJson;

  @override
  int get remainingSeconds;
  @override
  bool get isValidOtp;
  @override
  String get errorMessage;
  @override
  bool get isResendOtp;

  /// Create a copy of OtpTimerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtpTimerStateImplCopyWith<_$OtpTimerStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
