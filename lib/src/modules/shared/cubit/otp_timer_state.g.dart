// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_timer_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OtpTimerStateImpl _$$OtpTimerStateImplFromJson(Map<String, dynamic> json) =>
    _$OtpTimerStateImpl(
      remainingSeconds: (json['remainingSeconds'] as num?)?.toInt() ?? 300,
      isValidOtp: json['isValidOtp'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String? ?? '',
      isResendOtp: json['isResendOtp'] as bool? ?? false,
    );

Map<String, dynamic> _$$OtpTimerStateImplToJson(_$OtpTimerStateImpl instance) =>
    <String, dynamic>{
      'remainingSeconds': instance.remainingSeconds,
      'isValidOtp': instance.isValidOtp,
      'errorMessage': instance.errorMessage,
      'isResendOtp': instance.isResendOtp,
    };
