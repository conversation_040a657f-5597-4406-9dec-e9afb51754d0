// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'identifiable.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Identifiable _$IdentifiableFromJson(Map<String, dynamic> json) {
  return _Identifiable.fromJson(json);
}

/// @nodoc
mixin _$Identifiable {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;

  /// Serializes this Identifiable to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Identifiable
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentifiableCopyWith<Identifiable> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentifiableCopyWith<$Res> {
  factory $IdentifiableCopyWith(
          Identifiable value, $Res Function(Identifiable) then) =
      _$IdentifiableCopyWithImpl<$Res, Identifiable>;
  @useResult
  $Res call({int id, String name});
}

/// @nodoc
class _$IdentifiableCopyWithImpl<$Res, $Val extends Identifiable>
    implements $IdentifiableCopyWith<$Res> {
  _$IdentifiableCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Identifiable
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IdentifiableImplCopyWith<$Res>
    implements $IdentifiableCopyWith<$Res> {
  factory _$$IdentifiableImplCopyWith(
          _$IdentifiableImpl value, $Res Function(_$IdentifiableImpl) then) =
      __$$IdentifiableImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, String name});
}

/// @nodoc
class __$$IdentifiableImplCopyWithImpl<$Res>
    extends _$IdentifiableCopyWithImpl<$Res, _$IdentifiableImpl>
    implements _$$IdentifiableImplCopyWith<$Res> {
  __$$IdentifiableImplCopyWithImpl(
      _$IdentifiableImpl _value, $Res Function(_$IdentifiableImpl) _then)
      : super(_value, _then);

  /// Create a copy of Identifiable
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
  }) {
    return _then(_$IdentifiableImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IdentifiableImpl implements _Identifiable {
  _$IdentifiableImpl({this.id = 0, this.name = ""});

  factory _$IdentifiableImpl.fromJson(Map<String, dynamic> json) =>
      _$$IdentifiableImplFromJson(json);

  @override
  @JsonKey()
  final int id;
  @override
  @JsonKey()
  final String name;

  @override
  String toString() {
    return 'Identifiable(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentifiableImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name);

  /// Create a copy of Identifiable
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentifiableImplCopyWith<_$IdentifiableImpl> get copyWith =>
      __$$IdentifiableImplCopyWithImpl<_$IdentifiableImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IdentifiableImplToJson(
      this,
    );
  }
}

abstract class _Identifiable implements Identifiable {
  factory _Identifiable({final int id, final String name}) = _$IdentifiableImpl;

  factory _Identifiable.fromJson(Map<String, dynamic> json) =
      _$IdentifiableImpl.fromJson;

  @override
  int get id;
  @override
  String get name;

  /// Create a copy of Identifiable
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentifiableImplCopyWith<_$IdentifiableImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
