// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'country_selector_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CountrySelectorItemImpl _$$CountrySelectorItemImplFromJson(
        Map<String, dynamic> json) =>
    _$CountrySelectorItemImpl(
      name: json['name'] as String? ?? '',
      country: $enumDecodeNullable(_$CountryEnumMap, json['country']),
      isGlobal: json['isGlobal'] as bool? ?? false,
    );

Map<String, dynamic> _$$CountrySelectorItemImplToJson(
        _$CountrySelectorItemImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'country': _$CountryEnumMap[instance.country],
      'isGlobal': instance.isGlobal,
    };

const _$CountryEnumMap = {
  Country.INDONESIA: 'INDONESIA',
  Country.MALAYSIA: 'MALAYSIA',
  Country.SINGAPORE: 'SINGAPORE',
  Country.THAILAND: 'THAILAND',
  Country.GLOBAL: 'GLOBAL',
};
