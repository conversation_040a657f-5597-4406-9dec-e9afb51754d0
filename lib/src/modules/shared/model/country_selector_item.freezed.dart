// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'country_selector_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CountrySelectorItem _$CountrySelectorItemFromJson(Map<String, dynamic> json) {
  return _CountrySelectorItem.fromJson(json);
}

/// @nodoc
mixin _$CountrySelectorItem {
  String get name => throw _privateConstructorUsedError;
  Country? get country => throw _privateConstructorUsedError;
  bool get isGlobal => throw _privateConstructorUsedError;

  /// Serializes this CountrySelectorItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CountrySelectorItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CountrySelectorItemCopyWith<CountrySelectorItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CountrySelectorItemCopyWith<$Res> {
  factory $CountrySelectorItemCopyWith(
          CountrySelectorItem value, $Res Function(CountrySelectorItem) then) =
      _$CountrySelectorItemCopyWithImpl<$Res, CountrySelectorItem>;
  @useResult
  $Res call({String name, Country? country, bool isGlobal});
}

/// @nodoc
class _$CountrySelectorItemCopyWithImpl<$Res, $Val extends CountrySelectorItem>
    implements $CountrySelectorItemCopyWith<$Res> {
  _$CountrySelectorItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CountrySelectorItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? country = freezed,
    Object? isGlobal = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country?,
      isGlobal: null == isGlobal
          ? _value.isGlobal
          : isGlobal // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CountrySelectorItemImplCopyWith<$Res>
    implements $CountrySelectorItemCopyWith<$Res> {
  factory _$$CountrySelectorItemImplCopyWith(_$CountrySelectorItemImpl value,
          $Res Function(_$CountrySelectorItemImpl) then) =
      __$$CountrySelectorItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, Country? country, bool isGlobal});
}

/// @nodoc
class __$$CountrySelectorItemImplCopyWithImpl<$Res>
    extends _$CountrySelectorItemCopyWithImpl<$Res, _$CountrySelectorItemImpl>
    implements _$$CountrySelectorItemImplCopyWith<$Res> {
  __$$CountrySelectorItemImplCopyWithImpl(_$CountrySelectorItemImpl _value,
      $Res Function(_$CountrySelectorItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of CountrySelectorItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? country = freezed,
    Object? isGlobal = null,
  }) {
    return _then(_$CountrySelectorItemImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country?,
      isGlobal: null == isGlobal
          ? _value.isGlobal
          : isGlobal // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CountrySelectorItemImpl implements _CountrySelectorItem {
  _$CountrySelectorItemImpl(
      {this.name = '', this.country, this.isGlobal = false});

  factory _$CountrySelectorItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$CountrySelectorItemImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  final Country? country;
  @override
  @JsonKey()
  final bool isGlobal;

  @override
  String toString() {
    return 'CountrySelectorItem(name: $name, country: $country, isGlobal: $isGlobal)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CountrySelectorItemImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.isGlobal, isGlobal) ||
                other.isGlobal == isGlobal));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, country, isGlobal);

  /// Create a copy of CountrySelectorItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CountrySelectorItemImplCopyWith<_$CountrySelectorItemImpl> get copyWith =>
      __$$CountrySelectorItemImplCopyWithImpl<_$CountrySelectorItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CountrySelectorItemImplToJson(
      this,
    );
  }
}

abstract class _CountrySelectorItem implements CountrySelectorItem {
  factory _CountrySelectorItem(
      {final String name,
      final Country? country,
      final bool isGlobal}) = _$CountrySelectorItemImpl;

  factory _CountrySelectorItem.fromJson(Map<String, dynamic> json) =
      _$CountrySelectorItemImpl.fromJson;

  @override
  String get name;
  @override
  Country? get country;
  @override
  bool get isGlobal;

  /// Create a copy of CountrySelectorItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CountrySelectorItemImplCopyWith<_$CountrySelectorItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
