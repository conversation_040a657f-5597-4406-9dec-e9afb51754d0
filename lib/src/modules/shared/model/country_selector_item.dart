import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

part 'country_selector_item.freezed.dart';
part 'country_selector_item.g.dart';

@freezed
class CountrySelectorItem with _$CountrySelectorItem {
  factory CountrySelectorItem({
    @Default('') String name,
    Country? country,
    @Default(false) bool isGlobal,
  }) = _CountrySelectorItem;

  factory CountrySelectorItem.fromJson(Map<String, dynamic> json) =>
      _$CountrySelectorItemFromJson(json);
}
