// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'date_period.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DatePeriod _$DatePeriodFromJson(Map<String, dynamic> json) {
  return _DatePeriod.fromJson(json);
}

/// @nodoc
mixin _$DatePeriod {
  String get from => throw _privateConstructorUsedError;
  String get to => throw _privateConstructorUsedError;

  /// Serializes this DatePeriod to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DatePeriod
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DatePeriodCopyWith<DatePeriod> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DatePeriodCopyWith<$Res> {
  factory $DatePeriodCopyWith(
          DatePeriod value, $Res Function(DatePeriod) then) =
      _$DatePeriodCopyWithImpl<$Res, DatePeriod>;
  @useResult
  $Res call({String from, String to});
}

/// @nodoc
class _$DatePeriodCopyWithImpl<$Res, $Val extends DatePeriod>
    implements $DatePeriodCopyWith<$Res> {
  _$DatePeriodCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DatePeriod
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? from = null,
    Object? to = null,
  }) {
    return _then(_value.copyWith(
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DatePeriodImplCopyWith<$Res>
    implements $DatePeriodCopyWith<$Res> {
  factory _$$DatePeriodImplCopyWith(
          _$DatePeriodImpl value, $Res Function(_$DatePeriodImpl) then) =
      __$$DatePeriodImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String from, String to});
}

/// @nodoc
class __$$DatePeriodImplCopyWithImpl<$Res>
    extends _$DatePeriodCopyWithImpl<$Res, _$DatePeriodImpl>
    implements _$$DatePeriodImplCopyWith<$Res> {
  __$$DatePeriodImplCopyWithImpl(
      _$DatePeriodImpl _value, $Res Function(_$DatePeriodImpl) _then)
      : super(_value, _then);

  /// Create a copy of DatePeriod
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? from = null,
    Object? to = null,
  }) {
    return _then(_$DatePeriodImpl(
      from: null == from
          ? _value.from
          : from // ignore: cast_nullable_to_non_nullable
              as String,
      to: null == to
          ? _value.to
          : to // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DatePeriodImpl implements _DatePeriod {
  _$DatePeriodImpl({this.from = '', this.to = ''});

  factory _$DatePeriodImpl.fromJson(Map<String, dynamic> json) =>
      _$$DatePeriodImplFromJson(json);

  @override
  @JsonKey()
  final String from;
  @override
  @JsonKey()
  final String to;

  @override
  String toString() {
    return 'DatePeriod(from: $from, to: $to)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DatePeriodImpl &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, from, to);

  /// Create a copy of DatePeriod
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DatePeriodImplCopyWith<_$DatePeriodImpl> get copyWith =>
      __$$DatePeriodImplCopyWithImpl<_$DatePeriodImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DatePeriodImplToJson(
      this,
    );
  }
}

abstract class _DatePeriod implements DatePeriod {
  factory _DatePeriod({final String from, final String to}) = _$DatePeriodImpl;

  factory _DatePeriod.fromJson(Map<String, dynamic> json) =
      _$DatePeriodImpl.fromJson;

  @override
  String get from;
  @override
  String get to;

  /// Create a copy of DatePeriod
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DatePeriodImplCopyWith<_$DatePeriodImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
