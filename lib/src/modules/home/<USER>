import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/campaign/campaign_module.dart';
import 'package:koc_app/src/modules/home/<USER>/home_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/repository/home_repository.dart';
import 'package:koc_app/src/modules/home/<USER>/home_page.dart';
import 'package:koc_app/src/modules/home/<USER>/voucher_module.dart';
import 'package:koc_app/src/modules/notification/data/repository/notification_repository.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';

import '../report/data/repository/report_repository.dart';

class HomeModule extends Module {
  @override
  List<Module> get imports =>
      [SharedModule(), VoucherSharedModule(), CampaignSharedModule()];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addLazySingleton(HomeRepository.new);
    i.addLazySingleton(HomeCubit.new);
    i.addLazySingleton(NotificationRepository.new);
    i.addLazySingleton(ReportRepository.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.child('/', child: (i) => const HomePage());
    r.module('/voucher', module: VoucherModule());
  }
}
