// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HomeStateImpl _$$HomeStateImplFromJson(Map<String, dynamic> json) =>
    _$HomeStateImpl(
      promotedCampaigns: (json['promotedCampaigns'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      lastSevenDayPerformance: json['lastSevenDayPerformance'] == null
          ? null
          : ReportSummary.fromJson(
              json['lastSevenDayPerformance'] as Map<String, dynamic>),
      homeCarousel: (json['homeCarousel'] as List<dynamic>?)
              ?.map((e) => HomeCarousel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      campaigns: (json['campaigns'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      vouchers: (json['vouchers'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      superPoints: (json['superPoints'] as List<dynamic>?)
              ?.map((e) => SuperPoint.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      showChart: json['showChart'] as bool? ?? false,
      clicks: (json['clicks'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [0, 0, 0, 0, 0, 0, 0],
      conversions: (json['conversions'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [0, 0, 0, 0, 0, 0, 0],
      academyLink: json['academyLink'] as String? ?? '',
      inpageLink: json['inpageLink'] as String? ?? '',
      superPointLink: json['superPointLink'] as String? ?? '',
      errorMessage: json['errorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$$HomeStateImplToJson(_$HomeStateImpl instance) =>
    <String, dynamic>{
      'promotedCampaigns': instance.promotedCampaigns,
      'lastSevenDayPerformance': instance.lastSevenDayPerformance,
      'homeCarousel': instance.homeCarousel,
      'campaigns': instance.campaigns,
      'vouchers': instance.vouchers,
      'superPoints': instance.superPoints,
      'showChart': instance.showChart,
      'clicks': instance.clicks,
      'conversions': instance.conversions,
      'academyLink': instance.academyLink,
      'inpageLink': instance.inpageLink,
      'superPointLink': instance.superPointLink,
      'errorMessage': instance.errorMessage,
    };

_$SuperPointImpl _$$SuperPointImplFromJson(Map<String, dynamic> json) =>
    _$SuperPointImpl(
      name: json['name'] as String? ?? "",
      imageUrl: json['imageUrl'] as String? ?? "",
      point: (json['point'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$SuperPointImplToJson(_$SuperPointImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'point': instance.point,
    };

_$HomeCarouselImpl _$$HomeCarouselImplFromJson(Map<String, dynamic> json) =>
    _$HomeCarouselImpl(
      image: json['image'] as String? ?? "",
      targetUrl: json['targetUrl'] as String? ?? "",
    );

Map<String, dynamic> _$$HomeCarouselImplToJson(_$HomeCarouselImpl instance) =>
    <String, dynamic>{
      'image': instance.image,
      'targetUrl': instance.targetUrl,
    };
