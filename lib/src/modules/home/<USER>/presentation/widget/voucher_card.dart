import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/mixin/voucher_detail_mixin.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:share_plus/share_plus.dart';

class VoucherCard extends StatelessWidget with VoucherDetailMixin {
  final Voucher voucher;
  final double? width;
  const VoucherCard(this.voucher, {this.width, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showVoucherDetailPage(context, voucher);
      },
      child: Container(
        height: 180.r,
        width: width ?? ScreenUtil().screenWidth - 32.r,
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.r), color: Colors.grey[200]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SizedBox(
                    height: 100.r,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          maxLines: 2,
                          voucher.title,
                          style: Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.grey[700]),
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4.r),
                        Text(
                          maxLines: 2,
                          voucher.campaignName,
                          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
                          overflow: TextOverflow.ellipsis,
                        )
                      ],
                    ),
                  ),
                ),
                SizedBox(width: 16.r),
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedImageWithPlaceholder(
                    width: 100.r,
                    height: 100.r,
                    imageUrl: voucher.imageUrl,
                    fit: BoxFit.cover,
                  ),
                )
              ],
            ),
            SizedBox(height: 12.r),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 36.r,
                    padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 8.r),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.r),
                      color: Colors.white,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            voucher.code,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ),
                        Text(
                          ' • ',
                          style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.grey[700]),
                        ),
                        Text(
                          DateTime.parse(voucher.validDate).toDateMonthYear(),
                          style: Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.red[900]),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: 8.r),
                SizedBox(
                  width: 80.r,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildVoucherButton(Icons.copy, () {
                        final currentContext = context;
                        Clipboard.setData(ClipboardData(text: voucher.code)).then((_) {
                          if (currentContext.mounted) {
                            currentContext.showSnackBar('Code copied');
                          }
                        });
                      }),
                      _buildVoucherButton(Icons.share_outlined, () {
                        Share.share(voucher.code);
                      }),
                    ],
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildVoucherButton(IconData icon, VoidCallback action) {
    return GestureDetector(
      onTap: action,
      child: CircleAvatar(
        backgroundColor: Colors.white,
        radius: 18.r,
        child: Icon(
          icon,
          size: 20.r,
          color: const Color(0xFF464646),
        ),
      ),
    );
  }
}
