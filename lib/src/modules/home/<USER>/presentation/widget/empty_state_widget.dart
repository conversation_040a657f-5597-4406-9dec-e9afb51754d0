import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EmptyStateWidget extends StatelessWidget {
  final Widget? image;
  final String title;
  final String? description;
  final String? buttonText;
  final VoidCallback? onButtonPressed;

  const EmptyStateWidget({
    super.key,
    this.image,
    this.title = 'No results found',
    this.description = 'Please try another search',
    this.buttonText = 'Clear filters',
    this.onButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    final Widget defaultImage = SvgPicture.asset(
      'assets/images/mascot.svg',
      width: 100.r,
      height: 150.r,
    );
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          (image ?? defaultImage),
          SizedBox(height: 16.r),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          if (description != null && description!.isNotEmpty) ...[
            SizedBox(height: 8.r),
            Text(
              description!,
              style: Theme.of(context).textTheme.labelLarge,
              textAlign: TextAlign.center,
            ),
          ],
          if (buttonText != null && buttonText!.isNotEmpty && onButtonPressed != null) ...[
            SizedBox(height: 16.r),
            SizedBox(
              width: 120.r,
              child: ElevatedButton(
                onPressed: onButtonPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).elevatedButtonTheme.style?.backgroundColor?.resolve({}),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 8.r),
                ),
                child: Text(
                  buttonText!,
                  style: Theme.of(context).elevatedButtonTheme.style?.textStyle?.resolve({}) ??
                      Theme.of(context).textTheme.labelLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
