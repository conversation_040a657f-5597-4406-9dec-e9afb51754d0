import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_state.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/voucher_card.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/common_loading.dart';
import 'package:koc_app/src/shared/widgets/common_tab.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

import '../../../../../generated/locale_keys.g.dart';
import '../../../../shared/services/shared_preferences_service.dart';

class VoucherPage extends StatefulWidget {
  const VoucherPage({super.key});

  @override
  State<VoucherPage> createState() => _VoucherPageState();
}

class _VoucherPageState extends BasePageState<VoucherPage, VoucherCubit>
    with SingleTickerProviderStateMixin, CommonMixin {
  late TabController _tabController;

  @override
  void initState() {
    print('🔄 VoucherPage initState: cubit instance = ${cubit.hashCode}');
    print('🔄 VoucherPage initState: current allVouchers.length = ${cubit.state.allVouchers.length}');
    loadVouchers();
    _tabController = TabController(length: 7, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        cubit.changeIndex(_tabController.index);
        _loadTabContent(_tabController.index);
      }
    });
    super.initState();
  }

  Future<void> loadVouchers() async {
    await _loadAllVouchers();
    await cubit.getVoucherCategories();
  }

  Future<void> _loadAllVouchers() async {
    final siteId = await Modular.get<SharedPreferencesService>().getCurrentSiteId();
    if (siteId == null) {
      return;
    }
    await cubit.findVouchers(FindVouchersRequest(siteId: siteId, type: 'ALL'));
  }

  Future<void> _loadTabContent(int tabIndex) async {
    final siteId = await Modular.get<SharedPreferencesService>().getCurrentSiteId();

    try {
      switch (tabIndex) {
        case 0:
          break;
        case 1:
          await cubit
              .findVouchers(FindVouchersRequest(siteId: siteId!, category: VoucherCategoryType.travelHotel.categoryId));
          break;
        case 2:
          await cubit
              .findVouchers(FindVouchersRequest(siteId: siteId!, category: VoucherCategoryType.electronics.categoryId));
          break;
        case 3:
          await cubit
              .findVouchers(FindVouchersRequest(siteId: siteId!, category: VoucherCategoryType.fashion.categoryId));
          break;
        case 4:
          await cubit.findVouchers(
              FindVouchersRequest(siteId: siteId!, category: VoucherCategoryType.beautyHealth.categoryId));
          break;
        case 5:
          await cubit
              .findVouchers(FindVouchersRequest(siteId: siteId!, category: VoucherCategoryType.homeLiving.categoryId));
          break;
        case 6:
          await cubit
              .findVouchers(FindVouchersRequest(siteId: siteId!, category: VoucherCategoryType.foodGrocery.categoryId));
          break;
      }
    } catch (e) {
      debugPrint('Error loading tab content: $e');
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: buildSiteSelectionTitle(context, 'Voucher code', onTap: loadVouchers),
        customAction: IconButton(
          onPressed: () {
            Modular.to.pushNamed("/home/<USER>/search");
          },
          icon: Icon(
            Icons.search,
            size: 25.r,
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildTabBar(),
        _buildTabBarView(),
      ],
    );
  }

  Widget _buildTabBar() {
    return BlocBuilder<VoucherCubit, VoucherState>(
      bloc: cubit,
      builder: (context, state) {
        int getPromoCount(String categoryId) {
          final category = state.categories.firstWhere(
            (cat) => cat.categoryId == categoryId,
            orElse: () => const VoucherCategory(categoryId: '', numberOfPromos: 0),
          );
          return category.numberOfPromos;
        }

        int allCount = state.allVouchers.length;

        return TabBar(
          controller: _tabController,
          isScrollable: true,
          labelStyle: Theme.of(context).textTheme.labelLarge,
          tabAlignment: TabAlignment.start,
          indicatorColor: Colors.amber,
          labelColor: Colors.amber,
          tabs: [
            CommonTab('All', count: allCount),
            CommonTab(LocaleKeys.voucherCategory01.tr(),
                count: getPromoCount(VoucherCategoryType.travelHotel.categoryId)),
            CommonTab(LocaleKeys.voucherCategory02.tr(),
                count: getPromoCount(VoucherCategoryType.electronics.categoryId)),
            CommonTab(LocaleKeys.voucherCategory03.tr(), count: getPromoCount(VoucherCategoryType.fashion.categoryId)),
            CommonTab(LocaleKeys.voucherCategory04.tr(),
                count: getPromoCount(VoucherCategoryType.beautyHealth.categoryId)),
            CommonTab(LocaleKeys.voucherCategory05.tr(),
                count: getPromoCount(VoucherCategoryType.homeLiving.categoryId)),
            CommonTab(LocaleKeys.voucherCategory06.tr(),
                count: getPromoCount(VoucherCategoryType.foodGrocery.categoryId)),
          ],
        );
      },
    );
  }

  Widget _buildTabBarView() {
    return BlocBuilder<VoucherCubit, VoucherState>(
        bloc: cubit,
        builder: (context, state) {
          print('🔄 _buildTabBarView: Rebuilding with allVouchers.length = ${state.allVouchers.length}');
          return Expanded(
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: IndexedStack(index: state.tabIndex, children: [
                _buildVouchersWithLoading(
                    state.allVouchers, state.isDataLoaded, state.isPullToRefresh, state.isSiteSwitching),
                _buildVouchersWithLoading(state.travelAndHotels, !state.loadingCampaignVouchers, state.isPullToRefresh,
                    state.isSiteSwitching),
                _buildVouchersWithLoading(
                    state.electronics, !state.loadingCampaignVouchers, state.isPullToRefresh, state.isSiteSwitching),
                _buildVouchersWithLoading(
                    state.fashion, !state.loadingCampaignVouchers, state.isPullToRefresh, state.isSiteSwitching),
                _buildVouchersWithLoading(state.beautyAndHealth, !state.loadingCampaignVouchers, state.isPullToRefresh,
                    state.isSiteSwitching),
                _buildVouchersWithLoading(
                    state.homeAndLiving, !state.loadingCampaignVouchers, state.isPullToRefresh, state.isSiteSwitching),
                _buildVouchersWithLoading(
                    state.foodAndGrocery, !state.loadingCampaignVouchers, state.isPullToRefresh, state.isSiteSwitching),
              ]),
            ),
          );
        });
  }

  Widget _buildVouchersWithLoading(List<Voucher> vouchers, bool isLoaded, bool isPullToRefresh, bool isSiteSwitching) {
    print('🔄 _buildVouchersWithLoading: vouchers.length = ${vouchers.length}, isLoaded = $isLoaded');

    final shouldShowLoading = !isLoaded && !isPullToRefresh && !isSiteSwitching;

    if (shouldShowLoading) {
      return const CommonLoading();
    }

    if (vouchers.isEmpty && isLoaded) {
      return PullToRefreshWrapper(
        onRefresh: () => cubit.pullToRefresh(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            child: Center(
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 48.r),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(Icons.local_offer_outlined, color: Colors.grey, size: 48.0),
                    SizedBox(height: 12.r),
                    const Text(
                      'No coupons available',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    }

    return _buildVouchers(vouchers);
  }

  Widget _buildVouchers(List<Voucher> vouchers) {
    return PullToRefreshWrapper(
      onRefresh: () => cubit.pullToRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(spacing: 8.r, children: vouchers.map((voucher) => VoucherCard(voucher)).toList()),
      ),
    );
  }
}
