import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';

import '../../report/data/model/performance_report_data.dart';

part 'home_state.freezed.dart';
part 'home_state.g.dart';

@freezed
class HomeState extends BaseCubitState with _$HomeState {
  factory HomeState({
    @Default([]) List<DefaultCampaignSummary> promotedCampaigns,
    ReportSummary? lastSevenDayPerformance,
    @Default([]) List<HomeCarousel> homeCarousel,
    @Default([]) List<DefaultCampaignSummary> campaigns,
    @Default([]) List<Voucher> vouchers,
    @Default([]) List<SuperPoint> superPoints,
    @Default(false) bool showChart,
    @Default([0, 0, 0, 0, 0, 0, 0]) List<int> clicks,
    @Default([0, 0, 0, 0, 0, 0, 0]) List<int> conversions,
    @Default('') String academyLink,
    @Default('') String inpageLink,
    @Default('') String superPointLink,
    @Default('') String errorMessage,
  }) = _HomeState;

  factory HomeState.fromJson(Map<String, Object?> json) => _$HomeStateFromJson(json);
}

@freezed
class SuperPoint with _$SuperPoint {
  factory SuperPoint({
    @Default("") String name,
    @Default("") String imageUrl,
    @Default(0) int point,
  }) = _SuperPoint;

  factory SuperPoint.fromJson(Map<String, Object?> json) => _$SuperPointFromJson(json);
}

@freezed
class HomeCarousel with _$HomeCarousel {
  factory HomeCarousel({
    @Default("") String image,
    @Default("") String targetUrl,
  }) = _HomeCarousel;

  factory HomeCarousel.fromJson(Map<String, Object?> json) => _$HomeCarouselFromJson(json);
}
