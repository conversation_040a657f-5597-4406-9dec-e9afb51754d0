import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/shared/model/identifiable.dart';

part 'voucher_search_state.freezed.dart';
part 'voucher_search_state.g.dart';

@freezed
class VoucherSearchState extends BaseCubitState with _$VoucherSearchState {
  factory VoucherSearchState({
    @Default([]) List<Identifiable> campaigns,
    Identifiable? selectedCampaign,
    @Default("") String keyword,
    @Default([]) List<Voucher> searchResult,
    @Default(false) bool isSearching,
    @Default("") String errorMessage,
  }) = _VoucherSearchState;

  factory VoucherSearchState.fromJson(Map<String, Object?> json) =>
      _$VoucherSearchStateFromJson(json);
}
