import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';

part 'voucher_state.freezed.dart';
part 'voucher_state.g.dart';

@freezed
class VoucherState extends BaseCubitState with _$VoucherState {
  factory VoucherState(
      {@Default(0) int tabIndex,
      @Default([]) List<Voucher> travelAndHotels,
      @Default([]) List<Voucher> electronics,
      @Default([]) List<Voucher> fashion,
      @Default([]) List<Voucher> beautyAndHealth,
      @Default([]) List<Voucher> homeAndLiving,
      @Default([]) List<Voucher> foodAndGrocery,
      @Default([]) List<Voucher> allVouchers,
      @Default([]) List<VoucherCategory> categories,
      @Default([]) List<Voucher> campaignVouchers,
      @Default(false) bool isDataLoaded,
      @Default(false) bool loadingCategories,
      @Default(false) bool loadingCampaignVouchers,
      @Default(false) bool isPullToRefresh,
      @Default(false) bool isSiteSwitching,
      Voucher? currentVoucherDetail,
      @Default(false) bool loadingVoucherDetail,
      @Default("") String errorMessage}) = _VoucherState;

  factory VoucherState.fromJson(Map<String, Object?> json) => _$VoucherStateFromJson(json);
}
