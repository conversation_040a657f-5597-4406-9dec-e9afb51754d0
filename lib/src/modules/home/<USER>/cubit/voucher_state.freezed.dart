// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voucher_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VoucherState _$VoucherStateFromJson(Map<String, dynamic> json) {
  return _VoucherState.fromJson(json);
}

/// @nodoc
mixin _$VoucherState {
  int get tabIndex => throw _privateConstructorUsedError;
  List<Voucher> get travelAndHotels => throw _privateConstructorUsedError;
  List<Voucher> get electronics => throw _privateConstructorUsedError;
  List<Voucher> get fashion => throw _privateConstructorUsedError;
  List<Voucher> get beautyAndHealth => throw _privateConstructorUsedError;
  List<Voucher> get homeAndLiving => throw _privateConstructorUsedError;
  List<Voucher> get foodAndGrocery => throw _privateConstructorUsedError;
  List<Voucher> get allVouchers => throw _privateConstructorUsedError;
  List<VoucherCategory> get categories => throw _privateConstructorUsedError;
  List<Voucher> get campaignVouchers => throw _privateConstructorUsedError;
  bool get isDataLoaded => throw _privateConstructorUsedError;
  bool get loadingCategories => throw _privateConstructorUsedError;
  bool get loadingCampaignVouchers => throw _privateConstructorUsedError;
  bool get isPullToRefresh => throw _privateConstructorUsedError;
  Voucher? get currentVoucherDetail => throw _privateConstructorUsedError;
  bool get loadingVoucherDetail => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this VoucherState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoucherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoucherStateCopyWith<VoucherState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoucherStateCopyWith<$Res> {
  factory $VoucherStateCopyWith(
          VoucherState value, $Res Function(VoucherState) then) =
      _$VoucherStateCopyWithImpl<$Res, VoucherState>;
  @useResult
  $Res call(
      {int tabIndex,
      List<Voucher> travelAndHotels,
      List<Voucher> electronics,
      List<Voucher> fashion,
      List<Voucher> beautyAndHealth,
      List<Voucher> homeAndLiving,
      List<Voucher> foodAndGrocery,
      List<Voucher> allVouchers,
      List<VoucherCategory> categories,
      List<Voucher> campaignVouchers,
      bool isDataLoaded,
      bool loadingCategories,
      bool loadingCampaignVouchers,
      bool isPullToRefresh,
      Voucher? currentVoucherDetail,
      bool loadingVoucherDetail,
      String errorMessage});

  $VoucherCopyWith<$Res>? get currentVoucherDetail;
}

/// @nodoc
class _$VoucherStateCopyWithImpl<$Res, $Val extends VoucherState>
    implements $VoucherStateCopyWith<$Res> {
  _$VoucherStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoucherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabIndex = null,
    Object? travelAndHotels = null,
    Object? electronics = null,
    Object? fashion = null,
    Object? beautyAndHealth = null,
    Object? homeAndLiving = null,
    Object? foodAndGrocery = null,
    Object? allVouchers = null,
    Object? categories = null,
    Object? campaignVouchers = null,
    Object? isDataLoaded = null,
    Object? loadingCategories = null,
    Object? loadingCampaignVouchers = null,
    Object? isPullToRefresh = null,
    Object? currentVoucherDetail = freezed,
    Object? loadingVoucherDetail = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      tabIndex: null == tabIndex
          ? _value.tabIndex
          : tabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      travelAndHotels: null == travelAndHotels
          ? _value.travelAndHotels
          : travelAndHotels // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      electronics: null == electronics
          ? _value.electronics
          : electronics // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      fashion: null == fashion
          ? _value.fashion
          : fashion // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      beautyAndHealth: null == beautyAndHealth
          ? _value.beautyAndHealth
          : beautyAndHealth // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      homeAndLiving: null == homeAndLiving
          ? _value.homeAndLiving
          : homeAndLiving // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      foodAndGrocery: null == foodAndGrocery
          ? _value.foodAndGrocery
          : foodAndGrocery // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      allVouchers: null == allVouchers
          ? _value.allVouchers
          : allVouchers // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      categories: null == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<VoucherCategory>,
      campaignVouchers: null == campaignVouchers
          ? _value.campaignVouchers
          : campaignVouchers // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      isDataLoaded: null == isDataLoaded
          ? _value.isDataLoaded
          : isDataLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      loadingCategories: null == loadingCategories
          ? _value.loadingCategories
          : loadingCategories // ignore: cast_nullable_to_non_nullable
              as bool,
      loadingCampaignVouchers: null == loadingCampaignVouchers
          ? _value.loadingCampaignVouchers
          : loadingCampaignVouchers // ignore: cast_nullable_to_non_nullable
              as bool,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      currentVoucherDetail: freezed == currentVoucherDetail
          ? _value.currentVoucherDetail
          : currentVoucherDetail // ignore: cast_nullable_to_non_nullable
              as Voucher?,
      loadingVoucherDetail: null == loadingVoucherDetail
          ? _value.loadingVoucherDetail
          : loadingVoucherDetail // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of VoucherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VoucherCopyWith<$Res>? get currentVoucherDetail {
    if (_value.currentVoucherDetail == null) {
      return null;
    }

    return $VoucherCopyWith<$Res>(_value.currentVoucherDetail!, (value) {
      return _then(_value.copyWith(currentVoucherDetail: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VoucherStateImplCopyWith<$Res>
    implements $VoucherStateCopyWith<$Res> {
  factory _$$VoucherStateImplCopyWith(
          _$VoucherStateImpl value, $Res Function(_$VoucherStateImpl) then) =
      __$$VoucherStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int tabIndex,
      List<Voucher> travelAndHotels,
      List<Voucher> electronics,
      List<Voucher> fashion,
      List<Voucher> beautyAndHealth,
      List<Voucher> homeAndLiving,
      List<Voucher> foodAndGrocery,
      List<Voucher> allVouchers,
      List<VoucherCategory> categories,
      List<Voucher> campaignVouchers,
      bool isDataLoaded,
      bool loadingCategories,
      bool loadingCampaignVouchers,
      bool isPullToRefresh,
      Voucher? currentVoucherDetail,
      bool loadingVoucherDetail,
      String errorMessage});

  @override
  $VoucherCopyWith<$Res>? get currentVoucherDetail;
}

/// @nodoc
class __$$VoucherStateImplCopyWithImpl<$Res>
    extends _$VoucherStateCopyWithImpl<$Res, _$VoucherStateImpl>
    implements _$$VoucherStateImplCopyWith<$Res> {
  __$$VoucherStateImplCopyWithImpl(
      _$VoucherStateImpl _value, $Res Function(_$VoucherStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoucherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabIndex = null,
    Object? travelAndHotels = null,
    Object? electronics = null,
    Object? fashion = null,
    Object? beautyAndHealth = null,
    Object? homeAndLiving = null,
    Object? foodAndGrocery = null,
    Object? allVouchers = null,
    Object? categories = null,
    Object? campaignVouchers = null,
    Object? isDataLoaded = null,
    Object? loadingCategories = null,
    Object? loadingCampaignVouchers = null,
    Object? isPullToRefresh = null,
    Object? currentVoucherDetail = freezed,
    Object? loadingVoucherDetail = null,
    Object? errorMessage = null,
  }) {
    return _then(_$VoucherStateImpl(
      tabIndex: null == tabIndex
          ? _value.tabIndex
          : tabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      travelAndHotels: null == travelAndHotels
          ? _value._travelAndHotels
          : travelAndHotels // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      electronics: null == electronics
          ? _value._electronics
          : electronics // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      fashion: null == fashion
          ? _value._fashion
          : fashion // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      beautyAndHealth: null == beautyAndHealth
          ? _value._beautyAndHealth
          : beautyAndHealth // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      homeAndLiving: null == homeAndLiving
          ? _value._homeAndLiving
          : homeAndLiving // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      foodAndGrocery: null == foodAndGrocery
          ? _value._foodAndGrocery
          : foodAndGrocery // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      allVouchers: null == allVouchers
          ? _value._allVouchers
          : allVouchers // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      categories: null == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<VoucherCategory>,
      campaignVouchers: null == campaignVouchers
          ? _value._campaignVouchers
          : campaignVouchers // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      isDataLoaded: null == isDataLoaded
          ? _value.isDataLoaded
          : isDataLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      loadingCategories: null == loadingCategories
          ? _value.loadingCategories
          : loadingCategories // ignore: cast_nullable_to_non_nullable
              as bool,
      loadingCampaignVouchers: null == loadingCampaignVouchers
          ? _value.loadingCampaignVouchers
          : loadingCampaignVouchers // ignore: cast_nullable_to_non_nullable
              as bool,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      currentVoucherDetail: freezed == currentVoucherDetail
          ? _value.currentVoucherDetail
          : currentVoucherDetail // ignore: cast_nullable_to_non_nullable
              as Voucher?,
      loadingVoucherDetail: null == loadingVoucherDetail
          ? _value.loadingVoucherDetail
          : loadingVoucherDetail // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoucherStateImpl implements _VoucherState {
  _$VoucherStateImpl(
      {this.tabIndex = 0,
      final List<Voucher> travelAndHotels = const [],
      final List<Voucher> electronics = const [],
      final List<Voucher> fashion = const [],
      final List<Voucher> beautyAndHealth = const [],
      final List<Voucher> homeAndLiving = const [],
      final List<Voucher> foodAndGrocery = const [],
      final List<Voucher> allVouchers = const [],
      final List<VoucherCategory> categories = const [],
      final List<Voucher> campaignVouchers = const [],
      this.isDataLoaded = false,
      this.loadingCategories = false,
      this.loadingCampaignVouchers = false,
      this.isPullToRefresh = false,
      this.currentVoucherDetail,
      this.loadingVoucherDetail = false,
      this.errorMessage = ""})
      : _travelAndHotels = travelAndHotels,
        _electronics = electronics,
        _fashion = fashion,
        _beautyAndHealth = beautyAndHealth,
        _homeAndLiving = homeAndLiving,
        _foodAndGrocery = foodAndGrocery,
        _allVouchers = allVouchers,
        _categories = categories,
        _campaignVouchers = campaignVouchers;

  factory _$VoucherStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoucherStateImplFromJson(json);

  @override
  @JsonKey()
  final int tabIndex;
  final List<Voucher> _travelAndHotels;
  @override
  @JsonKey()
  List<Voucher> get travelAndHotels {
    if (_travelAndHotels is EqualUnmodifiableListView) return _travelAndHotels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_travelAndHotels);
  }

  final List<Voucher> _electronics;
  @override
  @JsonKey()
  List<Voucher> get electronics {
    if (_electronics is EqualUnmodifiableListView) return _electronics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_electronics);
  }

  final List<Voucher> _fashion;
  @override
  @JsonKey()
  List<Voucher> get fashion {
    if (_fashion is EqualUnmodifiableListView) return _fashion;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fashion);
  }

  final List<Voucher> _beautyAndHealth;
  @override
  @JsonKey()
  List<Voucher> get beautyAndHealth {
    if (_beautyAndHealth is EqualUnmodifiableListView) return _beautyAndHealth;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_beautyAndHealth);
  }

  final List<Voucher> _homeAndLiving;
  @override
  @JsonKey()
  List<Voucher> get homeAndLiving {
    if (_homeAndLiving is EqualUnmodifiableListView) return _homeAndLiving;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_homeAndLiving);
  }

  final List<Voucher> _foodAndGrocery;
  @override
  @JsonKey()
  List<Voucher> get foodAndGrocery {
    if (_foodAndGrocery is EqualUnmodifiableListView) return _foodAndGrocery;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_foodAndGrocery);
  }

  final List<Voucher> _allVouchers;
  @override
  @JsonKey()
  List<Voucher> get allVouchers {
    if (_allVouchers is EqualUnmodifiableListView) return _allVouchers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allVouchers);
  }

  final List<VoucherCategory> _categories;
  @override
  @JsonKey()
  List<VoucherCategory> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  final List<Voucher> _campaignVouchers;
  @override
  @JsonKey()
  List<Voucher> get campaignVouchers {
    if (_campaignVouchers is EqualUnmodifiableListView)
      return _campaignVouchers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaignVouchers);
  }

  @override
  @JsonKey()
  final bool isDataLoaded;
  @override
  @JsonKey()
  final bool loadingCategories;
  @override
  @JsonKey()
  final bool loadingCampaignVouchers;
  @override
  @JsonKey()
  final bool isPullToRefresh;
  @override
  final Voucher? currentVoucherDetail;
  @override
  @JsonKey()
  final bool loadingVoucherDetail;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'VoucherState(tabIndex: $tabIndex, travelAndHotels: $travelAndHotels, electronics: $electronics, fashion: $fashion, beautyAndHealth: $beautyAndHealth, homeAndLiving: $homeAndLiving, foodAndGrocery: $foodAndGrocery, allVouchers: $allVouchers, categories: $categories, campaignVouchers: $campaignVouchers, isDataLoaded: $isDataLoaded, loadingCategories: $loadingCategories, loadingCampaignVouchers: $loadingCampaignVouchers, isPullToRefresh: $isPullToRefresh, currentVoucherDetail: $currentVoucherDetail, loadingVoucherDetail: $loadingVoucherDetail, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoucherStateImpl &&
            (identical(other.tabIndex, tabIndex) ||
                other.tabIndex == tabIndex) &&
            const DeepCollectionEquality()
                .equals(other._travelAndHotels, _travelAndHotels) &&
            const DeepCollectionEquality()
                .equals(other._electronics, _electronics) &&
            const DeepCollectionEquality().equals(other._fashion, _fashion) &&
            const DeepCollectionEquality()
                .equals(other._beautyAndHealth, _beautyAndHealth) &&
            const DeepCollectionEquality()
                .equals(other._homeAndLiving, _homeAndLiving) &&
            const DeepCollectionEquality()
                .equals(other._foodAndGrocery, _foodAndGrocery) &&
            const DeepCollectionEquality()
                .equals(other._allVouchers, _allVouchers) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._campaignVouchers, _campaignVouchers) &&
            (identical(other.isDataLoaded, isDataLoaded) ||
                other.isDataLoaded == isDataLoaded) &&
            (identical(other.loadingCategories, loadingCategories) ||
                other.loadingCategories == loadingCategories) &&
            (identical(
                    other.loadingCampaignVouchers, loadingCampaignVouchers) ||
                other.loadingCampaignVouchers == loadingCampaignVouchers) &&
            (identical(other.isPullToRefresh, isPullToRefresh) ||
                other.isPullToRefresh == isPullToRefresh) &&
            (identical(other.currentVoucherDetail, currentVoucherDetail) ||
                other.currentVoucherDetail == currentVoucherDetail) &&
            (identical(other.loadingVoucherDetail, loadingVoucherDetail) ||
                other.loadingVoucherDetail == loadingVoucherDetail) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      tabIndex,
      const DeepCollectionEquality().hash(_travelAndHotels),
      const DeepCollectionEquality().hash(_electronics),
      const DeepCollectionEquality().hash(_fashion),
      const DeepCollectionEquality().hash(_beautyAndHealth),
      const DeepCollectionEquality().hash(_homeAndLiving),
      const DeepCollectionEquality().hash(_foodAndGrocery),
      const DeepCollectionEquality().hash(_allVouchers),
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_campaignVouchers),
      isDataLoaded,
      loadingCategories,
      loadingCampaignVouchers,
      isPullToRefresh,
      currentVoucherDetail,
      loadingVoucherDetail,
      errorMessage);

  /// Create a copy of VoucherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoucherStateImplCopyWith<_$VoucherStateImpl> get copyWith =>
      __$$VoucherStateImplCopyWithImpl<_$VoucherStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoucherStateImplToJson(
      this,
    );
  }
}

abstract class _VoucherState implements VoucherState {
  factory _VoucherState(
      {final int tabIndex,
      final List<Voucher> travelAndHotels,
      final List<Voucher> electronics,
      final List<Voucher> fashion,
      final List<Voucher> beautyAndHealth,
      final List<Voucher> homeAndLiving,
      final List<Voucher> foodAndGrocery,
      final List<Voucher> allVouchers,
      final List<VoucherCategory> categories,
      final List<Voucher> campaignVouchers,
      final bool isDataLoaded,
      final bool loadingCategories,
      final bool loadingCampaignVouchers,
      final bool isPullToRefresh,
      final Voucher? currentVoucherDetail,
      final bool loadingVoucherDetail,
      final String errorMessage}) = _$VoucherStateImpl;

  factory _VoucherState.fromJson(Map<String, dynamic> json) =
      _$VoucherStateImpl.fromJson;

  @override
  int get tabIndex;
  @override
  List<Voucher> get travelAndHotels;
  @override
  List<Voucher> get electronics;
  @override
  List<Voucher> get fashion;
  @override
  List<Voucher> get beautyAndHealth;
  @override
  List<Voucher> get homeAndLiving;
  @override
  List<Voucher> get foodAndGrocery;
  @override
  List<Voucher> get allVouchers;
  @override
  List<VoucherCategory> get categories;
  @override
  List<Voucher> get campaignVouchers;
  @override
  bool get isDataLoaded;
  @override
  bool get loadingCategories;
  @override
  bool get loadingCampaignVouchers;
  @override
  bool get isPullToRefresh;
  @override
  Voucher? get currentVoucherDetail;
  @override
  bool get loadingVoucherDetail;
  @override
  String get errorMessage;

  /// Create a copy of VoucherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoucherStateImplCopyWith<_$VoucherStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
