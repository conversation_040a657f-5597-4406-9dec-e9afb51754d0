// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voucher_search_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoucherSearchStateImpl _$$VoucherSearchStateImplFromJson(
        Map<String, dynamic> json) =>
    _$VoucherSearchStateImpl(
      campaigns: (json['campaigns'] as List<dynamic>?)
              ?.map((e) => Identifiable.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      selectedCampaign: json['selectedCampaign'] == null
          ? null
          : Identifiable.fromJson(
              json['selectedCampaign'] as Map<String, dynamic>),
      keyword: json['keyword'] as String? ?? "",
      searchResult: (json['searchResult'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isSearching: json['isSearching'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String? ?? "",
    );

Map<String, dynamic> _$$VoucherSearchStateImplToJson(
        _$VoucherSearchStateImpl instance) =>
    <String, dynamic>{
      'campaigns': instance.campaigns,
      'selectedCampaign': instance.selectedCampaign,
      'keyword': instance.keyword,
      'searchResult': instance.searchResult,
      'isSearching': instance.isSearching,
      'errorMessage': instance.errorMessage,
    };
