import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_search_state.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/data/repository/voucher_repository.dart';
import 'package:koc_app/src/modules/shared/model/identifiable.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class VoucherSearchCubit extends BaseCubit<VoucherSearchState> {
  final CampaignRepository campaignRepository;
  final VoucherRepository voucherRepository;
  VoucherSearchCubit(this.campaignRepository, this.voucherRepository) : super(VoucherSearchState());

  Future<void> findCampaigns() async {
    try {
      showLoading();
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      if (siteId == null) {
        emit(state.copyWith(errorMessage: "Site ID not found"));
        hideLoading();
        return;
      }

      final campaigns = await campaignRepository.findPromotedCampaigns(siteId);

      if (campaigns != null && campaigns is List) {
        final identifiables = campaigns
            .map((campaign) =>
                Identifiable(id: campaign['value'], name: campaign['name'] ?? 'Unknown Campaign'))
            .toList();

        emit(state.copyWith(campaigns: identifiables));
      } else {
        emit(state.copyWith(campaigns: []));
      }

      hideLoading();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      hideLoading();
    }
  }

  Future<void> findVouchers() async {
    try {
      emit(state.copyWith(isSearching: true));
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      if (siteId == null) {
        emit(state.copyWith(isSearching: false, errorMessage: "Site ID not found"));
        return;
      }

      FindVouchersRequest request = FindVouchersRequest(
        siteId: siteId,
        startIndex: 1,
        size: 100,
      );

      if (state.selectedCampaign != null) {
        request = request.copyWith(campaignId: state.selectedCampaign?.id ?? 0);
      }

      List<Voucher> vouchers = await voucherRepository.findVouchers(request);

      emit(state.copyWith(searchResult: vouchers, isSearching: false));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message, isSearching: false)));
    }
  }

  Future<void> searchVouchers(String keyword) async {
    emit(state.copyWith(keyword: keyword));
    await findVouchers();
  }

  void updateKeyword(String value) {
    emit(state.copyWith(keyword: value));
  }

  void selectCampaign(Identifiable? campaign) {
    emit(state.copyWith(selectedCampaign: campaign));
  }

  void clearVouchers() {
    emit(state.copyWith(searchResult: [], selectedCampaign: null));
  }
}
