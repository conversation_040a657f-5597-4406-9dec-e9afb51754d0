// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voucher_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoucherStateImpl _$$VoucherStateImplFromJson(Map<String, dynamic> json) =>
    _$VoucherStateImpl(
      tabIndex: (json['tabIndex'] as num?)?.toInt() ?? 0,
      travelAndHotels: (json['travelAndHotels'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      electronics: (json['electronics'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      fashion: (json['fashion'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      beautyAndHealth: (json['beautyAndHealth'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      homeAndLiving: (json['homeAndLiving'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      foodAndGrocery: (json['foodAndGrocery'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      allVouchers: (json['allVouchers'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => VoucherCategory.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      campaignVouchers: (json['campaignVouchers'] as List<dynamic>?)
              ?.map((e) => Voucher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isDataLoaded: json['isDataLoaded'] as bool? ?? false,
      loadingCategories: json['loadingCategories'] as bool? ?? false,
      loadingCampaignVouchers:
          json['loadingCampaignVouchers'] as bool? ?? false,
      isPullToRefresh: json['isPullToRefresh'] as bool? ?? false,
      currentVoucherDetail: json['currentVoucherDetail'] == null
          ? null
          : Voucher.fromJson(
              json['currentVoucherDetail'] as Map<String, dynamic>),
      loadingVoucherDetail: json['loadingVoucherDetail'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String? ?? "",
    );

Map<String, dynamic> _$$VoucherStateImplToJson(_$VoucherStateImpl instance) =>
    <String, dynamic>{
      'tabIndex': instance.tabIndex,
      'travelAndHotels': instance.travelAndHotels,
      'electronics': instance.electronics,
      'fashion': instance.fashion,
      'beautyAndHealth': instance.beautyAndHealth,
      'homeAndLiving': instance.homeAndLiving,
      'foodAndGrocery': instance.foodAndGrocery,
      'allVouchers': instance.allVouchers,
      'categories': instance.categories,
      'campaignVouchers': instance.campaignVouchers,
      'isDataLoaded': instance.isDataLoaded,
      'loadingCategories': instance.loadingCategories,
      'loadingCampaignVouchers': instance.loadingCampaignVouchers,
      'isPullToRefresh': instance.isPullToRefresh,
      'currentVoucherDetail': instance.currentVoucherDetail,
      'loadingVoucherDetail': instance.loadingVoucherDetail,
      'errorMessage': instance.errorMessage,
    };
