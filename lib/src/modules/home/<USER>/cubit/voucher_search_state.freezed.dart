// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voucher_search_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VoucherSearchState _$VoucherSearchStateFromJson(Map<String, dynamic> json) {
  return _VoucherSearchState.fromJson(json);
}

/// @nodoc
mixin _$VoucherSearchState {
  List<Identifiable> get campaigns => throw _privateConstructorUsedError;
  Identifiable? get selectedCampaign => throw _privateConstructorUsedError;
  String get keyword => throw _privateConstructorUsedError;
  List<Voucher> get searchResult => throw _privateConstructorUsedError;
  bool get isSearching => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this VoucherSearchState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoucherSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoucherSearchStateCopyWith<VoucherSearchState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoucherSearchStateCopyWith<$Res> {
  factory $VoucherSearchStateCopyWith(
          VoucherSearchState value, $Res Function(VoucherSearchState) then) =
      _$VoucherSearchStateCopyWithImpl<$Res, VoucherSearchState>;
  @useResult
  $Res call(
      {List<Identifiable> campaigns,
      Identifiable? selectedCampaign,
      String keyword,
      List<Voucher> searchResult,
      bool isSearching,
      String errorMessage});

  $IdentifiableCopyWith<$Res>? get selectedCampaign;
}

/// @nodoc
class _$VoucherSearchStateCopyWithImpl<$Res, $Val extends VoucherSearchState>
    implements $VoucherSearchStateCopyWith<$Res> {
  _$VoucherSearchStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoucherSearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaigns = null,
    Object? selectedCampaign = freezed,
    Object? keyword = null,
    Object? searchResult = null,
    Object? isSearching = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      campaigns: null == campaigns
          ? _value.campaigns
          : campaigns // ignore: cast_nullable_to_non_nullable
              as List<Identifiable>,
      selectedCampaign: freezed == selectedCampaign
          ? _value.selectedCampaign
          : selectedCampaign // ignore: cast_nullable_to_non_nullable
              as Identifiable?,
      keyword: null == keyword
          ? _value.keyword
          : keyword // ignore: cast_nullable_to_non_nullable
              as String,
      searchResult: null == searchResult
          ? _value.searchResult
          : searchResult // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      isSearching: null == isSearching
          ? _value.isSearching
          : isSearching // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of VoucherSearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $IdentifiableCopyWith<$Res>? get selectedCampaign {
    if (_value.selectedCampaign == null) {
      return null;
    }

    return $IdentifiableCopyWith<$Res>(_value.selectedCampaign!, (value) {
      return _then(_value.copyWith(selectedCampaign: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VoucherSearchStateImplCopyWith<$Res>
    implements $VoucherSearchStateCopyWith<$Res> {
  factory _$$VoucherSearchStateImplCopyWith(_$VoucherSearchStateImpl value,
          $Res Function(_$VoucherSearchStateImpl) then) =
      __$$VoucherSearchStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Identifiable> campaigns,
      Identifiable? selectedCampaign,
      String keyword,
      List<Voucher> searchResult,
      bool isSearching,
      String errorMessage});

  @override
  $IdentifiableCopyWith<$Res>? get selectedCampaign;
}

/// @nodoc
class __$$VoucherSearchStateImplCopyWithImpl<$Res>
    extends _$VoucherSearchStateCopyWithImpl<$Res, _$VoucherSearchStateImpl>
    implements _$$VoucherSearchStateImplCopyWith<$Res> {
  __$$VoucherSearchStateImplCopyWithImpl(_$VoucherSearchStateImpl _value,
      $Res Function(_$VoucherSearchStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoucherSearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? campaigns = null,
    Object? selectedCampaign = freezed,
    Object? keyword = null,
    Object? searchResult = null,
    Object? isSearching = null,
    Object? errorMessage = null,
  }) {
    return _then(_$VoucherSearchStateImpl(
      campaigns: null == campaigns
          ? _value._campaigns
          : campaigns // ignore: cast_nullable_to_non_nullable
              as List<Identifiable>,
      selectedCampaign: freezed == selectedCampaign
          ? _value.selectedCampaign
          : selectedCampaign // ignore: cast_nullable_to_non_nullable
              as Identifiable?,
      keyword: null == keyword
          ? _value.keyword
          : keyword // ignore: cast_nullable_to_non_nullable
              as String,
      searchResult: null == searchResult
          ? _value._searchResult
          : searchResult // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      isSearching: null == isSearching
          ? _value.isSearching
          : isSearching // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoucherSearchStateImpl implements _VoucherSearchState {
  _$VoucherSearchStateImpl(
      {final List<Identifiable> campaigns = const [],
      this.selectedCampaign,
      this.keyword = "",
      final List<Voucher> searchResult = const [],
      this.isSearching = false,
      this.errorMessage = ""})
      : _campaigns = campaigns,
        _searchResult = searchResult;

  factory _$VoucherSearchStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoucherSearchStateImplFromJson(json);

  final List<Identifiable> _campaigns;
  @override
  @JsonKey()
  List<Identifiable> get campaigns {
    if (_campaigns is EqualUnmodifiableListView) return _campaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaigns);
  }

  @override
  final Identifiable? selectedCampaign;
  @override
  @JsonKey()
  final String keyword;
  final List<Voucher> _searchResult;
  @override
  @JsonKey()
  List<Voucher> get searchResult {
    if (_searchResult is EqualUnmodifiableListView) return _searchResult;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_searchResult);
  }

  @override
  @JsonKey()
  final bool isSearching;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'VoucherSearchState(campaigns: $campaigns, selectedCampaign: $selectedCampaign, keyword: $keyword, searchResult: $searchResult, isSearching: $isSearching, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoucherSearchStateImpl &&
            const DeepCollectionEquality()
                .equals(other._campaigns, _campaigns) &&
            (identical(other.selectedCampaign, selectedCampaign) ||
                other.selectedCampaign == selectedCampaign) &&
            (identical(other.keyword, keyword) || other.keyword == keyword) &&
            const DeepCollectionEquality()
                .equals(other._searchResult, _searchResult) &&
            (identical(other.isSearching, isSearching) ||
                other.isSearching == isSearching) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_campaigns),
      selectedCampaign,
      keyword,
      const DeepCollectionEquality().hash(_searchResult),
      isSearching,
      errorMessage);

  /// Create a copy of VoucherSearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoucherSearchStateImplCopyWith<_$VoucherSearchStateImpl> get copyWith =>
      __$$VoucherSearchStateImplCopyWithImpl<_$VoucherSearchStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoucherSearchStateImplToJson(
      this,
    );
  }
}

abstract class _VoucherSearchState implements VoucherSearchState {
  factory _VoucherSearchState(
      {final List<Identifiable> campaigns,
      final Identifiable? selectedCampaign,
      final String keyword,
      final List<Voucher> searchResult,
      final bool isSearching,
      final String errorMessage}) = _$VoucherSearchStateImpl;

  factory _VoucherSearchState.fromJson(Map<String, dynamic> json) =
      _$VoucherSearchStateImpl.fromJson;

  @override
  List<Identifiable> get campaigns;
  @override
  Identifiable? get selectedCampaign;
  @override
  String get keyword;
  @override
  List<Voucher> get searchResult;
  @override
  bool get isSearching;
  @override
  String get errorMessage;

  /// Create a copy of VoucherSearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoucherSearchStateImplCopyWith<_$VoucherSearchStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
