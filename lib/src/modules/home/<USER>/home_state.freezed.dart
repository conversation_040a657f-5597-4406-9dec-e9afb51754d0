// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

HomeState _$HomeStateFromJson(Map<String, dynamic> json) {
  return _HomeState.fromJson(json);
}

/// @nodoc
mixin _$HomeState {
  List<DefaultCampaignSummary> get promotedCampaigns =>
      throw _privateConstructorUsedError;
  ReportSummary? get lastSevenDayPerformance =>
      throw _privateConstructorUsedError;
  List<HomeCarousel> get homeCarousel => throw _privateConstructorUsedError;
  List<DefaultCampaignSummary> get campaigns =>
      throw _privateConstructorUsedError;
  List<Voucher> get vouchers => throw _privateConstructorUsedError;
  List<SuperPoint> get superPoints => throw _privateConstructorUsedError;
  bool get showChart => throw _privateConstructorUsedError;
  List<int> get clicks => throw _privateConstructorUsedError;
  List<int> get conversions => throw _privateConstructorUsedError;
  String get academyLink => throw _privateConstructorUsedError;
  String get inpageLink => throw _privateConstructorUsedError;
  String get superPointLink => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this HomeState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeStateCopyWith<HomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeStateCopyWith<$Res> {
  factory $HomeStateCopyWith(HomeState value, $Res Function(HomeState) then) =
      _$HomeStateCopyWithImpl<$Res, HomeState>;
  @useResult
  $Res call(
      {List<DefaultCampaignSummary> promotedCampaigns,
      ReportSummary? lastSevenDayPerformance,
      List<HomeCarousel> homeCarousel,
      List<DefaultCampaignSummary> campaigns,
      List<Voucher> vouchers,
      List<SuperPoint> superPoints,
      bool showChart,
      List<int> clicks,
      List<int> conversions,
      String academyLink,
      String inpageLink,
      String superPointLink,
      String errorMessage});

  $ReportSummaryCopyWith<$Res>? get lastSevenDayPerformance;
}

/// @nodoc
class _$HomeStateCopyWithImpl<$Res, $Val extends HomeState>
    implements $HomeStateCopyWith<$Res> {
  _$HomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promotedCampaigns = null,
    Object? lastSevenDayPerformance = freezed,
    Object? homeCarousel = null,
    Object? campaigns = null,
    Object? vouchers = null,
    Object? superPoints = null,
    Object? showChart = null,
    Object? clicks = null,
    Object? conversions = null,
    Object? academyLink = null,
    Object? inpageLink = null,
    Object? superPointLink = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      promotedCampaigns: null == promotedCampaigns
          ? _value.promotedCampaigns
          : promotedCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      lastSevenDayPerformance: freezed == lastSevenDayPerformance
          ? _value.lastSevenDayPerformance
          : lastSevenDayPerformance // ignore: cast_nullable_to_non_nullable
              as ReportSummary?,
      homeCarousel: null == homeCarousel
          ? _value.homeCarousel
          : homeCarousel // ignore: cast_nullable_to_non_nullable
              as List<HomeCarousel>,
      campaigns: null == campaigns
          ? _value.campaigns
          : campaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      vouchers: null == vouchers
          ? _value.vouchers
          : vouchers // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      superPoints: null == superPoints
          ? _value.superPoints
          : superPoints // ignore: cast_nullable_to_non_nullable
              as List<SuperPoint>,
      showChart: null == showChart
          ? _value.showChart
          : showChart // ignore: cast_nullable_to_non_nullable
              as bool,
      clicks: null == clicks
          ? _value.clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as List<int>,
      conversions: null == conversions
          ? _value.conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as List<int>,
      academyLink: null == academyLink
          ? _value.academyLink
          : academyLink // ignore: cast_nullable_to_non_nullable
              as String,
      inpageLink: null == inpageLink
          ? _value.inpageLink
          : inpageLink // ignore: cast_nullable_to_non_nullable
              as String,
      superPointLink: null == superPointLink
          ? _value.superPointLink
          : superPointLink // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ReportSummaryCopyWith<$Res>? get lastSevenDayPerformance {
    if (_value.lastSevenDayPerformance == null) {
      return null;
    }

    return $ReportSummaryCopyWith<$Res>(_value.lastSevenDayPerformance!,
        (value) {
      return _then(_value.copyWith(lastSevenDayPerformance: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HomeStateImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeStateImplCopyWith(
          _$HomeStateImpl value, $Res Function(_$HomeStateImpl) then) =
      __$$HomeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<DefaultCampaignSummary> promotedCampaigns,
      ReportSummary? lastSevenDayPerformance,
      List<HomeCarousel> homeCarousel,
      List<DefaultCampaignSummary> campaigns,
      List<Voucher> vouchers,
      List<SuperPoint> superPoints,
      bool showChart,
      List<int> clicks,
      List<int> conversions,
      String academyLink,
      String inpageLink,
      String superPointLink,
      String errorMessage});

  @override
  $ReportSummaryCopyWith<$Res>? get lastSevenDayPerformance;
}

/// @nodoc
class __$$HomeStateImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeStateImpl>
    implements _$$HomeStateImplCopyWith<$Res> {
  __$$HomeStateImplCopyWithImpl(
      _$HomeStateImpl _value, $Res Function(_$HomeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promotedCampaigns = null,
    Object? lastSevenDayPerformance = freezed,
    Object? homeCarousel = null,
    Object? campaigns = null,
    Object? vouchers = null,
    Object? superPoints = null,
    Object? showChart = null,
    Object? clicks = null,
    Object? conversions = null,
    Object? academyLink = null,
    Object? inpageLink = null,
    Object? superPointLink = null,
    Object? errorMessage = null,
  }) {
    return _then(_$HomeStateImpl(
      promotedCampaigns: null == promotedCampaigns
          ? _value._promotedCampaigns
          : promotedCampaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      lastSevenDayPerformance: freezed == lastSevenDayPerformance
          ? _value.lastSevenDayPerformance
          : lastSevenDayPerformance // ignore: cast_nullable_to_non_nullable
              as ReportSummary?,
      homeCarousel: null == homeCarousel
          ? _value._homeCarousel
          : homeCarousel // ignore: cast_nullable_to_non_nullable
              as List<HomeCarousel>,
      campaigns: null == campaigns
          ? _value._campaigns
          : campaigns // ignore: cast_nullable_to_non_nullable
              as List<DefaultCampaignSummary>,
      vouchers: null == vouchers
          ? _value._vouchers
          : vouchers // ignore: cast_nullable_to_non_nullable
              as List<Voucher>,
      superPoints: null == superPoints
          ? _value._superPoints
          : superPoints // ignore: cast_nullable_to_non_nullable
              as List<SuperPoint>,
      showChart: null == showChart
          ? _value.showChart
          : showChart // ignore: cast_nullable_to_non_nullable
              as bool,
      clicks: null == clicks
          ? _value._clicks
          : clicks // ignore: cast_nullable_to_non_nullable
              as List<int>,
      conversions: null == conversions
          ? _value._conversions
          : conversions // ignore: cast_nullable_to_non_nullable
              as List<int>,
      academyLink: null == academyLink
          ? _value.academyLink
          : academyLink // ignore: cast_nullable_to_non_nullable
              as String,
      inpageLink: null == inpageLink
          ? _value.inpageLink
          : inpageLink // ignore: cast_nullable_to_non_nullable
              as String,
      superPointLink: null == superPointLink
          ? _value.superPointLink
          : superPointLink // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HomeStateImpl implements _HomeState {
  _$HomeStateImpl(
      {final List<DefaultCampaignSummary> promotedCampaigns = const [],
      this.lastSevenDayPerformance,
      final List<HomeCarousel> homeCarousel = const [],
      final List<DefaultCampaignSummary> campaigns = const [],
      final List<Voucher> vouchers = const [],
      final List<SuperPoint> superPoints = const [],
      this.showChart = false,
      final List<int> clicks = const [0, 0, 0, 0, 0, 0, 0],
      final List<int> conversions = const [0, 0, 0, 0, 0, 0, 0],
      this.academyLink = '',
      this.inpageLink = '',
      this.superPointLink = '',
      this.errorMessage = ''})
      : _promotedCampaigns = promotedCampaigns,
        _homeCarousel = homeCarousel,
        _campaigns = campaigns,
        _vouchers = vouchers,
        _superPoints = superPoints,
        _clicks = clicks,
        _conversions = conversions;

  factory _$HomeStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$HomeStateImplFromJson(json);

  final List<DefaultCampaignSummary> _promotedCampaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get promotedCampaigns {
    if (_promotedCampaigns is EqualUnmodifiableListView)
      return _promotedCampaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_promotedCampaigns);
  }

  @override
  final ReportSummary? lastSevenDayPerformance;
  final List<HomeCarousel> _homeCarousel;
  @override
  @JsonKey()
  List<HomeCarousel> get homeCarousel {
    if (_homeCarousel is EqualUnmodifiableListView) return _homeCarousel;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_homeCarousel);
  }

  final List<DefaultCampaignSummary> _campaigns;
  @override
  @JsonKey()
  List<DefaultCampaignSummary> get campaigns {
    if (_campaigns is EqualUnmodifiableListView) return _campaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaigns);
  }

  final List<Voucher> _vouchers;
  @override
  @JsonKey()
  List<Voucher> get vouchers {
    if (_vouchers is EqualUnmodifiableListView) return _vouchers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vouchers);
  }

  final List<SuperPoint> _superPoints;
  @override
  @JsonKey()
  List<SuperPoint> get superPoints {
    if (_superPoints is EqualUnmodifiableListView) return _superPoints;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_superPoints);
  }

  @override
  @JsonKey()
  final bool showChart;
  final List<int> _clicks;
  @override
  @JsonKey()
  List<int> get clicks {
    if (_clicks is EqualUnmodifiableListView) return _clicks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_clicks);
  }

  final List<int> _conversions;
  @override
  @JsonKey()
  List<int> get conversions {
    if (_conversions is EqualUnmodifiableListView) return _conversions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_conversions);
  }

  @override
  @JsonKey()
  final String academyLink;
  @override
  @JsonKey()
  final String inpageLink;
  @override
  @JsonKey()
  final String superPointLink;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'HomeState(promotedCampaigns: $promotedCampaigns, lastSevenDayPerformance: $lastSevenDayPerformance, homeCarousel: $homeCarousel, campaigns: $campaigns, vouchers: $vouchers, superPoints: $superPoints, showChart: $showChart, clicks: $clicks, conversions: $conversions, academyLink: $academyLink, inpageLink: $inpageLink, superPointLink: $superPointLink, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeStateImpl &&
            const DeepCollectionEquality()
                .equals(other._promotedCampaigns, _promotedCampaigns) &&
            (identical(
                    other.lastSevenDayPerformance, lastSevenDayPerformance) ||
                other.lastSevenDayPerformance == lastSevenDayPerformance) &&
            const DeepCollectionEquality()
                .equals(other._homeCarousel, _homeCarousel) &&
            const DeepCollectionEquality()
                .equals(other._campaigns, _campaigns) &&
            const DeepCollectionEquality().equals(other._vouchers, _vouchers) &&
            const DeepCollectionEquality()
                .equals(other._superPoints, _superPoints) &&
            (identical(other.showChart, showChart) ||
                other.showChart == showChart) &&
            const DeepCollectionEquality().equals(other._clicks, _clicks) &&
            const DeepCollectionEquality()
                .equals(other._conversions, _conversions) &&
            (identical(other.academyLink, academyLink) ||
                other.academyLink == academyLink) &&
            (identical(other.inpageLink, inpageLink) ||
                other.inpageLink == inpageLink) &&
            (identical(other.superPointLink, superPointLink) ||
                other.superPointLink == superPointLink) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_promotedCampaigns),
      lastSevenDayPerformance,
      const DeepCollectionEquality().hash(_homeCarousel),
      const DeepCollectionEquality().hash(_campaigns),
      const DeepCollectionEquality().hash(_vouchers),
      const DeepCollectionEquality().hash(_superPoints),
      showChart,
      const DeepCollectionEquality().hash(_clicks),
      const DeepCollectionEquality().hash(_conversions),
      academyLink,
      inpageLink,
      superPointLink,
      errorMessage);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      __$$HomeStateImplCopyWithImpl<_$HomeStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HomeStateImplToJson(
      this,
    );
  }
}

abstract class _HomeState implements HomeState {
  factory _HomeState(
      {final List<DefaultCampaignSummary> promotedCampaigns,
      final ReportSummary? lastSevenDayPerformance,
      final List<HomeCarousel> homeCarousel,
      final List<DefaultCampaignSummary> campaigns,
      final List<Voucher> vouchers,
      final List<SuperPoint> superPoints,
      final bool showChart,
      final List<int> clicks,
      final List<int> conversions,
      final String academyLink,
      final String inpageLink,
      final String superPointLink,
      final String errorMessage}) = _$HomeStateImpl;

  factory _HomeState.fromJson(Map<String, dynamic> json) =
      _$HomeStateImpl.fromJson;

  @override
  List<DefaultCampaignSummary> get promotedCampaigns;
  @override
  ReportSummary? get lastSevenDayPerformance;
  @override
  List<HomeCarousel> get homeCarousel;
  @override
  List<DefaultCampaignSummary> get campaigns;
  @override
  List<Voucher> get vouchers;
  @override
  List<SuperPoint> get superPoints;
  @override
  bool get showChart;
  @override
  List<int> get clicks;
  @override
  List<int> get conversions;
  @override
  String get academyLink;
  @override
  String get inpageLink;
  @override
  String get superPointLink;
  @override
  String get errorMessage;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SuperPoint _$SuperPointFromJson(Map<String, dynamic> json) {
  return _SuperPoint.fromJson(json);
}

/// @nodoc
mixin _$SuperPoint {
  String get name => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  int get point => throw _privateConstructorUsedError;

  /// Serializes this SuperPoint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SuperPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SuperPointCopyWith<SuperPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SuperPointCopyWith<$Res> {
  factory $SuperPointCopyWith(
          SuperPoint value, $Res Function(SuperPoint) then) =
      _$SuperPointCopyWithImpl<$Res, SuperPoint>;
  @useResult
  $Res call({String name, String imageUrl, int point});
}

/// @nodoc
class _$SuperPointCopyWithImpl<$Res, $Val extends SuperPoint>
    implements $SuperPointCopyWith<$Res> {
  _$SuperPointCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SuperPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? imageUrl = null,
    Object? point = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      point: null == point
          ? _value.point
          : point // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SuperPointImplCopyWith<$Res>
    implements $SuperPointCopyWith<$Res> {
  factory _$$SuperPointImplCopyWith(
          _$SuperPointImpl value, $Res Function(_$SuperPointImpl) then) =
      __$$SuperPointImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String imageUrl, int point});
}

/// @nodoc
class __$$SuperPointImplCopyWithImpl<$Res>
    extends _$SuperPointCopyWithImpl<$Res, _$SuperPointImpl>
    implements _$$SuperPointImplCopyWith<$Res> {
  __$$SuperPointImplCopyWithImpl(
      _$SuperPointImpl _value, $Res Function(_$SuperPointImpl) _then)
      : super(_value, _then);

  /// Create a copy of SuperPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? imageUrl = null,
    Object? point = null,
  }) {
    return _then(_$SuperPointImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      point: null == point
          ? _value.point
          : point // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SuperPointImpl implements _SuperPoint {
  _$SuperPointImpl({this.name = "", this.imageUrl = "", this.point = 0});

  factory _$SuperPointImpl.fromJson(Map<String, dynamic> json) =>
      _$$SuperPointImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String imageUrl;
  @override
  @JsonKey()
  final int point;

  @override
  String toString() {
    return 'SuperPoint(name: $name, imageUrl: $imageUrl, point: $point)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuperPointImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.point, point) || other.point == point));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, imageUrl, point);

  /// Create a copy of SuperPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuperPointImplCopyWith<_$SuperPointImpl> get copyWith =>
      __$$SuperPointImplCopyWithImpl<_$SuperPointImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SuperPointImplToJson(
      this,
    );
  }
}

abstract class _SuperPoint implements SuperPoint {
  factory _SuperPoint(
      {final String name,
      final String imageUrl,
      final int point}) = _$SuperPointImpl;

  factory _SuperPoint.fromJson(Map<String, dynamic> json) =
      _$SuperPointImpl.fromJson;

  @override
  String get name;
  @override
  String get imageUrl;
  @override
  int get point;

  /// Create a copy of SuperPoint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuperPointImplCopyWith<_$SuperPointImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

HomeCarousel _$HomeCarouselFromJson(Map<String, dynamic> json) {
  return _HomeCarousel.fromJson(json);
}

/// @nodoc
mixin _$HomeCarousel {
  String get image => throw _privateConstructorUsedError;
  String get targetUrl => throw _privateConstructorUsedError;

  /// Serializes this HomeCarousel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HomeCarousel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeCarouselCopyWith<HomeCarousel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeCarouselCopyWith<$Res> {
  factory $HomeCarouselCopyWith(
          HomeCarousel value, $Res Function(HomeCarousel) then) =
      _$HomeCarouselCopyWithImpl<$Res, HomeCarousel>;
  @useResult
  $Res call({String image, String targetUrl});
}

/// @nodoc
class _$HomeCarouselCopyWithImpl<$Res, $Val extends HomeCarousel>
    implements $HomeCarouselCopyWith<$Res> {
  _$HomeCarouselCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeCarousel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = null,
    Object? targetUrl = null,
  }) {
    return _then(_value.copyWith(
      image: null == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HomeCarouselImplCopyWith<$Res>
    implements $HomeCarouselCopyWith<$Res> {
  factory _$$HomeCarouselImplCopyWith(
          _$HomeCarouselImpl value, $Res Function(_$HomeCarouselImpl) then) =
      __$$HomeCarouselImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String image, String targetUrl});
}

/// @nodoc
class __$$HomeCarouselImplCopyWithImpl<$Res>
    extends _$HomeCarouselCopyWithImpl<$Res, _$HomeCarouselImpl>
    implements _$$HomeCarouselImplCopyWith<$Res> {
  __$$HomeCarouselImplCopyWithImpl(
      _$HomeCarouselImpl _value, $Res Function(_$HomeCarouselImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeCarousel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = null,
    Object? targetUrl = null,
  }) {
    return _then(_$HomeCarouselImpl(
      image: null == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HomeCarouselImpl implements _HomeCarousel {
  _$HomeCarouselImpl({this.image = "", this.targetUrl = ""});

  factory _$HomeCarouselImpl.fromJson(Map<String, dynamic> json) =>
      _$$HomeCarouselImplFromJson(json);

  @override
  @JsonKey()
  final String image;
  @override
  @JsonKey()
  final String targetUrl;

  @override
  String toString() {
    return 'HomeCarousel(image: $image, targetUrl: $targetUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeCarouselImpl &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.targetUrl, targetUrl) ||
                other.targetUrl == targetUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, image, targetUrl);

  /// Create a copy of HomeCarousel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeCarouselImplCopyWith<_$HomeCarouselImpl> get copyWith =>
      __$$HomeCarouselImplCopyWithImpl<_$HomeCarouselImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HomeCarouselImplToJson(
      this,
    );
  }
}

abstract class _HomeCarousel implements HomeCarousel {
  factory _HomeCarousel({final String image, final String targetUrl}) =
      _$HomeCarouselImpl;

  factory _HomeCarousel.fromJson(Map<String, dynamic> json) =
      _$HomeCarouselImpl.fromJson;

  @override
  String get image;
  @override
  String get targetUrl;

  /// Create a copy of HomeCarousel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeCarouselImplCopyWith<_$HomeCarouselImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
