import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

class VoucherRepository {
  final ApiService apiService;

  VoucherRepository(this.apiService);

  Future<List<Voucher>> findVouchers(FindVouchersRequest request) async {
    final queryParams = {
      'siteId': request.siteId.toString(),
      'type': request.type,
      'startIndex': request.startIndex.toString(),
      'size': request.size.toString(),
    };

    if (request.campaignId != null) {
      queryParams['campaignId'] = request.campaignId.toString();
    }

    if (request.category != null) {
      queryParams['category'] = request.category!;
    }

    final response = await apiService.getData('/v3/publishers/me/vouchers', params: queryParams);

    if (response != null && response is List) {
      return response.map((item) => Voucher.fromJson(item)).toList();
    }

    return [];
  }

  Future<Voucher?> findVoucherById(int voucherId, int siteId) async {
    final queryParams = {
      'siteId': siteId.toString(),
    };

    try {
      final response = await apiService.getData('/v3/publishers/me/vouchers/$voucherId', params: queryParams);
      if (response != null) {
        return Voucher.fromJson(response);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<List<VoucherCategory>> getVoucherCategories(int siteId, String type) async {
    final result = await apiService
        .getData('/v3/publishers/me/voucher-categories', params: {'siteId': siteId.toString(), 'type': type});
    if (result is List) {
      return result.map((item) => VoucherCategory.fromJson(item)).toList();
    }
    return [];
  }

  Future<dynamic> findCategories(FindVouchersRequest request) async {
    final queryParams = {'siteId': request.siteId.toString(), 'type': request.type};

    if (request.campaignId != null) {
      queryParams['campaignId'] = request.campaignId.toString();
    }

    final rest = await apiService.getData('/v3/publishers/me/voucher-categories', params: queryParams);
    return rest;
  }
}
