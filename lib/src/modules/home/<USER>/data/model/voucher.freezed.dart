// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voucher.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Voucher _$VoucherFromJson(Map<String, dynamic> json) {
  return _Voucher.fromJson(json);
}

/// @nodoc
mixin _$Voucher {
  int? get id => throw _privateConstructorUsedError;
  String get affiliateLink => throw _privateConstructorUsedError;
  String get campaignName => throw _privateConstructorUsedError;
  String get code => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get validDate => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<String> get categoryIds => throw _privateConstructorUsedError;

  /// Serializes this Voucher to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Voucher
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoucherCopyWith<Voucher> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoucherCopyWith<$Res> {
  factory $VoucherCopyWith(Voucher value, $Res Function(Voucher) then) =
      _$VoucherCopyWithImpl<$Res, Voucher>;
  @useResult
  $Res call(
      {int? id,
      String affiliateLink,
      String campaignName,
      String code,
      String imageUrl,
      String title,
      String validDate,
      String description,
      List<String> categoryIds});
}

/// @nodoc
class _$VoucherCopyWithImpl<$Res, $Val extends Voucher>
    implements $VoucherCopyWith<$Res> {
  _$VoucherCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Voucher
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? affiliateLink = null,
    Object? campaignName = null,
    Object? code = null,
    Object? imageUrl = null,
    Object? title = null,
    Object? validDate = null,
    Object? description = null,
    Object? categoryIds = null,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      affiliateLink: null == affiliateLink
          ? _value.affiliateLink
          : affiliateLink // ignore: cast_nullable_to_non_nullable
              as String,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      validDate: null == validDate
          ? _value.validDate
          : validDate // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      categoryIds: null == categoryIds
          ? _value.categoryIds
          : categoryIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VoucherImplCopyWith<$Res> implements $VoucherCopyWith<$Res> {
  factory _$$VoucherImplCopyWith(
          _$VoucherImpl value, $Res Function(_$VoucherImpl) then) =
      __$$VoucherImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String affiliateLink,
      String campaignName,
      String code,
      String imageUrl,
      String title,
      String validDate,
      String description,
      List<String> categoryIds});
}

/// @nodoc
class __$$VoucherImplCopyWithImpl<$Res>
    extends _$VoucherCopyWithImpl<$Res, _$VoucherImpl>
    implements _$$VoucherImplCopyWith<$Res> {
  __$$VoucherImplCopyWithImpl(
      _$VoucherImpl _value, $Res Function(_$VoucherImpl) _then)
      : super(_value, _then);

  /// Create a copy of Voucher
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? affiliateLink = null,
    Object? campaignName = null,
    Object? code = null,
    Object? imageUrl = null,
    Object? title = null,
    Object? validDate = null,
    Object? description = null,
    Object? categoryIds = null,
  }) {
    return _then(_$VoucherImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      affiliateLink: null == affiliateLink
          ? _value.affiliateLink
          : affiliateLink // ignore: cast_nullable_to_non_nullable
              as String,
      campaignName: null == campaignName
          ? _value.campaignName
          : campaignName // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      validDate: null == validDate
          ? _value.validDate
          : validDate // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      categoryIds: null == categoryIds
          ? _value._categoryIds
          : categoryIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoucherImpl implements _Voucher {
  _$VoucherImpl(
      {this.id,
      this.affiliateLink = "",
      this.campaignName = "",
      this.code = "",
      this.imageUrl = "",
      this.title = "",
      this.validDate = "",
      this.description = "",
      final List<String> categoryIds = const []})
      : _categoryIds = categoryIds;

  factory _$VoucherImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoucherImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey()
  final String affiliateLink;
  @override
  @JsonKey()
  final String campaignName;
  @override
  @JsonKey()
  final String code;
  @override
  @JsonKey()
  final String imageUrl;
  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final String validDate;
  @override
  @JsonKey()
  final String description;
  final List<String> _categoryIds;
  @override
  @JsonKey()
  List<String> get categoryIds {
    if (_categoryIds is EqualUnmodifiableListView) return _categoryIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categoryIds);
  }

  @override
  String toString() {
    return 'Voucher(id: $id, affiliateLink: $affiliateLink, campaignName: $campaignName, code: $code, imageUrl: $imageUrl, title: $title, validDate: $validDate, description: $description, categoryIds: $categoryIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoucherImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.affiliateLink, affiliateLink) ||
                other.affiliateLink == affiliateLink) &&
            (identical(other.campaignName, campaignName) ||
                other.campaignName == campaignName) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.validDate, validDate) ||
                other.validDate == validDate) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._categoryIds, _categoryIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      affiliateLink,
      campaignName,
      code,
      imageUrl,
      title,
      validDate,
      description,
      const DeepCollectionEquality().hash(_categoryIds));

  /// Create a copy of Voucher
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoucherImplCopyWith<_$VoucherImpl> get copyWith =>
      __$$VoucherImplCopyWithImpl<_$VoucherImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoucherImplToJson(
      this,
    );
  }
}

abstract class _Voucher implements Voucher {
  factory _Voucher(
      {final int? id,
      final String affiliateLink,
      final String campaignName,
      final String code,
      final String imageUrl,
      final String title,
      final String validDate,
      final String description,
      final List<String> categoryIds}) = _$VoucherImpl;

  factory _Voucher.fromJson(Map<String, dynamic> json) = _$VoucherImpl.fromJson;

  @override
  int? get id;
  @override
  String get affiliateLink;
  @override
  String get campaignName;
  @override
  String get code;
  @override
  String get imageUrl;
  @override
  String get title;
  @override
  String get validDate;
  @override
  String get description;
  @override
  List<String> get categoryIds;

  /// Create a copy of Voucher
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoucherImplCopyWith<_$VoucherImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FindVouchersRequest _$FindVouchersRequestFromJson(Map<String, dynamic> json) {
  return _FindVouchersRequest.fromJson(json);
}

/// @nodoc
mixin _$FindVouchersRequest {
  int get siteId => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  int get startIndex => throw _privateConstructorUsedError;
  int get size => throw _privateConstructorUsedError;
  int? get campaignId => throw _privateConstructorUsedError;
  String? get category => throw _privateConstructorUsedError;

  /// Serializes this FindVouchersRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FindVouchersRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FindVouchersRequestCopyWith<FindVouchersRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FindVouchersRequestCopyWith<$Res> {
  factory $FindVouchersRequestCopyWith(
          FindVouchersRequest value, $Res Function(FindVouchersRequest) then) =
      _$FindVouchersRequestCopyWithImpl<$Res, FindVouchersRequest>;
  @useResult
  $Res call(
      {int siteId,
      String type,
      int startIndex,
      int size,
      int? campaignId,
      String? category});
}

/// @nodoc
class _$FindVouchersRequestCopyWithImpl<$Res, $Val extends FindVouchersRequest>
    implements $FindVouchersRequestCopyWith<$Res> {
  _$FindVouchersRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FindVouchersRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? siteId = null,
    Object? type = null,
    Object? startIndex = null,
    Object? size = null,
    Object? campaignId = freezed,
    Object? category = freezed,
  }) {
    return _then(_value.copyWith(
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      startIndex: null == startIndex
          ? _value.startIndex
          : startIndex // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      campaignId: freezed == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FindVouchersRequestImplCopyWith<$Res>
    implements $FindVouchersRequestCopyWith<$Res> {
  factory _$$FindVouchersRequestImplCopyWith(_$FindVouchersRequestImpl value,
          $Res Function(_$FindVouchersRequestImpl) then) =
      __$$FindVouchersRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int siteId,
      String type,
      int startIndex,
      int size,
      int? campaignId,
      String? category});
}

/// @nodoc
class __$$FindVouchersRequestImplCopyWithImpl<$Res>
    extends _$FindVouchersRequestCopyWithImpl<$Res, _$FindVouchersRequestImpl>
    implements _$$FindVouchersRequestImplCopyWith<$Res> {
  __$$FindVouchersRequestImplCopyWithImpl(_$FindVouchersRequestImpl _value,
      $Res Function(_$FindVouchersRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of FindVouchersRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? siteId = null,
    Object? type = null,
    Object? startIndex = null,
    Object? size = null,
    Object? campaignId = freezed,
    Object? category = freezed,
  }) {
    return _then(_$FindVouchersRequestImpl(
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      startIndex: null == startIndex
          ? _value.startIndex
          : startIndex // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      campaignId: freezed == campaignId
          ? _value.campaignId
          : campaignId // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FindVouchersRequestImpl implements _FindVouchersRequest {
  _$FindVouchersRequestImpl(
      {this.siteId = 0,
      this.type = 'ALL',
      this.startIndex = 1,
      this.size = 100,
      this.campaignId,
      this.category});

  factory _$FindVouchersRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$FindVouchersRequestImplFromJson(json);

  @override
  @JsonKey()
  final int siteId;
  @override
  @JsonKey()
  final String type;
  @override
  @JsonKey()
  final int startIndex;
  @override
  @JsonKey()
  final int size;
  @override
  final int? campaignId;
  @override
  final String? category;

  @override
  String toString() {
    return 'FindVouchersRequest(siteId: $siteId, type: $type, startIndex: $startIndex, size: $size, campaignId: $campaignId, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FindVouchersRequestImpl &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.startIndex, startIndex) ||
                other.startIndex == startIndex) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.campaignId, campaignId) ||
                other.campaignId == campaignId) &&
            (identical(other.category, category) ||
                other.category == category));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, siteId, type, startIndex, size, campaignId, category);

  /// Create a copy of FindVouchersRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FindVouchersRequestImplCopyWith<_$FindVouchersRequestImpl> get copyWith =>
      __$$FindVouchersRequestImplCopyWithImpl<_$FindVouchersRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FindVouchersRequestImplToJson(
      this,
    );
  }
}

abstract class _FindVouchersRequest implements FindVouchersRequest {
  factory _FindVouchersRequest(
      {final int siteId,
      final String type,
      final int startIndex,
      final int size,
      final int? campaignId,
      final String? category}) = _$FindVouchersRequestImpl;

  factory _FindVouchersRequest.fromJson(Map<String, dynamic> json) =
      _$FindVouchersRequestImpl.fromJson;

  @override
  int get siteId;
  @override
  String get type;
  @override
  int get startIndex;
  @override
  int get size;
  @override
  int? get campaignId;
  @override
  String? get category;

  /// Create a copy of FindVouchersRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FindVouchersRequestImplCopyWith<_$FindVouchersRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VoucherCategory _$VoucherCategoryFromJson(Map<String, dynamic> json) {
  return _VoucherCategory.fromJson(json);
}

/// @nodoc
mixin _$VoucherCategory {
  String get categoryId => throw _privateConstructorUsedError;
  int get numberOfPromos => throw _privateConstructorUsedError;

  /// Serializes this VoucherCategory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VoucherCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VoucherCategoryCopyWith<VoucherCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoucherCategoryCopyWith<$Res> {
  factory $VoucherCategoryCopyWith(
          VoucherCategory value, $Res Function(VoucherCategory) then) =
      _$VoucherCategoryCopyWithImpl<$Res, VoucherCategory>;
  @useResult
  $Res call({String categoryId, int numberOfPromos});
}

/// @nodoc
class _$VoucherCategoryCopyWithImpl<$Res, $Val extends VoucherCategory>
    implements $VoucherCategoryCopyWith<$Res> {
  _$VoucherCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoucherCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categoryId = null,
    Object? numberOfPromos = null,
  }) {
    return _then(_value.copyWith(
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      numberOfPromos: null == numberOfPromos
          ? _value.numberOfPromos
          : numberOfPromos // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VoucherCategoryImplCopyWith<$Res>
    implements $VoucherCategoryCopyWith<$Res> {
  factory _$$VoucherCategoryImplCopyWith(_$VoucherCategoryImpl value,
          $Res Function(_$VoucherCategoryImpl) then) =
      __$$VoucherCategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String categoryId, int numberOfPromos});
}

/// @nodoc
class __$$VoucherCategoryImplCopyWithImpl<$Res>
    extends _$VoucherCategoryCopyWithImpl<$Res, _$VoucherCategoryImpl>
    implements _$$VoucherCategoryImplCopyWith<$Res> {
  __$$VoucherCategoryImplCopyWithImpl(
      _$VoucherCategoryImpl _value, $Res Function(_$VoucherCategoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoucherCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categoryId = null,
    Object? numberOfPromos = null,
  }) {
    return _then(_$VoucherCategoryImpl(
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      numberOfPromos: null == numberOfPromos
          ? _value.numberOfPromos
          : numberOfPromos // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoucherCategoryImpl implements _VoucherCategory {
  const _$VoucherCategoryImpl(
      {required this.categoryId, this.numberOfPromos = 0});

  factory _$VoucherCategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoucherCategoryImplFromJson(json);

  @override
  final String categoryId;
  @override
  @JsonKey()
  final int numberOfPromos;

  @override
  String toString() {
    return 'VoucherCategory(categoryId: $categoryId, numberOfPromos: $numberOfPromos)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoucherCategoryImpl &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.numberOfPromos, numberOfPromos) ||
                other.numberOfPromos == numberOfPromos));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, categoryId, numberOfPromos);

  /// Create a copy of VoucherCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VoucherCategoryImplCopyWith<_$VoucherCategoryImpl> get copyWith =>
      __$$VoucherCategoryImplCopyWithImpl<_$VoucherCategoryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoucherCategoryImplToJson(
      this,
    );
  }
}

abstract class _VoucherCategory implements VoucherCategory {
  const factory _VoucherCategory(
      {required final String categoryId,
      final int numberOfPromos}) = _$VoucherCategoryImpl;

  factory _VoucherCategory.fromJson(Map<String, dynamic> json) =
      _$VoucherCategoryImpl.fromJson;

  @override
  String get categoryId;
  @override
  int get numberOfPromos;

  /// Create a copy of VoucherCategory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VoucherCategoryImplCopyWith<_$VoucherCategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
