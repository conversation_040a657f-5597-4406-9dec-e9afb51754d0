import 'package:freezed_annotation/freezed_annotation.dart';

part 'voucher.freezed.dart';
part 'voucher.g.dart';

enum VoucherCategoryType {
  @JsonValue('TRAVEL_HOTEL')
  travelHotel('TRAVEL_HOTEL', 'CAT-0001', 'Travel & Hotel'),

  @JsonValue('ELECTRONICS')
  electronics('ELECTRONICS', 'CAT-0002', 'Electronics'),

  @JsonValue('FASHION')
  fashion('FASHION', 'CAT-0003', 'Fashion'),

  @JsonValue('BEAUTY_HEALTH')
  beautyHealth('BEAUTY_HEALTH', 'CAT-0004', 'Beauty & Health'),

  @JsonValue('HOME_LIVING')
  homeLiving('HOME_LIVING', 'CAT-0005', 'Home & Living'),

  @JsonValue('FOOD_GROCERY')
  foodGrocery('FOOD_GROCERY', 'CAT-0006', 'Food & Grocery');

  final String value;
  final String categoryId;
  final String displayName;

  const VoucherCategoryType(this.value, this.categoryId, this.displayName);

  static VoucherCategoryType? fromString(String? value) {
    if (value == null) return null;
    return VoucherCategoryType.values.firstWhere(
      (element) => element.value == value,
      orElse: () => throw ArgumentError('Invalid VoucherCategoryType: $value'),
    );
  }

  static VoucherCategoryType? fromCategoryId(String? categoryId) {
    if (categoryId == null) return null;
    try {
      return VoucherCategoryType.values.firstWhere(
        (element) => element.categoryId == categoryId,
      );
    } catch (_) {
      return null;
    }
  }
}

@freezed
class Voucher with _$Voucher {
  factory Voucher({
    int? id,
    @Default("") String affiliateLink,
    @Default("") String campaignName,
    @Default("") String code,
    @Default("") String imageUrl,
    @Default("") String title,
    @Default("") String validDate,
    @Default("") String description,
    @Default([]) List<String> categoryIds,
  }) = _Voucher;

  factory Voucher.fromJson(Map<String, Object?> json) => _$VoucherFromJson(json);
}

@freezed
class FindVouchersRequest with _$FindVouchersRequest {
  factory FindVouchersRequest(
      {@Default(0) int siteId,
      @Default('ALL') String type,
      @Default(1) int startIndex,
      @Default(100) int size,
      int? campaignId,
      String? category}) = _FindVouchersRequest;

  factory FindVouchersRequest.fromJson(Map<String, Object?> json) => _$FindVouchersRequestFromJson(json);
}

@freezed
class VoucherCategory with _$VoucherCategory {
  const factory VoucherCategory({
    required String categoryId,
    @Default(0) int numberOfPromos,
  }) = _VoucherCategory;

  factory VoucherCategory.fromJson(Map<String, dynamic> json) => _$VoucherCategoryFromJson(json);
}

enum VoucherStyle {
  ALL('All'),
  CODES('Codes'),
  OTHERS('Others');

  final String value;

  const VoucherStyle(this.value);
}
