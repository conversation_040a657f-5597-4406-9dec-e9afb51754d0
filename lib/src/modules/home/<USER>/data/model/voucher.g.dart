// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voucher.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoucherImpl _$$VoucherImplFromJson(Map<String, dynamic> json) =>
    _$VoucherImpl(
      id: (json['id'] as num?)?.toInt(),
      affiliateLink: json['affiliateLink'] as String? ?? "",
      campaignName: json['campaignName'] as String? ?? "",
      code: json['code'] as String? ?? "",
      imageUrl: json['imageUrl'] as String? ?? "",
      title: json['title'] as String? ?? "",
      validDate: json['validDate'] as String? ?? "",
      description: json['description'] as String? ?? "",
      categoryIds: (json['categoryIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$VoucherImplToJson(_$VoucherImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'affiliateLink': instance.affiliateLink,
      'campaignName': instance.campaignName,
      'code': instance.code,
      'imageUrl': instance.imageUrl,
      'title': instance.title,
      'validDate': instance.validDate,
      'description': instance.description,
      'categoryIds': instance.categoryIds,
    };

_$FindVouchersRequestImpl _$$FindVouchersRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$FindVouchersRequestImpl(
      siteId: (json['siteId'] as num?)?.toInt() ?? 0,
      type: json['type'] as String? ?? 'ALL',
      startIndex: (json['startIndex'] as num?)?.toInt() ?? 1,
      size: (json['size'] as num?)?.toInt() ?? 100,
      campaignId: (json['campaignId'] as num?)?.toInt(),
      category: json['category'] as String?,
    );

Map<String, dynamic> _$$FindVouchersRequestImplToJson(
        _$FindVouchersRequestImpl instance) =>
    <String, dynamic>{
      'siteId': instance.siteId,
      'type': instance.type,
      'startIndex': instance.startIndex,
      'size': instance.size,
      'campaignId': instance.campaignId,
      'category': instance.category,
    };

_$VoucherCategoryImpl _$$VoucherCategoryImplFromJson(
        Map<String, dynamic> json) =>
    _$VoucherCategoryImpl(
      categoryId: json['categoryId'] as String,
      numberOfPromos: (json['numberOfPromos'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$VoucherCategoryImplToJson(
        _$VoucherCategoryImpl instance) =>
    <String, dynamic>{
      'categoryId': instance.categoryId,
      'numberOfPromos': instance.numberOfPromos,
    };
