import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/campaign/campaign_module.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_search_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/data/repository/voucher_repository.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/voucher_page.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/voucher_search_page.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';

class VoucherModule extends Module {
  @override
  List<Module> get imports => [
        SharedModule(),
        VoucherSharedModule(),
        CampaignSharedModule(),
      ];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addLazySingleton(VoucherCubit.new);
    i.addLazySingleton(VoucherSearchCubit.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.child('/', child: (i) => const VoucherPage());
    r.child('/search', child: (i) => const VoucherSearchPage());
  }
}

class VoucherSharedModule extends Module {
  @override
  List<Module> get imports => [SharedModule()];

  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);
    i.addLazySingleton(VoucherRepository.new);
  }
}
