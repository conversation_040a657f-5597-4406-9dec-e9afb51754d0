import 'package:flutter_modular/flutter_modular.dart';

import '../shared/constants/instance_key.dart';
import '../shared/services/secure_storage_helper.dart';

class LoginRedirectGuard extends RouteGuard {
  LoginRedirectGuard() : super(redirectTo: '/navigation');
  static final storage = SecureStorageHelper();

  @override
  Future<bool> canActivate(String path, ParallelRoute route) async {
    final accessToken = await storage.read(InstanceConstants.tokenKey);
    return accessToken == null;
  }
}
