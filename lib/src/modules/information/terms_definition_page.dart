import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/shared/constants/locale_constants.dart';
import 'package:koc_app/src/shared/constants/text_constants.dart';

class TermsDefinitionPage extends StatelessWidget {
  const TermsDefinitionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Term\'s definition'),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1.r),
          child: Divider(
            height: 1.r,
          ),
        ),
        actions: [
          PopupMenuButton<Locale>(
            iconSize: 30.r,
            icon: const Icon(Icons.language, color: Colors.black),
            onSelected: (Locale locale) {
              context.setLocale(locale);
            },
            itemBuilder: (BuildContext context) {
              return LocaleConstants.supportedLocales.entries.map((entry) {
                return PopupMenuItem<Locale>(
                  value: entry.key,
                  child: Text(
                    entry.value,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                );
              }).toList();
            },
          ),
        ],
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    List<String> keys = TextConstants.termsDefinitions.keys.toList();
    List<String> values = TextConstants.termsDefinitions.values.toList();
    return Padding(
      padding: EdgeInsets.all(5.r),
      child: ListView.separated(
          itemBuilder: (_, i) {
            return Padding(
              padding: EdgeInsets.all(5.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    keys[i],
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  Text(
                    values[i].tr(),
                    style: Theme.of(context).textTheme.bodyMedium,
                  )
                ],
              ),
            );
          },
          separatorBuilder: (_, i) {
            return Divider(height: 1.r);
          },
          itemCount: TextConstants.termsDefinitions.length),
    );
  }
}
