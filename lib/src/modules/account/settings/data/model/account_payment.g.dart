// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_payment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AccountPaymentImpl _$$AccountPaymentImplFromJson(Map<String, dynamic> json) =>
    _$AccountPaymentImpl(
      bankId: (json['bankId'] as num?)?.toInt() ?? 0,
      bankName: json['bankName'] as String? ?? '',
      branchName: json['branchName'] as String? ?? '',
      accountNumber: json['accountNumber'] as String? ?? '',
      accountName: json['accountHolderName'] as String? ?? '',
      accountType:
          $enumDecodeNullable(_$BankAccountTypeEnumMap, json['accountType']) ??
              BankAccountType.CURRENT,
      bankPassbookImageName: json['bankPassbookImageName'] as String? ?? '',
      code: json['swiftBicCode'] as String? ?? '',
      currency: json['currency'] == null
          ? const CurrencyInfo()
          : CurrencyInfo.fromJson(json['currency'] as Map<String, dynamic>),
      branchAddress: json['address'] as String? ?? '',
      zipCode: json['zipCode'] as String? ?? '',
      ifsc: json['ifsc'] as String? ?? '',
    );

Map<String, dynamic> _$$AccountPaymentImplToJson(
        _$AccountPaymentImpl instance) =>
    <String, dynamic>{
      'bankId': instance.bankId,
      'bankName': instance.bankName,
      'branchName': instance.branchName,
      'accountNumber': instance.accountNumber,
      'accountHolderName': instance.accountName,
      'accountType': _$BankAccountTypeEnumMap[instance.accountType]!,
      'bankPassbookImageName': instance.bankPassbookImageName,
      'swiftBicCode': instance.code,
      'currency': instance.currency,
      'address': instance.branchAddress,
      'zipCode': instance.zipCode,
      'ifsc': instance.ifsc,
    };

const _$BankAccountTypeEnumMap = {
  BankAccountType.CURRENT: 'CURRENT',
  BankAccountType.SAVINGS: 'SAVINGS',
};
