// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/account/settings/data/model/currency_info.dart';

part 'account_payment.freezed.dart';
part 'account_payment.g.dart';

@freezed
class AccountPayment with _$AccountPayment {
  const factory AccountPayment(
      { 
    @Default(0) int bankId,
    @Default('') String bankName,
    @Default('') String branchName,
    @Default('') String accountNumber,
    @JsonKey(name: 'accountHolderName') @Default('') String accountName,
    @Default(BankAccountType.CURRENT) BankAccountType accountType,
    @Default('') String bankPassbookImageName,
    @JsonKey(name: 'swiftBicCode') @Default('') String code,
    @Default(CurrencyInfo()) CurrencyInfo currency,
    @JsonKey(name: 'address') @Default('') String branchAddress,
    @Default('') String zipCode,
    @Default('') String ifsc}) = _AccountPayment;

  factory AccountPayment.fromJson(Map<String, Object?> json) =>
      _$AccountPaymentFromJson(json);
}

enum BankAccountType {
  CURRENT,
  SAVINGS;

  static BankAccountType? fromString(String value) {
    for (var type in BankAccountType.values) {
      if (type.toString().split('.').last.toUpperCase() ==
          value.toUpperCase()) {
        return type;
      }
    }
    return null;
  }
}
