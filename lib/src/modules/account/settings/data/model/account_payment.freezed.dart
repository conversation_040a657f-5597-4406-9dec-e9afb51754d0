// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_payment.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AccountPayment _$AccountPaymentFromJson(Map<String, dynamic> json) {
  return _AccountPayment.fromJson(json);
}

/// @nodoc
mixin _$AccountPayment {
  int get bankId => throw _privateConstructorUsedError;
  String get bankName => throw _privateConstructorUsedError;
  String get branchName => throw _privateConstructorUsedError;
  String get accountNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'accountHolderName')
  String get accountName => throw _privateConstructorUsedError;
  BankAccountType get accountType => throw _privateConstructorUsedError;
  String get bankPassbookImageName => throw _privateConstructorUsedError;
  @JsonKey(name: 'swiftBicCode')
  String get code => throw _privateConstructorUsedError;
  CurrencyInfo get currency => throw _privateConstructorUsedError;
  @JsonKey(name: 'address')
  String get branchAddress => throw _privateConstructorUsedError;
  String get zipCode => throw _privateConstructorUsedError;
  String get ifsc => throw _privateConstructorUsedError;

  /// Serializes this AccountPayment to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountPayment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountPaymentCopyWith<AccountPayment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountPaymentCopyWith<$Res> {
  factory $AccountPaymentCopyWith(
          AccountPayment value, $Res Function(AccountPayment) then) =
      _$AccountPaymentCopyWithImpl<$Res, AccountPayment>;
  @useResult
  $Res call(
      {int bankId,
      String bankName,
      String branchName,
      String accountNumber,
      @JsonKey(name: 'accountHolderName') String accountName,
      BankAccountType accountType,
      String bankPassbookImageName,
      @JsonKey(name: 'swiftBicCode') String code,
      CurrencyInfo currency,
      @JsonKey(name: 'address') String branchAddress,
      String zipCode,
      String ifsc});

  $CurrencyInfoCopyWith<$Res> get currency;
}

/// @nodoc
class _$AccountPaymentCopyWithImpl<$Res, $Val extends AccountPayment>
    implements $AccountPaymentCopyWith<$Res> {
  _$AccountPaymentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountPayment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankId = null,
    Object? bankName = null,
    Object? branchName = null,
    Object? accountNumber = null,
    Object? accountName = null,
    Object? accountType = null,
    Object? bankPassbookImageName = null,
    Object? code = null,
    Object? currency = null,
    Object? branchAddress = null,
    Object? zipCode = null,
    Object? ifsc = null,
  }) {
    return _then(_value.copyWith(
      bankId: null == bankId
          ? _value.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as int,
      bankName: null == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String,
      branchName: null == branchName
          ? _value.branchName
          : branchName // ignore: cast_nullable_to_non_nullable
              as String,
      accountNumber: null == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String,
      accountName: null == accountName
          ? _value.accountName
          : accountName // ignore: cast_nullable_to_non_nullable
              as String,
      accountType: null == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as BankAccountType,
      bankPassbookImageName: null == bankPassbookImageName
          ? _value.bankPassbookImageName
          : bankPassbookImageName // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as CurrencyInfo,
      branchAddress: null == branchAddress
          ? _value.branchAddress
          : branchAddress // ignore: cast_nullable_to_non_nullable
              as String,
      zipCode: null == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String,
      ifsc: null == ifsc
          ? _value.ifsc
          : ifsc // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of AccountPayment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CurrencyInfoCopyWith<$Res> get currency {
    return $CurrencyInfoCopyWith<$Res>(_value.currency, (value) {
      return _then(_value.copyWith(currency: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AccountPaymentImplCopyWith<$Res>
    implements $AccountPaymentCopyWith<$Res> {
  factory _$$AccountPaymentImplCopyWith(_$AccountPaymentImpl value,
          $Res Function(_$AccountPaymentImpl) then) =
      __$$AccountPaymentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int bankId,
      String bankName,
      String branchName,
      String accountNumber,
      @JsonKey(name: 'accountHolderName') String accountName,
      BankAccountType accountType,
      String bankPassbookImageName,
      @JsonKey(name: 'swiftBicCode') String code,
      CurrencyInfo currency,
      @JsonKey(name: 'address') String branchAddress,
      String zipCode,
      String ifsc});

  @override
  $CurrencyInfoCopyWith<$Res> get currency;
}

/// @nodoc
class __$$AccountPaymentImplCopyWithImpl<$Res>
    extends _$AccountPaymentCopyWithImpl<$Res, _$AccountPaymentImpl>
    implements _$$AccountPaymentImplCopyWith<$Res> {
  __$$AccountPaymentImplCopyWithImpl(
      _$AccountPaymentImpl _value, $Res Function(_$AccountPaymentImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountPayment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankId = null,
    Object? bankName = null,
    Object? branchName = null,
    Object? accountNumber = null,
    Object? accountName = null,
    Object? accountType = null,
    Object? bankPassbookImageName = null,
    Object? code = null,
    Object? currency = null,
    Object? branchAddress = null,
    Object? zipCode = null,
    Object? ifsc = null,
  }) {
    return _then(_$AccountPaymentImpl(
      bankId: null == bankId
          ? _value.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as int,
      bankName: null == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String,
      branchName: null == branchName
          ? _value.branchName
          : branchName // ignore: cast_nullable_to_non_nullable
              as String,
      accountNumber: null == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String,
      accountName: null == accountName
          ? _value.accountName
          : accountName // ignore: cast_nullable_to_non_nullable
              as String,
      accountType: null == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as BankAccountType,
      bankPassbookImageName: null == bankPassbookImageName
          ? _value.bankPassbookImageName
          : bankPassbookImageName // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as CurrencyInfo,
      branchAddress: null == branchAddress
          ? _value.branchAddress
          : branchAddress // ignore: cast_nullable_to_non_nullable
              as String,
      zipCode: null == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String,
      ifsc: null == ifsc
          ? _value.ifsc
          : ifsc // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountPaymentImpl implements _AccountPayment {
  const _$AccountPaymentImpl(
      {this.bankId = 0,
      this.bankName = '',
      this.branchName = '',
      this.accountNumber = '',
      @JsonKey(name: 'accountHolderName') this.accountName = '',
      this.accountType = BankAccountType.CURRENT,
      this.bankPassbookImageName = '',
      @JsonKey(name: 'swiftBicCode') this.code = '',
      this.currency = const CurrencyInfo(),
      @JsonKey(name: 'address') this.branchAddress = '',
      this.zipCode = '',
      this.ifsc = ''});

  factory _$AccountPaymentImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountPaymentImplFromJson(json);

  @override
  @JsonKey()
  final int bankId;
  @override
  @JsonKey()
  final String bankName;
  @override
  @JsonKey()
  final String branchName;
  @override
  @JsonKey()
  final String accountNumber;
  @override
  @JsonKey(name: 'accountHolderName')
  final String accountName;
  @override
  @JsonKey()
  final BankAccountType accountType;
  @override
  @JsonKey()
  final String bankPassbookImageName;
  @override
  @JsonKey(name: 'swiftBicCode')
  final String code;
  @override
  @JsonKey()
  final CurrencyInfo currency;
  @override
  @JsonKey(name: 'address')
  final String branchAddress;
  @override
  @JsonKey()
  final String zipCode;
  @override
  @JsonKey()
  final String ifsc;

  @override
  String toString() {
    return 'AccountPayment(bankId: $bankId, bankName: $bankName, branchName: $branchName, accountNumber: $accountNumber, accountName: $accountName, accountType: $accountType, bankPassbookImageName: $bankPassbookImageName, code: $code, currency: $currency, branchAddress: $branchAddress, zipCode: $zipCode, ifsc: $ifsc)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountPaymentImpl &&
            (identical(other.bankId, bankId) || other.bankId == bankId) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.branchName, branchName) ||
                other.branchName == branchName) &&
            (identical(other.accountNumber, accountNumber) ||
                other.accountNumber == accountNumber) &&
            (identical(other.accountName, accountName) ||
                other.accountName == accountName) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.bankPassbookImageName, bankPassbookImageName) ||
                other.bankPassbookImageName == bankPassbookImageName) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.branchAddress, branchAddress) ||
                other.branchAddress == branchAddress) &&
            (identical(other.zipCode, zipCode) || other.zipCode == zipCode) &&
            (identical(other.ifsc, ifsc) || other.ifsc == ifsc));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      bankId,
      bankName,
      branchName,
      accountNumber,
      accountName,
      accountType,
      bankPassbookImageName,
      code,
      currency,
      branchAddress,
      zipCode,
      ifsc);

  /// Create a copy of AccountPayment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountPaymentImplCopyWith<_$AccountPaymentImpl> get copyWith =>
      __$$AccountPaymentImplCopyWithImpl<_$AccountPaymentImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountPaymentImplToJson(
      this,
    );
  }
}

abstract class _AccountPayment implements AccountPayment {
  const factory _AccountPayment(
      {final int bankId,
      final String bankName,
      final String branchName,
      final String accountNumber,
      @JsonKey(name: 'accountHolderName') final String accountName,
      final BankAccountType accountType,
      final String bankPassbookImageName,
      @JsonKey(name: 'swiftBicCode') final String code,
      final CurrencyInfo currency,
      @JsonKey(name: 'address') final String branchAddress,
      final String zipCode,
      final String ifsc}) = _$AccountPaymentImpl;

  factory _AccountPayment.fromJson(Map<String, dynamic> json) =
      _$AccountPaymentImpl.fromJson;

  @override
  int get bankId;
  @override
  String get bankName;
  @override
  String get branchName;
  @override
  String get accountNumber;
  @override
  @JsonKey(name: 'accountHolderName')
  String get accountName;
  @override
  BankAccountType get accountType;
  @override
  String get bankPassbookImageName;
  @override
  @JsonKey(name: 'swiftBicCode')
  String get code;
  @override
  CurrencyInfo get currency;
  @override
  @JsonKey(name: 'address')
  String get branchAddress;
  @override
  String get zipCode;
  @override
  String get ifsc;

  /// Create a copy of AccountPayment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountPaymentImplCopyWith<_$AccountPaymentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
