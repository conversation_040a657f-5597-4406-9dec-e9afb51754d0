// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';

part 'currency_info.freezed.dart';
part 'currency_info.g.dart';

@freezed
class CurrencyInfo with _$CurrencyInfo {
  const factory CurrencyInfo({
    @Default('') String code,
    @Default('') String name,
  }) = _CurrencyInfo;

  factory CurrencyInfo.fromJson(Map<String, Object?> json) =>
      _$CurrencyInfoFromJson(json);
}