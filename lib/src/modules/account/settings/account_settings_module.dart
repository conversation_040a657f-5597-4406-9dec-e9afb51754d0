import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/account/account_module.dart';
import 'package:koc_app/src/modules/account/data/repository/bank_repository.dart';
import 'package:koc_app/src/modules/account/presentation/page/account_verification_page.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/bank_list_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/edit_payment_cubit.dart';
import 'package:koc_app/src/modules/account/settings/presentation/account_settings_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/deactivate_account_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/edit_account_type_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/edit_address_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/edit_email_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/edit_name_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/edit_password_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/edit_payment_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/edit_phone_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/new_password_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/remove_profile_picture_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/show_deactivate_account_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/show_payment_page.dart';
import 'package:koc_app/src/modules/account/settings/presentation/show_phone_page.dart';
import 'package:koc_app/src/modules/shared/cubit/common_bool_cubit.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';

class AccountSettingsModule extends Module {
  @override
  List<Module> get imports => [SharedModule(), AccountSharedModule()];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addLazySingleton(BankRepository.new);
    i.addLazySingleton(BankListCubit.new);
    i.addLazySingleton(EditPaymentCubit.new);
    i.addLazySingleton(AccountSettingsCubit.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.child('/', child: (i) => const AccountSettingsPage());
    r.child('/remove-profile-picture', child: (i) => const RemoveProfilePicturePage());
    r.child('/edit-name', child: (i) => const EditNamePage());
    r.child('/edit-email', child: (i) => const EditEmailPage());
    r.child('/show-phone', child: (i) => const ShowPhonePage());
    r.child('/edit-phone', child: (i) => const EditPhonePage());
    r.child('/edit-address', child: (i) => const EditAddressPage());
    r.child('/new-password', child: (i) => const NewPasswordPage());
    r.child('/edit-password',
        child: (i) => BlocProvider.value(value: Modular.get<CommonBoolCubit>(), child: const EditPasswordPage()));
    r.child('/edit-account-type',
        child: (i) => BlocProvider.value(value: Modular.get<CommonBoolCubit>(), child: const EditAccountTypePage()));
    r.child(
      '/edit-payment',
      child: (i) => MultiBlocProvider(providers: [
        BlocProvider.value(value: Modular.get<EditPaymentCubit>()),
        BlocProvider.value(value: Modular.get<BankListCubit>()),
      ], child: const EditPaymentPage()),
    );
    r.child('/show-payment', child: (i) => const ShowPaymentPage());
    r.child('/show-deactivate-account', child: (i) => const ShowDeactivateAccountPage());
    r.child('/status/:action', child: (i) => DeactiveOrDeleteAccountPage(action: r.args.params['action']));
    r.child('/account-verification', child: (i) => const AccountVerificationPage());
  }
}
