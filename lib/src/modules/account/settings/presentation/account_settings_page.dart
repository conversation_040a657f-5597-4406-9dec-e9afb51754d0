import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_avatar_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_state.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:material_symbols_icons/symbols.dart';

class AccountSettingsPage extends StatefulWidget {
  const AccountSettingsPage({super.key});

  @override
  State<AccountSettingsPage> createState() => _AccountSettingsPageState();
}

class _AccountSettingsPageState extends BasePageState<AccountSettingsPage, AccountSettingsCubit> {
  @override
  void initState() {
    doLoadingAction(() async {
      await cubit.findAccount();
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Account settings')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<AccountSettingsCubit, AccountSettingsState>(
      bloc: cubit,
      builder: (context, state) {
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.r),
            child: Column(
              children: [
                SizedBox(
                  height: 16.r,
                ),
                _buildTile(
                    'Profile picture',
                    'Add a profile picture to help personalize your account',
                    '',
                    Symbols.add_a_photo_sharp,
                    InkWell(
                      onTap: () {
                        _showModalBottomSheet(state);
                      },
                      child: Column(
                        children: [
                          if (state.avatar.isNotEmpty)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(9999),
                              child: CachedImageWithPlaceholder(
                                imageUrl: state.avatar,
                                width: 32.r,
                                height: 32.r,
                                fit: BoxFit.cover,
                              ),
                            ),
                          if (state.avatar.isEmpty)
                            Container(
                              width: 32.r,
                              height: 32.r,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Color(0xFFFFB522),
                              ),
                              child: Center(
                                child:
                                    Text(state.firstName.isEmpty ? '' : state.firstName.substring(0, 1).toUpperCase(),
                                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                              color: Colors.white,
                                              fontWeight: FontWeight.w500,
                                            )),
                              ),
                            ),
                          SizedBox(height: 8.r),
                          Text(
                            'Edit',
                            style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFFEF6507)),
                          ),
                        ],
                      ),
                    )),
                _buildTile('Name', '${state.firstName} ${state.lastName}', '/account/settings/edit-name',
                    Symbols.badge_sharp, null),
                _buildTile('Email', state.email, '/account/settings/edit-email', Symbols.mail_sharp, null),
                _buildTile(
                    'Phone number',
                    _getOrNone(state.phoneNumber),
                    state.phoneNumber.isNotEmpty ? '/account/settings/show-phone' : '/account/settings/edit-phone',
                    Symbols.ad_units_sharp,
                    null),
                _buildTile('Address', _getOrNone(state.address), '/account/settings/edit-address',
                    Symbols.location_on_sharp, null),
                _buildTile(
                    'Password',
                    state.hasPassword
                        ? state.updatedPasswordOn != null
                            ? 'Last changed ${state.updatedPasswordOn!.toFormattedString()}'
                            : ''
                        : 'None',
                    state.hasPassword ? '/account/settings/edit-password' : '/account/settings/new-password',
                    Symbols.key_sharp,
                    null),
                _buildTile(
                    'Payment',
                    state.isBankAccountValid ? '' : 'None',
                    Modular.get<AccountCubit>().state.paymentOtpToken.isNotEmpty
                        ? '/account/settings/show-payment'
                        : '/account/settings/account-verification',
                    Symbols.account_balance_sharp,
                    null),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showModalBottomSheet(AccountSettingsState state) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.r),
              topRight: Radius.circular(12.r),
            ),
          ),
          padding: EdgeInsets.all(10.r),
          height: 180.r,
          child: Column(
            children: [
              _buildBottomTile(
                  () => _chooseFromGallery(), Icons.image_outlined, 'Choose from library', const Color(0xFF464646)),
              _buildBottomTile(
                  () => _takePicture(), Icons.camera_alt_outlined, 'Take a photo', const Color(0xFF464646)),
              if (state.avatar.isNotEmpty)
                _buildBottomTile(() {
                  Navigator.pop(context);
                  Modular.to.pushNamed('/account/settings/remove-profile-picture');
                }, Icons.delete_outline, 'Remove current photo', Colors.red),
            ],
          ),
        );
      },
    );
  }

  SizedBox _buildBottomTile(VoidCallback onTap, IconData iconData, String title, Color color) {
    return SizedBox(
      height: 48.r,
      child: ListTile(
        onTap: onTap,
        leading: Icon(
          iconData,
          size: 24.r,
          color: color,
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500, color: color),
        ),
      ),
    );
  }

  Future<void> _takePicture() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera, imageQuality: 70);
    if (image != null) {
      await _updateAvatar(image);
      Modular.to.pop();
    }
  }

  Future<void> _chooseFromGallery() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      await _updateAvatar(image);
      Modular.to.pop();
    }
  }

  Future<void> _updateAvatar(XFile image) async {
    await doLoadingAction(() async {
      final filePath = image.path;
      final lastIndex = filePath.lastIndexOf(RegExp(r'.jp'));
      final splitted = filePath.substring(0, (lastIndex));
      final outPath = "${splitted}_out${filePath.substring(lastIndex)}";

      var result = await FlutterImageCompress.compressAndGetFile(
        image.path,
        outPath,
        quality: 70,
      );

      if (result != null) {
        await cubit.updateAvatar(XFile(result.path));
        await cubit.findAccount();
        await Modular.get<AccountAvatarCubit>().findAvatar();
      }
    });
  }

  String _getOrNone(String value) {
    return value.isEmpty ? 'None' : value;
  }

  Widget _buildTile(String title, String description, String route, IconData iconData, Widget? iconButton) {
    return GestureDetector(
      onTap: () {
        if (route.isNotEmpty) {
          Modular.to.pushNamed(route);
        }
      },
      child: SizedBox(
        height: 72.r,
        child: Row(
          spacing: 16.r,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              iconData,
              size: 24.r,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
                  ),
                  if (description.trim().isNotEmpty)
                    Text(
                      description,
                      style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)),
                    )
                ],
              ),
            ),
            iconButton ?? const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}
