import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_avatar_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class RemoveProfilePicturePage extends StatefulWidget {
  const RemoveProfilePicturePage({super.key});

  @override
  State<RemoveProfilePicturePage> createState() => _RemoveProfilePicturePageState();
}

class _RemoveProfilePicturePageState extends BasePageState<RemoveProfilePicturePage, AccountSettingsCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text(' ')),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 24.r),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return Column(children: [
      Expanded(
        child: Column(
          spacing: 16.r,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Remove profile picture?',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Your profile picture will be removed and changed to your initials.',
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(9999),
                  child: CachedImageWithPlaceholder(
                    imageUrl: cubit.state.avatar,
                    width: 50.r,
                    height: 50.r,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.r),
                  child: Icon(Icons.arrow_forward_rounded, size: 20.r),
                ),
                Container(
                  width: 50.r,
                  height: 50.r,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xFFFFB522),
                  ),
                  child: Center(
                    child: Text(
                      cubit.state.firstName.isNotEmpty ? cubit.state.firstName.substring(0, 1).toUpperCase() : '',
                      style: Theme.of(context)
                          .textTheme
                          .labelLarge!
                          .copyWith(color: Colors.white, fontWeight: FontWeight.w500),
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      ),
      _buildButtons(),
    ]);
  }

  Widget _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextButton(
          onPressed: () {
            Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
          },
          child: Text(
            'Cancel',
            style: Theme.of(context)
                .textTheme
                .labelMedium!
                .copyWith(color: const Color(0xFFEF6507), fontWeight: FontWeight.w500),
          ),
        ),
        ElevatedButton(
            onPressed: () async {
              cubit.showLoading();
              await cubit.removeAvatar();
              await cubit.findAccount();
              await Modular.get<AccountAvatarCubit>().findAvatar();
              cubit.hideLoading();
              Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
            },
            child: Text(
              'Remove',
              style:
                  Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.white, fontWeight: FontWeight.w500),
            )),
      ],
    );
  }
}
