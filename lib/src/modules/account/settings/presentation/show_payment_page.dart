import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_state.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class ShowPaymentPage extends StatefulWidget {
  const ShowPaymentPage({super.key});

  @override
  State<ShowPaymentPage> createState() => _ShowPaymentPageState();
}

class _ShowPaymentPageState extends BasePageState<ShowPaymentPage, AccountSettingsCubit> {
  @override
  void initState() {
    doLoadingAction(() async {
      await cubit.getBankAccount();
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(appBar: const CommonAppBar(title: Text('Payment')), body: _buildBody());
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        spacing: 16.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Fill in your bank account details to ensure prompt payments.',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          BlocBuilder<AccountSettingsCubit, AccountSettingsState>(
              bloc: cubit,
              builder: (context, state) {
                return Row(
                  spacing: 16.r,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.credit_card_outlined,
                      size: 24.r,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          state.payment.bankName,
                          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          state.payment.accountNumber.toSecure(),
                          style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)),
                        ),
                      ],
                    ),
                  ],
                );
              }),
          Expanded(
            child: ConfirmationButtons(
              btnName: 'Change',
              showCancelButton: true,
              isValid: true,
              onTap: () async {
                final result = await Modular.to.pushNamed<bool?>('/account/settings/edit-payment');
                if (result == true) {
                  context.showSnackBar('Bank account is updated successfully', durationSecond: 0);
                }
              },
              onCancel: () {
                Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
              },
            ),
          )
        ],
      ),
    );
  }
}
