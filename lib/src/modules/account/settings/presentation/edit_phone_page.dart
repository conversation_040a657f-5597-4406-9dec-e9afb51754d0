import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/validator/validators.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class EditPhonePage extends StatefulWidget {
  const EditPhonePage({super.key});

  @override
  State<EditPhonePage> createState() => _EditPhonePageState();
}

class _EditPhonePageState extends BasePageState<EditPhonePage, AccountSettingsCubit> {
  final TextEditingController _phoneController = TextEditingController();

  @override
  void initState() {
    _phoneController.text = cubit.state.phoneNumber;
    super.initState();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
        appBar: const CommonAppBar(
          title: Text('Phone number'),
        ),
        body: Padding(
          padding: EdgeInsets.all(16.r),
          child: _buildBody(),
        ));
  }

  Widget _buildBody() {
    return Column(spacing: 16.r, children: [
      Text(
        'Your number can be used to deliver important notifications, help you sign in, and more ',
        style: Theme.of(context).textTheme.bodySmall,
      ),
      TextField(
        controller: _phoneController,
        style: Theme.of(context).textTheme.bodySmall,
        decoration: InputDecoration(
          labelText: 'Phone number',
          labelStyle: Theme.of(context).textTheme.bodySmall,
          border: const OutlineInputBorder(),
        ),
      ),
      ValueListenableBuilder(
        valueListenable: _phoneController,
        builder: (context, _, __) {
          return Expanded(
            child: ConfirmationButtons(
              btnName: 'Save',
              showCancelButton: true,
              onTap: () async {
                await doLoadingAction(() async {
                  bool result = await cubit.updatePhoneNumber(_phoneController.text);
                  if (context.mounted) {
                    context
                        .showSnackBar(result ? 'Phone number updated successfully' : 'Failed to update phone number');
                  }
                  Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
                });
              },
              isValid: _phoneController.text.isNotEmpty &&
                  cubit.state.phoneNumber != _phoneController.text &&
                  Validators.isValidPhoneNumber(_phoneController.text),
            ),
          );
        },
      ),
    ]);
  }
}
