import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class EditEmailPage extends StatefulWidget {
  const EditEmailPage({super.key});

  @override
  State<EditEmailPage> createState() => _EditEmailPageState();
}

class _EditEmailPageState extends BasePageState<EditEmailPage, AccountSettingsCubit> {
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    _emailController.text = cubit.state.email;
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Email')),
      body: Padding(
        padding: EdgeInsets.all(16.r),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Change email address?',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        SizedBox(
          height: 16.r,
        ),
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: context.textBodySmall(),
            children: [
              const TextSpan(
                text: 'We\'re sorry! You can’t change this address: ',
              ),
              TextSpan(text: cubit.state.email, style: context.textBodySmall(fontWeight: FontWeight.bold)),
            ],
          ),
        ),
        Text(
          'The address used to identify your account to you and others.',
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
