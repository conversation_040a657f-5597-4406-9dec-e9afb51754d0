import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class ShowDeactivateAccountPage extends StatefulWidget {
  const ShowDeactivateAccountPage({super.key});

  @override
  State<ShowDeactivateAccountPage> createState() =>
      _ShowDeactivateAccountPageState();
}

class _ShowDeactivateAccountPageState
    extends BasePageState<ShowDeactivateAccountPage, AccountCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Deactivate or Delete')),
      body: _buildBody(),
      bottomSheet: Container(
        height: 76.r,
        color: Colors.white,
        alignment: Alignment.center,
        child: GestureDetector(
          onTap: () {
            cubit.deactivateAccount();
            Modular.to.navigate('/');
          },
          child: Text(
            'Deactivate',
            style: Theme.of(context).textTheme.labelLarge!.copyWith(
                fontWeight: FontWeight.w500, color: const Color(0xFFEF6507)),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: 16.r,
        children: [
          Text(
            'Deactivate your account?',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            'Deactivate to temporarily hide your profile and no one can see your account.',
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          _buildPictureAndName(),
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(children: [
              TextSpan(
                  text:
                      'You can reactivate your account at anytime. If you want to use KOC mobile app again, just log in with ',
                  style: Theme.of(context).textTheme.bodySmall),
              TextSpan(
                  text: cubit.state.email,
                  style: Theme.of(context)
                      .textTheme
                      .bodySmall!
                      .copyWith(fontWeight: FontWeight.bold))
            ]),
          ),
        ],
      ),
    );
  }

  Widget _buildPictureAndName() {
    return Row(
      spacing: 8.r,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (cubit.state.profilePictureUrl.isNotEmpty)
          CircleAvatar(
            radius: 24.r,
            backgroundImage: NetworkImage(cubit.state.profilePictureUrl),
          ),
        if (cubit.state.profilePictureUrl.isEmpty)
          Container(
            width: 48.r,
            height: 48.r,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Color(0xFFFFB522),
            ),
            child: Center(
              child: Text(
                  cubit.state.firstName.isEmpty
                      ? ''
                      : cubit.state.firstName.substring(0, 1).toUpperCase(),
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      )),
            ),
          ),
        Text(
          '${cubit.state.lastName} ${cubit.state.firstName}',
          style: Theme.of(context).textTheme.bodySmall,
        )
      ],
    );
  }

  Widget _buildTile(String title, String subtitle, String route) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium!
                    .copyWith(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10.r),
              Text(
                subtitle,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium!
                    .copyWith(color: Colors.grey),
              )
            ],
          ),
        ),
        IconButton(
          icon: const Icon(Icons.chevron_right),
          iconSize: 30.r,
          onPressed: () {
            Modular.to.pushNamed(route);
          },
        )
      ],
    );
  }
}
