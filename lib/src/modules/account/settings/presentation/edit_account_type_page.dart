import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/data/model/account.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/shared/cubit/common_bool_cubit.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class EditAccountTypePage extends StatefulWidget {
  const EditAccountTypePage({super.key});

  @override
  State<EditAccountTypePage> createState() => _EditAccountTypePageState();
}

class _EditAccountTypePageState
    extends BasePageState<EditAccountTypePage, AccountCubit> {
  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _companyAddressController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    ReadContext(context).read<CommonBoolCubit>().setValue(isValid());
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _companyAddressController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
          title: Text(
              'Change to ${_isIndividual() ? 'personal' : 'company'} account')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(10.r),
      child: Wrap(
          crossAxisAlignment: WrapCrossAlignment.start,
          runSpacing: 10.r,
          children: [
            Text(
              'You are working for your corporation and would like to monetize your online media or traffic.',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                    _isIndividual()
                        ? Icons.account_circle_outlined
                        : Icons.apartment_outlined,
                    size: 50.r),
                Padding(
                  padding: EdgeInsets.only(left: 10.r, right: 10.r),
                  child: Icon(Icons.arrow_right_alt_outlined, size: 30.r),
                ),
                Icon(
                    _isIndividual()
                        ? Icons.apartment_outlined
                        : Icons.account_circle_outlined,
                    size: 50.r),
              ],
            ),
            Text(
              'Contact information',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'This will use as your default contact',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Row(
              children: [
                Icon(
                  Icons.person_2_outlined,
                  size: 25.r,
                ),
                SizedBox(
                  width: 10.r,
                ),
                Text(
                  '${cubit.state.firstName} ${cubit.state.lastName}',
                  style: Theme.of(context).textTheme.bodySmall,
                )
              ],
            ),
            Row(
              children: [
                Icon(
                  Icons.email_outlined,
                  size: 25.r,
                ),
                SizedBox(
                  width: 10.r,
                ),
                Text(
                  cubit.state.email,
                  style: Theme.of(context).textTheme.bodySmall,
                )
              ],
            ),
            Row(
              children: [
                Icon(
                  Icons.phone_outlined,
                  size: 25.r,
                ),
                SizedBox(
                  width: 10.r,
                ),
                Text(
                  cubit.state.phoneNumber,
                  style: Theme.of(context).textTheme.bodySmall,
                )
              ],
            ),
            if (_isIndividual())
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Company information',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  SizedBox(
                    height: 10.r,
                  ),
                  SizedBox(
                    width: double.infinity,
                    height: 50.r,
                    child: TextField(
                      onChanged: (value) {
                        ReadContext(context)
                            .read<CommonBoolCubit>()
                            .setValue(isValid());
                      },
                      controller: _companyNameController,
                      decoration: InputDecoration(
                        labelText: 'Company name',
                        labelStyle: Theme.of(context).textTheme.bodySmall,
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10.r,
                  ),
                  SizedBox(
                    width: double.infinity,
                    child: TextField(
                      onChanged: (value) {
                        ReadContext(context)
                            .read<CommonBoolCubit>()
                            .setValue(isValid());
                      },
                      controller: _companyAddressController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: 'Address',
                        hintStyle: Theme.of(context).textTheme.bodySmall,
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10.r,
                  ),
                  Text(
                    'e.g. 3560 Hoffman Avenue, Sunshine, New York, 10007, USA',
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall!
                        .copyWith(color: Colors.grey),
                  ),
                ],
              ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'By changing you agree to',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      'ACCESSTRADE\'s',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      ' Terms ',
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: Colors.blue,
                          decoration: TextDecoration.underline,
                          decorationColor: Colors.blue),
                    ),
                    Text(
                      'and',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text('Privacy Policy',
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                            decorationColor: Colors.blue)),
                  ],
                ),
              ],
            ),
            BlocBuilder<CommonBoolCubit, bool>(builder: (context, state) {
              return ConfirmationButtons(
                  btnName: 'Change',
                  isValid: state,
                  onTap: () async {
                    await cubit.updateAccount(cubit.state.copyWith(
                      accountType: _isIndividual()
                          ? AccountType.LOCAL_COMPANY
                          : AccountType.INDIVIDUAL,
                      companyName: _companyNameController.text,
                      companyAddress: _companyAddressController.text,
                    ));
                    Modular.to
                        .popUntil(ModalRoute.withName('/account/settings/'));
                  });
            })
          ]),
    );
  }

  bool isValid() {
    return !_isIndividual() ||
        _isIndividual() &&
            (_companyNameController.text.isNotEmpty &&
                _companyAddressController.text.isNotEmpty);
  }

  bool _isIndividual() {
    return cubit.state.accountType == AccountType.INDIVIDUAL;
  }
}
