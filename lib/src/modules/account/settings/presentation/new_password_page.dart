import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/shared/model/otp_endpoint.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class NewPasswordPage extends StatefulWidget {
  const NewPasswordPage({super.key});

  @override
  State<NewPasswordPage> createState() => _NewPasswordPageState();
}

class _NewPasswordPageState extends BasePageState<NewPasswordPage, AccountSettingsCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Password')),
      body: _buildBody(),
      bottomSheet: Container(
        color: Colors.white,
        padding: EdgeInsets.all(16.r),
        child: ConfirmationButtons(
          btnName: 'Continue',
          showCancelButton: true,
          onTap: () {
            Modular.to.pushNamed('/verify-identity/${cubit.state.email}', arguments: OtpEndpoint.resetPassword);
          },
          isValid: true,
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(mainAxisAlignment: MainAxisAlignment.center, spacing: 16.r, children: [
        Text(
          'Set a new password?',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          'To add a password to your account for the first time, you will need to use the password reset page so we can verify your identity.',
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ]),
    );
  }
}
