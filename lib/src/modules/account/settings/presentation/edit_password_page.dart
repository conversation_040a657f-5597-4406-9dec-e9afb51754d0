import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/common_bool_cubit.dart';
import 'package:koc_app/src/modules/shared/model/otp_endpoint.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class EditPasswordPage extends StatefulWidget {
  const EditPasswordPage({super.key});

  @override
  State<EditPasswordPage> createState() => _EditPasswordPageState();
}

class _EditPasswordPageState extends BasePageState<EditPasswordPage, AccountSettingsCubit> {
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Password')),
      body: _buildBody(),
      bottomSheet: Container(
        padding: EdgeInsets.all(16.r),
        color: Colors.white,
        child: ValueListenableBuilder(
          valueListenable: _passwordController,
          builder: (context, _, __) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    Modular.to.pushNamed('/forgot-password',
                        arguments: ['/verify-identity/${cubit.state.email}', OtpEndpoint.resetPassword]);
                  },
                  child: Text(
                    'Forgot password?',
                    style: Theme.of(context).textTheme.labelLarge!.copyWith(
                          color: const Color(0xFFEF6507),
                        ),
                  ),
                ),
                ConfirmationButtons(
                  btnName: 'Continue',
                  onTap: () async {
                    cubit.showLoading();
                    bool isPasswordVerified = await cubit.verifyCurrentPassword(_passwordController.text);
                    if (isPasswordVerified) {
                      Modular.to.pushNamed('/reset-password', arguments: _passwordController.text);
                    } else {
                      context.showSnackBar('Wrong password');
                    }
                    cubit.hideLoading();
                  },
                  isValid: _passwordController.text.isNotEmpty,
                  showCancelButton: false,
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          'A secure password helps protect your account ',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        SizedBox(height: 16.r),
        SizedBox(
            width: double.infinity,
            child: BlocBuilder<CommonBoolCubit, bool>(builder: (context, state) {
              return TextField(
                controller: _passwordController,
                focusNode: _focusNode,
                obscureText: !state,
                style: Theme.of(context).textTheme.bodySmall,
                decoration: InputDecoration(
                  labelText: 'Current password',
                  labelStyle: Theme.of(context).textTheme.bodySmall,
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: Icon(
                      !state ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      ReadContext(context).read<CommonBoolCubit>().toggle();
                    },
                  ),
                ),
              );
            })),
      ]),
    );
  }
}
