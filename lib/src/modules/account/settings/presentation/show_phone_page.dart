import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_state.dart';
import 'package:koc_app/src/shared/validator/validators.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class ShowPhonePage extends StatefulWidget {
  const ShowPhonePage({super.key});

  @override
  State<ShowPhonePage> createState() => _ShowPhonePageState();
}

class _ShowPhonePageState extends BasePageState<ShowPhonePage, AccountSettingsCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(appBar: const CommonAppBar(title: Text('Phone number')), body: _buildBody());
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        spacing: 16.r,
        children: [
          Text(
            'This phone number will use as your default contact.',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            spacing: 16.r,
            children: [
              Icon(
                Icons.phone_android_outlined,
                size: 24.r,
              ),
              Expanded(
                  child: BlocBuilder<AccountSettingsCubit, AccountSettingsState>(
                      bloc: cubit,
                      builder: (_, state) {
                        return Text(state.phoneNumber,
                            style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500));
                      }))
            ],
          ),
          Expanded(
            child: ConfirmationButtons(
                btnName: 'Change',
                showCancelButton: true,
                isValid: true,
                onTap: () {
                  _showChangePhoneNumberAlert();
                }),
          )
        ],
      ),
    );
  }

  void _showChangePhoneNumberAlert() {
    final TextEditingController controller = TextEditingController();
    final focusNode = FocusNode();
    controller.text = cubit.state.phoneNumber;
    showDialog(
        context: context,
        builder: (_) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Text(
              'Change phone number?',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              spacing: 16.r,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: controller,
                  focusNode: focusNode,
                  style: Theme.of(context).textTheme.labelLarge,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                )
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  'Cancel',
                  style: Theme.of(context)
                      .textTheme
                      .labelLarge!
                      .copyWith(fontWeight: FontWeight.w500, color: const Color(0xFFEF6507)),
                ),
              ),
              ValueListenableBuilder(
                  valueListenable: controller,
                  builder: (context, _, __) {
                    return ElevatedButton(
                      onPressed: controller.text.isNotEmpty &&
                              cubit.state.phoneNumber != controller.text &&
                              Validators.isValidPhoneNumber(controller.text)
                          ? () {
                              doLoadingAction(() async {
                                await cubit.updatePhoneNumber(controller.text);
                              });
                              Navigator.of(context).pop();
                            }
                          : null,
                      child: Text('Change',
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge!
                              .copyWith(fontWeight: FontWeight.w500, color: Colors.white)),
                    );
                  }),
            ],
          );
        }).then((_) => {
          focusNode.dispose(),
          controller.dispose(),
        });

    Future.delayed(const Duration(milliseconds: 100), () {
      focusNode.requestFocus();
    });
  }
}
