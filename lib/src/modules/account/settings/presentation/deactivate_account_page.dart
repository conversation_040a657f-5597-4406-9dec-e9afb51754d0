import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class DeactiveOrDeleteAccountPage extends StatefulWidget {
  final String action;
  const DeactiveOrDeleteAccountPage({super.key, required this.action});

  @override
  State<DeactiveOrDeleteAccountPage> createState() =>
      _DeactiveOrDeleteAccountPageState();
}

class _DeactiveOrDeleteAccountPageState
    extends BasePageState<DeactiveOrDeleteAccountPage, AccountCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(),
      body: _buildBody(),
      bottomNavigationBar: SizedBox(
        height: 60.r,
        child: TextButton(
          child: Text(
            _getButtonText(),
            style: Theme.of(context)
                .textTheme
                .bodyMedium!
                .copyWith(color: Colors.red),
          ),
          onPressed: () {
            if (_isDeactivate()) {
              cubit.deactivateAccount();
            } else {
              cubit.deleteAccount();
            }
            Modular.to.navigate('/');
          },
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(10.r),
      child: Wrap(
        runSpacing: 10.r,
        children: [
          Text(
            _getTitle(),
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          Text(
            _getDescription(),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (cubit.state.profilePictureUrl.isNotEmpty)
                CachedNetworkImage(
                  imageUrl: cubit.state.profilePictureUrl,
                  width: 100.r,
                  height: 100.r,
                  fit: BoxFit.cover,
                ),
              if (cubit.state.profilePictureUrl.isEmpty)
                Container(
                  width: 100.r,
                  height: 100.r,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.orange,
                  ),
                  child: Center(
                    child: Text(
                      cubit.state.firstName.substring(0, 1).toUpperCase(),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 40.r,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              SizedBox(width: 10.r),
              Text(
                '${cubit.state.firstName} ${cubit.state.lastName}',
                style: Theme.of(context).textTheme.bodyMedium,
              )
            ],
          ),
          Text(
            _getSubDescription(),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  String _getTitle() {
    if (_isDeactivate()) {
      return 'Deactivate your account?';
    } else {
      return 'Delete your account?';
    }
  }

  String _getDescription() {
    if (_isDeactivate()) {
      return 'Deactivate to temporarily hide your profile and no one can see your account. Reactivate your account and recover all content anytime.';
    } else {
      return 'Permanently delete your data and everything associated with your account.';
    }
  }

  String _getSubDescription() {
    if (_isDeactivate()) {
      return 'If you want to use KOC again, just log in with\n${cubit.state.email}';
    } else {
      return 'If you already to leave forever, we will send you an email with the final step to:\n${cubit.state.email}';
    }
  }

  String _getButtonText() {
    if (_isDeactivate()) {
      return 'Deactivate';
    } else {
      return 'Delete';
    }
  }

  bool _isDeactivate() {
    return widget.action.toLowerCase() == 'deactivate';
  }
}
