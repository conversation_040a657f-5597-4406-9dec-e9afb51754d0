// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_payment_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EditPaymentState _$EditPaymentStateFromJson(Map<String, dynamic> json) {
  return _EditPaymentState.fromJson(json);
}

/// @nodoc
mixin _$EditPaymentState {
  int get selectedBankId => throw _privateConstructorUsedError;
  String get selectedBank => throw _privateConstructorUsedError;
  BankAccountType get selectedBankAccountType =>
      throw _privateConstructorUsedError;
  String get selectedCurrency => throw _privateConstructorUsedError;
  String get selectedBranchName => throw _privateConstructorUsedError;
  bool get hasChanges => throw _privateConstructorUsedError;
  String get customBankName => throw _privateConstructorUsedError;
  bool get isValid => throw _privateConstructorUsedError;

  /// Serializes this EditPaymentState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditPaymentStateCopyWith<EditPaymentState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditPaymentStateCopyWith<$Res> {
  factory $EditPaymentStateCopyWith(
          EditPaymentState value, $Res Function(EditPaymentState) then) =
      _$EditPaymentStateCopyWithImpl<$Res, EditPaymentState>;
  @useResult
  $Res call(
      {int selectedBankId,
      String selectedBank,
      BankAccountType selectedBankAccountType,
      String selectedCurrency,
      String selectedBranchName,
      bool hasChanges,
      String customBankName,
      bool isValid});
}

/// @nodoc
class _$EditPaymentStateCopyWithImpl<$Res, $Val extends EditPaymentState>
    implements $EditPaymentStateCopyWith<$Res> {
  _$EditPaymentStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedBankId = null,
    Object? selectedBank = null,
    Object? selectedBankAccountType = null,
    Object? selectedCurrency = null,
    Object? selectedBranchName = null,
    Object? hasChanges = null,
    Object? customBankName = null,
    Object? isValid = null,
  }) {
    return _then(_value.copyWith(
      selectedBankId: null == selectedBankId
          ? _value.selectedBankId
          : selectedBankId // ignore: cast_nullable_to_non_nullable
              as int,
      selectedBank: null == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as String,
      selectedBankAccountType: null == selectedBankAccountType
          ? _value.selectedBankAccountType
          : selectedBankAccountType // ignore: cast_nullable_to_non_nullable
              as BankAccountType,
      selectedCurrency: null == selectedCurrency
          ? _value.selectedCurrency
          : selectedCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      selectedBranchName: null == selectedBranchName
          ? _value.selectedBranchName
          : selectedBranchName // ignore: cast_nullable_to_non_nullable
              as String,
      hasChanges: null == hasChanges
          ? _value.hasChanges
          : hasChanges // ignore: cast_nullable_to_non_nullable
              as bool,
      customBankName: null == customBankName
          ? _value.customBankName
          : customBankName // ignore: cast_nullable_to_non_nullable
              as String,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditPaymentStateImplCopyWith<$Res>
    implements $EditPaymentStateCopyWith<$Res> {
  factory _$$EditPaymentStateImplCopyWith(_$EditPaymentStateImpl value,
          $Res Function(_$EditPaymentStateImpl) then) =
      __$$EditPaymentStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int selectedBankId,
      String selectedBank,
      BankAccountType selectedBankAccountType,
      String selectedCurrency,
      String selectedBranchName,
      bool hasChanges,
      String customBankName,
      bool isValid});
}

/// @nodoc
class __$$EditPaymentStateImplCopyWithImpl<$Res>
    extends _$EditPaymentStateCopyWithImpl<$Res, _$EditPaymentStateImpl>
    implements _$$EditPaymentStateImplCopyWith<$Res> {
  __$$EditPaymentStateImplCopyWithImpl(_$EditPaymentStateImpl _value,
      $Res Function(_$EditPaymentStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedBankId = null,
    Object? selectedBank = null,
    Object? selectedBankAccountType = null,
    Object? selectedCurrency = null,
    Object? selectedBranchName = null,
    Object? hasChanges = null,
    Object? customBankName = null,
    Object? isValid = null,
  }) {
    return _then(_$EditPaymentStateImpl(
      selectedBankId: null == selectedBankId
          ? _value.selectedBankId
          : selectedBankId // ignore: cast_nullable_to_non_nullable
              as int,
      selectedBank: null == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as String,
      selectedBankAccountType: null == selectedBankAccountType
          ? _value.selectedBankAccountType
          : selectedBankAccountType // ignore: cast_nullable_to_non_nullable
              as BankAccountType,
      selectedCurrency: null == selectedCurrency
          ? _value.selectedCurrency
          : selectedCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      selectedBranchName: null == selectedBranchName
          ? _value.selectedBranchName
          : selectedBranchName // ignore: cast_nullable_to_non_nullable
              as String,
      hasChanges: null == hasChanges
          ? _value.hasChanges
          : hasChanges // ignore: cast_nullable_to_non_nullable
              as bool,
      customBankName: null == customBankName
          ? _value.customBankName
          : customBankName // ignore: cast_nullable_to_non_nullable
              as String,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EditPaymentStateImpl implements _EditPaymentState {
  _$EditPaymentStateImpl(
      {this.selectedBankId = 0,
      this.selectedBank = '',
      this.selectedBankAccountType = BankAccountType.CURRENT,
      this.selectedCurrency = '',
      this.selectedBranchName = '',
      this.hasChanges = false,
      this.customBankName = '',
      this.isValid = false});

  factory _$EditPaymentStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditPaymentStateImplFromJson(json);

  @override
  @JsonKey()
  final int selectedBankId;
  @override
  @JsonKey()
  final String selectedBank;
  @override
  @JsonKey()
  final BankAccountType selectedBankAccountType;
  @override
  @JsonKey()
  final String selectedCurrency;
  @override
  @JsonKey()
  final String selectedBranchName;
  @override
  @JsonKey()
  final bool hasChanges;
  @override
  @JsonKey()
  final String customBankName;
  @override
  @JsonKey()
  final bool isValid;

  @override
  String toString() {
    return 'EditPaymentState(selectedBankId: $selectedBankId, selectedBank: $selectedBank, selectedBankAccountType: $selectedBankAccountType, selectedCurrency: $selectedCurrency, selectedBranchName: $selectedBranchName, hasChanges: $hasChanges, customBankName: $customBankName, isValid: $isValid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditPaymentStateImpl &&
            (identical(other.selectedBankId, selectedBankId) ||
                other.selectedBankId == selectedBankId) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(
                    other.selectedBankAccountType, selectedBankAccountType) ||
                other.selectedBankAccountType == selectedBankAccountType) &&
            (identical(other.selectedCurrency, selectedCurrency) ||
                other.selectedCurrency == selectedCurrency) &&
            (identical(other.selectedBranchName, selectedBranchName) ||
                other.selectedBranchName == selectedBranchName) &&
            (identical(other.hasChanges, hasChanges) ||
                other.hasChanges == hasChanges) &&
            (identical(other.customBankName, customBankName) ||
                other.customBankName == customBankName) &&
            (identical(other.isValid, isValid) || other.isValid == isValid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedBankId,
      selectedBank,
      selectedBankAccountType,
      selectedCurrency,
      selectedBranchName,
      hasChanges,
      customBankName,
      isValid);

  /// Create a copy of EditPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditPaymentStateImplCopyWith<_$EditPaymentStateImpl> get copyWith =>
      __$$EditPaymentStateImplCopyWithImpl<_$EditPaymentStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditPaymentStateImplToJson(
      this,
    );
  }
}

abstract class _EditPaymentState implements EditPaymentState {
  factory _EditPaymentState(
      {final int selectedBankId,
      final String selectedBank,
      final BankAccountType selectedBankAccountType,
      final String selectedCurrency,
      final String selectedBranchName,
      final bool hasChanges,
      final String customBankName,
      final bool isValid}) = _$EditPaymentStateImpl;

  factory _EditPaymentState.fromJson(Map<String, dynamic> json) =
      _$EditPaymentStateImpl.fromJson;

  @override
  int get selectedBankId;
  @override
  String get selectedBank;
  @override
  BankAccountType get selectedBankAccountType;
  @override
  String get selectedCurrency;
  @override
  String get selectedBranchName;
  @override
  bool get hasChanges;
  @override
  String get customBankName;
  @override
  bool get isValid;

  /// Create a copy of EditPaymentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditPaymentStateImplCopyWith<_$EditPaymentStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
