// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_settings_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AccountSettingsState _$AccountSettingsStateFromJson(Map<String, dynamic> json) {
  return _AccountSettingsState.fromJson(json);
}

/// @nodoc
mixin _$AccountSettingsState {
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get avatar => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  DateTime? get updatedPasswordOn => throw _privateConstructorUsedError;
  bool get isBankAccountValid => throw _privateConstructorUsedError;
  bool get hasPassword => throw _privateConstructorUsedError;
  AccountPayment get payment => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this AccountSettingsState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountSettingsStateCopyWith<AccountSettingsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountSettingsStateCopyWith<$Res> {
  factory $AccountSettingsStateCopyWith(AccountSettingsState value,
          $Res Function(AccountSettingsState) then) =
      _$AccountSettingsStateCopyWithImpl<$Res, AccountSettingsState>;
  @useResult
  $Res call(
      {String firstName,
      String lastName,
      String avatar,
      String email,
      String phoneNumber,
      String address,
      DateTime? updatedPasswordOn,
      bool isBankAccountValid,
      bool hasPassword,
      AccountPayment payment,
      String errorMessage});

  $AccountPaymentCopyWith<$Res> get payment;
}

/// @nodoc
class _$AccountSettingsStateCopyWithImpl<$Res,
        $Val extends AccountSettingsState>
    implements $AccountSettingsStateCopyWith<$Res> {
  _$AccountSettingsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? avatar = null,
    Object? email = null,
    Object? phoneNumber = null,
    Object? address = null,
    Object? updatedPasswordOn = freezed,
    Object? isBankAccountValid = null,
    Object? hasPassword = null,
    Object? payment = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: null == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      updatedPasswordOn: freezed == updatedPasswordOn
          ? _value.updatedPasswordOn
          : updatedPasswordOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isBankAccountValid: null == isBankAccountValid
          ? _value.isBankAccountValid
          : isBankAccountValid // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPassword: null == hasPassword
          ? _value.hasPassword
          : hasPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      payment: null == payment
          ? _value.payment
          : payment // ignore: cast_nullable_to_non_nullable
              as AccountPayment,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of AccountSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountPaymentCopyWith<$Res> get payment {
    return $AccountPaymentCopyWith<$Res>(_value.payment, (value) {
      return _then(_value.copyWith(payment: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AccountSettingsStateImplCopyWith<$Res>
    implements $AccountSettingsStateCopyWith<$Res> {
  factory _$$AccountSettingsStateImplCopyWith(_$AccountSettingsStateImpl value,
          $Res Function(_$AccountSettingsStateImpl) then) =
      __$$AccountSettingsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String firstName,
      String lastName,
      String avatar,
      String email,
      String phoneNumber,
      String address,
      DateTime? updatedPasswordOn,
      bool isBankAccountValid,
      bool hasPassword,
      AccountPayment payment,
      String errorMessage});

  @override
  $AccountPaymentCopyWith<$Res> get payment;
}

/// @nodoc
class __$$AccountSettingsStateImplCopyWithImpl<$Res>
    extends _$AccountSettingsStateCopyWithImpl<$Res, _$AccountSettingsStateImpl>
    implements _$$AccountSettingsStateImplCopyWith<$Res> {
  __$$AccountSettingsStateImplCopyWithImpl(_$AccountSettingsStateImpl _value,
      $Res Function(_$AccountSettingsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? avatar = null,
    Object? email = null,
    Object? phoneNumber = null,
    Object? address = null,
    Object? updatedPasswordOn = freezed,
    Object? isBankAccountValid = null,
    Object? hasPassword = null,
    Object? payment = null,
    Object? errorMessage = null,
  }) {
    return _then(_$AccountSettingsStateImpl(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: null == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      updatedPasswordOn: freezed == updatedPasswordOn
          ? _value.updatedPasswordOn
          : updatedPasswordOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isBankAccountValid: null == isBankAccountValid
          ? _value.isBankAccountValid
          : isBankAccountValid // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPassword: null == hasPassword
          ? _value.hasPassword
          : hasPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      payment: null == payment
          ? _value.payment
          : payment // ignore: cast_nullable_to_non_nullable
              as AccountPayment,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountSettingsStateImpl implements _AccountSettingsState {
  const _$AccountSettingsStateImpl(
      {this.firstName = '',
      this.lastName = '',
      this.avatar = '',
      this.email = '',
      this.phoneNumber = '',
      this.address = '',
      this.updatedPasswordOn,
      this.isBankAccountValid = false,
      this.hasPassword = false,
      this.payment = const AccountPayment(),
      this.errorMessage = ''});

  factory _$AccountSettingsStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountSettingsStateImplFromJson(json);

  @override
  @JsonKey()
  final String firstName;
  @override
  @JsonKey()
  final String lastName;
  @override
  @JsonKey()
  final String avatar;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final String phoneNumber;
  @override
  @JsonKey()
  final String address;
  @override
  final DateTime? updatedPasswordOn;
  @override
  @JsonKey()
  final bool isBankAccountValid;
  @override
  @JsonKey()
  final bool hasPassword;
  @override
  @JsonKey()
  final AccountPayment payment;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'AccountSettingsState(firstName: $firstName, lastName: $lastName, avatar: $avatar, email: $email, phoneNumber: $phoneNumber, address: $address, updatedPasswordOn: $updatedPasswordOn, isBankAccountValid: $isBankAccountValid, hasPassword: $hasPassword, payment: $payment, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountSettingsStateImpl &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.updatedPasswordOn, updatedPasswordOn) ||
                other.updatedPasswordOn == updatedPasswordOn) &&
            (identical(other.isBankAccountValid, isBankAccountValid) ||
                other.isBankAccountValid == isBankAccountValid) &&
            (identical(other.hasPassword, hasPassword) ||
                other.hasPassword == hasPassword) &&
            (identical(other.payment, payment) || other.payment == payment) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      firstName,
      lastName,
      avatar,
      email,
      phoneNumber,
      address,
      updatedPasswordOn,
      isBankAccountValid,
      hasPassword,
      payment,
      errorMessage);

  /// Create a copy of AccountSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountSettingsStateImplCopyWith<_$AccountSettingsStateImpl>
      get copyWith =>
          __$$AccountSettingsStateImplCopyWithImpl<_$AccountSettingsStateImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountSettingsStateImplToJson(
      this,
    );
  }
}

abstract class _AccountSettingsState implements AccountSettingsState {
  const factory _AccountSettingsState(
      {final String firstName,
      final String lastName,
      final String avatar,
      final String email,
      final String phoneNumber,
      final String address,
      final DateTime? updatedPasswordOn,
      final bool isBankAccountValid,
      final bool hasPassword,
      final AccountPayment payment,
      final String errorMessage}) = _$AccountSettingsStateImpl;

  factory _AccountSettingsState.fromJson(Map<String, dynamic> json) =
      _$AccountSettingsStateImpl.fromJson;

  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get avatar;
  @override
  String get email;
  @override
  String get phoneNumber;
  @override
  String get address;
  @override
  DateTime? get updatedPasswordOn;
  @override
  bool get isBankAccountValid;
  @override
  bool get hasPassword;
  @override
  AccountPayment get payment;
  @override
  String get errorMessage;

  /// Create a copy of AccountSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountSettingsStateImplCopyWith<_$AccountSettingsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
