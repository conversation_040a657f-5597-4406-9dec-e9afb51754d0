import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/account/settings/data/model/account_payment.dart';

part 'account_settings_state.freezed.dart';
part 'account_settings_state.g.dart';

@freezed
class AccountSettingsState extends BaseCubitState with _$AccountSettingsState {
  const factory AccountSettingsState({
    @Default('') String firstName,
    @Default('') String lastName,
    @Default('') String avatar,
    @Default('') String email,
    @Default('') String phoneNumber,
    @Default('') String address,
    DateTime? updatedPasswordOn,
    @Default(false) bool isBankAccountValid,
    @Default(false) bool hasPassword,
    @Default(AccountPayment()) AccountPayment payment,
    @Default('') String errorMessage,
  }) = _AccountSettingsState;

  factory AccountSettingsState.fromJson(Map<String, Object?> json) => _$AccountSettingsStateFromJson(json);
}
