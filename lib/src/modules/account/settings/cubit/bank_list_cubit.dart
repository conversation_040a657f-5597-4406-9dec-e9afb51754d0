import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koc_app/src/modules/account/data/model/bank_info.dart';
import 'package:koc_app/src/modules/account/data/repository/bank_repository.dart';

class BankListCubit extends Cubit<List<BankInfo>> {
  final BankRepository _bankRepository;
  BankListCubit(this._bankRepository) : super([]);

  Future<void> fetchBanks() async {
    emit(await _bankRepository.getBanks());
  }
}
