// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_settings_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AccountSettingsStateImpl _$$AccountSettingsStateImplFromJson(
        Map<String, dynamic> json) =>
    _$AccountSettingsStateImpl(
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      email: json['email'] as String? ?? '',
      phoneNumber: json['phoneNumber'] as String? ?? '',
      address: json['address'] as String? ?? '',
      updatedPasswordOn: json['updatedPasswordOn'] == null
          ? null
          : DateTime.parse(json['updatedPasswordOn'] as String),
      isBankAccountValid: json['isBankAccountValid'] as bool? ?? false,
      hasPassword: json['hasPassword'] as bool? ?? false,
      payment: json['payment'] == null
          ? const AccountPayment()
          : AccountPayment.fromJson(json['payment'] as Map<String, dynamic>),
      errorMessage: json['errorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$$AccountSettingsStateImplToJson(
        _$AccountSettingsStateImpl instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'avatar': instance.avatar,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'address': instance.address,
      'updatedPasswordOn': instance.updatedPasswordOn?.toIso8601String(),
      'isBankAccountValid': instance.isBankAccountValid,
      'hasPassword': instance.hasPassword,
      'payment': instance.payment,
      'errorMessage': instance.errorMessage,
    };
