import 'dart:developer' as dev;

import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/data/model/account.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_state.dart';
import 'package:koc_app/src/modules/account/settings/data/model/account_payment.dart';
import 'package:koc_app/src/modules/authentication/data/repository/authentication_repository.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';
import 'package:share_plus/share_plus.dart';

class AccountSettingsCubit extends BaseCubit<AccountSettingsState> {
  final AccountRepository _accountRepository;
  final AuthenticationRepository _authenticationRepository;

  AccountSettingsCubit(this._accountRepository, this._authenticationRepository) : super(const AccountSettingsState());

  Future<void> findAccount() async {
    try {
      final results = await Future.wait([
        _accountRepository.getAccount(),
        _hasPassword(),
      ]);
      AccountData result = results[0] as AccountData;
      emit(state.copyWith(
        firstName: result.firstName,
        lastName: result.lastName,
        email: result.email,
        phoneNumber: result.phoneNumber,
        address: result.address,
        avatar: result.avatar,
        updatedPasswordOn: result.updatedPasswordOn.isNotEmpty ? DateTime.parse(result.updatedPasswordOn) : null,
        isBankAccountValid: result.isBankAccountValid,
        hasPassword: results[1] as bool,
      ));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<bool> updatePhoneNumber(String phoneNumber) async {
    try {
      await _accountRepository.updatePhoneNumber(phoneNumber);
      emit(state.copyWith(phoneNumber: phoneNumber));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<bool> updateAddress(String address) async {
    try {
      await _accountRepository.updateAddress(address);
      emit(state.copyWith(address: address));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<bool> updateName(String firstName, String lastName) async {
    try {
      await _accountRepository.updateName(firstName, lastName);
      emit(state.copyWith(firstName: firstName, lastName: lastName));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<void> updateAvatar(XFile image) async {
    try {
      final oldAvatarUrl = state.avatar;

      await _accountRepository.updateAvatar(image);

      await _clearAvatarCaches(oldAvatarUrl);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> removeAvatar() async {
    try {
      final oldAvatarUrl = state.avatar;

      await _accountRepository.removeAvatar(state.avatar);
      emit(state.copyWith(avatar: ''));

      await _clearAvatarCaches(oldAvatarUrl);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<bool> _hasPassword() async {
    try {
      final countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      final email = await commonCubit.sharedPreferencesService.getEmail();
      final result = await _authenticationRepository.checkUserExistingBy(email!, countryCode!);
      return result['users'][0]['hasPassword'] as bool;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<bool> verifyCurrentPassword(String password) async {
    try {
      bool result = await _accountRepository.verifyCurrentPassword(password);
      return result;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      await _accountRepository.changePassword(currentPassword, newPassword);
      emit(state.copyWith(updatedPasswordOn: DateTime.now()));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<bool> resetPassword(String password) async {
    try {
      final token = Modular.get<AccountCubit>().state.authTokenInfo!.token;
      await _accountRepository.resetPassword(password, token);
      emit(state.copyWith(updatedPasswordOn: DateTime.now()));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<void> getBankAccount() async {
    try {
      final bankAccount = await _accountRepository.getBankAccount(Modular.get<AccountCubit>().state.paymentOtpToken);
      emit(state.copyWith(payment: bankAccount));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      rethrow;
    }
  }

  Future<void> updateBankAccount(AccountPayment payment) async {
    try {
      await _accountRepository.updateBankAccount(payment, Modular.get<AccountCubit>().state.paymentOtpToken);
      emit(state.copyWith(payment: payment));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  void clearError() {
    emit(state.copyWith(errorMessage: ''));
  }

  Future<void> _clearAvatarCaches(String? oldAvatarUrl) async {
    try {
      final apiService = Modular.get<ApiService>();
      await apiService.clearCacheForEndpoint('/v3/publishers/me/avatar');
      if (oldAvatarUrl != null && oldAvatarUrl.isNotEmpty) {
        await apiService.removeImageFromCache(oldAvatarUrl);
      }
      await apiService.clearCacheForEndpoint('/v3/publishers/me/account');
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
