// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_payment_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EditPaymentStateImpl _$$EditPaymentStateImplFromJson(
        Map<String, dynamic> json) =>
    _$EditPaymentStateImpl(
      selectedBankId: (json['selectedBankId'] as num?)?.toInt() ?? 0,
      selectedBank: json['selectedBank'] as String? ?? '',
      selectedBankAccountType: $enumDecodeNullable(
              _$BankAccountTypeEnumMap, json['selectedBankAccountType']) ??
          BankAccountType.CURRENT,
      selectedCurrency: json['selectedCurrency'] as String? ?? '',
      selectedBranchName: json['selectedBranchName'] as String? ?? '',
      hasChanges: json['hasChanges'] as bool? ?? false,
      customBankName: json['customBankName'] as String? ?? '',
      isValid: json['isValid'] as bool? ?? false,
    );

Map<String, dynamic> _$$EditPaymentStateImplToJson(
        _$EditPaymentStateImpl instance) =>
    <String, dynamic>{
      'selectedBankId': instance.selectedBankId,
      'selectedBank': instance.selectedBank,
      'selectedBankAccountType':
          _$BankAccountTypeEnumMap[instance.selectedBankAccountType]!,
      'selectedCurrency': instance.selectedCurrency,
      'selectedBranchName': instance.selectedBranchName,
      'hasChanges': instance.hasChanges,
      'customBankName': instance.customBankName,
      'isValid': instance.isValid,
    };

const _$BankAccountTypeEnumMap = {
  BankAccountType.CURRENT: 'CURRENT',
  BankAccountType.SAVINGS: 'SAVINGS',
};
