import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koc_app/src/modules/account/settings/cubit/reset_password_state.dart';

class ResetPasswordCubit extends Cubit<ResetPasswordState> {
  ResetPasswordCubit() : super(ResetPasswordState());

  void setValid(bool value) => emit(state.copyWith(isValid: value));
  void toggleObscurePassword() =>
      emit(state.copyWith(obscurePassword: !state.obscurePassword));
  void toggleObscureConfirmPassword() => emit(
      state.copyWith(obscureConfirmPassword: !state.obscureConfirmPassword));
}
