import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';

part 'account_avatar_state.freezed.dart';
part 'account_avatar_state.g.dart';

@freezed
class AccountAvatarState extends BaseCubitState with _$AccountAvatarState {
  const factory AccountAvatarState({
    @Default('') String name,
    @Default('') String avatar,
    @Default('') String errorMessage,
  }) = _AccountAvatarState;

  factory AccountAvatarState.fromJson(Map<String, Object?> json) => _$AccountAvatarStateFromJson(json);
}
