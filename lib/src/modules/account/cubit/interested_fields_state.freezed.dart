// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'interested_fields_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InterestedFieldsState {
  List<PassionateItem> get fields => throw _privateConstructorUsedError;

  /// Create a copy of InterestedFieldsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InterestedFieldsStateCopyWith<InterestedFieldsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InterestedFieldsStateCopyWith<$Res> {
  factory $InterestedFieldsStateCopyWith(InterestedFieldsState value,
          $Res Function(InterestedFieldsState) then) =
      _$InterestedFieldsStateCopyWithImpl<$Res, InterestedFieldsState>;
  @useResult
  $Res call({List<PassionateItem> fields});
}

/// @nodoc
class _$InterestedFieldsStateCopyWithImpl<$Res,
        $Val extends InterestedFieldsState>
    implements $InterestedFieldsStateCopyWith<$Res> {
  _$InterestedFieldsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InterestedFieldsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fields = null,
  }) {
    return _then(_value.copyWith(
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as List<PassionateItem>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InterestedFieldsStateImplCopyWith<$Res>
    implements $InterestedFieldsStateCopyWith<$Res> {
  factory _$$InterestedFieldsStateImplCopyWith(
          _$InterestedFieldsStateImpl value,
          $Res Function(_$InterestedFieldsStateImpl) then) =
      __$$InterestedFieldsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PassionateItem> fields});
}

/// @nodoc
class __$$InterestedFieldsStateImplCopyWithImpl<$Res>
    extends _$InterestedFieldsStateCopyWithImpl<$Res,
        _$InterestedFieldsStateImpl>
    implements _$$InterestedFieldsStateImplCopyWith<$Res> {
  __$$InterestedFieldsStateImplCopyWithImpl(_$InterestedFieldsStateImpl _value,
      $Res Function(_$InterestedFieldsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of InterestedFieldsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fields = null,
  }) {
    return _then(_$InterestedFieldsStateImpl(
      fields: null == fields
          ? _value._fields
          : fields // ignore: cast_nullable_to_non_nullable
              as List<PassionateItem>,
    ));
  }
}

/// @nodoc

class _$InterestedFieldsStateImpl implements _InterestedFieldsState {
  _$InterestedFieldsStateImpl({final List<PassionateItem> fields = const []})
      : _fields = fields;

  final List<PassionateItem> _fields;
  @override
  @JsonKey()
  List<PassionateItem> get fields {
    if (_fields is EqualUnmodifiableListView) return _fields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fields);
  }

  @override
  String toString() {
    return 'InterestedFieldsState(fields: $fields)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InterestedFieldsStateImpl &&
            const DeepCollectionEquality().equals(other._fields, _fields));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_fields));

  /// Create a copy of InterestedFieldsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InterestedFieldsStateImplCopyWith<_$InterestedFieldsStateImpl>
      get copyWith => __$$InterestedFieldsStateImplCopyWithImpl<
          _$InterestedFieldsStateImpl>(this, _$identity);
}

abstract class _InterestedFieldsState implements InterestedFieldsState {
  factory _InterestedFieldsState({final List<PassionateItem> fields}) =
      _$InterestedFieldsStateImpl;

  @override
  List<PassionateItem> get fields;

  /// Create a copy of InterestedFieldsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InterestedFieldsStateImplCopyWith<_$InterestedFieldsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
