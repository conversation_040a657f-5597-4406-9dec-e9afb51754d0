// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'site_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SiteState _$SiteStateFromJson(Map<String, dynamic> json) {
  return _SiteState.fromJson(json);
}

/// @nodoc
mixin _$SiteState {
  List<PublisherSite> get sites => throw _privateConstructorUsedError;
  int get currentSiteId => throw _privateConstructorUsedError;

  /// Serializes this SiteState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SiteState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SiteStateCopyWith<SiteState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SiteStateCopyWith<$Res> {
  factory $SiteStateCopyWith(SiteState value, $Res Function(SiteState) then) =
      _$SiteStateCopyWithImpl<$Res, SiteState>;
  @useResult
  $Res call({List<PublisherSite> sites, int currentSiteId});
}

/// @nodoc
class _$SiteStateCopyWithImpl<$Res, $Val extends SiteState>
    implements $SiteStateCopyWith<$Res> {
  _$SiteStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SiteState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sites = null,
    Object? currentSiteId = null,
  }) {
    return _then(_value.copyWith(
      sites: null == sites
          ? _value.sites
          : sites // ignore: cast_nullable_to_non_nullable
              as List<PublisherSite>,
      currentSiteId: null == currentSiteId
          ? _value.currentSiteId
          : currentSiteId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SiteStateImplCopyWith<$Res>
    implements $SiteStateCopyWith<$Res> {
  factory _$$SiteStateImplCopyWith(
          _$SiteStateImpl value, $Res Function(_$SiteStateImpl) then) =
      __$$SiteStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PublisherSite> sites, int currentSiteId});
}

/// @nodoc
class __$$SiteStateImplCopyWithImpl<$Res>
    extends _$SiteStateCopyWithImpl<$Res, _$SiteStateImpl>
    implements _$$SiteStateImplCopyWith<$Res> {
  __$$SiteStateImplCopyWithImpl(
      _$SiteStateImpl _value, $Res Function(_$SiteStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SiteState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sites = null,
    Object? currentSiteId = null,
  }) {
    return _then(_$SiteStateImpl(
      sites: null == sites
          ? _value._sites
          : sites // ignore: cast_nullable_to_non_nullable
              as List<PublisherSite>,
      currentSiteId: null == currentSiteId
          ? _value.currentSiteId
          : currentSiteId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SiteStateImpl implements _SiteState {
  _$SiteStateImpl(
      {final List<PublisherSite> sites = const [], this.currentSiteId = 0})
      : _sites = sites;

  factory _$SiteStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$SiteStateImplFromJson(json);

  final List<PublisherSite> _sites;
  @override
  @JsonKey()
  List<PublisherSite> get sites {
    if (_sites is EqualUnmodifiableListView) return _sites;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sites);
  }

  @override
  @JsonKey()
  final int currentSiteId;

  @override
  String toString() {
    return 'SiteState(sites: $sites, currentSiteId: $currentSiteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SiteStateImpl &&
            const DeepCollectionEquality().equals(other._sites, _sites) &&
            (identical(other.currentSiteId, currentSiteId) ||
                other.currentSiteId == currentSiteId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_sites), currentSiteId);

  /// Create a copy of SiteState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SiteStateImplCopyWith<_$SiteStateImpl> get copyWith =>
      __$$SiteStateImplCopyWithImpl<_$SiteStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SiteStateImplToJson(
      this,
    );
  }
}

abstract class _SiteState implements SiteState {
  factory _SiteState(
      {final List<PublisherSite> sites,
      final int currentSiteId}) = _$SiteStateImpl;

  factory _SiteState.fromJson(Map<String, dynamic> json) =
      _$SiteStateImpl.fromJson;

  @override
  List<PublisherSite> get sites;
  @override
  int get currentSiteId;

  /// Create a copy of SiteState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SiteStateImplCopyWith<_$SiteStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
