import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/account/data/model/publisher_sites.dart';

part 'site_state.freezed.dart';
part 'site_state.g.dart';

@freezed
class SiteState extends BaseCubitState with _$SiteState {
  factory SiteState({
    @Default([]) List<PublisherSite> sites,
    @Default(0) int currentSiteId,
  }) = _SiteState;

  factory SiteState.fromJson(Map<String, Object?> json) => _$SiteStateFromJson(json);
}
