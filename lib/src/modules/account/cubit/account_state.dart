import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/account/data/model/account.dart';
import 'package:koc_app/src/modules/account/settings/data/model/account_payment.dart';
import 'package:koc_app/src/modules/authentication/data/models/auth_token_info.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

part 'account_state.freezed.dart';
part 'account_state.g.dart';

@freezed
class AccountState extends BaseCubitState with _$AccountState {
  factory AccountState(
      {AuthTokenInfo? authTokenInfo,
      @Default(0) int userId,
      @Default(AccountType.INDIVIDUAL) AccountType accountType,
      @Default([]) List<SocialInfo> trafficSources,
      @Default([]) List<PassionateItem> interestedFields,
      @Default('') String language,
      @Default(false) bool isNotificationEnabled,
      @Default(false) bool isFacebookLoginEnabled,
      @Default(false) bool isBiometricsEnabled,
      @Default('') String profilePictureUrl,
      @Default('') String firstName,
      @Default('') String lastName,
      @Default('') String email,
      @Default(false) bool isEmailVerified,
      @Default('') String phoneNumber,
      DateTime? phoneVerifiedOn,
      @Default('') String address,
      DateTime? lastPasswordChangedOn,
      @Default('') String companyName,
      @Default('') String companyAddress,
      @Default('') String paymentOtpToken,
      @Default('') String errorMessage,
      @Default(false) bool isPullToRefresh,
      @Default(AccountPayment()) AccountPayment payment}) = _AccountState;

  factory AccountState.fromJson(Map<String, Object?> json) => _$AccountStateFromJson(json);
}
