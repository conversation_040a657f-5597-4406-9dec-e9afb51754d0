import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

part 'interested_fields_state.freezed.dart';

@freezed
class InterestedFieldsState extends BaseCubitState
    with _$InterestedFieldsState {
  factory InterestedFieldsState({
    @Default([]) List<PassionateItem> fields,
  }) = _InterestedFieldsState;
}
