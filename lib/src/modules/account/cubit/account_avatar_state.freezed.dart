// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_avatar_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AccountAvatarState _$AccountAvatarStateFromJson(Map<String, dynamic> json) {
  return _AccountAvatarState.fromJson(json);
}

/// @nodoc
mixin _$AccountAvatarState {
  String get name => throw _privateConstructorUsedError;
  String get avatar => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this AccountAvatarState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountAvatarState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountAvatarStateCopyWith<AccountAvatarState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountAvatarStateCopyWith<$Res> {
  factory $AccountAvatarStateCopyWith(
          AccountAvatarState value, $Res Function(AccountAvatarState) then) =
      _$AccountAvatarStateCopyWithImpl<$Res, AccountAvatarState>;
  @useResult
  $Res call({String name, String avatar, String errorMessage});
}

/// @nodoc
class _$AccountAvatarStateCopyWithImpl<$Res, $Val extends AccountAvatarState>
    implements $AccountAvatarStateCopyWith<$Res> {
  _$AccountAvatarStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountAvatarState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? avatar = null,
    Object? errorMessage = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: null == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountAvatarStateImplCopyWith<$Res>
    implements $AccountAvatarStateCopyWith<$Res> {
  factory _$$AccountAvatarStateImplCopyWith(_$AccountAvatarStateImpl value,
          $Res Function(_$AccountAvatarStateImpl) then) =
      __$$AccountAvatarStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String avatar, String errorMessage});
}

/// @nodoc
class __$$AccountAvatarStateImplCopyWithImpl<$Res>
    extends _$AccountAvatarStateCopyWithImpl<$Res, _$AccountAvatarStateImpl>
    implements _$$AccountAvatarStateImplCopyWith<$Res> {
  __$$AccountAvatarStateImplCopyWithImpl(_$AccountAvatarStateImpl _value,
      $Res Function(_$AccountAvatarStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountAvatarState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? avatar = null,
    Object? errorMessage = null,
  }) {
    return _then(_$AccountAvatarStateImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: null == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountAvatarStateImpl implements _AccountAvatarState {
  const _$AccountAvatarStateImpl(
      {this.name = '', this.avatar = '', this.errorMessage = ''});

  factory _$AccountAvatarStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountAvatarStateImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String avatar;
  @override
  @JsonKey()
  final String errorMessage;

  @override
  String toString() {
    return 'AccountAvatarState(name: $name, avatar: $avatar, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountAvatarStateImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, avatar, errorMessage);

  /// Create a copy of AccountAvatarState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountAvatarStateImplCopyWith<_$AccountAvatarStateImpl> get copyWith =>
      __$$AccountAvatarStateImplCopyWithImpl<_$AccountAvatarStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountAvatarStateImplToJson(
      this,
    );
  }
}

abstract class _AccountAvatarState implements AccountAvatarState {
  const factory _AccountAvatarState(
      {final String name,
      final String avatar,
      final String errorMessage}) = _$AccountAvatarStateImpl;

  factory _AccountAvatarState.fromJson(Map<String, dynamic> json) =
      _$AccountAvatarStateImpl.fromJson;

  @override
  String get name;
  @override
  String get avatar;
  @override
  String get errorMessage;

  /// Create a copy of AccountAvatarState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountAvatarStateImplCopyWith<_$AccountAvatarStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
