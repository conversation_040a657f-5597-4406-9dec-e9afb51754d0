// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AccountState _$AccountStateFromJson(Map<String, dynamic> json) {
  return _AccountState.fromJson(json);
}

/// @nodoc
mixin _$AccountState {
  AuthTokenInfo? get authTokenInfo => throw _privateConstructorUsedError;
  int get userId => throw _privateConstructorUsedError;
  AccountType get accountType => throw _privateConstructorUsedError;
  List<SocialInfo> get trafficSources => throw _privateConstructorUsedError;
  List<PassionateItem> get interestedFields =>
      throw _privateConstructorUsedError;
  String get language => throw _privateConstructorUsedError;
  bool get isNotificationEnabled => throw _privateConstructorUsedError;
  bool get isFacebookLoginEnabled => throw _privateConstructorUsedError;
  bool get isBiometricsEnabled => throw _privateConstructorUsedError;
  String get profilePictureUrl => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  bool get isEmailVerified => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  DateTime? get phoneVerifiedOn => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  DateTime? get lastPasswordChangedOn => throw _privateConstructorUsedError;
  String get companyName => throw _privateConstructorUsedError;
  String get companyAddress => throw _privateConstructorUsedError;
  String get paymentOtpToken => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  bool get isPullToRefresh => throw _privateConstructorUsedError;
  AccountPayment get payment => throw _privateConstructorUsedError;

  /// Serializes this AccountState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountStateCopyWith<AccountState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountStateCopyWith<$Res> {
  factory $AccountStateCopyWith(
          AccountState value, $Res Function(AccountState) then) =
      _$AccountStateCopyWithImpl<$Res, AccountState>;
  @useResult
  $Res call(
      {AuthTokenInfo? authTokenInfo,
      int userId,
      AccountType accountType,
      List<SocialInfo> trafficSources,
      List<PassionateItem> interestedFields,
      String language,
      bool isNotificationEnabled,
      bool isFacebookLoginEnabled,
      bool isBiometricsEnabled,
      String profilePictureUrl,
      String firstName,
      String lastName,
      String email,
      bool isEmailVerified,
      String phoneNumber,
      DateTime? phoneVerifiedOn,
      String address,
      DateTime? lastPasswordChangedOn,
      String companyName,
      String companyAddress,
      String paymentOtpToken,
      String errorMessage,
      bool isPullToRefresh,
      AccountPayment payment});

  $AuthTokenInfoCopyWith<$Res>? get authTokenInfo;
  $AccountPaymentCopyWith<$Res> get payment;
}

/// @nodoc
class _$AccountStateCopyWithImpl<$Res, $Val extends AccountState>
    implements $AccountStateCopyWith<$Res> {
  _$AccountStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authTokenInfo = freezed,
    Object? userId = null,
    Object? accountType = null,
    Object? trafficSources = null,
    Object? interestedFields = null,
    Object? language = null,
    Object? isNotificationEnabled = null,
    Object? isFacebookLoginEnabled = null,
    Object? isBiometricsEnabled = null,
    Object? profilePictureUrl = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? email = null,
    Object? isEmailVerified = null,
    Object? phoneNumber = null,
    Object? phoneVerifiedOn = freezed,
    Object? address = null,
    Object? lastPasswordChangedOn = freezed,
    Object? companyName = null,
    Object? companyAddress = null,
    Object? paymentOtpToken = null,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
    Object? payment = null,
  }) {
    return _then(_value.copyWith(
      authTokenInfo: freezed == authTokenInfo
          ? _value.authTokenInfo
          : authTokenInfo // ignore: cast_nullable_to_non_nullable
              as AuthTokenInfo?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      accountType: null == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as AccountType,
      trafficSources: null == trafficSources
          ? _value.trafficSources
          : trafficSources // ignore: cast_nullable_to_non_nullable
              as List<SocialInfo>,
      interestedFields: null == interestedFields
          ? _value.interestedFields
          : interestedFields // ignore: cast_nullable_to_non_nullable
              as List<PassionateItem>,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      isNotificationEnabled: null == isNotificationEnabled
          ? _value.isNotificationEnabled
          : isNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isFacebookLoginEnabled: null == isFacebookLoginEnabled
          ? _value.isFacebookLoginEnabled
          : isFacebookLoginEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isBiometricsEnabled: null == isBiometricsEnabled
          ? _value.isBiometricsEnabled
          : isBiometricsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      profilePictureUrl: null == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      phoneVerifiedOn: freezed == phoneVerifiedOn
          ? _value.phoneVerifiedOn
          : phoneVerifiedOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      lastPasswordChangedOn: freezed == lastPasswordChangedOn
          ? _value.lastPasswordChangedOn
          : lastPasswordChangedOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      companyName: null == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      companyAddress: null == companyAddress
          ? _value.companyAddress
          : companyAddress // ignore: cast_nullable_to_non_nullable
              as String,
      paymentOtpToken: null == paymentOtpToken
          ? _value.paymentOtpToken
          : paymentOtpToken // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      payment: null == payment
          ? _value.payment
          : payment // ignore: cast_nullable_to_non_nullable
              as AccountPayment,
    ) as $Val);
  }

  /// Create a copy of AccountState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AuthTokenInfoCopyWith<$Res>? get authTokenInfo {
    if (_value.authTokenInfo == null) {
      return null;
    }

    return $AuthTokenInfoCopyWith<$Res>(_value.authTokenInfo!, (value) {
      return _then(_value.copyWith(authTokenInfo: value) as $Val);
    });
  }

  /// Create a copy of AccountState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountPaymentCopyWith<$Res> get payment {
    return $AccountPaymentCopyWith<$Res>(_value.payment, (value) {
      return _then(_value.copyWith(payment: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AccountStateImplCopyWith<$Res>
    implements $AccountStateCopyWith<$Res> {
  factory _$$AccountStateImplCopyWith(
          _$AccountStateImpl value, $Res Function(_$AccountStateImpl) then) =
      __$$AccountStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AuthTokenInfo? authTokenInfo,
      int userId,
      AccountType accountType,
      List<SocialInfo> trafficSources,
      List<PassionateItem> interestedFields,
      String language,
      bool isNotificationEnabled,
      bool isFacebookLoginEnabled,
      bool isBiometricsEnabled,
      String profilePictureUrl,
      String firstName,
      String lastName,
      String email,
      bool isEmailVerified,
      String phoneNumber,
      DateTime? phoneVerifiedOn,
      String address,
      DateTime? lastPasswordChangedOn,
      String companyName,
      String companyAddress,
      String paymentOtpToken,
      String errorMessage,
      bool isPullToRefresh,
      AccountPayment payment});

  @override
  $AuthTokenInfoCopyWith<$Res>? get authTokenInfo;
  @override
  $AccountPaymentCopyWith<$Res> get payment;
}

/// @nodoc
class __$$AccountStateImplCopyWithImpl<$Res>
    extends _$AccountStateCopyWithImpl<$Res, _$AccountStateImpl>
    implements _$$AccountStateImplCopyWith<$Res> {
  __$$AccountStateImplCopyWithImpl(
      _$AccountStateImpl _value, $Res Function(_$AccountStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authTokenInfo = freezed,
    Object? userId = null,
    Object? accountType = null,
    Object? trafficSources = null,
    Object? interestedFields = null,
    Object? language = null,
    Object? isNotificationEnabled = null,
    Object? isFacebookLoginEnabled = null,
    Object? isBiometricsEnabled = null,
    Object? profilePictureUrl = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? email = null,
    Object? isEmailVerified = null,
    Object? phoneNumber = null,
    Object? phoneVerifiedOn = freezed,
    Object? address = null,
    Object? lastPasswordChangedOn = freezed,
    Object? companyName = null,
    Object? companyAddress = null,
    Object? paymentOtpToken = null,
    Object? errorMessage = null,
    Object? isPullToRefresh = null,
    Object? payment = null,
  }) {
    return _then(_$AccountStateImpl(
      authTokenInfo: freezed == authTokenInfo
          ? _value.authTokenInfo
          : authTokenInfo // ignore: cast_nullable_to_non_nullable
              as AuthTokenInfo?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      accountType: null == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as AccountType,
      trafficSources: null == trafficSources
          ? _value._trafficSources
          : trafficSources // ignore: cast_nullable_to_non_nullable
              as List<SocialInfo>,
      interestedFields: null == interestedFields
          ? _value._interestedFields
          : interestedFields // ignore: cast_nullable_to_non_nullable
              as List<PassionateItem>,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      isNotificationEnabled: null == isNotificationEnabled
          ? _value.isNotificationEnabled
          : isNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isFacebookLoginEnabled: null == isFacebookLoginEnabled
          ? _value.isFacebookLoginEnabled
          : isFacebookLoginEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isBiometricsEnabled: null == isBiometricsEnabled
          ? _value.isBiometricsEnabled
          : isBiometricsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      profilePictureUrl: null == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      phoneVerifiedOn: freezed == phoneVerifiedOn
          ? _value.phoneVerifiedOn
          : phoneVerifiedOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      lastPasswordChangedOn: freezed == lastPasswordChangedOn
          ? _value.lastPasswordChangedOn
          : lastPasswordChangedOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      companyName: null == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      companyAddress: null == companyAddress
          ? _value.companyAddress
          : companyAddress // ignore: cast_nullable_to_non_nullable
              as String,
      paymentOtpToken: null == paymentOtpToken
          ? _value.paymentOtpToken
          : paymentOtpToken // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      isPullToRefresh: null == isPullToRefresh
          ? _value.isPullToRefresh
          : isPullToRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      payment: null == payment
          ? _value.payment
          : payment // ignore: cast_nullable_to_non_nullable
              as AccountPayment,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountStateImpl implements _AccountState {
  _$AccountStateImpl(
      {this.authTokenInfo,
      this.userId = 0,
      this.accountType = AccountType.INDIVIDUAL,
      final List<SocialInfo> trafficSources = const [],
      final List<PassionateItem> interestedFields = const [],
      this.language = '',
      this.isNotificationEnabled = false,
      this.isFacebookLoginEnabled = false,
      this.isBiometricsEnabled = false,
      this.profilePictureUrl = '',
      this.firstName = '',
      this.lastName = '',
      this.email = '',
      this.isEmailVerified = false,
      this.phoneNumber = '',
      this.phoneVerifiedOn,
      this.address = '',
      this.lastPasswordChangedOn,
      this.companyName = '',
      this.companyAddress = '',
      this.paymentOtpToken = '',
      this.errorMessage = '',
      this.isPullToRefresh = false,
      this.payment = const AccountPayment()})
      : _trafficSources = trafficSources,
        _interestedFields = interestedFields;

  factory _$AccountStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountStateImplFromJson(json);

  @override
  final AuthTokenInfo? authTokenInfo;
  @override
  @JsonKey()
  final int userId;
  @override
  @JsonKey()
  final AccountType accountType;
  final List<SocialInfo> _trafficSources;
  @override
  @JsonKey()
  List<SocialInfo> get trafficSources {
    if (_trafficSources is EqualUnmodifiableListView) return _trafficSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_trafficSources);
  }

  final List<PassionateItem> _interestedFields;
  @override
  @JsonKey()
  List<PassionateItem> get interestedFields {
    if (_interestedFields is EqualUnmodifiableListView)
      return _interestedFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_interestedFields);
  }

  @override
  @JsonKey()
  final String language;
  @override
  @JsonKey()
  final bool isNotificationEnabled;
  @override
  @JsonKey()
  final bool isFacebookLoginEnabled;
  @override
  @JsonKey()
  final bool isBiometricsEnabled;
  @override
  @JsonKey()
  final String profilePictureUrl;
  @override
  @JsonKey()
  final String firstName;
  @override
  @JsonKey()
  final String lastName;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final bool isEmailVerified;
  @override
  @JsonKey()
  final String phoneNumber;
  @override
  final DateTime? phoneVerifiedOn;
  @override
  @JsonKey()
  final String address;
  @override
  final DateTime? lastPasswordChangedOn;
  @override
  @JsonKey()
  final String companyName;
  @override
  @JsonKey()
  final String companyAddress;
  @override
  @JsonKey()
  final String paymentOtpToken;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool isPullToRefresh;
  @override
  @JsonKey()
  final AccountPayment payment;

  @override
  String toString() {
    return 'AccountState(authTokenInfo: $authTokenInfo, userId: $userId, accountType: $accountType, trafficSources: $trafficSources, interestedFields: $interestedFields, language: $language, isNotificationEnabled: $isNotificationEnabled, isFacebookLoginEnabled: $isFacebookLoginEnabled, isBiometricsEnabled: $isBiometricsEnabled, profilePictureUrl: $profilePictureUrl, firstName: $firstName, lastName: $lastName, email: $email, isEmailVerified: $isEmailVerified, phoneNumber: $phoneNumber, phoneVerifiedOn: $phoneVerifiedOn, address: $address, lastPasswordChangedOn: $lastPasswordChangedOn, companyName: $companyName, companyAddress: $companyAddress, paymentOtpToken: $paymentOtpToken, errorMessage: $errorMessage, isPullToRefresh: $isPullToRefresh, payment: $payment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountStateImpl &&
            (identical(other.authTokenInfo, authTokenInfo) ||
                other.authTokenInfo == authTokenInfo) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            const DeepCollectionEquality()
                .equals(other._trafficSources, _trafficSources) &&
            const DeepCollectionEquality()
                .equals(other._interestedFields, _interestedFields) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.isNotificationEnabled, isNotificationEnabled) ||
                other.isNotificationEnabled == isNotificationEnabled) &&
            (identical(other.isFacebookLoginEnabled, isFacebookLoginEnabled) ||
                other.isFacebookLoginEnabled == isFacebookLoginEnabled) &&
            (identical(other.isBiometricsEnabled, isBiometricsEnabled) ||
                other.isBiometricsEnabled == isBiometricsEnabled) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.phoneVerifiedOn, phoneVerifiedOn) ||
                other.phoneVerifiedOn == phoneVerifiedOn) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.lastPasswordChangedOn, lastPasswordChangedOn) ||
                other.lastPasswordChangedOn == lastPasswordChangedOn) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.companyAddress, companyAddress) ||
                other.companyAddress == companyAddress) &&
            (identical(other.paymentOtpToken, paymentOtpToken) ||
                other.paymentOtpToken == paymentOtpToken) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isPullToRefresh, isPullToRefresh) ||
                other.isPullToRefresh == isPullToRefresh) &&
            (identical(other.payment, payment) || other.payment == payment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        authTokenInfo,
        userId,
        accountType,
        const DeepCollectionEquality().hash(_trafficSources),
        const DeepCollectionEquality().hash(_interestedFields),
        language,
        isNotificationEnabled,
        isFacebookLoginEnabled,
        isBiometricsEnabled,
        profilePictureUrl,
        firstName,
        lastName,
        email,
        isEmailVerified,
        phoneNumber,
        phoneVerifiedOn,
        address,
        lastPasswordChangedOn,
        companyName,
        companyAddress,
        paymentOtpToken,
        errorMessage,
        isPullToRefresh,
        payment
      ]);

  /// Create a copy of AccountState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountStateImplCopyWith<_$AccountStateImpl> get copyWith =>
      __$$AccountStateImplCopyWithImpl<_$AccountStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountStateImplToJson(
      this,
    );
  }
}

abstract class _AccountState implements AccountState {
  factory _AccountState(
      {final AuthTokenInfo? authTokenInfo,
      final int userId,
      final AccountType accountType,
      final List<SocialInfo> trafficSources,
      final List<PassionateItem> interestedFields,
      final String language,
      final bool isNotificationEnabled,
      final bool isFacebookLoginEnabled,
      final bool isBiometricsEnabled,
      final String profilePictureUrl,
      final String firstName,
      final String lastName,
      final String email,
      final bool isEmailVerified,
      final String phoneNumber,
      final DateTime? phoneVerifiedOn,
      final String address,
      final DateTime? lastPasswordChangedOn,
      final String companyName,
      final String companyAddress,
      final String paymentOtpToken,
      final String errorMessage,
      final bool isPullToRefresh,
      final AccountPayment payment}) = _$AccountStateImpl;

  factory _AccountState.fromJson(Map<String, dynamic> json) =
      _$AccountStateImpl.fromJson;

  @override
  AuthTokenInfo? get authTokenInfo;
  @override
  int get userId;
  @override
  AccountType get accountType;
  @override
  List<SocialInfo> get trafficSources;
  @override
  List<PassionateItem> get interestedFields;
  @override
  String get language;
  @override
  bool get isNotificationEnabled;
  @override
  bool get isFacebookLoginEnabled;
  @override
  bool get isBiometricsEnabled;
  @override
  String get profilePictureUrl;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get email;
  @override
  bool get isEmailVerified;
  @override
  String get phoneNumber;
  @override
  DateTime? get phoneVerifiedOn;
  @override
  String get address;
  @override
  DateTime? get lastPasswordChangedOn;
  @override
  String get companyName;
  @override
  String get companyAddress;
  @override
  String get paymentOtpToken;
  @override
  String get errorMessage;
  @override
  bool get isPullToRefresh;
  @override
  AccountPayment get payment;

  /// Create a copy of AccountState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountStateImplCopyWith<_$AccountStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
