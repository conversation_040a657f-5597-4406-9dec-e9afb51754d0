import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/interested_fields_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

class InterestedFieldsCubit extends BaseCubit<InterestedFieldsState> {
  InterestedFieldsCubit() : super(InterestedFieldsState());

  void updateFields(List<PassionateItem> fields) {
    emit(state.copyWith(fields: fields));
  }

  void add(PassionateItem item) {
    emit(state.copyWith(fields: List.from(state.fields)..add(item)));
  }

  void remove(PassionateItem item) {
    emit(state.copyWith(fields: List.from(state.fields)..remove(item)));
  }
}
