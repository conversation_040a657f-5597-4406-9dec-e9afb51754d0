import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_avatar_state.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';

class AccountAvatarCubit extends BaseCubit<AccountAvatarState> {
  final AccountRepository _accountRepository;
  AccountAvatarCubit(this._accountRepository) : super(const AccountAvatarState());

  Future<void> findAvatar() async {
    try {
      final result = await _accountRepository.findAvatar();
      emit(state.copyWith(avatar: result.avatar, name: result.name));
    } catch (e) {
      emit(state.copyWith(errorMessage: e.toString()));
    }
  }

  void removeAvatar() {
    emit(state.copyWith(avatar: ''));
  }

  void updateName(String firstName, String lastName) {
    emit(state.copyWith(name: '$firstName $lastName'.trim()));
  }
}
