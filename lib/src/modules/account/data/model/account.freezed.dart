// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AccountData _$AccountDataFromJson(Map<String, dynamic> json) {
  return _AccountData.fromJson(json);
}

/// @nodoc
mixin _$AccountData {
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get avatar => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  String get updatedPasswordOn => throw _privateConstructorUsedError;
  bool get isBankAccountValid => throw _privateConstructorUsedError;

  /// Serializes this AccountData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountDataCopyWith<AccountData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountDataCopyWith<$Res> {
  factory $AccountDataCopyWith(
          AccountData value, $Res Function(AccountData) then) =
      _$AccountDataCopyWithImpl<$Res, AccountData>;
  @useResult
  $Res call(
      {String firstName,
      String lastName,
      String avatar,
      String email,
      String phoneNumber,
      String address,
      String updatedPasswordOn,
      bool isBankAccountValid});
}

/// @nodoc
class _$AccountDataCopyWithImpl<$Res, $Val extends AccountData>
    implements $AccountDataCopyWith<$Res> {
  _$AccountDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? avatar = null,
    Object? email = null,
    Object? phoneNumber = null,
    Object? address = null,
    Object? updatedPasswordOn = null,
    Object? isBankAccountValid = null,
  }) {
    return _then(_value.copyWith(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: null == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      updatedPasswordOn: null == updatedPasswordOn
          ? _value.updatedPasswordOn
          : updatedPasswordOn // ignore: cast_nullable_to_non_nullable
              as String,
      isBankAccountValid: null == isBankAccountValid
          ? _value.isBankAccountValid
          : isBankAccountValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountDataImplCopyWith<$Res>
    implements $AccountDataCopyWith<$Res> {
  factory _$$AccountDataImplCopyWith(
          _$AccountDataImpl value, $Res Function(_$AccountDataImpl) then) =
      __$$AccountDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String firstName,
      String lastName,
      String avatar,
      String email,
      String phoneNumber,
      String address,
      String updatedPasswordOn,
      bool isBankAccountValid});
}

/// @nodoc
class __$$AccountDataImplCopyWithImpl<$Res>
    extends _$AccountDataCopyWithImpl<$Res, _$AccountDataImpl>
    implements _$$AccountDataImplCopyWith<$Res> {
  __$$AccountDataImplCopyWithImpl(
      _$AccountDataImpl _value, $Res Function(_$AccountDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? avatar = null,
    Object? email = null,
    Object? phoneNumber = null,
    Object? address = null,
    Object? updatedPasswordOn = null,
    Object? isBankAccountValid = null,
  }) {
    return _then(_$AccountDataImpl(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: null == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      updatedPasswordOn: null == updatedPasswordOn
          ? _value.updatedPasswordOn
          : updatedPasswordOn // ignore: cast_nullable_to_non_nullable
              as String,
      isBankAccountValid: null == isBankAccountValid
          ? _value.isBankAccountValid
          : isBankAccountValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountDataImpl implements _AccountData {
  const _$AccountDataImpl(
      {this.firstName = '',
      this.lastName = '',
      this.avatar = '',
      this.email = '',
      this.phoneNumber = '',
      this.address = '',
      this.updatedPasswordOn = '',
      this.isBankAccountValid = false});

  factory _$AccountDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountDataImplFromJson(json);

  @override
  @JsonKey()
  final String firstName;
  @override
  @JsonKey()
  final String lastName;
  @override
  @JsonKey()
  final String avatar;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final String phoneNumber;
  @override
  @JsonKey()
  final String address;
  @override
  @JsonKey()
  final String updatedPasswordOn;
  @override
  @JsonKey()
  final bool isBankAccountValid;

  @override
  String toString() {
    return 'AccountData(firstName: $firstName, lastName: $lastName, avatar: $avatar, email: $email, phoneNumber: $phoneNumber, address: $address, updatedPasswordOn: $updatedPasswordOn, isBankAccountValid: $isBankAccountValid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountDataImpl &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.updatedPasswordOn, updatedPasswordOn) ||
                other.updatedPasswordOn == updatedPasswordOn) &&
            (identical(other.isBankAccountValid, isBankAccountValid) ||
                other.isBankAccountValid == isBankAccountValid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, firstName, lastName, avatar,
      email, phoneNumber, address, updatedPasswordOn, isBankAccountValid);

  /// Create a copy of AccountData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountDataImplCopyWith<_$AccountDataImpl> get copyWith =>
      __$$AccountDataImplCopyWithImpl<_$AccountDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountDataImplToJson(
      this,
    );
  }
}

abstract class _AccountData implements AccountData {
  const factory _AccountData(
      {final String firstName,
      final String lastName,
      final String avatar,
      final String email,
      final String phoneNumber,
      final String address,
      final String updatedPasswordOn,
      final bool isBankAccountValid}) = _$AccountDataImpl;

  factory _AccountData.fromJson(Map<String, dynamic> json) =
      _$AccountDataImpl.fromJson;

  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get avatar;
  @override
  String get email;
  @override
  String get phoneNumber;
  @override
  String get address;
  @override
  String get updatedPasswordOn;
  @override
  bool get isBankAccountValid;

  /// Create a copy of AccountData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountDataImplCopyWith<_$AccountDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AccountAvatarResponse _$AccountAvatarResponseFromJson(
    Map<String, dynamic> json) {
  return _AccountAvatarResponse.fromJson(json);
}

/// @nodoc
mixin _$AccountAvatarResponse {
  String get name => throw _privateConstructorUsedError;
  String get avatar => throw _privateConstructorUsedError;

  /// Serializes this AccountAvatarResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountAvatarResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountAvatarResponseCopyWith<AccountAvatarResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountAvatarResponseCopyWith<$Res> {
  factory $AccountAvatarResponseCopyWith(AccountAvatarResponse value,
          $Res Function(AccountAvatarResponse) then) =
      _$AccountAvatarResponseCopyWithImpl<$Res, AccountAvatarResponse>;
  @useResult
  $Res call({String name, String avatar});
}

/// @nodoc
class _$AccountAvatarResponseCopyWithImpl<$Res,
        $Val extends AccountAvatarResponse>
    implements $AccountAvatarResponseCopyWith<$Res> {
  _$AccountAvatarResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountAvatarResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? avatar = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: null == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountAvatarResponseImplCopyWith<$Res>
    implements $AccountAvatarResponseCopyWith<$Res> {
  factory _$$AccountAvatarResponseImplCopyWith(
          _$AccountAvatarResponseImpl value,
          $Res Function(_$AccountAvatarResponseImpl) then) =
      __$$AccountAvatarResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String avatar});
}

/// @nodoc
class __$$AccountAvatarResponseImplCopyWithImpl<$Res>
    extends _$AccountAvatarResponseCopyWithImpl<$Res,
        _$AccountAvatarResponseImpl>
    implements _$$AccountAvatarResponseImplCopyWith<$Res> {
  __$$AccountAvatarResponseImplCopyWithImpl(_$AccountAvatarResponseImpl _value,
      $Res Function(_$AccountAvatarResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountAvatarResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? avatar = null,
  }) {
    return _then(_$AccountAvatarResponseImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: null == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountAvatarResponseImpl implements _AccountAvatarResponse {
  const _$AccountAvatarResponseImpl({this.name = '', this.avatar = ''});

  factory _$AccountAvatarResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountAvatarResponseImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String avatar;

  @override
  String toString() {
    return 'AccountAvatarResponse(name: $name, avatar: $avatar)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountAvatarResponseImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.avatar, avatar) || other.avatar == avatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, avatar);

  /// Create a copy of AccountAvatarResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountAvatarResponseImplCopyWith<_$AccountAvatarResponseImpl>
      get copyWith => __$$AccountAvatarResponseImplCopyWithImpl<
          _$AccountAvatarResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountAvatarResponseImplToJson(
      this,
    );
  }
}

abstract class _AccountAvatarResponse implements AccountAvatarResponse {
  const factory _AccountAvatarResponse(
      {final String name, final String avatar}) = _$AccountAvatarResponseImpl;

  factory _AccountAvatarResponse.fromJson(Map<String, dynamic> json) =
      _$AccountAvatarResponseImpl.fromJson;

  @override
  String get name;
  @override
  String get avatar;

  /// Create a copy of AccountAvatarResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountAvatarResponseImplCopyWith<_$AccountAvatarResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
