// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AccountDataImpl _$$AccountDataImplFromJson(Map<String, dynamic> json) =>
    _$AccountDataImpl(
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      email: json['email'] as String? ?? '',
      phoneNumber: json['phoneNumber'] as String? ?? '',
      address: json['address'] as String? ?? '',
      updatedPasswordOn: json['updatedPasswordOn'] as String? ?? '',
      isBankAccountValid: json['isBankAccountValid'] as bool? ?? false,
    );

Map<String, dynamic> _$$AccountDataImplToJson(_$AccountDataImpl instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'avatar': instance.avatar,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'address': instance.address,
      'updatedPasswordOn': instance.updatedPasswordOn,
      'isBankAccountValid': instance.isBankAccountValid,
    };

_$AccountAvatarResponseImpl _$$AccountAvatarResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AccountAvatarResponseImpl(
      name: json['name'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
    );

Map<String, dynamic> _$$AccountAvatarResponseImplToJson(
        _$AccountAvatarResponseImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'avatar': instance.avatar,
    };
