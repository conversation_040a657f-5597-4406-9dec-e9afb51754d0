import 'package:freezed_annotation/freezed_annotation.dart';

part 'publisher_sites.freezed.dart';
part 'publisher_sites.g.dart';

@unfreezed
class PublisherSite with _$PublisherSite {
  const factory PublisherSite({
    required final int id,
    required final String name,
    required final String url,
    required final String status,
    required final String socialMediaType
  }) = _PublisherSite;

  factory PublisherSite.fromJson(Map<String, dynamic> json) =>
      _$PublisherSiteFromJson(json);
}