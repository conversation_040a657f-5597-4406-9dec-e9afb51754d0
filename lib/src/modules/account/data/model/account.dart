// ignore_for_file: constant_identifier_names
import 'package:freezed_annotation/freezed_annotation.dart';

part 'account.freezed.dart';
part 'account.g.dart';

@freezed
class AccountData with _$AccountData {
  const factory AccountData({
    @Default('') String firstName,
    @Default('') String lastName,
    @Default('') String avatar,
    @Default('') String email,
    @Default('') String phoneNumber,
    @Default('') String address,
    @Default('') String updatedPasswordOn,
    @Default(false) bool isBankAccountValid,
  }) = _AccountData;

  factory AccountData.fromJson(Map<String, dynamic> json) => _$AccountDataFromJson(json);
}

@freezed
class AccountAvatarResponse with _$AccountAvatarResponse {
  const factory AccountAvatarResponse({
    @Default('') String name,
    @Default('') String avatar,
  }) = _AccountAvatarResponse;

  factory AccountAvatarResponse.fromJson(Map<String, dynamic> json) => _$AccountAvatarResponseFromJson(json);
}

enum AccountType {
  INDIVIDUAL,
  LOCAL_COMPANY;
}
