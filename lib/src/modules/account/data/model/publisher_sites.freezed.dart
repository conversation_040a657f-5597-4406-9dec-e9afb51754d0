// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'publisher_sites.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PublisherSite _$PublisherSiteFromJson(Map<String, dynamic> json) {
  return _PublisherSite.fromJson(json);
}

/// @nodoc
mixin _$PublisherSite {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String get socialMediaType => throw _privateConstructorUsedError;

  /// Serializes this PublisherSite to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PublisherSite
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PublisherSiteCopyWith<PublisherSite> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PublisherSiteCopyWith<$Res> {
  factory $PublisherSiteCopyWith(
          PublisherSite value, $Res Function(PublisherSite) then) =
      _$PublisherSiteCopyWithImpl<$Res, PublisherSite>;
  @useResult
  $Res call(
      {int id, String name, String url, String status, String socialMediaType});
}

/// @nodoc
class _$PublisherSiteCopyWithImpl<$Res, $Val extends PublisherSite>
    implements $PublisherSiteCopyWith<$Res> {
  _$PublisherSiteCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PublisherSite
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? url = null,
    Object? status = null,
    Object? socialMediaType = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      socialMediaType: null == socialMediaType
          ? _value.socialMediaType
          : socialMediaType // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PublisherSiteImplCopyWith<$Res>
    implements $PublisherSiteCopyWith<$Res> {
  factory _$$PublisherSiteImplCopyWith(
          _$PublisherSiteImpl value, $Res Function(_$PublisherSiteImpl) then) =
      __$$PublisherSiteImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id, String name, String url, String status, String socialMediaType});
}

/// @nodoc
class __$$PublisherSiteImplCopyWithImpl<$Res>
    extends _$PublisherSiteCopyWithImpl<$Res, _$PublisherSiteImpl>
    implements _$$PublisherSiteImplCopyWith<$Res> {
  __$$PublisherSiteImplCopyWithImpl(
      _$PublisherSiteImpl _value, $Res Function(_$PublisherSiteImpl) _then)
      : super(_value, _then);

  /// Create a copy of PublisherSite
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? url = null,
    Object? status = null,
    Object? socialMediaType = null,
  }) {
    return _then(_$PublisherSiteImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      socialMediaType: null == socialMediaType
          ? _value.socialMediaType
          : socialMediaType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PublisherSiteImpl implements _PublisherSite {
  const _$PublisherSiteImpl(
      {required this.id,
      required this.name,
      required this.url,
      required this.status,
      required this.socialMediaType});

  factory _$PublisherSiteImpl.fromJson(Map<String, dynamic> json) =>
      _$$PublisherSiteImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  final String url;
  @override
  final String status;
  @override
  final String socialMediaType;

  @override
  String toString() {
    return 'PublisherSite(id: $id, name: $name, url: $url, status: $status, socialMediaType: $socialMediaType)';
  }

  /// Create a copy of PublisherSite
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PublisherSiteImplCopyWith<_$PublisherSiteImpl> get copyWith =>
      __$$PublisherSiteImplCopyWithImpl<_$PublisherSiteImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PublisherSiteImplToJson(
      this,
    );
  }
}

abstract class _PublisherSite implements PublisherSite {
  const factory _PublisherSite(
      {required final int id,
      required final String name,
      required final String url,
      required final String status,
      required final String socialMediaType}) = _$PublisherSiteImpl;

  factory _PublisherSite.fromJson(Map<String, dynamic> json) =
      _$PublisherSiteImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  String get url;
  @override
  String get status;
  @override
  String get socialMediaType;

  /// Create a copy of PublisherSite
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PublisherSiteImplCopyWith<_$PublisherSiteImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
