

import 'package:koc_app/src/modules/account/data/model/bank_info.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

class BankRepository {
  final ApiService apiService;

  BankRepository(this.apiService);

  Future<List<BankInfo>> getBanks() async {
    final response = await apiService.getData('/v3/publishers/banks');
    return (response as List)
        .map((bank) => BankInfo.fromJson(bank))
        .toList();
  }
}