import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/shared/constants/locale_constants.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class LanguagePage extends StatefulWidget {
  const LanguagePage({super.key});

  @override
  State<LanguagePage> createState() => _LanguagePageState();
}

class _LanguagePageState extends BasePageState<LanguagePage, AccountCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Language')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(10.r),
      child: Wrap(
        runSpacing: 10.r,
        children: LocaleConstants.supportedLocales.entries
            .map((e) => ListTile(
                  title: Text(
                    e.value,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  trailing: Radio<Locale>(
                      value: e.key,
                      groupValue: context.locale,
                      onChanged: (value) {
                        cubit.updateAccount(cubit.state
                            .copyWith(language: value!.languageCode));
                        context.setLocale(value);
                      }),
                ))
            .toList(),
      ),
    );
  }
}
