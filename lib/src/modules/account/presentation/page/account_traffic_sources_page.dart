import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_state.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_cubit.dart';
import 'package:koc_app/src/modules/account/mixin/traffic_sources_mixin.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/social_info_tab_bar.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';
import 'package:koc_app/src/shared/widgets/social_media_icon.dart';

class AccountTrafficSourcesPage extends StatefulWidget {
  const AccountTrafficSourcesPage({super.key});

  @override
  State<AccountTrafficSourcesPage> createState() => _AccountTrafficSourcesPageState();
}

class _AccountTrafficSourcesPageState extends BasePageState<AccountTrafficSourcesPage, AccountCubit>
    with TrafficSourcesMixin {
  @override
  void initState() {
    super.initState();
    initializeCubit();
  }

  Future<void> initializeCubit() async {
    if (!cubit.state.isPullToRefresh) {
      cubit.showLoading();
    }
    await cubit.getAccount();
    await cubit.getTrafficSources();
    if (!cubit.state.isPullToRefresh) {
      cubit.hideLoading();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: Text('Traffic sources'),
        showBottomDivider: false,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return PullToRefreshWrapper(
      onRefresh: () => cubit.pullToRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
          child: Column(
            spacing: 8.r,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Pick the best traffic sources for driving visitors to your affiliate links.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.r)),
              BlocBuilder<SurveyTabCubit, SurveyTabState>(builder: (_, surveyTabState) {
                return SocialInfoTabBar(
                    tabView: BlocBuilder<AccountCubit, AccountState>(
                        bloc: cubit,
                        builder: (_, state) {
                          final filteredSources = state.trafficSources.where((e) {
                            return surveyTabState.currentTab == 0
                                ? e.socialMediaType != SocialType.OTHER
                                : e.socialMediaType == SocialType.OTHER;
                          }).toList();
                          return _buildTabView(filteredSources, surveyTabState.currentTab == 1);
                        }));
              }),
              ElevatedButton(
                onPressed: () {
                  ReadContext(context).read<TrafficSourcesCubit>().emitSocialInfo(const SocialInfo());
                  ReadContext(context).read<SurveyTabCubit>().resetSocialInfo();
                  showTrafficSourcesModal(context);
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.add,
                      size: 15.r,
                      color: Colors.white,
                    ),
                    Text(
                      ' Add ',
                      style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabView(List<SocialInfo> trafficSources, bool isWebsiteTab) {
    return Column(
      children: trafficSources
          .map((data) => GestureDetector(
                onTap: () {
                  Modular.to.pushNamed('/account/traffic-sources/update', arguments: data);
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 14.r, horizontal: 5.r),
                  child: Row(
                    spacing: 16.r,
                    children: [
                      SizedBox(
                        height: 30.r,
                        width: 30.r,
                        child: SocialMediaIcon(url: data.url),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              data.name,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(fontWeight: FontWeight.w500, fontSize: 16.r),
                            ),
                            Text(
                              data.url,
                              style: Theme.of(context)
                                  .textTheme
                                  .labelLarge!
                                  .copyWith(color: const Color(0xFF767676), fontSize: 14.r),
                              overflow: isWebsiteTab ? TextOverflow.ellipsis : null,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ))
          .toList(),
    );
  }
}
