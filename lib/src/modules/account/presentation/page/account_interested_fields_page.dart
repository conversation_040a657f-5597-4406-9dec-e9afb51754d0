import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/interested_fields_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/interested_fields_state.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/passionate_item_view.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class AccountInterestedFieldsPage extends StatefulWidget {
  const AccountInterestedFieldsPage({super.key});

  @override
  State<AccountInterestedFieldsPage> createState() => _AccountInterestedFieldsPageState();
}

class _AccountInterestedFieldsPageState extends BasePageState<AccountInterestedFieldsPage, AccountCubit> {
  bool isSaved = false;
  bool _showValidMessage = false;

  @override
  void initState() {
    super.initState();
    initialize();
  }

  Future<void> initialize() async {
    cubit.showLoading();
    await cubit.getInterestedFields();
    ReadContext(context).read<InterestedFieldsCubit>().updateFields(cubit.state.interestedFields);
    cubit.hideLoading();
  }

  @override
  void dispose() {
    if (!isSaved) {
      // cubit.updateInterestedFields(selectedItems);
    }
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: Text('Interested fields'),
        showBottomDivider: false,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        spacing: 16.r,
        children: [
          Text('Select the fields that you interested so we can suggest you a suitable campaign to promote.',
              style: Theme.of(context).textTheme.bodySmall),
          BlocBuilder<InterestedFieldsCubit, InterestedFieldsState>(builder: (context, state) {
            return Column(
              spacing: 16.r,
              children: [
                if (_showValidMessage && state.fields.length < 3)
                  Text(
                    'Please select at least 3 for the best experience.',
                    style: context.textLabelLarge(fontWeight: FontWeight.w500, color: Colors.red),
                  ),
                PassionateItemView(
                    selectedItems: state.fields,
                    onTap: (item) {
                      setState(() {
                        _showValidMessage = true;
                      });
                      InterestedFieldsCubit interestedFieldsCubit = ReadContext(context).read<InterestedFieldsCubit>();
                      if (state.fields.contains(item)) {
                        interestedFieldsCubit.remove(item);
                      } else {
                        interestedFieldsCubit.add(item);
                      }
                    }),
              ],
            );
          }),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text('Cancel',
                      style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFFEF6507))),
                ),
                BlocBuilder<InterestedFieldsCubit, InterestedFieldsState>(builder: (context, state) {
                  return ElevatedButton(
                    onPressed: state.fields.length >= 3
                        ? () {
                            cubit.updateInterestedFields(
                                ReadContext(context).read<InterestedFieldsCubit>().state.fields);
                            Navigator.pop(context);
                          }
                        : null,
                    child: Text(
                      'Save',
                      style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
                    ),
                  );
                }),
              ],
            ),
          )
        ],
      ),
    );
  }
}
