import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ConfirmationButtons extends StatelessWidget {
  final String btnName;
  final void Function() onTap;
  final bool isValid;
  final bool showCancelButton;
  final void Function()? onCancel;
  final MainAxisAlignment? alignment;
  final String cancelButtonName;
  const ConfirmationButtons(
      {super.key,
      required this.btnName,
      required this.onTap,
      this.isValid = false,
      this.showCancelButton = false,
      this.onCancel,
      this.alignment,
      this.cancelButtonName = 'Cancel'});

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8.r,
      mainAxisAlignment: alignment ??
          (showCancelButton
              ? MainAxisAlignment.spaceBetween
              : MainAxisAlignment.end),
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (showCancelButton)
          TextButton(
              onPressed: () {
                if (onCancel != null) {
                  onCancel!();
                } else {
                  Modular.to.pop();
                }
              },
              child: Text(cancelButtonName,
                  style: Theme.of(context).textTheme.labelLarge!.copyWith(
                      color: const Color(0xFFEF6507),
                      fontWeight: FontWeight.w500))),
        ElevatedButton(
            onPressed: isValid ? () => onTap() : null,
            child: Text(btnName,
                style: Theme.of(context).textTheme.labelLarge!.copyWith(
                    color: Colors.white, fontWeight: FontWeight.w500)))
      ],
    );
  }
}
