import 'dart:developer' as developer;
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:koc_app/src/app/cubit/app_cubit.dart';
import 'package:koc_app/src/app/cubit/app_state.dart';
import 'package:koc_app/src/shared/app_theme.dart';
import 'package:koc_app/src/shared/constants/device_constants.dart';
import 'package:koc_app/src/shared/debug/floating_debug_button.dart';
import 'package:koc_app/src/shared/services/app_lifecycle_service.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final AppLifecycleService _lifecycleService = AppLifecycleService();

  @override
  void initState() {
    super.initState();
    _lifecycleService.init();
    developer.log('Initialized with AppLifecycleService');

    // Remove native splash screen when Flutter UI is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FlutterNativeSplash.remove();
      developer.log('Native splash screen removed');
    });
  }

  @override
  void dispose() {
    _lifecycleService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => Modular.get<AppCubit>(),
      child: ScreenUtilInit(
        designSize: const Size(DeviceConstants.deviceDesignWith, DeviceConstants.deviceDesignHeight),
        minTextAdapt: true,
        splitScreenMode: false,
        builder: (context, child) {
          Modular.get<AppCubit>().changeTheme(AppTheme.lightTheme);
          return BlocBuilder<AppCubit, AppState>(builder: (context, state) {
            return MaterialApp.router(
              localizationsDelegates: context.localizationDelegates,
              supportedLocales: context.supportedLocales,
              locale: context.locale,
              theme: state.themeData,
              debugShowCheckedModeBanner: false,
              routeInformationParser: Modular.routeInformationParser,
              routerDelegate: Modular.routerDelegate,
              builder: (context, child) {
                return Stack(
                  children: [
                    child ?? const SizedBox.shrink(),
                    const FloatingDebugButton(),
                  ],
                );
              },
            );
          });
        },
      ),
    );
  }
}
