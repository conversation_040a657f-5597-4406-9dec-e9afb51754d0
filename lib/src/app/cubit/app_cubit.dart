import 'package:flutter/material.dart';
import 'package:koc_app/src/app/cubit/app_state.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

class AppCubit extends BaseCubit<AppState> {
  final SharedPreferencesService sharedPreferencesService;
  AppCubit(this.sharedPreferencesService) : super(AppState());

  void changeTheme(ThemeData themeData) {
    emit(state.copyWith(themeData: themeData));
  }
}
