import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';

/// Callback for progress updates during speed tests
typedef ProgressCallback = void Function(double progress, String status);

/// Service for testing network speed with enhanced accuracy
class NetworkSpeedService {
  static final NetworkSpeedService _instance = NetworkSpeedService._internal();
  factory NetworkSpeedService() => _instance;
  NetworkSpeedService._internal();

  final Dio _dio = Dio();


  /// Enhanced download speed test with larger data sizes and progress tracking
  Future<NetworkSpeedResult> testDownloadSpeedEnhanced({
    int sizeMB = 10,
    int iterations = 3,
    Duration timeout = const Duration(seconds: 60),
    ProgressCallback? onProgress,
  }) async {
    final List<double> speeds = [];
    final List<int> latencies = [];
    int totalBytesDownloaded = 0;
    int totalTimeMs = 0;

    try {
      onProgress?.call(0.0, 'Starting enhanced download test...');

      for (int i = 0; i < iterations; i++) {
        onProgress?.call((i / iterations), 'Download test ${i + 1}/$iterations');

        final result = await _performSingleDownloadTest(sizeMB, timeout);
        if (result.success) {
          speeds.add(result.downloadSpeedMbps!);
          latencies.add(result.timeElapsedMs!);
          totalBytesDownloaded += result.bytesDownloaded!;
          totalTimeMs += result.timeElapsedMs!;
        } else {
          if (sizeMB > 1) {
            onProgress?.call((i / iterations), 'Retrying with smaller size...');
            final fallbackResult = await _performSingleDownloadTest(1, timeout);
            if (fallbackResult.success) {
              speeds.add(fallbackResult.downloadSpeedMbps!);
              latencies.add(fallbackResult.timeElapsedMs!);
              totalBytesDownloaded += fallbackResult.bytesDownloaded!;
              totalTimeMs += fallbackResult.timeElapsedMs!;
            }
          }
        }
      }

      onProgress?.call(1.0, 'Download test completed');

      if (speeds.isEmpty) {
        return NetworkSpeedResult(
          success: false,
          error: 'All download test iterations failed',
        );
      }

      final avgSpeed = speeds.reduce((a, b) => a + b) / speeds.length;

      return NetworkSpeedResult(
        success: true,
        downloadSpeedMbps: avgSpeed,
        downloadSpeedKbps: avgSpeed * 1024,
        bytesDownloaded: totalBytesDownloaded,
        timeElapsedMs: totalTimeMs,
      );
    } catch (e) {
      onProgress?.call(1.0, 'Download test failed');
      return NetworkSpeedResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Perform a single download test
  Future<NetworkSpeedResult> _performSingleDownloadTest(int sizeMB, Duration timeout) async {
    // Use multiple reliable test endpoints with different sizes
    final testUrls = [
      'https://httpbin.org/bytes/${sizeMB * 1024 * 1024}', // Primary endpoint
      'https://speed.hetzner.de/${sizeMB}MB.bin', // Alternative endpoint
      'https://proof.ovh.net/files/${sizeMB}Mb.dat', // Another alternative
    ];

    for (final url in testUrls) {
      try {
        final stopwatch = Stopwatch()..start();

        final response = await _dio.get(
          url,
          options: Options(
            responseType: ResponseType.bytes,
            receiveTimeout: timeout,
            sendTimeout: timeout,
          ),
        );

        stopwatch.stop();

        if (response.statusCode == 200 && response.data != null) {
          final bytes = (response.data as Uint8List).length;
          final timeInSeconds = stopwatch.elapsedMilliseconds / 1000.0;
          final speedBps = bytes / timeInSeconds;
          final speedMbps = (speedBps * 8) / (1024 * 1024);

          return NetworkSpeedResult(
            success: true,
            downloadSpeedMbps: speedMbps,
            downloadSpeedKbps: speedMbps * 1024,
            bytesDownloaded: bytes,
            timeElapsedMs: stopwatch.elapsedMilliseconds,
          );
        }
      } catch (e) {
        // Try next endpoint
        continue;
      }
    }

    return NetworkSpeedResult(
      success: false,
      error: 'All download endpoints failed for ${sizeMB}MB test',
    );
  }

  /// Legacy download speed test (kept for compatibility)
  Future<NetworkSpeedResult> testDownloadSpeed({
    String? testUrl,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    return _performSingleDownloadTest(1, timeout); // Use 1MB for legacy test
  }

  /// Enhanced upload speed test with larger data sizes and progress tracking
  Future<NetworkSpeedResult> testUploadSpeedEnhanced({
    int sizeKB = 2048, // Default 2MB for accurate results
    int iterations = 3, // Multiple iterations for averaging
    Duration timeout = const Duration(seconds: 60),
    ProgressCallback? onProgress,
  }) async {
    final List<double> speeds = [];
    final List<int> latencies = [];
    int totalBytesUploaded = 0;
    int totalTimeMs = 0;

    try {
      onProgress?.call(0.0, 'Starting enhanced upload test...');

      for (int i = 0; i < iterations; i++) {
        onProgress?.call((i / iterations), 'Upload test ${i + 1}/$iterations');

        final result = await _performSingleUploadTest(sizeKB, timeout);
        if (result.success) {
          speeds.add(result.uploadSpeedMbps!);
          latencies.add(result.timeElapsedMs!);
          totalBytesUploaded += result.bytesUploaded!;
          totalTimeMs += result.timeElapsedMs!;
        } else {
          // If any iteration fails, try with smaller size
          if (sizeKB > 256) {
            onProgress?.call((i / iterations), 'Retrying with smaller size...');
            final fallbackResult = await _performSingleUploadTest(256, timeout);
            if (fallbackResult.success) {
              speeds.add(fallbackResult.uploadSpeedMbps!);
              latencies.add(fallbackResult.timeElapsedMs!);
              totalBytesUploaded += fallbackResult.bytesUploaded!;
              totalTimeMs += fallbackResult.timeElapsedMs!;
            }
          }
        }
      }

      onProgress?.call(1.0, 'Upload test completed');

      if (speeds.isEmpty) {
        return NetworkSpeedResult(
          success: false,
          error: 'All upload test iterations failed',
        );
      }

      final avgSpeed = speeds.reduce((a, b) => a + b) / speeds.length;

      return NetworkSpeedResult(
        success: true,
        uploadSpeedMbps: avgSpeed,
        uploadSpeedKbps: avgSpeed * 1024,
        bytesUploaded: totalBytesUploaded,
        timeElapsedMs: totalTimeMs,
      );
    } catch (e) {
      onProgress?.call(1.0, 'Upload test failed');
      return NetworkSpeedResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Perform a single upload test
  Future<NetworkSpeedResult> _performSingleUploadTest(int sizeKB, Duration timeout) async {
    // Use multiple reliable endpoints for upload testing
    final testUrls = [
      'https://postman-echo.com/post',
      'https://httpbin.org/post',
      'https://jsonplaceholder.typicode.com/posts',
    ];

    for (final url in testUrls) {
      try {
        // Create test data of specified size
        final testData = Uint8List(sizeKB * 1024);
        // Fill with random data for more realistic testing
        for (int i = 0; i < testData.length; i++) {
          testData[i] = i % 256;
        }

        final stopwatch = Stopwatch()..start();

        final response = await _dio.post(
          url,
          data: testData,
          options: Options(
            sendTimeout: timeout,
            receiveTimeout: timeout,
            headers: {
              'Content-Type': 'application/octet-stream',
            },
          ),
        );

        stopwatch.stop();

        if (response.statusCode == 200 || response.statusCode == 201) {
          final timeInSeconds = stopwatch.elapsedMilliseconds / 1000.0;
          final speedBps = testData.length / timeInSeconds;
          final speedMbps = (speedBps * 8) / (1024 * 1024);

          return NetworkSpeedResult(
            success: true,
            uploadSpeedMbps: speedMbps,
            uploadSpeedKbps: speedMbps * 1024,
            bytesUploaded: testData.length,
            timeElapsedMs: stopwatch.elapsedMilliseconds,
          );
        }
      } catch (e) {
        // Try next endpoint
        continue;
      }
    }

    return NetworkSpeedResult(
      success: false,
      error: 'All upload endpoints failed for ${sizeKB}KB test',
    );
  }

  /// Legacy upload speed test (kept for compatibility)
  Future<NetworkSpeedResult> testUploadSpeed({
    String? testUrl,
    int dataSizeBytes = 262144, // 256KB default (smaller for mobile)
    Duration timeout = const Duration(seconds: 30),
  }) async {
    // Use multiple reliable endpoints for upload testing
    final urls = [
      'https://postman-echo.com/post',
      'https://httpbin.org/post',
      'https://jsonplaceholder.typicode.com/posts',
    ];

    final url = testUrl ?? urls[0];

    try {
      // Create smaller test data to avoid memory issues
      final testString = 'x' * (dataSizeBytes ~/ 10); // Reduce size for JSON

      final stopwatch = Stopwatch()..start();

      // Use simple JSON data
      final response = await _dio.post(
        url,
        data: {
          'data': testString,
          'size': dataSizeBytes,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'test': 'speed_test',
        },
        options: Options(
          sendTimeout: timeout,
          receiveTimeout: timeout,
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      stopwatch.stop();

      if (response.statusCode == 200 || response.statusCode == 201) {
        final timeInSeconds = stopwatch.elapsedMilliseconds / 1000.0;
        final actualDataSize = testString.length + 100; // Add overhead for JSON structure
        final speedBps = actualDataSize / timeInSeconds; // bytes per second
        final speedMbps = (speedBps * 8) / (1024 * 1024); // Megabits per second

        return NetworkSpeedResult(
          success: true,
          uploadSpeedMbps: speedMbps,
          uploadSpeedKbps: speedMbps * 1024,
          bytesUploaded: actualDataSize,
          timeElapsedMs: stopwatch.elapsedMilliseconds,
        );
      } else {
        return NetworkSpeedResult(
          success: false,
          error: 'Upload failed: ${response.statusCode}',
        );
      }
    } catch (e) {
      // Try alternative endpoint if first one fails
      if (testUrl == null && url == urls[0]) {
        try {
          return await testUploadSpeed(
            testUrl: urls[1],
            dataSizeBytes: dataSizeBytes,
            timeout: timeout,
          );
        } catch (e2) {
          // If second also fails, try third
          try {
            return await testUploadSpeed(
              testUrl: urls[2],
              dataSizeBytes: dataSizeBytes,
              timeout: timeout,
            );
          } catch (e3) {
            return NetworkSpeedResult(
              success: false,
              error: 'All upload endpoints failed: ${e.toString()}',
            );
          }
        }
      }

      return NetworkSpeedResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Test ping/latency
  Future<NetworkSpeedResult> testPing({
    String host = 'google.com',
    int port = 80,
    Duration timeout = const Duration(seconds: 5),
  }) async {
    try {
      final stopwatch = Stopwatch()..start();

      final socket = await Socket.connect(host, port, timeout: timeout);
      stopwatch.stop();

      await socket.close();

      return NetworkSpeedResult(
        success: true,
        pingMs: stopwatch.elapsedMilliseconds,
      );
    } catch (e) {
      return NetworkSpeedResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Enhanced ping test with multiple iterations for better accuracy
  Future<NetworkSpeedResult> testPingEnhanced({
    String host = 'google.com',
    int port = 80,
    int iterations = 5,
    Duration timeout = const Duration(seconds: 5),
    ProgressCallback? onProgress,
  }) async {
    final List<int> pings = [];

    try {
      onProgress?.call(0.0, 'Starting ping test...');

      for (int i = 0; i < iterations; i++) {
        onProgress?.call((i / iterations), 'Ping ${i + 1}/$iterations');

        final result = await testPing(host: host, port: port, timeout: timeout);
        if (result.success && result.pingMs != null) {
          pings.add(result.pingMs!);
        }

        // Small delay between pings
        if (i < iterations - 1) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      onProgress?.call(1.0, 'Ping test completed');

      if (pings.isEmpty) {
        return NetworkSpeedResult(
          success: false,
          error: 'All ping attempts failed',
        );
      }

      final avgPing = pings.reduce((a, b) => a + b) / pings.length;

      return NetworkSpeedResult(
        success: true,
        pingMs: avgPing.round(),
      );
    } catch (e) {
      onProgress?.call(1.0, 'Ping test failed');
      return NetworkSpeedResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Run enhanced comprehensive speed test with progress tracking
  Future<NetworkSpeedResult> runEnhancedComprehensiveTest({
    int downloadSizeMB = 5, // Smaller default for mobile
    int uploadSizeKB = 1024, // 1MB for upload
    int iterations = 3,
    Duration timeout = const Duration(seconds: 60),
    ProgressCallback? onProgress,
  }) async {
    try {
      onProgress?.call(0.0, 'Starting comprehensive speed test...');

      // Test ping first (20% of progress)
      onProgress?.call(0.0, 'Testing ping...');
      final pingResult = await testPingEnhanced(
        iterations: 5,
        onProgress: (progress, status) => onProgress?.call(progress * 0.2, status),
      );

      // Test download speed (40% of progress)
      onProgress?.call(0.2, 'Testing download speed...');
      final downloadResult = await testDownloadSpeedEnhanced(
        sizeMB: downloadSizeMB,
        iterations: iterations,
        timeout: timeout,
        onProgress: (progress, status) => onProgress?.call(0.2 + progress * 0.4, status),
      );

      // Test upload speed (40% of progress)
      onProgress?.call(0.6, 'Testing upload speed...');
      final uploadResult = await testUploadSpeedEnhanced(
        sizeKB: uploadSizeKB,
        iterations: iterations,
        timeout: timeout,
        onProgress: (progress, status) => onProgress?.call(0.6 + progress * 0.4, status),
      );

      onProgress?.call(1.0, 'Comprehensive test completed');

      return NetworkSpeedResult(
        success: pingResult.success && downloadResult.success && uploadResult.success,
        pingMs: pingResult.pingMs,
        downloadSpeedMbps: downloadResult.downloadSpeedMbps,
        downloadSpeedKbps: downloadResult.downloadSpeedKbps,
        uploadSpeedMbps: uploadResult.uploadSpeedMbps,
        uploadSpeedKbps: uploadResult.uploadSpeedKbps,
        bytesDownloaded: downloadResult.bytesDownloaded,
        bytesUploaded: uploadResult.bytesUploaded,
        timeElapsedMs: (downloadResult.timeElapsedMs ?? 0) + (uploadResult.timeElapsedMs ?? 0),
        error: !pingResult.success
            ? pingResult.error
            : !downloadResult.success
                ? downloadResult.error
                : !uploadResult.success
                    ? uploadResult.error
                    : null,
      );
    } catch (e) {
      onProgress?.call(1.0, 'Comprehensive test failed');
      return NetworkSpeedResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Legacy comprehensive test (kept for compatibility)
  Future<NetworkSpeedResult> runComprehensiveTest({
    Duration timeout = const Duration(seconds: 30),
  }) async {
    return runEnhancedComprehensiveTest(
      downloadSizeMB: 1,
      uploadSizeKB: 256,
      iterations: 1,
      timeout: timeout,
    );
  }
}

/// Result class for network speed tests
class NetworkSpeedResult {
  final bool success;
  final String? error;
  final double? downloadSpeedMbps;
  final double? downloadSpeedKbps;
  final double? uploadSpeedMbps;
  final double? uploadSpeedKbps;
  final int? pingMs;
  final int? bytesDownloaded;
  final int? bytesUploaded;
  final int? timeElapsedMs;

  NetworkSpeedResult({
    required this.success,
    this.error,
    this.downloadSpeedMbps,
    this.downloadSpeedKbps,
    this.uploadSpeedMbps,
    this.uploadSpeedKbps,
    this.pingMs,
    this.bytesDownloaded,
    this.bytesUploaded,
    this.timeElapsedMs,
  });

  @override
  String toString() {
    if (!success) return 'Error: $error';

    final buffer = StringBuffer();
    if (pingMs != null) buffer.writeln('Ping: ${pingMs}ms');
    if (downloadSpeedKbps != null) buffer.writeln('Download: ${downloadSpeedKbps!.toStringAsFixed(0)} Kbps');
    if (uploadSpeedKbps != null) buffer.writeln('Upload: ${uploadSpeedKbps!.toStringAsFixed(0)} Kbps');

    return buffer.toString().trim();
  }
}
