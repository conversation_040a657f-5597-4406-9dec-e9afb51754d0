import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:synchronized/synchronized.dart';

class SecureStorageHelper {
  static const _storage = FlutterSecureStorage();
  static final _lock = Lock();

  Future<String?> read(String key) async {
    return await _lock.synchronized(() async {
      return await _storage.read(key: key);
    });
  }

  Future<void> write(String key, String? value) async {
    await _lock.synchronized(() async {
      await _storage.write(key: key, value: value);
    });
  }

  Future<void> deleteAll() async {
    await _lock.synchronized(() async {
      await _storage.deleteAll();
    });
  }
}