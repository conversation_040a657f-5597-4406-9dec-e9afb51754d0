import 'dart:convert';

import 'package:koc_app/src/modules/account/data/model/publisher_sites.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesService {
  Future<void> setToInstance<T>(String key, T value) async {
    var prefs = await SharedPreferences.getInstance();
    if (value is String) {
      await prefs.setString(key, value);
      return;
    }
    if (value is bool) {
      await prefs.setBool(key, value);
      return;
    }
    if (value is int) {
      await prefs.setInt(key, value);
      return;
    }
    if (value is double) {
      await prefs.setDouble(key, value);
      return;
    }
    await prefs.setString(key, value as String);
    return;
  }

  Future<bool?> getBoolFromInstance(String key) async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key);
  }

  Future<String?> getStringFromInstance(String key) async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  Future<num?> getNumFromInstance(String key) async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(key);
  }

  Future<String?> getAccessToken() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getString(InstanceConstants.tokenKey);
  }

  Future<void> setAccessToken(String token) async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.setString(InstanceConstants.tokenKey, token);
  }

  Future<String?> getRefreshToken() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getString(InstanceConstants.refreshTokenKey);
  }

  Future<void> setRefreshToken(String token) async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.setString(InstanceConstants.refreshTokenKey, token);
  }

  Future<void> setLanguage(String language) async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.setString(InstanceConstants.languageKey, language);
  }

  Future<String?> getLanguage() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getString(InstanceConstants.languageKey);
  }

  Future<void> setEmail(String email) async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.setString(InstanceConstants.emailKey, email);
  }

  Future<String?> getEmail() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getString(InstanceConstants.emailKey);
  }

  Future<String?> getCountryCode() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getString(InstanceConstants.countryCodeKey);
  }

  Future<void> clearEmail() async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.remove(InstanceConstants.emailKey);
  }

  Future<void> setUserId(int userId) async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.setInt(InstanceConstants.userIdKey, userId);
  }

  Future<int?> getUserId() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getInt(InstanceConstants.userIdKey);
  }

  Future<String?> getProfilePictureUrl() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getString(InstanceConstants.profilePictureUrlKey);
  }

  Future<void> clearUserId() async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.remove(InstanceConstants.userIdKey);
  }

  Future<void> setCampaignSearchHistories(String keyword) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> campaignSearchHistories = List.from(await getCampaignSearchHistories());
    campaignSearchHistories.insert(0, keyword);
    await prefs.setStringList(InstanceConstants.campaignSearchHistoriesKey, campaignSearchHistories);
  }

  Future<List<String>> getCampaignSearchHistories() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(InstanceConstants.campaignSearchHistoriesKey) ?? [];
  }

  Future<void> clearCampaignSearchHistories() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(InstanceConstants.campaignSearchHistoriesKey);
  }

  Future<void> deleteKey(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  Future<Set<String>> getKeyList() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getKeys();
  }

  Future<void> setCurrentSiteId(int siteId) async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.setInt(InstanceConstants.currentSiteIdKey, siteId);
  }

  Future<int?> getCurrentSiteId() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getInt(InstanceConstants.currentSiteIdKey);
  }

  Future<void> setSites(List<PublisherSite> sites) async {
    var prefs = await SharedPreferences.getInstance();
    String sitesJson = jsonEncode(sites.map((site) => site.toJson()).toList());
    await prefs.setString(InstanceConstants.sitesKey, sitesJson);
  }

  Future<List<PublisherSite>> getSites() async {
    var prefs = await SharedPreferences.getInstance();
    String? sitesJson = prefs.getString(InstanceConstants.sitesKey);
    if (sitesJson == null) {
      return [];
    }
    List<dynamic> jsonList = jsonDecode(sitesJson);
    return jsonList.map((json) => PublisherSite.fromJson(json)).toList();
  }

  Future<void> setPublisherCurrency(String currency) async {
    var prefs = await SharedPreferences.getInstance();
    await prefs.setString(InstanceConstants.publisherCurrencyKey, currency);
  }

  Future<String?> getPublisherCurrency() async {
    var prefs = await SharedPreferences.getInstance();
    return prefs.getString(InstanceConstants.publisherCurrencyKey);
  }

  Future<void> clearAll() async {
    var prefs = await SharedPreferences.getInstance();
    prefs.clear();
  }
}
