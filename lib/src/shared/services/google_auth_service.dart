import 'dart:convert';
import 'dart:developer' as dev;

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:koc_app/src/modules/authentication/data/models/sign_in_sign_up.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';

class GoogleAuthService {
  final GoogleSignIn _googleSignIn;
  static const storage = FlutterSecureStorage();

  GoogleAuthService(this._googleSignIn);

  Future<GoogleSignInTokens?> signInWithGoogle() async {
    try {
      await _googleSignIn.signOut();
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      final tokenData = GoogleSignInTokens(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
        email: googleUser.email,
        displayName: googleUser.displayName,
        photoUrl: googleUser.photoUrl,
      );

      await storage.write(
        key: InstanceConstants.googleAuthDataKey,
        value: jsonEncode({
          'accessToken': tokenData.accessToken,
          'idToken': tokenData.idToken,
          'email': tokenData.email,
          'displayName': tokenData.displayName,
          'photoUrl': tokenData.photoUrl,
        }),
      );

      return tokenData;
    } catch (e) {
      dev.log('Error signing in with Google: $e');
      return null;
    }
  }

  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await storage.delete(key: InstanceConstants.googleAuthDataKey);
    } catch (e) {
      dev.log('Error during Google sign out: $e');
    }
  }
}
