// url_helpers.dart
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart' show SocialType;

SocialType getSocialTypeFromUrl(String url) {
  final lowerUrl = url.toLowerCase();

  if (lowerUrl.startsWith('https://www.facebook.com') ||
      lowerUrl.startsWith('https://facebook.com') ||
      lowerUrl.startsWith('https://www.fb.com') ||
      lowerUrl.startsWith('https://fb.com')) {
    return SocialType.FACEBOOK;
  }
  
  else if (lowerUrl.startsWith('https://www.instagram.com') ||
           lowerUrl.startsWith('https://instagram.com')) {
    return SocialType.INSTAGRAM;
  }

  else if (lowerUrl.startsWith('https://www.youtube.com') ||
           lowerUrl.startsWith('https://youtube.com') ||
           lowerUrl.startsWith('https://www.youtu.be') ||
           lowerUrl.startsWith('https://youtu.be')) {
    return SocialType.YOUTUBE;
  }
  
  else if (lowerUrl.startsWith('https://www.tiktok.com') ||
           lowerUrl.startsWith('https://tiktok.com')) {
    return SocialType.TIKTOK;
  }

  else if (lowerUrl.startsWith('https://www.x.com') ||
           lowerUrl.startsWith('https://x.com') ||
           lowerUrl.contains('twitter.com')) {
    return SocialType.TWITTER;
  }
  
  return SocialType.OTHER;
}
