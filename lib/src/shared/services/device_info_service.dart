import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';

/// Service for getting device information and network details
class DeviceInfoService {
  static final DeviceInfoService _instance = DeviceInfoService._internal();
  factory DeviceInfoService() => _instance;
  DeviceInfoService._internal();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final Connectivity _connectivity = Connectivity();
  final Dio _dio = Dio();

  /// Get device model information
  Future<String> getDeviceModel() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return '${iosInfo.name} (${iosInfo.model})';
      } else {
        return 'Unknown Device';
      }
    } catch (e) {
      // Handle simulator/emulator cases
      if (e.toString().contains('MissingPluginException')) {
        if (Platform.isAndroid) {
          return 'Android Simulator';
        } else if (Platform.isIOS) {
          return 'iOS Simulator';
        } else {
          return 'Simulator/Emulator';
        }
      }
      return 'Device info unavailable';
    }
  }

  /// Get device OS version
  Future<String> getOSVersion() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return 'Android ${androidInfo.version.release} (API ${androidInfo.version.sdkInt})';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return 'iOS ${iosInfo.systemVersion}';
      } else {
        return 'Unknown OS';
      }
    } catch (e) {
      // Handle simulator/emulator cases
      if (e.toString().contains('MissingPluginException')) {
        if (Platform.isAndroid) {
          return 'Android Simulator';
        } else if (Platform.isIOS) {
          return 'iOS Simulator';
        } else {
          return 'Simulator OS';
        }
      }
      return 'OS info unavailable';
    }
  }

  /// Get current IP address from external service
  Future<String> getCurrentIP() async {
    try {
      // Try multiple services for reliability
      final services = [
        'https://api.ipify.org?format=text',
        'https://ipinfo.io/ip',
        'https://icanhazip.com',
      ];

      for (final service in services) {
        try {
          final response = await _dio.get(
            service,
            options: Options(
              receiveTimeout: const Duration(seconds: 5),
              sendTimeout: const Duration(seconds: 5),
            ),
          );

          if (response.statusCode == 200 && response.data != null) {
            final ip = response.data.toString().trim();
            // Basic IP validation
            if (RegExp(r'^(\d{1,3}\.){3}\d{1,3}$').hasMatch(ip)) {
              return ip;
            }
          }
        } catch (e) {
          // Try next service
          continue;
        }
      }

      return 'Unable to get IP';
    } catch (e) {
      return 'Error: $e';
    }
  }

  /// Get local IP address (WiFi/Mobile)
  Future<String> getLocalIP() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();

      if (connectivityResult.contains(ConnectivityResult.wifi)) {
        // Try to get WiFi IP
        for (final interface in await NetworkInterface.list()) {
          if (interface.name.toLowerCase().contains('wlan') ||
              interface.name.toLowerCase().contains('wifi') ||
              interface.name.toLowerCase().contains('en0')) {
            for (final addr in interface.addresses) {
              if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
                return '${addr.address} (WiFi)';
              }
            }
          }
        }
      }

      if (connectivityResult.contains(ConnectivityResult.mobile)) {
        // Try to get mobile IP
        for (final interface in await NetworkInterface.list()) {
          if (interface.name.toLowerCase().contains('rmnet') ||
              interface.name.toLowerCase().contains('pdp') ||
              interface.name.toLowerCase().contains('cellular')) {
            for (final addr in interface.addresses) {
              if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
                return '${addr.address} (Mobile)';
              }
            }
          }
        }
      }

      // Fallback: get any non-loopback IPv4 address
      for (final interface in await NetworkInterface.list()) {
        for (final addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
            return addr.address;
          }
        }
      }

      return 'No local IP found';
    } catch (e) {
      // Handle simulator/emulator cases
      if (e.toString().contains('MissingPluginException')) {
        return 'Local IP unavailable (Simulator)';
      }
      return 'Local IP unavailable';
    }
  }

  /// Get connection type
  Future<String> getConnectionType() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();

      if (connectivityResult.contains(ConnectivityResult.wifi)) {
        return 'WiFi';
      } else if (connectivityResult.contains(ConnectivityResult.mobile)) {
        return 'Mobile Data';
      } else if (connectivityResult.contains(ConnectivityResult.ethernet)) {
        return 'Ethernet';
      } else {
        return 'No Connection';
      }
    } catch (e) {
      // Handle simulator/emulator cases
      if (e.toString().contains('MissingPluginException')) {
        return 'Simulator Network';
      }
      return 'Unknown';
    }
  }

  /// Get comprehensive device and network info
  Future<DeviceNetworkInfo> getDeviceNetworkInfo() async {
    try {
      final results = await Future.wait([
        getDeviceModel(),
        getOSVersion(),
        getCurrentIP(),
        getLocalIP(),
        getConnectionType(),
      ]);

      return DeviceNetworkInfo(
        deviceModel: results[0],
        osVersion: results[1],
        publicIP: results[2],
        localIP: results[3],
        connectionType: results[4],
      );
    } catch (e) {
      return DeviceNetworkInfo(
        deviceModel: 'Error',
        osVersion: 'Error',
        publicIP: 'Error',
        localIP: 'Error',
        connectionType: 'Error',
        error: e.toString(),
      );
    }
  }
}

/// Data class for device and network information
class DeviceNetworkInfo {
  final String deviceModel;
  final String osVersion;
  final String publicIP;
  final String localIP;
  final String connectionType;
  final String? error;

  DeviceNetworkInfo({
    required this.deviceModel,
    required this.osVersion,
    required this.publicIP,
    required this.localIP,
    required this.connectionType,
    this.error,
  });

  @override
  String toString() {
    if (error != null) return 'Error: $error';

    return '''
Device: $deviceModel
OS: $osVersion
Connection: $connectionType
Public IP: $publicIP
Local IP: $localIP''';
  }
}
