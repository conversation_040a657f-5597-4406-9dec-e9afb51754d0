import 'package:local_auth/local_auth.dart';

class LocalAuthService {
  final LocalAuthentication _localAuthentication;
  LocalAuthService(this._localAuthentication);

  Future<bool> canBiometric() async {
    return await _localAuthentication.isDeviceSupported();
  }

  Future<bool> authenticate() async {
    return await _localAuthentication.authenticate(
      localizedReason: 'Authenticate to continue',
    );
  }
}