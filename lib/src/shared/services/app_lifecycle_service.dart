import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:koc_app/src/shared/cache/unified_cache_manager.dart';

/// **AppLifecycleService** - Manages app lifecycle events and resources
///
/// **Purpose:**
/// - Monitors app lifecycle states (resumed, paused, detached, etc.)
/// - Automatically manages cache and resources based on app state
/// - Fixes splash screen hanging issue when app is reopened
/// - Optimizes memory usage when app runs in background
///
/// **Problems Solved:**
/// 1. **Cache Deadlock**: Cache can deadlock when app restarts
/// 2. **Memory Leaks**: App holds too many resources in background
/// 3. **Cold Start Issues**: App takes too long to restart
/// 4. **Resource Management**: No proper cleanup when app is killed
///
/// **How it works:**
/// - Monitors app lifecycle states via WidgetsBindingObserver
/// - Distinguishes "cold start" (background > 5 min) vs "warm start"
/// - Automatically reinitializes cache when necessary
/// - Cleans up resources to prevent memory issues
///
/// **Usage:**
/// ```dart
/// final lifecycleService = AppLifecycleService();
/// lifecycleService.init(); // In initState()
/// lifecycleService.dispose(); // In dispose()
/// ```
class AppLifecycleService with WidgetsBindingObserver {
  static final AppLifecycleService _instance = AppLifecycleService._internal();
  factory AppLifecycleService() => _instance;
  AppLifecycleService._internal();

  bool _isInitialized = false;
  Timer? _backgroundTimer;
  DateTime? _backgroundTime;

  /// Duration after which to consider the app as "cold start" when resumed
  static const Duration _coldStartThreshold = Duration(minutes: 5);

  /// Initialize the lifecycle service
  void init() {
    if (_isInitialized) return;

    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;
    developer.log('🔄 AppLifecycleService initialized');
  }

  /// Dispose the lifecycle service
  void dispose() {
    if (_isInitialized) {
      WidgetsBinding.instance.removeObserver(this);
      _backgroundTimer?.cancel();
      _isInitialized = false;
      developer.log('🔄 AppLifecycleService disposed');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    developer.log('📱 App lifecycle state: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  void _handleAppResumed() {
    developer.log('📱 App resumed');
    _backgroundTimer?.cancel();

    if (_backgroundTime != null) {
      final backgroundDuration = DateTime.now().difference(_backgroundTime!);
      developer.log('📱 App was in background for: ${backgroundDuration.inSeconds}s');

      if (backgroundDuration > _coldStartThreshold) {
        developer.log('🔄 Cold start detected, reinitializing cache...');
        _reinitializeCache();
      }
    }

    _backgroundTime = null;
  }

  void _handleAppPaused() {
    developer.log('📱 App paused');
    _backgroundTime = DateTime.now();

    _backgroundTimer = Timer(const Duration(minutes: 10), () {
      developer.log('🧹 App in background for 10 minutes, cleaning up resources...');
      _cleanupBackgroundResources();
    });
  }

  void _handleAppDetached() {
    developer.log('📱 App detached');
    _cleanupAllResources();
  }

  void _handleAppInactive() {
    developer.log('📱 App inactive');
  }

  void _handleAppHidden() {
    developer.log('📱 App hidden');
  }

  /// Reinitialize cache after cold start
  Future<void> _reinitializeCache() async {
    try {
      final cacheManager = UnifiedCacheManager();

      await cacheManager.reset();
      await cacheManager.init();

      developer.log('✅ Cache reinitialized successfully');
    } catch (e) {
      developer.log('❌ Failed to reinitialize cache: $e');
    }
  }

  /// Clean up resources when app is in background for too long
  Future<void> _cleanupBackgroundResources() async {
    try {
      final cacheManager = UnifiedCacheManager();
      if (cacheManager.isInitialized) {
        await cacheManager.clearImageCache();
        developer.log('🧹 Image cache cleared to free memory');
      }
    } catch (e) {
      developer.log('❌ Error cleaning up background resources: $e');
    }
  }

  /// Clean up all resources when app is being terminated
  Future<void> _cleanupAllResources() async {
    try {
      final cacheManager = UnifiedCacheManager();
      await cacheManager.dispose();
      developer.log('🧹 All resources cleaned up');
    } catch (e) {
      developer.log('❌ Error cleaning up all resources: $e');
    }
  }

  /// Check if app is considered to be doing a cold start
  bool get isColdStart {
    if (_backgroundTime == null) return false;
    final backgroundDuration = DateTime.now().difference(_backgroundTime!);
    return backgroundDuration > _coldStartThreshold;
  }

  /// Get the duration the app has been in background
  Duration? get backgroundDuration {
    if (_backgroundTime == null) return null;
    return DateTime.now().difference(_backgroundTime!);
  }
}
