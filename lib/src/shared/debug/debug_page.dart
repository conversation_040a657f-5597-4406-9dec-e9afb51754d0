import 'package:flutter/material.dart';
import 'package:koc_app/src/shared/cache/cache_test_page.dart';
import 'package:koc_app/src/shared/debug/performance_tab.dart';

/// A debug page with multiple tabs for testing and debugging
class DebugPage extends StatefulWidget {
  const DebugPage({Key? key}) : super(key: key);

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Tools'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48.0),
          child: TabBar(
            controller: _tabController,
            tabAlignment: TabAlignment.center, // Explicitly set alignment to center
            tabs: const [
              Tab(text: 'Cache', icon: Icon(Icons.storage)),
              Tab(text: 'Performance', icon: Icon(Icons.speed)),
            ],
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          CacheTestPage(),
          PerformanceTab(),
        ],
      ),
    );
  }
}
