import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/shared/debug/debug_button_style.dart';
import 'package:koc_app/src/shared/services/network_speed_service.dart';
import 'package:koc_app/src/shared/services/device_info_service.dart';

/// Log entry with color information and category
class LogEntry {
  final String message;
  final Color color;
  final DateTime timestamp;
  final LogCategory category;

  LogEntry({
    required this.message,
    this.color = Colors.green,
    DateTime? timestamp,
    this.category = LogCategory.system,
  }) : timestamp = timestamp ?? DateTime.now();

  String get formattedMessage {
    final timeStr = timestamp.toString().substring(11, 19);
    final categoryIcon = _getCategoryIcon();
    // Remove extra space when no icon is present
    if (categoryIcon.isEmpty) {
      return '$timeStr: $message';
    }
    return '$timeStr: $categoryIcon $message';
  }

  String _getCategoryIcon() {
    // Remove icons as per user preference
    return '';
  }
}

/// Categories for different types of log entries
enum LogCategory {
  system,
  network,
  device,
  performance,
  summary,
  error,
}

/// A tab for monitoring app performance
class PerformanceTab extends StatefulWidget {
  const PerformanceTab({super.key});

  @override
  State<PerformanceTab> createState() => _PerformanceTabState();
}

class _PerformanceTabState extends State<PerformanceTab> with SingleTickerProviderStateMixin {
  final List<LogEntry> _logs = [];
  bool _isMonitoring = false;
  late Timer _timer;
  int _frameCount = 0;
  double _fps = 0;
  late Stopwatch _stopwatch;

  // Network speed test variables
  final NetworkSpeedService _networkSpeedService = NetworkSpeedService();
  bool _isTestingSpeed = false;
  double _speedTestProgress = 0.0;
  String _speedTestStatus = '';

  // Device info variables
  final DeviceInfoService _deviceInfoService = DeviceInfoService();
  DeviceNetworkInfo? _deviceInfo;
  bool _isLoadingDeviceInfo = false;

  @override
  void initState() {
    super.initState();
    _addLog('Performance Monitor Initialized');
    _stopwatch = Stopwatch();
    _loadDeviceInfo();
  }

  @override
  void dispose() {
    if (_isMonitoring) {
      _timer.cancel();
    }
    super.dispose();
  }

  void _addLog(String message, {Color color = Colors.green, LogCategory category = LogCategory.system}) {
    setState(() {
      _logs.add(LogEntry(message: message, color: color, category: category));
      if (_logs.length > 100) {
        _logs.removeAt(0);
      }
    });
  }

  void _startMonitoring() {
    if (_isMonitoring) return;

    setState(() {
      _isMonitoring = true;
      _frameCount = 0;
      _fps = 0;
    });

    _addLog('Starting performance monitoring...', color: Colors.cyan, category: LogCategory.system);
    _stopwatch.reset();
    _stopwatch.start();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final elapsedSeconds = _stopwatch.elapsedMilliseconds / 1000;
      if (elapsedSeconds > 0) {
        setState(() {
          _fps = _frameCount / elapsedSeconds;
          _addLog('FPS: ${_fps.toStringAsFixed(1)}', color: Colors.green, category: LogCategory.performance);
          _frameCount = 0;
        });
        _stopwatch.reset();
        _stopwatch.start();
      }
    });

    WidgetsBinding.instance.addPostFrameCallback(_onFrame);
  }

  void _onFrame(Duration timeStamp) {
    if (!_isMonitoring) return;

    _frameCount++;
    WidgetsBinding.instance.addPostFrameCallback(_onFrame);
  }

  void _stopMonitoring() {
    if (!_isMonitoring) return;

    _timer.cancel();
    _stopwatch.stop();

    setState(() {
      _isMonitoring = false;
    });

    _addLog('Stopped performance monitoring', color: Colors.cyan, category: LogCategory.system);
  }

  Future<void> _testNetworkSpeed() async {
    if (_isTestingSpeed) return;

    setState(() {
      _isTestingSpeed = true;
      _speedTestProgress = 0.0;
      _speedTestStatus = 'Initializing...';
    });

    _addLog('Starting enhanced network speed test...', color: Colors.cyan, category: LogCategory.network);

    try {
      // Run enhanced comprehensive test with progress tracking
      final result = await _networkSpeedService.runEnhancedComprehensiveTest(
        downloadSizeMB: 5, // 5MB for more accurate download test
        uploadSizeKB: 1024, // 1MB for upload test
        iterations: 3, // 3 iterations for averaging
        timeout: const Duration(seconds: 60),
        onProgress: (progress, status) {
          setState(() {
            _speedTestProgress = progress;
            _speedTestStatus = status;
          });
          _addLog('Progress: ${(progress * 100).toStringAsFixed(0)}% - $status',
              color: Colors.white, category: LogCategory.network);
        },
      );

      if (result.success) {
        // Display detailed results
        _addLog('=== ENHANCED SPEED TEST RESULTS ===', color: Colors.cyan, category: LogCategory.summary);

        if (result.pingMs != null) {
          _addLog('Ping: ${result.pingMs}ms (avg of 5 tests)', color: Colors.yellow, category: LogCategory.summary);
        }

        if (result.downloadSpeedKbps != null) {
          _addLog('Download: ${result.downloadSpeedKbps!.toStringAsFixed(0)} Kbps (avg of 3 tests)',
              color: Colors.yellow, category: LogCategory.summary);
        }

        if (result.uploadSpeedKbps != null) {
          _addLog('Upload: ${result.uploadSpeedKbps!.toStringAsFixed(0)} Kbps (avg of 3 tests)',
              color: Colors.yellow, category: LogCategory.summary);
        }

        if (result.bytesDownloaded != null) {
          _addLog('Data Downloaded: ${(result.bytesDownloaded! / (1024 * 1024)).toStringAsFixed(1)} MB',
              color: Colors.lightBlue, category: LogCategory.summary);
        }

        if (result.bytesUploaded != null) {
          _addLog('Data Uploaded: ${(result.bytesUploaded! / (1024 * 1024)).toStringAsFixed(1)} MB',
              color: Colors.lightBlue, category: LogCategory.summary);
        }

        if (result.timeElapsedMs != null) {
          _addLog('Total Test Time: ${(result.timeElapsedMs! / 1000).toStringAsFixed(1)}s',
              color: Colors.lightBlue, category: LogCategory.summary);
        }

        if (_deviceInfo != null) {
          _addLog('Device: ${_deviceInfo!.deviceModel}', color: Colors.yellow, category: LogCategory.summary);
          _addLog('OS: ${_deviceInfo!.osVersion}', color: Colors.yellow, category: LogCategory.summary);
          _addLog('Connection: ${_deviceInfo!.connectionType}', color: Colors.yellow, category: LogCategory.summary);
          if (_deviceInfo!.publicIP.isNotEmpty && _deviceInfo!.publicIP != 'Unable to get IP') {
            _addLog('Public IP: ${_deviceInfo!.publicIP}', color: Colors.yellow, category: LogCategory.summary);
          }
          if (_deviceInfo!.localIP.isNotEmpty && _deviceInfo!.localIP != 'Unable to get local IP') {
            _addLog('Local IP: ${_deviceInfo!.localIP}', color: Colors.yellow, category: LogCategory.summary);
          }
        }

        _addLog('=== END ENHANCED SPEED TEST ===', color: Colors.cyan, category: LogCategory.summary);
      } else {
        _addLog('Enhanced speed test failed: ${result.error}', color: Colors.red, category: LogCategory.error);

        // Fallback to basic test
        _addLog('Attempting basic speed test...', color: Colors.orange, category: LogCategory.network);
        await _runBasicSpeedTest();
      }
    } catch (e) {
      _addLog('Enhanced network speed test error: $e', color: Colors.red, category: LogCategory.error);

      // Fallback to basic test
      _addLog('Attempting basic speed test...', color: Colors.orange, category: LogCategory.network);
      await _runBasicSpeedTest();
    } finally {
      setState(() {
        _isTestingSpeed = false;
        _speedTestProgress = 0.0;
        _speedTestStatus = '';
      });
    }
  }

  /// Fallback basic speed test for when enhanced test fails
  Future<void> _runBasicSpeedTest() async {
    try {
      // Test ping
      _addLog('Testing ping...', color: Colors.white, category: LogCategory.network);
      final pingResult = await _networkSpeedService.testPing();
      if (pingResult.success && pingResult.pingMs != null) {
        _addLog('Ping: ${pingResult.pingMs}ms', color: Colors.lightBlue, category: LogCategory.network);
      } else {
        _addLog('Ping failed: ${pingResult.error}', color: Colors.red, category: LogCategory.error);
      }

      // Test download speed
      _addLog('Testing download speed...', color: Colors.white, category: LogCategory.network);
      final downloadResult = await _networkSpeedService.testDownloadSpeed();
      if (downloadResult.success && downloadResult.downloadSpeedKbps != null) {
        _addLog('Download: ${downloadResult.downloadSpeedKbps!.toStringAsFixed(0)} Kbps',
            color: Colors.lightBlue, category: LogCategory.network);
      } else {
        _addLog('Download test failed: ${downloadResult.error}', color: Colors.red, category: LogCategory.error);
      }

      // Test upload speed
      _addLog('Testing upload speed...', color: Colors.white, category: LogCategory.network);
      final uploadResult = await _networkSpeedService.testUploadSpeed(dataSizeBytes: 262144); // 256KB
      if (uploadResult.success && uploadResult.uploadSpeedKbps != null) {
        _addLog('Upload: ${uploadResult.uploadSpeedKbps!.toStringAsFixed(0)} Kbps',
            color: Colors.lightBlue, category: LogCategory.network);
      } else {
        _addLog('Upload test failed: ${uploadResult.error}', color: Colors.red, category: LogCategory.error);
      }

      // Display basic summary
      _addLog('--- Basic Speed Test Results ---', color: Colors.cyan, category: LogCategory.summary);
      if (pingResult.pingMs != null) {
        _addLog('Ping: ${pingResult.pingMs}ms', color: Colors.yellow, category: LogCategory.summary);
      }
      if (downloadResult.downloadSpeedKbps != null) {
        _addLog('Download: ${downloadResult.downloadSpeedKbps!.toStringAsFixed(0)} Kbps',
            color: Colors.yellow, category: LogCategory.summary);
      }
      if (uploadResult.uploadSpeedKbps != null) {
        _addLog('Upload: ${uploadResult.uploadSpeedKbps!.toStringAsFixed(0)} Kbps',
            color: Colors.yellow, category: LogCategory.summary);
      }
      _addLog('--- End Basic Speed Test ---', color: Colors.cyan, category: LogCategory.summary);
    } catch (e) {
      _addLog('Basic speed test error: $e', color: Colors.red, category: LogCategory.error);
    }
  }

  Future<void> _loadDeviceInfo() async {
    setState(() {
      _isLoadingDeviceInfo = true;
    });

    _addLog('Loading device info...', color: Colors.cyan, category: LogCategory.device);

    try {
      final deviceInfo = await _deviceInfoService.getDeviceNetworkInfo();
      setState(() {
        _deviceInfo = deviceInfo;
      });

      _addLog('--- Device Information ---', color: Colors.cyan, category: LogCategory.device);
      _addLog('Device: ${deviceInfo.deviceModel}', color: Colors.orange, category: LogCategory.device);
      _addLog('OS: ${deviceInfo.osVersion}', color: Colors.orange, category: LogCategory.device);
      _addLog('Connection: ${deviceInfo.connectionType}', color: Colors.orange, category: LogCategory.device);
      _addLog('Public IP: ${deviceInfo.publicIP}', color: Colors.orange, category: LogCategory.device);
      _addLog('Local IP: ${deviceInfo.localIP}', color: Colors.orange, category: LogCategory.device);
      _addLog('--- End Device Info ---', color: Colors.cyan, category: LogCategory.device);
    } catch (e) {
      _addLog('Device info error: $e', color: Colors.red, category: LogCategory.error);
    } finally {
      setState(() {
        _isLoadingDeviceInfo = false;
      });
    }
  }

  Future<void> _refreshDeviceInfo() async {
    if (_isLoadingDeviceInfo) return;
    await _loadDeviceInfo();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 4.0), // Reduced padding
                  child: Row(
                    children: [
                      ElevatedButton(
                        onPressed: _isMonitoring ? null : _startMonitoring,
                        style: DebugButtonStyle.debugButtonStyle,
                        child: const Text('Start FPS'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _isMonitoring ? _stopMonitoring : null,
                        style: DebugButtonStyle.debugButtonStyle,
                        child: const Text('Stop FPS'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _isTestingSpeed ? null : _testNetworkSpeed,
                        style: DebugButtonStyle.debugButtonStyle,
                        child: const Text('Speed Test'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _isLoadingDeviceInfo ? null : _refreshDeviceInfo,
                        style: DebugButtonStyle.debugButtonStyle,
                        child: const Text('Refresh Info'),
                      ),
                    ],
                  ),
                ),
              ),
              if (_isMonitoring)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    'FPS: ${_fps.toStringAsFixed(1)}',
                    style: TextStyle(
                      fontSize: 14.r,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              if (_isTestingSpeed)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Column(
                    children: [
                      Text(
                        _speedTestStatus.isNotEmpty ? _speedTestStatus : 'Testing network speed...',
                        style: TextStyle(
                          fontSize: 14.r,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                      SizedBox(height: 8.r),
                      LinearProgressIndicator(
                        value: _speedTestProgress,
                        backgroundColor: Colors.grey[300],
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
                      ),
                      SizedBox(height: 4.r),
                      Text(
                        '${(_speedTestProgress * 100).toStringAsFixed(0)}%',
                        style: TextStyle(
                          fontSize: 12.r,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              if (_isLoadingDeviceInfo)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    'Loading device info...',
                    style: TextStyle(
                      fontSize: 14.r,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
            ],
          ),
          Expanded(
            child: Container(
              color: Colors.black,
              child: ListView.builder(
                padding: const EdgeInsets.only(
                    left: 8.0, right: 8.0, top: 8.0, bottom: 16.0), // Add bottom spacing for consistency
                itemCount: _logs.length,
                reverse: true,
                itemBuilder: (context, index) {
                  final reversedIndex = _logs.length - 1 - index;
                  final logEntry = _logs[reversedIndex];
                  return Text(
                    logEntry.formattedMessage,
                    style: TextStyle(
                      color: logEntry.color,
                      fontFamily: 'monospace',
                      fontSize: 14.0, // Consistent font size with cache tab
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
