import 'package:flutter/material.dart';

/// A simple route tracker to keep track of current route
class RouteTracker {
  static final RouteTracker _instance = RouteTracker._internal();
  factory RouteTracker() => _instance;
  RouteTracker._internal();

  String _currentRoute = '';
  
  String get currentRoute => _currentRoute;
  
  void updateRoute(String route) {
    _currentRoute = route;
    debugPrint('RouteTracker - Route updated to: $route');
  }
  
  bool get isOnDebugPage => _currentRoute == '/debug';
}

/// A custom route observer to track route changes
class DebugRouteObserver extends RouteObserver<PageRoute<dynamic>> {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (route.settings.name != null) {
      RouteTracker().updateRoute(route.settings.name!);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute?.settings.name != null) {
      RouteTracker().updateRoute(previousRoute!.settings.name!);
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute?.settings.name != null) {
      RouteTracker().updateRoute(newRoute!.settings.name!);
    }
  }
}
