import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Provides consistent button styling for debug pages
class DebugButtonStyle {
  DebugButtonStyle._();

  /// Returns a button style for debug pages with gray color and smaller size
  static ButtonStyle get debugButtonStyle {
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.grey[600],
      foregroundColor: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 3.r, horizontal: 6.r),
      textStyle: TextStyle(
        fontSize: 11.r,
        fontWeight: FontWeight.w400,
      ),
      minimumSize: Size(44.r, 26.r),
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
    );
  }
}
