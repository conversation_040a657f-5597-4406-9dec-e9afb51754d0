import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

/// A widget that displays cache information and provides controls to manage the cache
class CacheInfoWidget extends StatefulWidget {
  const CacheInfoWidget({Key? key}) : super(key: key);

  @override
  State<CacheInfoWidget> createState() => _CacheInfoWidgetState();
}

class _CacheInfoWidgetState extends State<CacheInfoWidget> {
  final ApiService _apiService = Modular.get<ApiService>();
  Map<String, dynamic> _cacheStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCacheStats();
  }

  Future<void> _loadCacheStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final stats = await _apiService.getCacheStats();
      setState(() {
        _cacheStats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _clearCache() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _apiService.clearAllCache();
      await _loadCacheStats();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _resetMetrics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _apiService.resetCacheMetrics();
      await _loadCacheStats();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Cache Information',
              style: TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16.0),
            _buildStatRow('Cache Size', _cacheStats['formattedSize'] ?? 'Unknown'),
            _buildStatRow('Entries', '${_cacheStats['entryCount'] ?? 0}'),
            _buildStatRow('Hit Rate', '${(_cacheStats['hitRate'] * 100).toStringAsFixed(1)}%'),
            _buildStatRow('Hits', '${_cacheStats['hits'] ?? 0}'),
            _buildStatRow('Misses', '${_cacheStats['misses'] ?? 0}'),
            _buildStatRow('Writes', '${_cacheStats['writes'] ?? 0}'),
            _buildStatRow('Fallbacks', '${_cacheStats['fallbacks'] ?? 0}'),
            const SizedBox(height: 16.0),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              alignment: WrapAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _clearCache,
                  child: const Text('Clear Cache'),
                ),
                ElevatedButton(
                  onPressed: _resetMetrics,
                  child: const Text('Reset Metrics'),
                ),
                ElevatedButton(
                  onPressed: _loadCacheStats,
                  child: const Text('Refresh'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(value),
        ],
      ),
    );
  }
}
