import 'package:flutter/material.dart';

class SocialMediaIcon extends StatelessWidget {
  final String url;

  const SocialMediaIcon({required this.url, super.key});

  @override
  Widget build(BuildContext context) {
    String imagePath = '';

    if (url.contains('youtube.com')) {
      imagePath = 'assets/images/socials/youtube.png';
    } else if (url.contains('x.com')) {
      imagePath = 'assets/images/socials/x.png';
    } else if (url.contains('facebook.com')) {
      imagePath = 'assets/images/socials/facebook.png';
    } else if (url.contains('tiktok.com')) {
      imagePath = 'assets/images/socials/tiktok.png';
    } else if (url.contains('instagram.com')) {
      imagePath = 'assets/images/socials/instagram.png';
    } else {
      imagePath = 'assets/images/socials/default.png';
    }

    return Image.asset(
      imagePath,
      fit: BoxFit.contain,
    );
  }
}
