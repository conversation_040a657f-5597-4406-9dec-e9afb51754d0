import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommonTab extends StatelessWidget {
  final String title;
  final int? count;
  const CommonTab(this.title, {this.count, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: 14.r), 
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(title, style: Theme.of(context).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w500)),
          if (count != null) SizedBox(width: 4.r),
          if (count != null) _buildCountBadge(),
        ],
      ),
    );
  }

  Widget _buildCountBadge() {
    final countText = '$count';
    const double height = 16.0;
    return Container(
      height: height.r,
      constraints: BoxConstraints(
        minWidth: height.r,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(height.r / 2),
        color: const Color(0xFFF2F2F2),
      ),
      padding: EdgeInsets.symmetric(horizontal: 4.r),
      alignment: Alignment.center,
      child: Text(
        countText,
        style: TextStyle(
          fontSize: 12.r,
          fontWeight: FontWeight.w500,
          fontFamily: 'Metropolis',
          height: 1.0, 
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
