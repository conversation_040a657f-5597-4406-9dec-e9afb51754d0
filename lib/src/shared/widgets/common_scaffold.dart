import 'package:flutter/material.dart';

class CommonScaffold extends StatefulWidget {
  final PreferredSizeWidget? appBar;
  final Widget body;
  final bool resizeToAvoidBottomInset;
  const CommonScaffold(
      {this.appBar,
      required this.body,
      this.resizeToAvoidBottomInset = false,
      super.key});

  @override
  State<CommonScaffold> createState() => _CommonScaffoldState();
}

class _CommonScaffoldState extends State<CommonScaffold> {
  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double statusBarHeight = MediaQuery.of(context).padding.top;
    double heightWithoutStatusBar = screenHeight - statusBarHeight;

    return Scaffold(
      // resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
      appBar: widget.appBar,
      body: SingleChildScrollView(
        reverse: true,
        physics: const BouncingScrollPhysics(),
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: heightWithoutStatusBar,
          child: widget.body,
        ),
      ),
    );
  }
}
