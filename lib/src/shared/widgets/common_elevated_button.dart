import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/base/cubit/common/common_state.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';

class CommonElevatedButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;

  const CommonElevatedButton(
    this.label,
    this.onPressed, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.r,
      child: ElevatedButton(
        onPressed: onPressed != null ? () => onPressed!() : null,
        style: ElevatedButton.styleFrom(
          disabledBackgroundColor: ColorConstants.disabledButtonColor,
          disabledForegroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 8.r, horizontal: 12.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: Theme.of(context).elevatedButtonTheme.style!.textStyle!.resolve({}),
            ),
            BlocBuilder<CommonCubit, CommonState>(
              builder: (_, state) {
                if (!state.isLoadingButton) return const SizedBox.shrink();

                return Padding(
                  padding: EdgeInsets.only(left: 8.r),
                  child: SizedBox(
                    width: 16.r,
                    height: 16.r,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
