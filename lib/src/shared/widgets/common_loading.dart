import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommonLoading extends StatelessWidget {
  final double size;
  final double padding;
  final Color? color;

  const CommonLoading({
    super.key,
    this.size = 24,
    this.padding = 24,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(padding.r),
        child: SizedBox(
          width: size.r,
          height: size.r,
          child: const CircularProgressIndicator(
            strokeWidth: 2,
            color: Color(0xFFFFB522),
          ),
        ),
      ),
    );
  }
}
