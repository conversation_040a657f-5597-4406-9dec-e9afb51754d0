import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/app/cubit/app_cubit.dart';
import 'package:koc_app/src/shared/app_theme.dart';

class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final Widget? logo;
  final bool showNotificationAction;
  final bool showFindCampaignAction;
  final bool showThemeAction;
  final bool showBottomDivider;
  final Widget? customAction;
  final Function()? onSearchTap;

  const CommonAppBar(
      {this.title,
      this.logo,
      this.showNotificationAction = false,
      this.showFindCampaignAction = false,
      this.showThemeAction = false,
      this.showBottomDivider = false,
      this.customAction,
      this.onSearchTap,
      super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: title,
      leading: logo,
      centerTitle: false,
      automaticallyImplyLeading: logo == null,
      elevation: 0,
      actions: [
        if (showNotificationAction)
          IconButton(
            icon: const Icon(
              Icons.notifications_none,
            ),
            onPressed: () {
              Modular.to.pushNamed('/notification');
            },
          ),
        if (showFindCampaignAction)
          IconButton(
            icon: const Icon(
              Icons.search,
            ),
            onPressed: () {
              if (onSearchTap != null) {
                onSearchTap!();
              } else {
                Modular.to.pushNamed('/campaign/search');
              }
            },
          ),
        if (showThemeAction)
          IconButton(
            icon: const Icon(
              Icons.dark_mode_outlined,
            ),
            onPressed: () {
              ThemeData? themeData = ReadContext(context).read<AppCubit>().state.themeData;
              if (themeData != null) {
                if (themeData == AppTheme.lightTheme) {
                  ReadContext(context).read<AppCubit>().changeTheme(AppTheme.darkTheme);
                } else {
                  ReadContext(context).read<AppCubit>().changeTheme(AppTheme.lightTheme);
                }
              }
            },
          ),
        if (customAction != null) customAction!
      ],
      bottom: showBottomDivider
          ? PreferredSize(
              preferredSize: Size.fromHeight(1.r),
              child: Divider(
                height: 1.r,
                color: const Color(0xFFE7E7E7),
              ),
            )
          : null,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
