import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A reusable wrapper widget that adds pull-to-refresh functionality
/// to any scrollable content. Integrates with the app's existing
/// caching and state management architecture.
class PullToRefreshWrapper extends StatefulWidget {
  /// The child widget (usually a scrollable widget)
  final Widget child;

  /// Callback function to execute when refresh is triggered
  final Future<void> Function() onRefresh;

  /// Optional custom refresh indicator displacement
  final double? displacement;

  /// Optional custom refresh indicator stroke width
  final double? strokeWidth;

  /// Optional custom refresh indicator color
  final Color? color;

  /// Optional custom refresh indicator background color
  final Color? backgroundColor;

  /// The duration to wait before allowing another refresh
  final Duration throttleDuration;

  const PullToRefreshWrapper({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement,
    this.strokeWidth,
    this.color,
    this.backgroundColor,
    this.throttleDuration = const Duration(seconds: 10),
  });

  @override
  State<PullToRefreshWrapper> createState() => _PullToRefreshWrapperState();
}

class _PullToRefreshWrapperState extends State<PullToRefreshWrapper> {
  DateTime? _lastRefreshTime;
  bool _isRefreshing = false;

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      displacement: widget.displacement ?? 40.0.r,
      strokeWidth: widget.strokeWidth ?? 2.0.r,
      color: widget.color ?? const Color(0xFFFFB522),
      backgroundColor: widget.backgroundColor ?? Colors.white,
      child: widget.child,
    );
  }

  Future<void> _handleRefresh() async {
    if (_isRefreshing) {
      return Future.delayed(const Duration(milliseconds: 300));
    }
    
    final now = DateTime.now();
    if (_lastRefreshTime != null) {
      final timeSinceLastRefresh = now.difference(_lastRefreshTime!);
      if (timeSinceLastRefresh < widget.throttleDuration) {
        return Future.delayed(const Duration(milliseconds: 800));
      }
    }

    setState(() {
      _isRefreshing = true;
    });

    try {
      await widget.onRefresh();
      
      _lastRefreshTime = DateTime.now();
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }
}

/// Extension to add pull-to-refresh functionality to existing scrollable widgets
extension PullToRefreshExtension on Widget {
  /// Wraps the widget with pull-to-refresh functionality
  Widget withPullToRefresh({
    required Future<void> Function() onRefresh,
    double? displacement,
    double? strokeWidth,
    Color? color,
    Color? backgroundColor,
    Duration throttleDuration = const Duration(seconds: 5),
  }) {
    return PullToRefreshWrapper(
      onRefresh: onRefresh,
      displacement: displacement,
      strokeWidth: strokeWidth,
      color: color,
      backgroundColor: backgroundColor,
      throttleDuration: throttleDuration,
      child: this,
    );
  }
}

/// A specialized pull-to-refresh wrapper for list-based content
/// that handles empty states and loading states appropriately
class ListPullToRefreshWrapper extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final bool isLoading;
  final bool isEmpty;
  final Widget? emptyStateWidget;
  final Widget? loadingWidget;

  const ListPullToRefreshWrapper({
    super.key,
    required this.child,
    required this.onRefresh,
    this.isLoading = false,
    this.isEmpty = false,
    this.emptyStateWidget,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && loadingWidget != null) {
      return loadingWidget!;
    }

    if (isEmpty && emptyStateWidget != null && !isLoading) {
      return PullToRefreshWrapper(
        onRefresh: onRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.7,
            child: emptyStateWidget!,
          ),
        ),
      );
    }

    return PullToRefreshWrapper(
      onRefresh: onRefresh,
      child: child,
    );
  }
}

/// Enhanced pull-to-refresh wrapper that can suppress global loading overlays
/// during refresh operations for a cleaner user experience
class CleanPullToRefreshWrapper extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double? displacement;
  final double? strokeWidth;
  final Color? color;
  final Color? backgroundColor;
  final bool enableHapticFeedback;
  final bool suppressGlobalLoading;

  const CleanPullToRefreshWrapper({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement,
    this.strokeWidth,
    this.color,
    this.backgroundColor,
    this.enableHapticFeedback = true,
    this.suppressGlobalLoading = true,
  });

  @override
  State<CleanPullToRefreshWrapper> createState() => _CleanPullToRefreshWrapperState();
}

class _CleanPullToRefreshWrapperState extends State<CleanPullToRefreshWrapper> {
  bool _isRefreshing = false;

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        if (_isRefreshing) return;

        setState(() {
          _isRefreshing = true;
        });

        try {
          if (widget.enableHapticFeedback) {
            try {
              await Future.delayed(const Duration(milliseconds: 50));
            } catch (e) {
              // Ignore haptic feedback errors on unsupported devices
            }
          }

          await widget.onRefresh();
        } finally {
          if (mounted) {
            setState(() {
              _isRefreshing = false;
            });
          }
        }
      },
      displacement: widget.displacement ?? 40.0.r,
      strokeWidth: widget.strokeWidth ?? 2.0.r,
      color: widget.color ?? const Color(0xFFFFB522),
      backgroundColor: widget.backgroundColor ?? Colors.white,
      child: widget.child,
    );
  }
}
