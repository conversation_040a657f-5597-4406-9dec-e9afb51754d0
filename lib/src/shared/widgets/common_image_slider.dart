import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/shared/data/image_data.dart';

class CommonImageSlider extends StatefulWidget {
  final List<ImageData> imageUrls;
  final bool showSize;
  final bool showPage;
  final Function(int)? onPageChanged;
  const CommonImageSlider(this.imageUrls,
      {this.showSize = false, this.showPage = false, this.onPageChanged, super.key});

  @override
  State<CommonImageSlider> createState() => _CommonImageSliderState();
}

class _CommonImageSliderState extends State<CommonImageSlider> {
  int _sliderIndex = 0;
  final CarouselSliderController _controller = CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CarouselSlider(
          carouselController: _controller,
          items: _buildImageSliders(),
          options: CarouselOptions(
            enableInfiniteScroll: widget.imageUrls.length > 1,
            viewportFraction: 1,
            enlargeCenterPage: true,
            onPageChanged: (index, reason) {
              setState(() {
                _sliderIndex = index;
              });
              if (widget.onPageChanged != null) {
                widget.onPageChanged!(index);
              }
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: widget.imageUrls.asMap().entries.map((entry) {
            return GestureDetector(
              onTap: () => _controller.animateToPage(entry.key),
              child: Container(
                width: 6.r,
                height: 6.r,
                margin: EdgeInsets.only(top: 8.0.r, bottom: 8.0.r, left: 2.0.r, right: 2.0.r),
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _sliderIndex == entry.key ? const Color(0xFFFFB522) : const Color(0xFFE7E7E7)),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  List<Widget> _buildImageSliders() {
    return widget.imageUrls
        .map((item) => SizedBox(
              width: double.infinity,
              child: Stack(
                children: <Widget>[
                  Center(
                      child: CachedImageWithPlaceholder(
                    imageUrl: item.imageUrl,
                    fit: BoxFit.fitHeight,
                  )),
                  if (widget.showPage)
                    Positioned(
                        top: 16.r,
                        right: 16.r,
                        child: Container(
                          padding: EdgeInsets.only(top: 2.r, left: 8.r, right: 8.r),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.0.r),
                            color: const Color(0xFF202124).withValues(alpha: 0.6),
                            border: Border.all(
                              color: const Color(0xFFE7E7E7),
                              width: 1.0.r,
                            ),
                          ),
                          child: Text(
                            ' ${widget.imageUrls.indexOf(item) + 1}/${widget.imageUrls.length} ',
                            style: Theme.of(context)
                                .textTheme
                                .labelMedium!
                                .copyWith(fontWeight: FontWeight.w500, color: Colors.white),
                          ),
                        )),
                  if (widget.showSize)
                    Positioned(
                        top: 16.r,
                        left: 16.r,
                        child: Container(
                          padding: EdgeInsets.only(top: 2.r, left: 8.r, right: 8.r),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.0.r),
                            color: const Color(0xFF202124).withValues(alpha: 0.6),
                            border: Border.all(
                              color: const Color(0xFFE7E7E7),
                              width: 1.0.r,
                            ),
                          ),
                          child: Text('${item.width}/${item.height}',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelMedium!
                                  .copyWith(fontWeight: FontWeight.w500, color: Colors.white)),
                        ))
                ],
              ),
            ))
        .toList();
  }
}
