import 'dart:io';
import 'dart:math';

import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_state.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/shared/cubit/pagination_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/pagination_state.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/social_media_icon.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

mixin CommonMixin {
  void showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.transparent,
          content: Center(
            child: SizedBox(
              width: 55.r,
              height: 55.r,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                color: Color(0xFFFFB522),
              ),
            ),
          ),
        );
      },
    );
  }

  void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }

  Future<T?> showConfirmationDialog<T>(
    BuildContext context,
    Widget title,
    Widget content,
    String btnName,
    VoidCallback onTap,
    bool isValid, {
    String cancelButtonName = 'Cancel',
    EdgeInsetsGeometry? titlePadding,
    EdgeInsetsGeometry? contentPadding,
  }) async {
    T? result = await showDialog<T>(
        useSafeArea: true,
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: title,
            content: content,
            titlePadding: titlePadding,
            contentPadding: contentPadding,
            actions: [
              ConfirmationButtons(
                isValid: isValid,
                btnName: btnName,
                onTap: onTap,
                showCancelButton: true,
                alignment: MainAxisAlignment.end,
                cancelButtonName: cancelButtonName,
              ),
            ],
          );
        });
    return result;
  }

  void showDescription(BuildContext context, String title, String description) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Text(
              title,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
            ),
            content: Text(description, style: Theme.of(context).textTheme.labelLarge),
          );
        });
  }

  Widget buildPagination(BuildContext context, PaginationCubit cubit) {
    return BlocBuilder<PaginationCubit, PaginationState>(
      bloc: cubit,
      builder: (_, state) {
        if (state.total == 0 || state.total <= state.pageSize) {
          return const SizedBox.shrink();
        }
        return Container(
          color: Colors.white,
          height: 56.r,
          padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
          child: Row(
            spacing: 10.r,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () {
                  cubit.setCurrentPage(1);
                },
                child: Icon(
                  Icons.first_page,
                  size: 30.r,
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (state.currentPage == 1) return;
                  cubit.setCurrentPage(state.currentPage - 1);
                },
                child: Icon(
                  Icons.arrow_back_ios,
                  size: 20.r,
                ),
              ),
              Expanded(
                child: Text(
                  '${1 + ((state.currentPage - 1) * state.pageSize)} - ${state.currentPage * state.pageSize > state.total ? state.total : state.currentPage * state.pageSize} of ${state.total}',
                  style: context.textLabelLarge(),
                  textAlign: TextAlign.center,
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (state.currentPage * state.pageSize >= state.total) return;
                  cubit.setCurrentPage(state.currentPage + 1);
                },
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 20.r,
                ),
              ),
              GestureDetector(
                onTap: () {
                  cubit.setCurrentPage((state.total / state.pageSize).ceil());
                },
                child: Icon(
                  Icons.last_page,
                  size: 30.r,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildCustomSelector(Widget title, VoidCallback onTap, {Key? key}) {
    return GestureDetector(
      key: key,
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(9999.r),
            border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            title,
            Icon(
              Icons.keyboard_arrow_down,
              size: 16.r,
            )
          ],
        ),
      ),
    );
  }

  Future<void> saveCsvToDownloadFolder(BuildContext context, List<List<dynamic>> rows, String fileName) async {
    String csv = const ListToCsvConverter().convert(rows);
    final directory = await getApplicationDocumentsDirectory();
    final path = "${directory.path}/$fileName";
    final file = File(path);
    await file.writeAsString(csv);

    if (Platform.isAndroid) {
      final downloadPath = "/storage/emulated/0/Download/$fileName.csv";
      await file.copy(downloadPath);

      if (context.mounted) {
        context.showSnackBar('The report is downloaded successfully!', durationSecond: 3);
      }
    } else if (Platform.isIOS) {
      await Share.shareXFiles([XFile(file.path)], subject: '$fileName.csv');
    }
  }

  Future<void> saveImageToDownloadFolder(BuildContext context, String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        if (context.mounted) {
          context.showSnackBar('File not found', durationSecond: 3);
        }
        return;
      }

      final fileName = filePath.split('/').last;

      if (Platform.isAndroid) {
        final downloadPath = "/storage/emulated/0/Download/$fileName";
        await file.copy(downloadPath);

        if (context.mounted) {
          context.showSnackBar('Image has been downloaded to Downloads folder', durationSecond: 3);
        }
      } else if (Platform.isIOS || Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
        if (context.mounted) {
          await Share.shareXFiles([XFile(filePath)], subject: fileName);
        }
      } else {
        if (context.mounted) {
          context.showSnackBar('Image saved to: $filePath', durationSecond: 3);
        }
      }
    } catch (e) {
      if (context.mounted) {
        context.showSnackBar('Failed to save image: ${e.toString()}', durationSecond: 3);
      }
      debugPrint('Error saving image: $e');
    }
  }

  int getMaxY(List<num> value1, List<num> value2) {
    final combinedList = (value1.map((e) => e.toInt()).toList() + value2.map((e) => e.toInt()).toList());

    if (combinedList.isEmpty) {
      return 0;
    }

    return combinedList.reduce((a, b) => a > b ? a : b);
  }

  int getRoundUpValue(int value) {
    if (value <= 0) return 10;
    int magnitude = pow(10, value.toString().length - 1).toInt();
    int n = (value / magnitude).ceil();

    int rounded;
    if (n <= 1) {
      rounded = 1 * magnitude;
    } else if (n <= 2) {
      rounded = 2 * magnitude;
    } else if (n <= 3) {
      rounded = 3 * magnitude;
    } else if (n <= 5) {
      rounded = 5 * magnitude;
    } else {
      rounded = 10 * magnitude;
    }
    return rounded;
  }

  Future<int?> showSiteSelectionBottomSheet(BuildContext context,
      {VoidCallback? onTap, bool canSelectSameId = false, int selectedSiteId = 0, bool doUpdatingCubit = true}) async {
    return showModalBottomSheet<int?>(
      isScrollControlled: true,
      context: context,
      useSafeArea: true,
      backgroundColor: Colors.white,
      builder: (_) {
        return ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.r),
            topRight: Radius.circular(12.r),
          ),
          child: Container(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  spacing: 12.r,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.close,
                        size: 20.r,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                    Text(
                      'Property',
                      style: context.textBodyMedium(fontSize: 18.r, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                SizedBox(height: 20.r),
                Expanded(
                  child: SingleChildScrollView(
                    child: BlocBuilder<SiteCubit, SiteState>(
                        bloc: Modular.get<SiteCubit>(),
                        builder: (_, state) {
                          return Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12.r),
                                border: Border.all(color: ColorConstants.borderColor, width: 1.r),
                              ),
                              child: state.sites.isEmpty
                                  ? null
                                  : Column(
                                      children: List.generate(state.sites.length * 2 - 1, (index) {
                                        if (index.isEven) {
                                          final site = state.sites[index ~/ 2];
                                          return GestureDetector(
                                            onTap: () async {
                                              if (doUpdatingCubit) {
                                                Modular.get<SiteCubit>().setCurrentSiteId(site.id);
                                                onTap?.call();
                                                Navigator.of(context).pop();
                                                return;
                                              } else if (canSelectSameId || selectedSiteId != site.id) {
                                                Navigator.of(context).pop(site.id);
                                              } else if (context.mounted) {
                                                Navigator.of(context).pop();
                                              }
                                            },
                                            child: Container(
                                              color: site.id == selectedSiteId
                                                  ? ColorConstants.selectedColor
                                                  : Colors.white,
                                              padding: EdgeInsets.symmetric(vertical: 12.r, horizontal: 8.r),
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  Row(
                                                    spacing: 14.r,
                                                    children: [
                                                      SizedBox(
                                                          height: 24.r,
                                                          width: 24.r,
                                                          child: SocialMediaIcon(url: site.url)),
                                                      Text(
                                                        site.name,
                                                        style: context.textLabelLarge(fontWeight: FontWeight.w500),
                                                      ),
                                                    ],
                                                  ),
                                                  if (site.id == state.currentSiteId)
                                                    Container(
                                                      decoration: BoxDecoration(
                                                        color: const Color(0xFF1AAA55),
                                                        borderRadius: BorderRadius.circular(9999.r),
                                                      ),
                                                      padding: EdgeInsets.symmetric(horizontal: 4.r),
                                                      child: Text('Default',
                                                          style: context.textLabelMedium(
                                                              color: Colors.white, fontWeight: FontWeight.w500)),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          );
                                        } else {
                                          return Divider(
                                            color: ColorConstants.borderColor,
                                            thickness: 1.r,
                                            height: 1.r,
                                          );
                                        }
                                      }),
                                    ));
                        }),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildSiteSelectionTitle(BuildContext context, String title, {VoidCallback? onTap}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(child: Text(title)),
        if (Modular.get<SiteCubit>().state.sites.length > 1)
          GestureDetector(
            onTap: () {
              showSiteSelectionBottomSheet(context, onTap: onTap);
            },
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFFFB522),
                borderRadius: BorderRadius.circular(9999.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
              child: Row(
                spacing: 4.r,
                children: [
                  BlocBuilder<SiteCubit, SiteState>(
                      bloc: Modular.get<SiteCubit>(),
                      builder: (_, state) {
                        if (state.sites.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return Text(
                          state.sites.firstWhere((site) => site.id == state.currentSiteId).name,
                          style: context.textLabelMedium(color: Colors.white, fontWeight: FontWeight.w500),
                          overflow: TextOverflow.ellipsis,
                        );
                      }),
                  Icon(
                    Icons.keyboard_arrow_down_outlined,
                    size: 12.r,
                    color: Colors.white,
                  )
                ],
              ),
            ),
          ),
      ],
    );
  }
}
