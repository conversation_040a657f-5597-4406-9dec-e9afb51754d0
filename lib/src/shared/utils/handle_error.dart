import 'package:dio/dio.dart';
import 'package:koc_app/src/shared/extensions.dart';

import '../constants/dio_constants.dart';

void handleError(dynamic error, Function(String) emitError) {
  if (error is DioException) {
    final failure = _handleError(error);
    emitError(failure.message);
  } else {
    emitError(error.toString());
  }
}

Failure _handleError(DioException error) {
  switch (error.type) {
    case DioExceptionType.connectionTimeout:
      return DataSource.connectTimeout.getFailure();
    case DioExceptionType.sendTimeout:
      return DataSource.sendTimeout.getFailure();
    case DioExceptionType.receiveTimeout:
      return DataSource.receiveTimeout.getFailure();
    case DioExceptionType.badResponse:
      return _handleBadResponse(error);
    case DioExceptionType.cancel:
      return DataSource.cancel.getFailure();
    case DioExceptionType.connectionError:
      return DataSource.connectionError.getFailure();
    default:
      return _handleDefaultError(error);
  }
}

_handleBadResponse(DioException error) {
  try {
    final code = error.response?.statusCode ?? ResponseCode.defaultError;
    String message = '';
    switch (code) {
      case ResponseCode.unauthorised:
        return DataSource.unauthorised.getFailure();
      case ResponseCode.forbidden:
        return DataSource.forbidden.getFailure();
      case ResponseCode.notFound:
        return DataSource.notFound.getFailure();
      default:
        message = _extractErrorMessage(error.response?.data);
        return Failure(code, message);
    }
  } catch (e) {
    return DataSource.defaultError.getFailure();
  }
}

_handleDefaultError(DioException error) {
  if (error.response?.statusCode == ResponseCode.noInternetConnection) {
    return DataSource.noInternetConnection.getFailure();
  } else {
    return DataSource.defaultError.getFailure();
  }
}

String _extractErrorMessage(dynamic data) {
  if (data is String) return data;
  String message = '';
  if (data is Map) {
    if (data.containsKey('errorMessage')) {
      message = data['errorMessage'];
    } else if (data.containsKey('message')) {
      message = data['message'];
    }
  }
  return message;
}
