class Validators {
  static final RegExp _emailRegExp = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  static final RegExp _urlRegExp = RegExp(
    r'^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-zA-Z0-9]+([\-\.]{1}[a-zA-Z0-9]+)*\.[a-zA-Z]{2,5}(:[0-9]{1,5})?(\/.*)?$',
  );

  static final RegExp _passwordRegex = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,}$');

  static final RegExp _phoneNumberRegExp = RegExp(
    r'^(?:\+?\d{1,3}[- ]?)?\(?\d{2,4}?\)?[- ]?\d{3,4}[- ]?\d{4}$',
  );

  static final RegExp _passwordRegExp = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,}$');

  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!_emailRegExp.hasMatch(value)) {
      return 'Invalid email format';
    }
    return null;
  }

  static bool isValidEmail(String email) {
    return _emailRegExp.hasMatch(email);
  }

  static bool isValidOtp(String otp) {
    return otp.length == 6 && RegExp(r'^[0-9]+$').hasMatch(otp);
  }

  static bool isValidPassword(String password) {
    return _passwordRegExp.hasMatch(password);
  }

  static String? validateUrl(String? value) {
    if (value == null || value.isEmpty) {
      return 'URL is required';
    }
    if (!_urlRegExp.hasMatch(value)) {
      return 'Invalid URL format';
    }
    return null;
  }

  static bool isValidUrl(String url, {List<String> allowedPrefixes = const []}) {
    if (!_urlRegExp.hasMatch(url)) {
      return false;
    }

    if (allowedPrefixes.isNotEmpty) {
      return allowedPrefixes.any((prefix) => url.startsWith(prefix));
    }
    return true;
  }

  static bool isValidSocialUrl(String url) {
    const Set<String> allowedPrefixes = {
      'https://www.facebook.com',
      'https://facebook.com',
      'https://www.fb.com',
      'https://fb.com',
      'https://www.instagram.com',
      'https://instagram.com',
      'https://www.x.com',
      'https://x.com',
      'https://www.youtube.com',
      'https://youtube.com',
      'https://www.youtu.be',
      'https://youtu.be',
      'https://www.tiktok.com',
      'https://tiktok.com',
    };

    final String matchedPrefix = allowedPrefixes.firstWhere(
      (prefix) => url.startsWith(prefix),
      orElse: () => '',
    );

    if (matchedPrefix.isEmpty) return false;

    final String path = url.substring(matchedPrefix.length);
    return RegExp(r'^\/[\w@_\-+]+').hasMatch(path);
  }

  static bool isValidPhoneNumber(String phoneNumber) {
    return _phoneNumberRegExp.hasMatch(phoneNumber);
  }

  static bool isValidCustomLink(String url, List<String>? acceptedUrls) {
    if (!isValidUrl(url)) {
      return false;
    }

    if (acceptedUrls == null || acceptedUrls.isEmpty) {
      return true;
    }

    return matchesAcceptedUrls(url, acceptedUrls);
  }

  static bool matchesAcceptedUrls(String url, List<String> acceptedUrls) {
    Uri? uri;
    try {
      uri = Uri.parse(url);
    } catch (_) {
      return false;
    }

    for (String acceptedUrl in acceptedUrls) {
      if (_isExactMatch(url, acceptedUrl) || _isBaseUrlMatch(url, acceptedUrl) || _isWildcardMatch(url, acceptedUrl)) {
        return true;
      }
    }

    return false;
  }

  static bool _isExactMatch(String url, String acceptedUrl) {
    return url == acceptedUrl;
  }

  static bool _isBaseUrlMatch(String url, String acceptedUrl) {
    String normalizedAcceptedUrl = acceptedUrl;
    if (normalizedAcceptedUrl.endsWith('/')) {
      String baseUrl = normalizedAcceptedUrl.substring(0, normalizedAcceptedUrl.length - 1);
      if (url.startsWith(baseUrl)) {
        return true;
      }
    }
    
    if (url.startsWith(acceptedUrl) && url.length > acceptedUrl.length) {
      if (url[acceptedUrl.length] == '/') {
        return true;
      }
    }
    
    return false;
  }

  static bool _isWildcardMatch(String url, String acceptedUrl) {
    if (acceptedUrl.contains('*')) {
      String pattern = acceptedUrl.replaceAll('.', '\\.').replaceAll('*', '.*');

      if (!pattern.startsWith('http')) {
        pattern = '(https?://)?$pattern';
      }

      pattern = '$pattern(\\/.*)?\$';

      RegExp regExp = RegExp('^$pattern');
      return regExp.hasMatch(url);
    }
    
    return false;
  }

  static List<String> extractUrls(String input) {
    List<String> urls = [];
    List<String> lines = input.split('\n');

    for (String line in lines) {
      if (line.contains(',')) {
        urls.addAll(line.split(',').map((url) => url.trim()).where((url) => url.isNotEmpty));
      } else {
        String trimmedLine = line.trim();
        if (trimmedLine.isNotEmpty) {
          urls.add(trimmedLine);
        }
      }
    }

    return urls;
  }

  static bool isValidMultipleCustomLinks(String input, List<String>? acceptedUrls) {
    if (input.trim().isEmpty) {
      return false;
    }

    List<String> urls = extractUrls(input);

    if (urls.isEmpty) {
      return false;
    }

    for (String url in urls) {
      if (!isValidCustomLink(url, acceptedUrls)) {
        return false;
      }
    }

    return true;
  }

  static String? getUrlErrorMessage(String text, List<String>? acceptedUrls) {
    if (text.isEmpty) {
      return null;
    }

    List<String> urls = extractUrls(text);
    for (String url in urls) {
      if (!isValidUrl(url)) {
        return 'URL not valid: $url';
      }

      if (acceptedUrls != null && acceptedUrls.isNotEmpty) {
        if (!matchesAcceptedUrls(url, acceptedUrls)) {
          return 'URL not matching any accepted format: $url';
        }
      }
    }

    return null;
  }
}
