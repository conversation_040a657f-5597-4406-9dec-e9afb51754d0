import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvConfig {
  EnvConfig._();
  static bool get isAndroid => Platform.isAndroid;
  static bool get isIOS => Platform.isIOS;
  static bool get isWeb => kIsWeb;

  static String get googleClientId {
    if (kIsWeb) {
      return dotenv.env['GOOGLE_CLIENT_ID_WEB'] ?? '';
    } else if (Platform.isIOS) {
      return dotenv.env['GOOGLE_CLIENT_ID_IOS'] ?? '';
    } else if (Platform.isAndroid) {
      return dotenv.env['GOOGLE_CLIENT_ID_ANDROID'] ?? '';
    }
    return '';
  }

  static String get googleApiKey {
    return dotenv.env['GOOGLE_API_KEY'] ?? '';
  }

  static String get googleProjectId {
    return dotenv.env['GOOGLE_PROJECT_ID'] ?? '';
  }

  static String get googleProjectNumber {
    return dotenv.env['GOOGLE_PROJECT_NUMBER'] ?? '';
  }

  static String get facebookAppId {
    return dotenv.env['FACEBOOK_APP_ID'] ?? '';
  }

  static String get facebookAppSecret {
    if (kDebugMode) {
      print('WARNING: Accessing Facebook App Secret. This should only be used in secure contexts.');
    }
    return dotenv.env['FACEBOOK_APP_SECRET'] ?? '';
  }

  static String get facebookClientToken {
    return dotenv.env['FACEBOOK_CLIENT_TOKEN'] ?? '';
  }

  static String get facebookRedirectUri {
    return dotenv.env['FACEBOOK_REDIRECT_URI'] ?? '';
  }

  static String get facebookLoginScheme {
    return 'fb$facebookAppId';
  }
}
