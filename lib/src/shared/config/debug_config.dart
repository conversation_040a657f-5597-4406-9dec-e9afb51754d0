import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Configuration class for debug-related settings
class DebugConfig {
  DebugConfig._();
  
  /// Whether to show debug tools in the app
  static bool get showDebugTools {
    // Always return false in release mode
    if (kReleaseMode) {
      return false;
    }
    
    // Check the environment variable
    final showDebugTools = dotenv.env['SHOW_DEBUG_TOOLS'];
    return showDebugTools?.toLowerCase() == 'true';
  }
}
