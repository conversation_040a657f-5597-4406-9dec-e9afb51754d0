import 'package:flutter/material.dart';

class CommonDataTableSource extends DataTableSource {
  final List<DataRow> _rows;
  final int _totalRows;
  CommonDataTableSource(this._rows, this._totalRows);

  @override
  DataRow? getRow(int index) {
    int newIndex = index % 10;
    if (newIndex >= _rows.length) {
      return null;
    }
    return _rows[newIndex];
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => _totalRows;

  @override
  int get selectedRowCount => 0;
}
