/// A class for tracking cache performance metrics. Used to monitor cache usage and performance.
class CacheMetrics {
  /// Number of cache hits
  int hits;
  
  /// Number of cache misses
  int misses;
  
  /// Number of expired cache entries
  int expired;
  
  /// Number of cache writes
  int writes;
  
  /// Number of errors encountered
  int errors;
  
  /// Number of network errors where cache was used as fallback
  int fallbacks;
  
  /// Timestamp when metrics were last reset
  int lastReset;

  CacheMetrics({
    this.hits = 0,
    this.misses = 0,
    this.expired = 0,
    this.writes = 0,
    this.errors = 0,
    this.fallbacks = 0,
    int? lastReset,
  }) : lastReset = lastReset ?? DateTime.now().millisecondsSinceEpoch;

  /// Reset all metrics
  void reset() {
    hits = 0;
    misses = 0;
    expired = 0;
    writes = 0;
    errors = 0;
    fallbacks = 0;
    lastReset = DateTime.now().millisecondsSinceEpoch;
  }

  /// Increment the hit counter
  void incrementHits() {
    hits++;
  }

  /// Increment the miss counter
  void incrementMisses() {
    misses++;
  }

  /// Increment the expired counter
  void incrementExpired() {
    expired++;
  }

  /// Increment the write counter
  void incrementWrites() {
    writes++;
  }

  /// Increment the error counter
  void incrementErrors() {
    errors++;
  }

  /// Increment the fallback counter
  void incrementFallbacks() {
    fallbacks++;
  }

  /// Calculate the hit rate (hits / total requests)
  double get hitRate {
    final total = hits + misses;
    if (total == 0) return 0.0;
    return hits / total;
  }

  /// Calculate the miss rate (misses / total requests)
  double get missRate {
    final total = hits + misses;
    if (total == 0) return 0.0;
    return misses / total;
  }

  /// Get the total number of requests
  int get totalRequests {
    return hits + misses;
  }

  /// Get the date when metrics were last reset
  DateTime get lastResetDate {
    return DateTime.fromMillisecondsSinceEpoch(lastReset);
  }

  /// Convert metrics to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'hits': hits,
      'misses': misses,
      'expired': expired,
      'writes': writes,
      'errors': errors,
      'fallbacks': fallbacks,
      'lastReset': lastReset,
    };
  }

  /// Create metrics from a JSON map
  factory CacheMetrics.fromJson(Map<String, dynamic> json) {
    return CacheMetrics(
      hits: json['hits'] ?? 0,
      misses: json['misses'] ?? 0,
      expired: json['expired'] ?? 0,
      writes: json['writes'] ?? 0,
      errors: json['errors'] ?? 0,
      fallbacks: json['fallbacks'] ?? 0,
      lastReset: json['lastReset'] ?? DateTime.now().millisecondsSinceEpoch,
    );
  }
}
