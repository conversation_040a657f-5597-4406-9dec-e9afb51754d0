import 'package:flutter/foundation.dart';

/// Cache cleanup strategy when limits are exceeded
enum CacheCleanupStrategy {
  /// Remove oldest entries first (LRU - Least Recently Used)
  lru,

  /// Remove largest entries first
  largestFirst,

  /// Remove entries with shortest TTL first
  shortestTtlFirst,
}

/// Configuration for the unified caching system (API + Images)
class CacheConfig {
  /// Default TTL (Time-To-Live) for cached data in seconds
  /// 1 hour
  final int defaultTtl;

  /// Map of endpoint-specific TTLs
  final Map<String, int> endpointTtls;

  /// Whether to enable caching globally
  final bool enableCaching;

  /// List of endpoints to exclude from caching
  final List<String> excludedEndpoints;

  /// List of endpoints to cache even in case of errors
  final List<String> cacheOnErrorEndpoints;

  /// Maximum size of the API cache in bytes (default: 50MB)
  final int maxApiCacheSize;

  /// Maximum size of the image cache in bytes (default: 100MB)
  final int maxImageCacheSize;

  /// Maximum total cache size in bytes (API + Images, default: 200MB)
  final int maxTotalCacheSize;

  /// Whether to serve stale data when network is unavailable
  final bool serveStaleDataOnError;

  /// Whether to cache POST requests (default: false)
  final bool cachePostRequests;

  /// Whether to enable image caching (default: true)
  final bool enableImageCaching;

  /// Default TTL for cached images in seconds (default: 7 days)
  final int defaultImageTtl;

  /// Cache cleanup strategy when limits are exceeded
  final CacheCleanupStrategy cleanupStrategy;

  const CacheConfig({
    this.defaultTtl = 3600,
    this.endpointTtls = const {},
    this.enableCaching = true,
    this.excludedEndpoints = const [],
    this.cacheOnErrorEndpoints = const [],
    this.maxApiCacheSize = 50 * 1024 * 1024,
    this.maxImageCacheSize = 100 * 1024 * 1024,
    this.maxTotalCacheSize = 200 * 1024 * 1024,
    this.serveStaleDataOnError = true,
    this.cachePostRequests = false,
    this.enableImageCaching = true,
    this.defaultImageTtl = 7 * 24 * 3600,
    this.cleanupStrategy = CacheCleanupStrategy.lru,
  });

  /// Legacy getter for backward compatibility
  int get maxCacheSize => maxApiCacheSize;

  /// Creates a copy of this config with the given fields replaced
  CacheConfig copyWith({
    int? defaultTtl,
    Map<String, int>? endpointTtls,
    bool? enableCaching,
    List<String>? excludedEndpoints,
    List<String>? cacheOnErrorEndpoints,
    int? maxApiCacheSize,
    int? maxImageCacheSize,
    int? maxTotalCacheSize,
    bool? serveStaleDataOnError,
    bool? cachePostRequests,
    bool? enableImageCaching,
    int? defaultImageTtl,
    CacheCleanupStrategy? cleanupStrategy,
    int? maxCacheSize,
  }) {
    return CacheConfig(
      defaultTtl: defaultTtl ?? this.defaultTtl,
      endpointTtls: endpointTtls ?? this.endpointTtls,
      enableCaching: enableCaching ?? this.enableCaching,
      excludedEndpoints: excludedEndpoints ?? this.excludedEndpoints,
      cacheOnErrorEndpoints: cacheOnErrorEndpoints ?? this.cacheOnErrorEndpoints,
      maxApiCacheSize: maxCacheSize ?? maxApiCacheSize ?? this.maxApiCacheSize,
      maxImageCacheSize: maxImageCacheSize ?? this.maxImageCacheSize,
      maxTotalCacheSize: maxTotalCacheSize ?? this.maxTotalCacheSize,
      serveStaleDataOnError: serveStaleDataOnError ?? this.serveStaleDataOnError,
      cachePostRequests: cachePostRequests ?? this.cachePostRequests,
      enableImageCaching: enableImageCaching ?? this.enableImageCaching,
      defaultImageTtl: defaultImageTtl ?? this.defaultImageTtl,
      cleanupStrategy: cleanupStrategy ?? this.cleanupStrategy,
    );
  }

  /// Gets the TTL for a specific endpoint
  int getTtlForEndpoint(String endpoint) {
    final sortedEntries = endpointTtls.entries.toList()..sort((a, b) => b.key.length.compareTo(a.key.length));

    for (final entry in sortedEntries) {
      if (_matchesEndpointPattern(endpoint, entry.key)) {
        return entry.value;
      }
    }
    return defaultTtl;
  }

  /// Checks if an endpoint matches a pattern (supports wildcards)
  bool _matchesEndpointPattern(String endpoint, String pattern) {
    if (endpoint == pattern) {
      return true;
    }

    if (pattern.contains('*')) {
      String regexPattern = pattern.replaceAll(RegExp(r'[.+^${}()|[\]\\]'), r'\$&').replaceAll('*', '.*');

      try {
        final regex = RegExp('^$regexPattern\$');
        return regex.hasMatch(endpoint);
      } catch (e) {
        return endpoint.contains(pattern.replaceAll('*', ''));
      }
    }

    return endpoint.contains(pattern);
  }

  /// Checks if caching should be enabled for a specific endpoint and method
  bool shouldCacheEndpoint(String endpoint, String method) {
    if (!enableCaching) return false;

    for (final excluded in excludedEndpoints) {
      if (_matchesEndpointPattern(endpoint, excluded)) {
        return false;
      }
    }

    if (method != 'GET' && !cachePostRequests) {
      return false;
    }

    return true;
  }

  /// Checks if we should cache responses even on error for a specific endpoint
  bool shouldCacheOnError(String endpoint) {
    for (final included in cacheOnErrorEndpoints) {
      if (_matchesEndpointPattern(endpoint, included)) {
        return true;
      }
    }
    return false;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CacheConfig &&
        other.defaultTtl == defaultTtl &&
        mapEquals(other.endpointTtls, endpointTtls) &&
        other.enableCaching == enableCaching &&
        listEquals(other.excludedEndpoints, excludedEndpoints) &&
        listEquals(other.cacheOnErrorEndpoints, cacheOnErrorEndpoints) &&
        other.maxApiCacheSize == maxApiCacheSize &&
        other.maxImageCacheSize == maxImageCacheSize &&
        other.maxTotalCacheSize == maxTotalCacheSize &&
        other.serveStaleDataOnError == serveStaleDataOnError &&
        other.cachePostRequests == cachePostRequests &&
        other.enableImageCaching == enableImageCaching &&
        other.defaultImageTtl == defaultImageTtl &&
        other.cleanupStrategy == cleanupStrategy;
  }

  @override
  int get hashCode {
    return defaultTtl.hashCode ^
        endpointTtls.hashCode ^
        enableCaching.hashCode ^
        excludedEndpoints.hashCode ^
        cacheOnErrorEndpoints.hashCode ^
        maxApiCacheSize.hashCode ^
        maxImageCacheSize.hashCode ^
        maxTotalCacheSize.hashCode ^
        serveStaleDataOnError.hashCode ^
        cachePostRequests.hashCode ^
        enableImageCaching.hashCode ^
        defaultImageTtl.hashCode ^
        cleanupStrategy.hashCode;
  }

  /// Converts the cache config to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'defaultTtl': defaultTtl,
      'endpointTtls': endpointTtls,
      'enableCaching': enableCaching,
      'excludedEndpoints': excludedEndpoints,
      'cacheOnErrorEndpoints': cacheOnErrorEndpoints,
      'maxApiCacheSize': maxApiCacheSize,
      'maxImageCacheSize': maxImageCacheSize,
      'maxTotalCacheSize': maxTotalCacheSize,
      'serveStaleDataOnError': serveStaleDataOnError,
      'cachePostRequests': cachePostRequests,
      'enableImageCaching': enableImageCaching,
      'defaultImageTtl': defaultImageTtl,
      'cleanupStrategy': cleanupStrategy.name,
      'maxCacheSize': maxApiCacheSize,
    };
  }
}
