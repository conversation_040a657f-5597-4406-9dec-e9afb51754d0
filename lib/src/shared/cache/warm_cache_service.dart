import 'dart:developer' as developer;
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/network/dio_client.dart';
import 'package:koc_app/src/shared/cache/cache_service.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

import 'cache.dart';

/// Service for managing cache warming operations
class WarmCacheService {
  static final WarmCacheService _instance = WarmCacheService._internal();
  factory WarmCacheService() => _instance;
  WarmCacheService._internal();

  CacheService? _cacheService;
  bool _isWarming = false;

  CacheService get cacheService {
    if (_cacheService == null) {
      try {
        final dioClient = Modular.get<DioClient>();
        _cacheService = CacheService(dio: dioClient.dio);
      } catch (e) {
        rethrow;
      }
    }
    return _cacheService!;
  }

  bool get isDioClientAvailable {
    try {
      Modular.get<DioClient>();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Critical endpoints that should be warmed on app startup
  /// Note: Voucher endpoints are excluded to avoid race conditions with site switching
  static const List<String> _criticalEndpoints = [
    '/v3/publishers/me/account',
    '/v3/publishers/me/sites',
    '/v3/publishers/banks',
  ];

  /// Secondary endpoints that can be warmed after critical ones
  /// Note: Voucher endpoints are excluded to avoid race conditions with site switching
  static const List<String> _secondaryEndpoints = [
    '/v3/publishers/me/notifications',
  ];

  /// Dynamic endpoints that require site ID (will be warmed when site ID is available)
  static const List<String> _dynamicEndpointTemplates = [
    '/v3/publishers/me/sites/{siteId}/campaigns/featured-summary',
    '/v3/publishers/me/sites/{siteId}/campaigns/top-summary',
    '/v3/publishers/me/sites/{siteId}/campaigns/fastest-growing-summary',
    '/v3/publishers/me/sites/{siteId}/categories',
  ];

  /// Check if warming is currently in progress
  bool get isWarming => _isWarming;

  /// Warm critical endpoints on app startup
  Future<void> warmCriticalEndpoints() async {
    if (_isWarming) {
      developer.log('Cache warming already in progress, skipping...');
      return;
    }

    if (!isDioClientAvailable) {
      developer.log('❌ DioClient not available yet, skipping cache warming');
      return;
    }

    _isWarming = true;
    developer.log('🔥 Starting critical cache warming...');

    try {
      await cacheService.warmCache(_criticalEndpoints).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          developer.log('⚠️ Critical cache warming timeout');
        },
      );
      developer.log('✅ Critical cache warming completed');
    } catch (e) {
      developer.log('❌ Critical cache warming failed: $e');
    } finally {
      _isWarming = false;
    }
  }

  /// Warm secondary endpoints (can be called after app is fully loaded)
  Future<void> warmSecondaryEndpoints() async {
    if (_isWarming) {
      developer.log('Cache warming already in progress, skipping secondary...');
      return;
    }

    if (!isDioClientAvailable) {
      developer.log('❌ DioClient not available yet, skipping secondary cache warming');
      return;
    }

    _isWarming = true;
    developer.log('🔥 Starting secondary cache warming...');

    try {
      await cacheService.warmCache(_secondaryEndpoints);
      developer.log('✅ Secondary cache warming completed');
    } catch (e) {
      developer.log('❌ Secondary cache warming failed: $e');
    } finally {
      _isWarming = false;
    }
  }

  /// Warm dynamic endpoints for a specific site
  Future<void> warmSiteSpecificEndpoints(String siteId) async {
    if (_isWarming) {
      developer.log('Cache warming already in progress, skipping site-specific...');
      return;
    }

    if (!isDioClientAvailable) {
      developer.log('❌ DioClient not available yet, skipping site-specific cache warming');
      return;
    }

    _isWarming = true;
    developer.log('🔥 Starting site-specific cache warming for site: $siteId');

    try {
      final endpoints = _dynamicEndpointTemplates.map((template) => template.replaceAll('{siteId}', siteId)).toList();

      await cacheService.warmCache(endpoints);
      developer.log('✅ Site-specific cache warming completed for site: $siteId');
    } catch (e) {
      developer.log('❌ Site-specific cache warming failed: $e');
    } finally {
      _isWarming = false;
    }
  }

  /// Warm all available endpoints (for testing purposes)
  Future<void> warmAllEndpoints({String? siteId}) async {
    if (_isWarming) {
      developer.log('Cache warming already in progress, skipping all...');
      return;
    }

    if (!isDioClientAvailable) {
      developer.log('❌ DioClient not available yet, skipping complete cache warming');
      return;
    }

    _isWarming = true;
    developer.log('🔥 Starting complete cache warming...');

    try {
      // Warm critical endpoints first
      await cacheService.warmCache(_criticalEndpoints);
      developer.log('✅ Critical endpoints warmed');

      // Then secondary endpoints
      await cacheService.warmCache(_secondaryEndpoints);
      developer.log('✅ Secondary endpoints warmed');

      // Finally site-specific endpoints if siteId is provided
      if (siteId != null && siteId.isNotEmpty) {
        final siteEndpoints =
            _dynamicEndpointTemplates.map((template) => template.replaceAll('{siteId}', siteId)).toList();
        await cacheService.warmCache(siteEndpoints);
        developer.log('✅ Site-specific endpoints warmed');
      }

      developer.log('✅ Complete cache warming finished');
    } catch (e) {
      developer.log('❌ Complete cache warming failed: $e');
    } finally {
      _isWarming = false;
    }
  }

  /// Get list of all endpoints that can be warmed
  Map<String, List<String>> getAllWarmableEndpoints() {
    return {
      'critical': _criticalEndpoints,
      'secondary': _secondaryEndpoints,
      'dynamic_templates': _dynamicEndpointTemplates,
    };
  }

  /// Warm specific endpoints (for custom warming)
  Future<void> warmCustomEndpoints(List<String> endpoints) async {
    if (_isWarming) {
      developer.log('Cache warming already in progress, skipping custom...');
      return;
    }

    if (endpoints.isEmpty) {
      developer.log('No endpoints provided for custom warming');
      return;
    }

    if (!isDioClientAvailable) {
      developer.log('❌ DioClient not available yet, skipping custom cache warming');
      return;
    }

    _isWarming = true;
    developer.log('🔥 Starting custom cache warming for ${endpoints.length} endpoints');

    try {
      await cacheService.warmCache(endpoints);
      developer.log('✅ Custom cache warming completed');
    } catch (e) {
      developer.log('❌ Custom cache warming failed: $e');
    } finally {
      _isWarming = false;
    }
  }

  /// Warm cache after user authentication (when site ID is available)
  Future<void> warmCacheAfterAuth() async {
    if (_isWarming) {
      developer.log('Cache warming already in progress, skipping post-auth...');
      return;
    }

    if (!isDioClientAvailable) {
      developer.log('❌ DioClient not available yet, skipping post-auth cache warming');
      return;
    }

    try {
      // Get current site ID from SharedPreferences
      final sharedPrefs = Modular.get<SharedPreferencesService>();
      final siteId = await sharedPrefs.getCurrentSiteId();

      if (siteId != null && siteId > 0) {
        developer.log('🔥 Starting post-authentication cache warming for site: $siteId');

        await warmSecondaryEndpoints();

        await warmSiteSpecificEndpoints(siteId.toString());

        developer.log('✅ Post-authentication cache warming completed');
      } else {
        developer.log('⚠️ No valid site ID found, skipping post-auth cache warming');
      }
    } catch (e) {
      developer.log('❌ Post-authentication cache warming failed: $e');
    }
  }

  Future<String?> getCurrentSiteId() async {
    try {
      final sharedPrefs = Modular.get<SharedPreferencesService>();
      final siteId = await sharedPrefs.getCurrentSiteId();
      return siteId?.toString();
    } catch (e) {
      developer.log('Failed to get current site ID: $e');
      return null;
    }
  }
}
