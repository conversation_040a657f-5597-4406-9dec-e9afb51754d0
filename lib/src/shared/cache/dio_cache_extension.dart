import 'package:dio/dio.dart';
import 'cache_utils.dart';

/// Extension methods for Dio to make it easier to use caching features
extension DioCacheExtension on Dio {
  /// Make a GET request with a custom cache TTL
  Future<Response<T>> getWithTtl<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    int ttl = 3600, // 1 hour default
  }) async {
    final cacheOptions = options ?? Options();
    cacheOptions.extra = CacheUtils.withTtl(cacheOptions.extra ?? {}, ttl);

    return get<T>(
      path,
      queryParameters: queryParameters,
      options: cacheOptions,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
    );
  }

  /// Make a POST request that will be cached
  Future<Response<T>> postWithCache<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    int ttl = 3600, // 1 hour default
  }) async {
    final cacheOptions = options ?? Options();
    cacheOptions.extra = CacheUtils.withTtl(
      CacheUtils.forceCache(cacheOptions.extra ?? {}),
      ttl,
    );

    return post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: cacheOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }
}
