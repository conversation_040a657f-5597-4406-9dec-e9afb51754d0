import 'dart:convert';

import 'package:hive/hive.dart';

part 'cache_entry.g.dart';

/// A class representing a cached API response
@HiveType(typeId: 0)
class CacheEntry {
  /// The data of the cached response
  @HiveField(0)
  final String data;

  /// The headers of the cached response
  @HiveField(1)
  final Map<String, dynamic> headers;

  /// The timestamp when the cache entry was created
  @HiveField(2)
  final int createdAt;

  /// The time-to-live in seconds
  @HiveField(3)
  final int ttl;

  /// The status code of the response
  @HiveField(4)
  final int statusCode;

  /// The URL of the request
  @HiveField(5)
  final String url;

  /// The HTTP method of the request
  @HiveField(6)
  final String method;

  CacheEntry({
    required this.data,
    required this.headers,
    required this.createdAt,
    required this.ttl,
    required this.statusCode,
    required this.url,
    required this.method,
  });

  /// Checks if the cache entry is still valid
  bool get isValid {
    final now = DateTime.now().millisecondsSinceEpoch;
    final expiresAt = createdAt + (ttl * 1000);
    return now < expiresAt;
  }

  /// Returns the time remaining until expiration in seconds
  int get timeRemaining {
    final now = DateTime.now().millisecondsSinceEpoch;
    final expiresAt = createdAt + (ttl * 1000);
    return (expiresAt - now) ~/ 1000;
  }

  /// Returns the expiration date as a DateTime
  DateTime get expiresAt {
    return DateTime.fromMillisecondsSinceEpoch(createdAt + (ttl * 1000));
  }

  /// Returns the creation date as a DateTime
  DateTime get createdAtDate {
    return DateTime.fromMillisecondsSinceEpoch(createdAt);
  }

  /// Converts the cache entry to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'headers': headers,
      'createdAt': createdAt,
      'ttl': ttl,
      'statusCode': statusCode,
      'url': url,
      'method': method,
    };
  }

  /// Creates a cache entry from a JSON map
  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      data: json['data'],
      headers: Map<String, dynamic>.from(json['headers']),
      createdAt: json['createdAt'],
      ttl: json['ttl'],
      statusCode: json['statusCode'],
      url: json['url'],
      method: json['method'],
    );
  }

  /// Creates a copy of this cache entry with the given fields replaced
  CacheEntry copyWith({
    String? data,
    Map<String, dynamic>? headers,
    int? createdAt,
    int? ttl,
    int? statusCode,
    String? url,
    String? method,
  }) {
    return CacheEntry(
      data: data ?? this.data,
      headers: headers ?? this.headers,
      createdAt: createdAt ?? this.createdAt,
      ttl: ttl ?? this.ttl,
      statusCode: statusCode ?? this.statusCode,
      url: url ?? this.url,
      method: method ?? this.method,
    );
  }

  /// Refreshes the cache entry with a new creation timestamp
  CacheEntry refresh() {
    return copyWith(
      createdAt: DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Returns the data as a decoded JSON object
  dynamic get decodedData {
    try {
      return json.decode(data);
    } catch (e) {
      return data;
    }
  }
}
