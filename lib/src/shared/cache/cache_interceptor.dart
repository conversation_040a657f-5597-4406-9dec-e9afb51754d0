import 'dart:developer' as developer;
import 'dart:io';

import 'package:dio/dio.dart';

import 'cache_config.dart';
import 'cache_manager.dart';

/// An interceptor for Dio that handles caching of API responses
class CacheInterceptor extends Interceptor {
  final CacheManager _cacheManager;
  final CacheConfig _config;

  /// Key for storing whether a request should skip cache
  static const String skipCacheKey = 'skipCache';

  /// Key for storing whether a response should be cached
  static const String forceCacheKey = 'forceCache';

  /// Key for storing a custom TTL for a request
  static const String cacheTtlKey = 'cacheTtl';

  CacheInterceptor({
    CacheManager? cacheManager,
    CacheConfig? config,
  })  : _cacheManager = cacheManager ?? CacheManager(),
        _config = config ?? const CacheConfig();

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    await _cacheManager.init();

    if (options.extra[skipCacheKey] == true) {
      return handler.next(options);
    }

    final method = options.method;
    final uri = Uri.parse(options.path);
    final url = uri.toString();

    if (!_config.shouldCacheEndpoint(url, method)) {
      return handler.next(options);
    }

    try {
      final cachedEntry = await _cacheManager.getCachedResponse(
        url,
        method,
        options.queryParameters,
      );

      if (cachedEntry != null) {
        developer.log('🔵 [CACHE HIT] $method $url (TTL: ${cachedEntry.timeRemaining}s)');

        final responseData = cachedEntry.decodedData;
        final headers = Headers.fromMap(Map<String, List<String>>.fromEntries(
            cachedEntry.headers.entries.map((e) => MapEntry(e.key, [e.value.toString()]))));

        headers.set('x-cache', 'HIT');
        headers.set('x-cache-ttl', cachedEntry.timeRemaining.toString());
        headers.set('x-cache-expires', cachedEntry.expiresAt.toIso8601String());

        final response = Response(
          data: responseData,
          headers: headers,
          requestOptions: options,
          statusCode: cachedEntry.statusCode,
          isRedirect: false,
          statusMessage: 'OK (Cached)',
        );

        return handler.resolve(response);
      }
    } catch (e) {
      developer.log('Error retrieving from cache: $e');
    }

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    if (response.requestOptions.extra[skipCacheKey] == true) {
      return handler.next(response);
    }

    final method = response.requestOptions.method;
    final uri = Uri.parse(response.requestOptions.path);
    final url = uri.path;

    final shouldCache =
        _config.shouldCacheEndpoint(url, method) || response.requestOptions.extra[forceCacheKey] == true;

    if (!shouldCache) {
      return handler.next(response);
    }

    try {
      int ttl = response.requestOptions.extra[cacheTtlKey] as int? ?? _config.getTtlForEndpoint(url);

      await _cacheManager.cacheResponse(
        url,
        method,
        response.requestOptions.queryParameters,
        response,
        ttl,
      );

      developer.log('🟢 [CACHE WRITE] $method $url (TTL: ${ttl}s)');

      response.headers.set('x-cache', 'MISS');
      response.headers.set('x-cache-ttl', ttl.toString());
      final expiresAt = DateTime.now().add(Duration(seconds: ttl));
      response.headers.set('x-cache-expires', expiresAt.toIso8601String());
    } catch (e) {
      developer.log('Error caching response: $e');
    }

    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final method = err.requestOptions.method;
    final uri = Uri.parse(err.requestOptions.path);
    final url = uri.path;

    if (!_config.serveStaleDataOnError && !_config.shouldCacheOnError(url)) {
      return handler.next(err);
    }

    final isNetworkError = err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.error is SocketException;

    final isServerError = err.response?.statusCode != null && err.response!.statusCode! >= 500;

    if (!isNetworkError && !isServerError) {
      return handler.next(err);
    }

    try {
      final cachedEntry = await _cacheManager.getCachedResponse(
        url,
        method,
        err.requestOptions.queryParameters,
      );

      if (cachedEntry != null) {
        developer.log('🟠 [CACHE FALLBACK] $method $url (Error: ${err.message})');

        final responseData = cachedEntry.decodedData;
        final headers = Headers.fromMap(Map<String, List<String>>.fromEntries(
            cachedEntry.headers.entries.map((e) => MapEntry(e.key, [e.value.toString()]))));

        headers.set('x-cache', 'FALLBACK');
        headers.set('x-cache-stale', (!cachedEntry.isValid).toString());
        headers.set('x-cache-error', err.message ?? 'Unknown error');

        final response = Response(
          data: responseData,
          headers: headers,
          requestOptions: err.requestOptions,
          statusCode: cachedEntry.statusCode,
          isRedirect: false,
          statusMessage: 'OK (Cached Fallback)',
        );

        _cacheManager.metrics.incrementFallbacks();

        return handler.resolve(response);
      }
    } catch (e) {
      developer.log('Error retrieving from cache for fallback: $e');
    }

    return handler.next(err);
  }
}
