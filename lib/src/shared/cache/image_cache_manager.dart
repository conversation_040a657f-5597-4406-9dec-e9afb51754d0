import 'dart:developer' as developer;
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:synchronized/synchronized.dart';

import 'cache_config.dart';

/// A manager for handling image caching with configurable size limits
class ImageCacheManager {
  static const String _cacheKey = 'koc_app_image_cache';

  late DefaultCacheManager _cacheManager;
  final Lock _lock = Lock();
  bool _initialized = false;
  CacheConfig _config = const CacheConfig();

  /// Singleton instance
  static final ImageCacheManager _instance = ImageCacheManager._internal();

  /// Factory constructor to return the singleton instance
  factory ImageCacheManager() => _instance;

  /// Private constructor
  ImageCacheManager._internal();

  /// Initialize the image cache manager
  Future<void> init([CacheConfig? config]) async {
    if (_initialized) return;

    await _lock.synchronized(() async {
      if (_initialized) return;

      _config = config ?? _config;

      _cacheManager = DefaultCacheManager();

      _initialized = true;
      developer.log('Image cache manager initialized');
    });
  }

  /// Get the cache manager instance
  DefaultCacheManager get cacheManager {
    if (!_initialized) {
      throw StateError('ImageCacheManager not initialized. Call init() first.');
    }
    return _cacheManager;
  }

  /// Update the cache configuration
  Future<void> updateConfig(CacheConfig newConfig) async {
    await init(newConfig);
    _config = newConfig;
  }

  /// Get current cache configuration
  CacheConfig get config => _config;

  /// Get the current image cache size in bytes
  Future<int> getCacheSize() async {
    await init();

    try {
      final repo = _cacheManager.config.repo;
      final allObjects = await repo.getAllObjects();
      int totalSize = 0;

      for (final cacheObject in allObjects) {
        final file = await _cacheManager.getFileFromCache(cacheObject.key);
        if (file != null) {
          final fileSize = await file.file.length();
          totalSize += fileSize;
        }
      }

      return totalSize;
    } catch (e) {
      developer.log('Error calculating image cache size: $e');
      return 0;
    }
  }

  /// Get the number of cached images
  Future<int> getCacheCount() async {
    await init();

    try {
      final repo = _cacheManager.config.repo;
      final allObjects = await repo.getAllObjects();
      return allObjects.length;
    } catch (e) {
      developer.log('Error counting cached images: $e');
      return 0;
    }
  }

  /// Get all cached image objects with detailed information
  Future<List<Map<String, dynamic>>> getAllCacheObjects() async {
    await init();

    try {
      final repo = _cacheManager.config.repo;
      final allObjects = await repo.getAllObjects();
      final cacheObjects = <Map<String, dynamic>>[];

      for (final cacheObject in allObjects) {
        final file = await _cacheManager.getFileFromCache(cacheObject.key);
        if (file != null) {
          final fileSize = await file.file.length();
          final sizeKB = (fileSize / 1024).toStringAsFixed(2);

          // Extract filename from URL
          String displayUrl;
          try {
            final uri = Uri.parse(cacheObject.url ?? cacheObject.key);
            final pathSegments = uri.pathSegments;
            if (pathSegments.isNotEmpty) {
              displayUrl = pathSegments.last;
              if (displayUrl.isEmpty) {
                displayUrl = uri.path.isNotEmpty ? uri.path : cacheObject.key;
              }
            } else {
              displayUrl = cacheObject.key;
            }
          } catch (e) {
            displayUrl = cacheObject.key;
          }

          // Format the cache timestamp
          final cachedTime = cacheObject.touched ?? DateTime.now();
          final formattedTime = '${cachedTime.year.toString().padLeft(4, '0')}/'
              '${cachedTime.month.toString().padLeft(2, '0')}/'
              '${cachedTime.day.toString().padLeft(2, '0')} '
              '${cachedTime.hour.toString().padLeft(2, '0')}:'
              '${cachedTime.minute.toString().padLeft(2, '0')}:'
              '${cachedTime.second.toString().padLeft(2, '0')}';

          // Calculate age in hours/days
          final now = DateTime.now();
          final age = now.difference(cachedTime);
          String ageString;
          if (age.inDays > 0) {
            ageString = '${age.inDays}d';
          } else if (age.inHours > 0) {
            ageString = '${age.inHours}h';
          } else if (age.inMinutes > 0) {
            ageString = '${age.inMinutes}m';
          } else {
            ageString = '${age.inSeconds}s';
          }

          cacheObjects.add({
            'key': cacheObject.key,
            'url': cacheObject.url ?? cacheObject.key,
            'displayUrl': displayUrl,
            'size': fileSize,
            'sizeKB': sizeKB,
            'cachedAt': formattedTime,
            'age': ageString,
            'touched': cachedTime,
          });
        }
      }

      // Sort by most recently touched first
      cacheObjects.sort((a, b) => (b['touched'] as DateTime).compareTo(a['touched'] as DateTime));

      return cacheObjects;
    } catch (e) {
      developer.log('Error getting cached image objects: $e');
      return [];
    }
  }

  /// Clear all cached images
  Future<void> clearCache() async {
    await init();

    try {
      await _cacheManager.emptyCache();
      developer.log('Image cache cleared');
    } catch (e) {
      developer.log('Error clearing image cache: $e');
    }
  }

  /// Remove a specific image from cache
  Future<void> removeFromCache(String url) async {
    await init();

    try {
      await _cacheManager.removeFile(url);
      developer.log('Removed image from cache: $url');
    } catch (e) {
      developer.log('Error removing image from cache: $e');
    }
  }

  /// Enforce cache size limits by removing oldest files
  Future<void> enforceCacheSizeLimit() async {
    await init();

    try {
      // For now, just clear cache if needed
      // TODO: Implement proper size enforcement when custom cache manager is available
      final currentSize = await getCacheSize();
      if (currentSize > _config.maxImageCacheSize) {
        await clearCache();
        developer.log('Image cache cleared to enforce size limit');
      }
    } catch (e) {
      developer.log('Error enforcing image cache size limit: $e');
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    await init();

    final size = await getCacheSize();
    final count = await getCacheCount();

    return {
      'size': size,
      'count': count,
      'maxSize': _config.maxImageCacheSize,
      'formattedSize': _formatBytes(size),
      'formattedMaxSize': _formatBytes(_config.maxImageCacheSize),
      'usagePercentage': _config.maxImageCacheSize > 0 ? (size / _config.maxImageCacheSize * 100).clamp(0, 100) : 0,
    };
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Dispose resources
  Future<void> dispose() async {
    if (_initialized) {
      // Note: DefaultCacheManager doesn't have a dispose method
      // The system will clean up resources automatically
      _initialized = false;
    }
  }
}
