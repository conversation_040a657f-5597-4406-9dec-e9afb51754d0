import 'dart:async';
import 'dart:developer' as developer;
import 'package:synchronized/synchronized.dart';

import 'cache_config.dart';
import 'cache_manager.dart';
import 'image_cache_manager.dart';

/// A unified manager for handling both API and image caching
class UnifiedCacheManager {
  final CacheManager _apiCacheManager;
  final ImageCacheManager _imageCacheManager;
  final Lock _lock = Lock();
  bool _initialized = false;
  CacheConfig _config = const CacheConfig();

  /// Singleton instance
  static final UnifiedCacheManager _instance = UnifiedCacheManager._internal();

  /// Factory constructor to return the singleton instance
  factory UnifiedCacheManager() => _instance;

  /// Private constructor
  UnifiedCacheManager._internal()
      : _apiCacheManager = CacheManager(),
        _imageCacheManager = ImageCacheManager();

  /// Initialize the unified cache manager
  Future<void> init([CacheConfig? config]) async {
    if (_initialized) return;

    await _lock.synchronized(() async {
      if (_initialized) return;

      try {
        developer.log('🔄 Initializing unified cache manager...');
        _config = config ?? _config;

        await Future.wait([
          _apiCacheManager.init().timeout(const Duration(seconds: 10)),
          _imageCacheManager.init(_config).timeout(const Duration(seconds: 10)),
        ]);

        if (config != null) {
          await Future.wait([
            _apiCacheManager.updateConfig(config).timeout(const Duration(seconds: 5)),
            _imageCacheManager.updateConfig(config).timeout(const Duration(seconds: 5)),
          ]);
        }

        _initialized = true;
        developer.log('✅ Unified cache manager initialized successfully');
      } catch (e, stackTrace) {
        developer.log('❌ Failed to initialize unified cache manager: $e', stackTrace: stackTrace);
        _initialized = false;
        rethrow;
      }
    });
  }

  /// Get the API cache manager
  CacheManager get apiCacheManager => _apiCacheManager;

  /// Get the image cache manager
  ImageCacheManager get imageCacheManager => _imageCacheManager;

  /// Get current cache configuration
  CacheConfig get config => _config;

  /// Update the cache configuration for both managers
  Future<void> updateConfig(CacheConfig newConfig) async {
    await init();

    _config = newConfig;
    await _apiCacheManager.updateConfig(newConfig);
    await _imageCacheManager.updateConfig(newConfig);

    developer.log('Unified cache configuration updated');
  }

  /// Get comprehensive cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    await init();

    final apiStats = await _getApiCacheStats();
    final imageStats = await _imageCacheManager.getCacheStats();

    final totalSize = apiStats['size'] + imageStats['size'];
    final totalMaxSize = _config.maxTotalCacheSize;

    return {
      'api': apiStats,
      'image': imageStats,
      'total': {
        'size': totalSize,
        'maxSize': totalMaxSize,
        'formattedSize': _formatBytes(totalSize),
        'formattedMaxSize': _formatBytes(totalMaxSize),
        'usagePercentage': totalMaxSize > 0 ? (totalSize / totalMaxSize * 100).clamp(0, 100) : 0,
      },
      'config': {
        'enableCaching': _config.enableCaching,
        'enableImageCaching': _config.enableImageCaching,
        'maxApiCacheSize': _config.maxApiCacheSize,
        'maxImageCacheSize': _config.maxImageCacheSize,
        'maxTotalCacheSize': _config.maxTotalCacheSize,
        'cleanupStrategy': _config.cleanupStrategy.name,
      },
    };
  }

  /// Get API cache statistics
  Future<Map<String, dynamic>> _getApiCacheStats() async {
    final size = await _apiCacheManager.getCacheSize();
    final count = await _apiCacheManager.getCacheEntryCount();
    final metrics = _apiCacheManager.metrics;

    return {
      'size': size,
      'count': count,
      'maxSize': _config.maxApiCacheSize,
      'formattedSize': _formatBytes(size),
      'formattedMaxSize': _formatBytes(_config.maxApiCacheSize),
      'usagePercentage': _config.maxApiCacheSize > 0 ? (size / _config.maxApiCacheSize * 100).clamp(0, 100) : 0,
      'hits': metrics.hits,
      'misses': metrics.misses,
      'writes': metrics.writes,
      'expired': metrics.expired,
      'hitRate': metrics.hitRate,
    };
  }

  /// Clear all caches (both API and image)
  Future<void> clearAllCache() async {
    await init();

    await Future.wait([
      _apiCacheManager.clearCache(),
      _imageCacheManager.clearCache(),
    ]);

    developer.log('All caches cleared');
  }

  /// Clear only API cache
  Future<void> clearApiCache() async {
    await init();
    await _apiCacheManager.clearCache();
    developer.log('API cache cleared');
  }

  /// Clear only image cache
  Future<void> clearImageCache() async {
    await init();
    await _imageCacheManager.clearCache();
    developer.log('Image cache cleared');
  }

  /// Clear cache for a specific API endpoint
  Future<void> clearApiEndpoint(String endpoint) async {
    await init();
    await _apiCacheManager.clearEndpoint(endpoint);
    developer.log('API cache cleared for endpoint: $endpoint');
  }

  /// Clear all site-specific cache entries for a given site ID
  /// This clears both API cache entries and any site-specific image cache entries
  Future<void> clearSiteSpecificCache(int siteId) async {
    await init();
    await _apiCacheManager.clearSiteSpecificCache(siteId);
    developer.log('Site-specific cache cleared for site ID: $siteId');
  }

  /// Remove a specific image from cache
  Future<void> removeImageFromCache(String url) async {
    await init();
    await _imageCacheManager.removeFromCache(url);
  }

  /// Get all cached image objects with detailed information
  Future<List<Map<String, dynamic>>> getAllImageCacheObjects() async {
    await init();
    return await _imageCacheManager.getAllCacheObjects();
  }

  /// Enforce cache size limits for both API and image caches
  Future<void> enforceCacheSizeLimits() async {
    await init();

    // Check total cache size first
    final stats = await getCacheStats();
    final totalSize = stats['total']['size'] as int;

    if (totalSize > _config.maxTotalCacheSize) {
      // If total size exceeds limit, prioritize based on cleanup strategy
      await _enforceGlobalCacheLimit(totalSize);
    } else {
      // Otherwise, enforce individual cache limits
      await Future.wait([
        _enforceApiCacheSize(),
        _imageCacheManager.enforceCacheSizeLimit(),
      ]);
    }
  }

  /// Enforce API cache size limit
  Future<void> _enforceApiCacheSize() async {
    try {
      // For now, just clear API cache if needed
      // TODO: Implement proper size enforcement when available
      final currentSize = await _apiCacheManager.getCacheSize();
      if (currentSize > _config.maxApiCacheSize) {
        await _apiCacheManager.clearCache();
        developer.log('API cache cleared to enforce size limit');
      }
    } catch (e) {
      developer.log('Error enforcing API cache size limit: $e');
    }
  }

  /// Enforce global cache size limit across both API and image caches
  Future<void> _enforceGlobalCacheLimit(int currentTotalSize) async {
    switch (_config.cleanupStrategy) {
      case CacheCleanupStrategy.lru:
        // For LRU, let each cache manager handle its own cleanup
        await Future.wait([
          _enforceApiCacheSize(),
          _imageCacheManager.enforceCacheSizeLimit(),
        ]);
        break;

      case CacheCleanupStrategy.largestFirst:
        // Clear from the larger cache first
        final apiSize = await _apiCacheManager.getCacheSize();
        final imageSize = await _imageCacheManager.getCacheSize();

        if (apiSize > imageSize) {
          await _apiCacheManager.clearCache();
          if (await getCacheSize() > _config.maxTotalCacheSize) {
            await _imageCacheManager.clearCache();
          }
        } else {
          await _imageCacheManager.clearCache();
          if (await getCacheSize() > _config.maxTotalCacheSize) {
            await _apiCacheManager.clearCache();
          }
        }
        break;

      case CacheCleanupStrategy.shortestTtlFirst:
        // For now, treat this the same as LRU
        await Future.wait([
          _enforceApiCacheSize(),
          _imageCacheManager.enforceCacheSizeLimit(),
        ]);
        break;
    }

    developer.log('Global cache size limit enforced');
  }

  /// Get total cache size (API + images)
  Future<int> getCacheSize() async {
    await init();

    final apiSize = await _apiCacheManager.getCacheSize();
    final imageSize = await _imageCacheManager.getCacheSize();

    return apiSize + imageSize;
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Enable caching for both API and images
  Future<void> enableCaching() async {
    final newConfig = _config.copyWith(
      enableCaching: true,
      enableImageCaching: true,
    );
    await updateConfig(newConfig);
  }

  /// Disable caching for both API and images
  Future<void> disableCaching() async {
    final newConfig = _config.copyWith(
      enableCaching: false,
      enableImageCaching: false,
    );
    await updateConfig(newConfig);
  }

  /// Enable only API caching
  Future<void> enableApiCaching() async {
    final newConfig = _config.copyWith(enableCaching: true);
    await updateConfig(newConfig);
  }

  /// Enable only image caching
  Future<void> enableImageCaching() async {
    final newConfig = _config.copyWith(enableImageCaching: true);
    await updateConfig(newConfig);
  }

  /// Set maximum cache sizes
  Future<void> setMaxCacheSizes({
    int? maxApiCacheSize,
    int? maxImageCacheSize,
    int? maxTotalCacheSize,
  }) async {
    final newConfig = _config.copyWith(
      maxApiCacheSize: maxApiCacheSize,
      maxImageCacheSize: maxImageCacheSize,
      maxTotalCacheSize: maxTotalCacheSize,
    );
    await updateConfig(newConfig);
  }

  /// Set cache cleanup strategy
  Future<void> setCleanupStrategy(CacheCleanupStrategy strategy) async {
    final newConfig = _config.copyWith(cleanupStrategy: strategy);
    await updateConfig(newConfig);
  }

  /// Reset the cache manager (useful for app restart scenarios)
  Future<void> reset() async {
    await _lock.synchronized(() async {
      try {
        developer.log('🔄 Resetting unified cache manager...');

        if (_initialized) {
          await _imageCacheManager.dispose();
          await _apiCacheManager.clearCache();
        }

        _initialized = false;
        developer.log('✅ Unified cache manager reset successfully');
      } catch (e) {
        developer.log('❌ Error resetting unified cache manager: $e');
      }
    });
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _lock.synchronized(() async {
      if (_initialized) {
        try {
          developer.log('🔄 Disposing unified cache manager...');
          await _imageCacheManager.dispose();
          _initialized = false;
          developer.log('✅ Unified cache manager disposed successfully');
        } catch (e) {
          developer.log('❌ Error disposing unified cache manager: $e');
        }
      }
    });
  }

  /// Check if the cache manager is initialized
  bool get isInitialized => _initialized;
}
