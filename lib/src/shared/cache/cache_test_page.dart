import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/shared/cache/warm_cache_service.dart';
import 'package:koc_app/src/shared/debug/debug_button_style.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

/// Log entry for cache operations with categorization
class CacheLogEntry {
  final String message;
  final CacheLogCategory category;
  final DateTime timestamp;
  final bool isSmallText;

  CacheLogEntry({
    required this.message,
    required this.category,
    required this.isSmallText,
  }) : timestamp = DateTime.now();

  String get formattedMessage {
    final timeStr = timestamp.toString().substring(11, 19);
    return '$timeStr: $message';
  }

  Color get textColor {
    switch (category) {
      case CacheLogCategory.system:
        return Colors.cyan;
      case CacheLogCategory.request:
        return Colors.blue;
      case CacheLogCategory.cache:
        return Colors.purple;
      case CacheLogCategory.stats:
        return Colors.yellow;
      case CacheLogCategory.keys:
        return Colors.orange;
      case CacheLogCategory.error:
        return Colors.red;
      case CacheLogCategory.success:
        return Colors.green;
      case CacheLogCategory.header:
        return Colors.white;
      case CacheLogCategory.imageHeader:
        return Colors.cyan;
      case CacheLogCategory.imageStats:
        return Colors.lightGreen;
      case CacheLogCategory.imageKeys:
        return Colors.deepOrange;
    }
  }
}

/// Categories for different types of cache log entries
enum CacheLogCategory {
  system,
  request,
  cache,
  stats,
  keys,
  error,
  success,
  header,
  imageHeader,
  imageStats,
  imageKeys,
}

/// A test page to verify the caching implementation
class CacheTestPage extends StatefulWidget {
  const CacheTestPage({super.key});

  @override
  State<CacheTestPage> createState() => _CacheTestPageState();
}

class _CacheTestPageState extends State<CacheTestPage> {
  final ApiService _apiService = Modular.get<ApiService>();
  final WarmCacheService _warmCacheService = WarmCacheService();
  final List<CacheLogEntry> _logs = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _addLog('Cache Test Page Initialized', CacheLogCategory.system);
  }

  void _addLog(String message, CacheLogCategory category, {bool isSmallText = false}) {
    setState(() {
      _logs.add(CacheLogEntry(
        message: message,
        category: category,
        isSmallText: isSmallText,
      ));
      if (_logs.length > 100) {
        _logs.removeAt(0);
      }
    });
  }

  Future<void> _testRegularRequest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Making regular GET request...', CacheLogCategory.request);
      final startTime = DateTime.now();
      final response = await _apiService.getData('/v3/publishers/me/sites');
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;

      _addLog('Request completed in $duration ms', CacheLogCategory.request);
      _addLog('Response: ${response.toString().substring(0, 50)}...', CacheLogCategory.request);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testCachedRequest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Making GET request (should use cache if available)...', CacheLogCategory.request);
      final startTime = DateTime.now();
      final response = await _apiService.getData('/v3/publishers/me/sites');
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;

      _addLog('Request completed in $duration ms', CacheLogCategory.request);
      _addLog('Response: ${response.toString().substring(0, 50)}...', CacheLogCategory.request);

      // Check if it was a cache hit
      if (duration < 100) {
        _addLog('CACHE HIT (fast response time)', CacheLogCategory.success);
      } else {
        _addLog('CACHE MISS (slow response time)', CacheLogCategory.cache);
      }
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testFreshRequest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Clearing cache and making fresh GET request...', CacheLogCategory.request);

      // Clear cache for this endpoint first
      await _apiService.clearCacheForEndpoint('/v3/publishers/me/sites');

      final startTime = DateTime.now();
      final response = await _apiService.getData('/v3/publishers/me/sites');
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;

      _addLog('Request completed in $duration ms', CacheLogCategory.request);
      _addLog('Response: ${response.toString().substring(0, 50)}...', CacheLogCategory.request);
      _addLog('FRESH DATA (cache was cleared)', CacheLogCategory.success);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _clearCacheWithConfirm() async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: const Icon(
            Icons.warning_amber,
            color: Colors.orange,
            size: 48,
          ),
          content: const Text(
            'Confirm clear cache',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Clear'),
            ),
          ],
        );
      },
    );

    // Only proceed if user confirmed
    if (confirmed == true) {
      await _clearCache();
    }
  }

  Future<void> _clearCache() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Clearing all cache...', CacheLogCategory.cache);
      await _apiService.clearAllCache();
      _addLog('All cache cleared', CacheLogCategory.success);

      final stats = await _apiService.getCacheStats();
      final total = stats['total'] as Map<String, dynamic>;
      _addLog('Total cache size: ${total['formattedSize']}', CacheLogCategory.stats);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _clearApiCacheWithConfirm() async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: const Icon(
            Icons.warning_amber,
            color: Colors.orange,
            size: 48,
          ),
          content: const Text(
            'Confirm clear API cache',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Clear'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      await _clearApiCache();
    }
  }

  Future<void> _clearApiCache() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Clearing API cache...', CacheLogCategory.cache);
      await _apiService.clearApiCache();
      _addLog('API cache cleared', CacheLogCategory.success);

      final stats = await _apiService.getCacheStats();
      final api = stats['api'] as Map<String, dynamic>;
      _addLog('API cache size: ${api['formattedSize']}', CacheLogCategory.stats);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _clearImageCacheWithConfirm() async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: const Icon(
            Icons.warning_amber,
            color: Colors.orange,
            size: 48,
          ),
          content: const Text(
            'Confirm clear image cache',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Clear'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      await _clearImageCache();
    }
  }

  Future<void> _clearImageCache() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Clearing image cache...', CacheLogCategory.cache);
      await _apiService.clearImageCache();
      _addLog('Image cache cleared', CacheLogCategory.success);

      final stats = await _apiService.getCacheStats();
      final image = stats['image'] as Map<String, dynamic>;
      _addLog('Image cache size: ${image['formattedSize']}', CacheLogCategory.stats);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getCacheStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Getting unified cache stats...', CacheLogCategory.stats);
      final stats = await _apiService.getCacheStats();

      final total = stats['total'] as Map<String, dynamic>;
      _addLog('=== TOTAL CACHE ===', CacheLogCategory.header);
      _addLog('Size: ${total['formattedSize']} / ${total['formattedMaxSize']}', CacheLogCategory.stats);
      _addLog('Usage: ${total['usagePercentage'].toStringAsFixed(1)}%', CacheLogCategory.stats);

      final api = stats['api'] as Map<String, dynamic>;
      _addLog('=== API CACHE ===', CacheLogCategory.header);
      _addLog('Size: ${api['formattedSize']} / ${api['formattedMaxSize']}', CacheLogCategory.stats);
      _addLog('Entries: ${api['count']}', CacheLogCategory.stats);
      _addLog('Hit rate: ${(api['hitRate'] * 100).toStringAsFixed(1)}%', CacheLogCategory.stats);
      _addLog('Hits: ${api['hits']} | Misses: ${api['misses']}', CacheLogCategory.stats);
      _addLog('Writes: ${api['writes']} | Expired: ${api['expired']}', CacheLogCategory.stats);

      final image = stats['image'] as Map<String, dynamic>;
      _addLog('=== IMAGE CACHE ===', CacheLogCategory.header);
      _addLog('Size: ${image['formattedSize']} / ${image['formattedMaxSize']}', CacheLogCategory.stats);
      _addLog('Images: ${image['count']}', CacheLogCategory.stats);
      _addLog('Usage: ${image['usagePercentage'].toStringAsFixed(1)}%', CacheLogCategory.stats);

      final config = stats['config'] as Map<String, dynamic>;
      _addLog('=== CONFIG ===', CacheLogCategory.header);
      _addLog('API Cache: ${config['enableCaching'] ? 'ON' : 'OFF'}', CacheLogCategory.stats);
      _addLog('Image Cache: ${config['enableImageCaching'] ? 'ON' : 'OFF'}', CacheLogCategory.stats);
      _addLog('Cleanup: ${config['cleanupStrategy']}', CacheLogCategory.stats);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getCacheKeys() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Getting all cache keys...', CacheLogCategory.keys);
      final cacheKeys = await _apiService.getAllCacheKeys();

      if (cacheKeys.isEmpty) {
        _addLog('No cache entries found', CacheLogCategory.keys);
        return;
      }

      _addLog('Found ${cacheKeys.length} cache entries:', CacheLogCategory.keys);
      _addLog('', CacheLogCategory.keys);

      // Compact table header with small text (metadata only)
      _addLog('┌───┬──┬──────┬─────┬─────────┬──────┐', CacheLogCategory.system, isSmallText: true);
      _addLog('│No │St│Size  │TTL  │ Expires │Remain│', CacheLogCategory.system, isSmallText: true);
      _addLog('├───┼──┼──────┼─────┼─────────┼──────┤', CacheLogCategory.system, isSmallText: true);

      for (int i = 0; i < cacheKeys.length; i++) {
        final entry = cacheKeys[i];
        final isValid = entry['isValid'] as bool;
        final validityStatus = isValid ? '✓' : '✗';
        final url = entry['url'] as String;
        final sizeKB = entry['sizeKB'] as String;
        final ttl = entry['ttl'].toString();
        final expirationDate = entry['expirationDate'] as String;
        final timeRemaining = entry['timeRemaining'] as int;

        // Extract path from URL (remove domain)
        String displayPath;
        try {
          final uri = Uri.parse(url);
          displayPath = uri.path;
          if (uri.query.isNotEmpty) {
            displayPath += '?${uri.query}';
          }
        } catch (e) {
          displayPath = url; // Fallback to full URL if parsing fails
        }

        // Truncate path if too long (show rightmost characters for better debugging)
        if (displayPath.length > 60) {
          displayPath = '...${displayPath.substring(displayPath.length - 57)}';
        }

        // Compact expiration date (show MM/DD HH:MM)
        final compactExpire = expirationDate.length >= 16 ? expirationDate.substring(5, 16) : expirationDate;
        final shortExpire = compactExpire.length > 8 ? compactExpire.substring(0, 8) : compactExpire;

        // Format the metadata row (without path)
        final no = (i + 1).toString().padLeft(2);
        final status = validityStatus;
        final sizePadded = '${sizeKB.padLeft(4)}K';
        final ttlPadded = ttl.length > 4 ? '${ttl.substring(0, 3)}+' : ttl.padLeft(4);
        final expirePadded = shortExpire.padRight(8);
        final remainingPadded = isValid
            ? (timeRemaining > 9999 ? '${(timeRemaining / 3600).toStringAsFixed(0)}h' : timeRemaining.toString())
                .padLeft(5)
            : 'EXP'.padLeft(5);

        // First row: Metadata (no icon, use system category for gray color)
        _addLog('│$no │$status│$sizePadded │$ttlPadded │ $expirePadded │$remainingPadded │', CacheLogCategory.system,
            isSmallText: true);

        // Second row: Path with different color (no icon)
        _addLog(displayPath, CacheLogCategory.keys, isSmallText: true);
      }

      // Compact table footer with small text
      _addLog('└───┴──┴──────┴─────┴─────────┴──────┘', CacheLogCategory.system, isSmallText: true);
      _addLog('', CacheLogCategory.keys);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getImageCacheStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Getting image cache stats...', CacheLogCategory.imageStats);
      final stats = await _apiService.getCacheStats();

      // Image cache stats
      final image = stats['image'] as Map<String, dynamic>;
      _addLog('=== IMAGE CACHE STATS ===', CacheLogCategory.imageHeader);
      _addLog('Size: ${image['formattedSize']} / ${image['formattedMaxSize']}', CacheLogCategory.imageStats);
      _addLog('Images: ${image['count']}', CacheLogCategory.imageStats);
      _addLog('Usage: ${image['usagePercentage'].toStringAsFixed(1)}%', CacheLogCategory.imageStats);
      _addLog('', CacheLogCategory.imageStats);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getImageCacheKeys() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Getting all cached images...', CacheLogCategory.imageKeys);
      final imageObjects = await _apiService.getAllImageCacheObjects();

      if (imageObjects.isEmpty) {
        _addLog('No cached images found', CacheLogCategory.imageKeys);
        return;
      }

      _addLog('Found ${imageObjects.length} cached images:', CacheLogCategory.imageKeys);
      _addLog('', CacheLogCategory.imageKeys);

      // Compact table header with small text
      _addLog('┌───┬──────┬─────────┬─────┐', CacheLogCategory.system, isSmallText: true);
      _addLog('│No │Size  │ Cached  │ Age │', CacheLogCategory.system, isSmallText: true);
      _addLog('├───┼──────┼─────────┼─────┤', CacheLogCategory.system, isSmallText: true);

      for (int i = 0; i < imageObjects.length; i++) {
        final entry = imageObjects[i];
        final sizeKB = entry['sizeKB'] as String;
        final cachedAt = entry['cachedAt'] as String;
        final age = entry['age'] as String;
        final displayUrl = entry['displayUrl'] as String;

        // Compact cached date (show MM/DD HH:MM)
        final compactCached = cachedAt.length >= 16 ? cachedAt.substring(5, 16) : cachedAt;
        final shortCached = compactCached.length > 8 ? compactCached.substring(0, 8) : compactCached;

        // Format the metadata row
        final no = (i + 1).toString().padLeft(2);
        final sizePadded = '${sizeKB.padLeft(4)}K';
        final cachedPadded = shortCached.padRight(8);
        final agePadded = age.padLeft(4);

        // First row: Metadata
        _addLog('│$no │$sizePadded │ $cachedPadded │$agePadded │', CacheLogCategory.system, isSmallText: true);

        // Second row: Image filename/URL with different color
        String truncatedUrl = displayUrl;
        if (truncatedUrl.length > 60) {
          // Show the end (filename) part for images
          truncatedUrl = '...${truncatedUrl.substring(truncatedUrl.length - 57)}';
        }
        _addLog(truncatedUrl, CacheLogCategory.imageKeys, isSmallText: true);
      }

      // Compact table footer with small text
      _addLog('└───┴──────┴─────────┴─────┘', CacheLogCategory.system, isSmallText: true);
      _addLog('', CacheLogCategory.imageKeys);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _warmCriticalEndpoints() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Starting critical cache warming...', CacheLogCategory.system);
      await _warmCacheService.warmCriticalEndpoints();
      _addLog('Critical cache warming completed', CacheLogCategory.success);
    } catch (e) {
      _addLog('Error warming critical endpoints: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _warmSecondaryEndpoints() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Starting secondary cache warming...', CacheLogCategory.system);
      await _warmCacheService.warmSecondaryEndpoints();
      _addLog('Secondary cache warming completed', CacheLogCategory.success);
    } catch (e) {
      _addLog('Error warming secondary endpoints: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _warmSiteSpecificEndpoints() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final siteId = await _warmCacheService.getCurrentSiteId();
      if (siteId != null) {
        _addLog('Starting site-specific cache warming for site: $siteId', CacheLogCategory.system);
        await _warmCacheService.warmSiteSpecificEndpoints(siteId);
        _addLog('Site-specific cache warming completed', CacheLogCategory.success);
      } else {
        _addLog('No site ID found, cannot warm site-specific endpoints', CacheLogCategory.error);
      }
    } catch (e) {
      _addLog('Error warming site-specific endpoints: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _warmAllEndpoints() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final siteId = await _warmCacheService.getCurrentSiteId();
      _addLog('Starting complete cache warming...', CacheLogCategory.system);
      await _warmCacheService.warmAllEndpoints(siteId: siteId);
      _addLog('Complete cache warming finished', CacheLogCategory.success);
    } catch (e) {
      _addLog('Error warming all endpoints: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testTtlMatching() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Testing TTL matching for common endpoints...', CacheLogCategory.system);

      _addLog('=== TTL MATCHING TEST ===', CacheLogCategory.header);

      // Test common endpoints
      final testEndpoints = [
        '/v3/publishers/me/account',
        '/v3/publishers/me/sites',
        '/v3/publishers/me/sites/123/campaigns/featured-summary',
        '/v3/publishers/me/sites/456/campaigns/789/creatives/quicklink',
        '/v3/publishers/me/reports/summary',
        '/v3/publishers/me/notifications',
        '/v3/publishers/me/notifications/123',
        '/v3/publishers/me/vouchers',
        '/v3/publishers/me/voucher-categories',
        '/v3/publishers/banks',
        '/v3/some/unknown/endpoint',
      ];

      for (final endpoint in testEndpoints) {
        // We'll simulate the TTL matching logic here
        // Since we can't directly access the config, we'll show expected values
        int expectedTtl = 3600; // default
        String category = 'Default';

        if (endpoint.contains('/v3/publishers/me/account')) {
          expectedTtl = 86400;
          category = 'Profile (1d)';
        } else if (endpoint.contains('/v3/publishers/me/sites') && !endpoint.contains('campaigns')) {
          expectedTtl = 21600;
          category = 'Sites (6h)';
        } else if (endpoint.contains('/v3/publishers/banks')) {
          expectedTtl = 21600;
          category = 'Banks (6h)';
        } else if (endpoint.contains('campaigns')) {
          expectedTtl = 1800;
          category = 'Campaigns (30m)';
        } else if (endpoint.contains('reports') || endpoint.contains('payment-summary')) {
          expectedTtl = 900;
          category = 'Reports (15m)';
        } else if (endpoint.contains('notifications')) {
          expectedTtl = 300;
          category = 'Notifications (5m)';
        } else if (endpoint.contains('voucher')) {
          expectedTtl = 1800;
          category = 'Vouchers (30m)';
        }

        // Format TTL for display
        String ttlDisplay;
        if (expectedTtl >= 86400) {
          ttlDisplay = '${(expectedTtl / 86400).toStringAsFixed(0)}d';
        } else if (expectedTtl >= 3600) {
          ttlDisplay = '${(expectedTtl / 3600).toStringAsFixed(0)}h';
        } else if (expectedTtl >= 60) {
          ttlDisplay = '${(expectedTtl / 60).toStringAsFixed(0)}m';
        } else {
          ttlDisplay = '${expectedTtl}s';
        }

        // Truncate endpoint for display
        String displayEndpoint = endpoint;
        if (displayEndpoint.length > 45) {
          displayEndpoint = '...${displayEndpoint.substring(displayEndpoint.length - 42)}';
        }

        _addLog(displayEndpoint, CacheLogCategory.keys, isSmallText: true);
        _addLog('  → $category: $ttlDisplay (${expectedTtl}s)', CacheLogCategory.stats, isSmallText: true);
      }

      _addLog('', CacheLogCategory.system);
      _addLog('Note: These are expected TTL values based on configuration.', CacheLogCategory.system,
          isSmallText: true);
      _addLog('Check "Keys" to see actual TTL values for cached endpoints.', CacheLogCategory.system,
          isSmallText: true);
    } catch (e) {
      _addLog('Error: $e', CacheLogCategory.error);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // When used as a tab, we don't need the AppBar
    final bool isInTabView = ModalRoute.of(context)?.settings.name != '/cache-test';

    Widget content = Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4.0), // Reduced padding
            child: Row(
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _testRegularRequest,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Regular'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testCachedRequest,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Cached'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testFreshRequest,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Fresh Request'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _getCacheStats,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Stats'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _getCacheKeys,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Keys'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _getImageCacheStats,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('IMG Stats'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _getImageCacheKeys,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('IMG Keys'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testTtlMatching,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('TTL Test'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _warmCriticalEndpoints,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Warm Critical'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _warmSecondaryEndpoints,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Warm Secondary'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _warmSiteSpecificEndpoints,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Warm Site'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _warmAllEndpoints,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Warm All'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _clearApiCacheWithConfirm,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Clear API'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _clearImageCacheWithConfirm,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Clear IMG'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _clearCacheWithConfirm,
                  style: DebugButtonStyle.debugButtonStyle,
                  child: const Text('Clear All'),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          child: Container(
            color: Colors.black,
            child: ListView.builder(
              padding: const EdgeInsets.only(
                  left: 8.0, right: 8.0, top: 8.0, bottom: 16.0), // Add bottom spacing for consistency
              itemCount: _logs.length,
              reverse: true,
              itemBuilder: (context, index) {
                final reversedIndex = _logs.length - 1 - index;
                final logEntry = _logs[reversedIndex];

                return Text(
                  logEntry.formattedMessage,
                  style: TextStyle(
                    color: logEntry.textColor,
                    fontFamily: 'monospace',
                    fontSize: logEntry.isSmallText ? 9.5 : 14.0, // Better readable size for cache table
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );

    // If we're not in a tab view, wrap with Scaffold
    if (!isInTabView) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Cache Test'),
        ),
        body: content,
      );
    }

    // Otherwise, just return the content
    return content;
  }
}
