// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cache_entry.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CacheEntryAdapter extends TypeAdapter<CacheEntry> {
  @override
  final int typeId = 0;

  @override
  CacheEntry read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CacheEntry(
      data: fields[0] as String,
      headers: (fields[1] as Map).cast<String, dynamic>(),
      createdAt: fields[2] as int,
      ttl: fields[3] as int,
      statusCode: fields[4] as int,
      url: fields[5] as String,
      method: fields[6] as String,
    );
  }

  @override
  void write(BinaryWriter writer, CacheEntry obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.data)
      ..writeByte(1)
      ..write(obj.headers)
      ..writeByte(2)
      ..write(obj.createdAt)
      ..writeByte(3)
      ..write(obj.ttl)
      ..writeByte(4)
      ..write(obj.statusCode)
      ..writeByte(5)
      ..write(obj.url)
      ..writeByte(6)
      ..write(obj.method);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CacheEntryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
