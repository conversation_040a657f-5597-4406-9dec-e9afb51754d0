import 'dart:collection';

import 'package:flutter/material.dart';

class LocaleConstants {
  const LocaleConstants._();

  static final Map<Locale, String> _supportedLocales = {
    const Locale('en'): 'English',
    const Locale('vi'): 'Tiếng Việt',
    const Locale('th'): 'ไทย',
    const Locale('ja'): '日本語',
    const Locale('zh'): '中文',
    const Locale('id'): 'Bahasa',
  };

  static Map<Locale, String> get supportedLocales =>
      UnmodifiableMapView(_supportedLocales);
}
