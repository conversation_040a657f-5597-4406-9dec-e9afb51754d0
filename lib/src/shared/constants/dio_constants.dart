class ResponseMessage {
  static const String success = AppStrings.strSuccess; // success with data
  static const String noContent = AppStrings.strNoContent; // success with no data (no content)
  static const String badRequest = AppStrings.strBadRequestError; // failure, API rejected request
  static const String unauthorised = AppStrings.strUnauthorizedError; // failure, user is not authorised
  static const String forbidden = AppStrings.strForbiddenError; //  failure, API rejected request
  static const String internalServerError = AppStrings.strInternalServerError; // failure, crash in server side
  static const String notFound = AppStrings.strNotFoundError; // failure, crash in server side

  // local status code
  static const String connectTimeout = AppStrings.strTimeoutError;
  static const String cancel = AppStrings.strDefaultError;
  static const String receiveTimeout = AppStrings.strTimeoutError;
  static const String sendTimeout = AppStrings.strTimeoutError;
  static const String cacheError = AppStrings.strCacheError;
  static const String noInternetConnection = AppStrings.strNoInternetError;
  static const String defaultError = AppStrings.strDefaultError;
  static const String connectionError = AppStrings.strDefaultError;
}

class AppStrings {
  static const String strSuccess = 'Success';
  static const String strNoContent = 'No content';
  static const String strBadRequestError = 'Bad request';
  static const String strUnauthorizedError = 'Incorrect password. Please try again';
  static const String strForbiddenError = 'Forbidden';
  static const String strInternalServerError = 'Internal server error';
  static const String strNotFoundError = 'Not found';
  static const String strTimeoutError = 'Timeout error';
  static const String strCacheError = 'Cache error';
  static const String strNoInternetError = 'No internet connection';
  static const String strDefaultError = 'Default error';
}

class ResponseCode {
  static const int success = 200; // success with data
  static const int noContent = 201; // success with no data (no content)
  static const int badRequest = 400; // failure, API rejected request
  static const int unauthorised = 401; // failure, user is not authorised
  static const int forbidden = 403; //  failure, API rejected request
  static const int internalServerError = 500; // failure, crash in server side
  static const int notFound = 404; // failure, not found
  static const int invalidData = 422; // failure, not found

  // local status code
  static const int connectTimeout = -1;
  static const int cancel = -2;
  static const int receiveTimeout = -3;
  static const int sendTimeout = -4;
  static const int cacheError = -5;
  static const int noInternetConnection = -6;
  static const int locationDenied = -7;
  static const int defaultError = -8;
  static const int connectionError = -9;
}

enum DataSource {
  success,
  noContent,
  badRequest,
  forbidden,
  unauthorised,
  notFound,
  internetServerError,
  connectTimeout,
  connectionError,
  cancel,
  receiveTimeout,
  sendTimeout,
  cacheError,
  noInternetConnection,
  defaultError
}