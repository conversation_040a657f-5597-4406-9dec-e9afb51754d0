import 'dart:collection';

import 'package:koc_app/generated/locale_keys.g.dart';

class TextConstants {
  const TextConstants._();

  static final Map<String, String> _trafficRestrictionsTooltips = {
    'Adult, Pornographic, & Violence': 'ADULT_PORNOGRAPHIC_VIOLENCE_TOOLTIP',
    'Brand Bidding': 'BRAND_BIDDING_TOOLTIP',
    'Cashback': 'CASHBACK_TOOLTIP',
    'Coupon & Discount Codes': 'COUPON_DISCOUNT_CODES_TOOLTIP',
    'Deep Linking': 'DEEP_LINKING_TOOLTIP',
    'Direct Linking': 'DIRECT_LINKING_TOOLTIP',
    'Display Ads': 'DISPLAY_ADS_TOOLTIP',
    'Display Banner': 'DISPLAY_BANNER_TOOLTIP',
    'Email Marketing': 'EMAIL_MARKETING_TOOLTIP',
    'Gambling': 'GAMBLING_TOOLTIP',
    'Incentive Traffic / Loyalty': 'INCENTIVE_TRAFFIC_LOYALTY_TOOLTIP',
    'Interstitial': 'INTERSTITIAL_TOOLTIP',
    'Native Ads': 'NATIVE_ADS_TOOLTIP',
    'Pop-Up & Pop-Under': 'POPUP_POP_UNDER_TOOLTIP',
    'Push Notification': 'PUSH_NOTIFICATION_TOOLTIP',
    'Search Engine Marketing': 'SEARCH_ENGINE_MARKETING_TOOLTIP',
    'Self conversion': 'SELF_CONVERSION_TOOLTIP',
    'Social Media Ads': 'SOCIAL_MEDIA_ADS_TOOLTIP',
    'Social Media Platform': 'SOCIAL_MEDIA_PLATFORM_TOOLTIP',
    'Social Messager App': 'SOCIAL_MESSAGER_APP_TOOLTIP',
    'Sub-affiliate Network': 'SUB_AFFILIATE_NETWORK_TOOLTIP'
  };

  static final Map<String, String> _termsDefinitions = {
    "Adult Pornography & Violence":
        LocaleKeys.termsDefinitionAdultPornographyAndViolence,
    "Brand Bidding": LocaleKeys.termsDefinitionBrandBidding,
    "Cashback": LocaleKeys.termsDefinitionCashback,
    "Coupon & Discount Codes": LocaleKeys.termsDefinitionCouponAndDiscountCodes,
    "Deep Linking": LocaleKeys.termsDefinitionDeepLinking,
    "Direct Linking": LocaleKeys.termsDefinitionDirectLinking,
    "Display Ads": LocaleKeys.termsDefinitionDisplayAds,
    "Display Banner": LocaleKeys.termsDefinitionDisplayBanner,
    "Email Marketing": LocaleKeys.termsDefinitionEmailMarketing,
    "Gambling": LocaleKeys.termsDefinitionGambling,
    "Incentive Traffic / Loyalty":
        LocaleKeys.termsDefinitionIncentiveTrafficLoyalty,
    "Interstitial": LocaleKeys.termsDefinitionInterstitial,
    "Native Ads": LocaleKeys.termsDefinitionNativeAds,
    "Pop-Up & Pop-Under": LocaleKeys.termsDefinitionPopUpPopUnder,
    "Push Notification": LocaleKeys.termsDefinitionPushNotification,
    "Search Engine Marketing": LocaleKeys.termsDefinitionSearchEngineMarketing,
    "Self Conversion": LocaleKeys.termsDefinitionSelfConversion,
    "Social Media Ads": LocaleKeys.termsDefinitionSocialMediaAds,
    "Social Media Platform": LocaleKeys.termsDefinitionSocialMediaPlatform,
    "Social Messager App": LocaleKeys.termsDefinitionSocialMessagerApp,
    "Sub-affiliate Network": LocaleKeys.termsDefinitionSubAffiliateNetwork
  };

  static Map<String, String> get trafficRestrictionsTooltips =>
      UnmodifiableMapView(_trafficRestrictionsTooltips);

  static Map<String, String> get termsDefinitions =>
      UnmodifiableMapView(_termsDefinitions);
}
