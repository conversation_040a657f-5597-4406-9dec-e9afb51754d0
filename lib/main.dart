import 'dart:async';
import 'dart:developer' as developer;
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/app/my_app.dart';
import 'package:koc_app/src/modules/main_module.dart';
import 'package:koc_app/src/shared/cache/cache.dart';
import 'package:koc_app/src/shared/constants/locale_constants.dart';

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    await EasyLocalization.ensureInitialized().timeout(
      const Duration(seconds: 10),
      onTimeout: () {
        developer.log('EasyLocalization initialization timeout');
      },
    );

    await dotenv.load(fileName: '.env').timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        developer.log('Dotenv loading timeout');
      },
    );

    await _initializeCacheWithRetry();

    Modular.setInitialRoute('/');

    runApp(
      ModularApp(
        module: MainModule(),
        child: EasyLocalization(
            supportedLocales: LocaleConstants.supportedLocales.keys.toList(),
            path: 'assets/translations',
            fallbackLocale: LocaleConstants.supportedLocales.keys.first,
            child: const MyApp()),
      ),
    );

    _warmCriticalCacheAfterInit();
  } catch (e, stackTrace) {
    developer.log('Main initialization error: $e', stackTrace: stackTrace);
  }
}

/// Initialize cache with retry mechanism and timeout
Future<void> _initializeCacheWithRetry() async {
  int retryCount = 0;
  const maxRetries = 3;
  const retryDelay = Duration(seconds: 2);

  while (retryCount < maxRetries) {
    try {
      developer.log('Attempting cache initialization (attempt ${retryCount + 1}/$maxRetries)');

      await UnifiedCacheManager().init().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw TimeoutException('Cache initialization timeout', const Duration(seconds: 15));
        },
      );

      developer.log('Cache initialization successful');
      return;
    } catch (e) {
      retryCount++;
      developer.log('Cache initialization failed (attempt $retryCount): $e');

      if (retryCount >= maxRetries) {
        developer.log('Cache initialization failed after $maxRetries attempts, continuing without cache');
        return;
      }

      await Future.delayed(retryDelay);
    }
  }
}

/// Warm critical cache endpoints after app initialization
void _warmCriticalCacheAfterInit() {
  Future.microtask(() async {
    await Future.delayed(const Duration(milliseconds: 500));

    try {
      developer.log('Starting cache warming after app initialization...');

      await WarmCacheService().warmCriticalEndpoints().timeout(
        const Duration(seconds: 8),
        onTimeout: () {
          developer.log('⚠️ Cache warming timeout - continuing anyway');
        },
      );

      developer.log('Cache warming completed');
    } catch (e) {
      developer.log('❌ Cache warming failed: $e');
    }
  });
}
