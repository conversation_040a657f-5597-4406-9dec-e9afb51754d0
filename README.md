# koc_app

A koc mobile app project

## About Dependencies

- cached_network_image : Read image from network then use cache
- dio : Http requests for communication with the backend
- easy_localization : Multiple languages
- equatable : Comparing objects.
- flutter_bloc : Bloc design pattern
- flutter_dotenv : Handling for environment file
- flutter_native_splash : Native splash screen generation
- flutter_screenutil : Handling screen resolution for multiple devices
- freezed_annotation : Model code generator, Details [here](https://pub.dev/packages/freezed)
- lazy_load_indexed_stack : For handling tab view with lazy loading
- url_launcher : Open URL by external mobile browser
- visibility_detector : Visibility checker of widgets

## About Environment

We are using `flutter_dotenv` package
Download target environment `.env` file from S3 and replace it when build

## Project Structure Skeleton

```bash
lib/
├── main.dart
├── app/
│   ├── app_cubit.dart
│   ├── app_state.dart
│   └── app_theme.dart
├── modules/
│   └── authentication/
│       ├── models/
│       │   └── user.dart
│       ├── repository/
│       │   └── authentication_repository.dart
│       ├── cubit/
│       │   ├── authentication_cubit.dart
│       │   └── authentication_state.dart
│       └── presentation/
│           ├── screens/
│           │   ├── sign_in_page.dart
│           │   └── sign_up_page.dart
│           └── widgets/
│               └── login_widget.dart
└── shared/
    ├── constants.dart
    ├── utils.dart
    └── widgets/
        └── custom_button.dart

```

- `app/`: Contains settings and management code that applies to the entire application.
  - `app_cubit.dart`: A global cubit for managing the overall state of the application.
  - `app_theme.dart`: Manages the theme settings of the application, ensuring consistency in UI design elements.
- `modules/`: A directory for each functional module, grouping related code and resources.
- `shared/`: Contains common code and resources that are shared across multiple modules.
  - `constants.dart`: Defines constant values used throughout the application.
  - `utils.dart`: Contains various utility functions (e.g., data transformation, formatting).
  - `widgets/`: Contains common widgets that can be reused across multiple modules, e.g., custom_button.dart, which defines a custom button widget.
