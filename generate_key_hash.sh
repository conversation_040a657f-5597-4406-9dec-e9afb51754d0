#!/bin/bash

# Check if keytool is available
if ! command -v keytool &> /dev/null; then
    echo "Error: keytool command not found. Make sure Java is installed and in your PATH."
    exit 1
fi

# Check if openssl is available
if ! command -v openssl &> /dev/null; then
    echo "Error: openssl command not found. Please install openssl."
    exit 1
fi

# Default keystore paths
DEBUG_KEYSTORE="android/app/debug.keystore"
RELEASE_KEYSTORE="android/release.keystore"

echo "Facebook Key Hash Generator for Android"
echo "======================================="
echo

# Function to generate key hash
generate_key_hash() {
    local keystore_path=$1
    local keystore_password=$2
    local key_alias=$3
    
    echo "Generating key hash for: $keystore_path"
    
    if [ ! -f "$keystore_path" ]; then
        echo "Error: Keystore file not found at $keystore_path"
        return 1
    fi
    
    # Extract the certificate and generate the hash
    keytool -exportcert -alias "$key_alias" -keystore "$keystore_path" -storepass "$keystore_password" | openssl sha1 -binary | openssl base64
    
    if [ $? -ne 0 ]; then
        echo "Error generating key hash. Check your keystore path, password, and alias."
        return 1
    fi
}

# Generate debug key hash
echo "Generating Debug Key Hash:"
generate_key_hash "$DEBUG_KEYSTORE" "android" "androiddebugkey"
echo

# Ask if user wants to generate release key hash
read -p "Do you want to generate a release key hash? (y/n): " generate_release

if [ "$generate_release" = "y" ] || [ "$generate_release" = "Y" ]; then
    # Ask for release keystore details
    read -p "Enter release keystore path (default: $RELEASE_KEYSTORE): " input_release_path
    release_path=${input_release_path:-$RELEASE_KEYSTORE}
    
    read -p "Enter release keystore password: " release_password
    read -p "Enter release key alias: " release_alias
    
    echo
    echo "Generating Release Key Hash:"
    generate_key_hash "$release_path" "$release_password" "$release_alias"
fi

echo
echo "Add these key hashes to your Facebook Developer Console:"
echo "https://developers.facebook.com/apps/493145729987242/settings/basic/"
echo
echo "Instructions:"
echo "1. Go to the Facebook Developer Console"
echo "2. Select your app"
echo "3. Go to Settings > Basic"
echo "4. Scroll down to 'Android' section"
echo "5. Add the key hash(es) generated above"
