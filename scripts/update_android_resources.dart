import 'dart:io';

/// A simple script to update Android string resources with values from .env file
/// Run this script with: dart scripts/update_android_resources.dart
void main() async {
  // Read the .env file
  final envFile = File('.env');
  if (!await envFile.exists()) {
    print('Error: .env file not found');
    exit(1);
  }

  final envContent = await envFile.readAsString();
  final envLines = envContent.split('\n');

  // Extract Facebook & Google values from .env
  String? facebookAppId;
  String? facebookClientToken;
  String? googleWebClientId;

  for (final line in envLines) {
    if (line.trim().startsWith('#') || line.trim().isEmpty) continue;

    final parts = line.split('=');
    if (parts.length < 2) continue;
    final key = parts[0].trim();
    final value = parts.sublist(1).join('=').trim();

    if (key == 'FACEBOOK_APP_ID') {
      facebookAppId = value;
    } else if (key == 'FACEBOOK_CLIENT_TOKEN') {
      facebookClientToken = value;
    } else if (key == 'GOOGLE_CLIENT_ID_WEB') { // Assuming GOOGLE_CLIENT_ID_WEB is for the strings.xml google_web_client_id
      googleWebClientId = value;
    }
  }
  
  if (facebookAppId == null || facebookClientToken == null) {
    print('Warning: Facebook App ID or Client Token not found in .env file. Skipping Facebook update.');
  }
  if (googleWebClientId == null) {
    print('Warning: GOOGLE_CLIENT_ID_WEB (for google_web_client_id string) not found in .env file. Skipping Google update.');
  }

  // Read the strings.xml file
  final stringsFile = File('android/app/src/main/res/values/strings.xml');
  if (!await stringsFile.exists()) {
    print('Error: strings.xml file not found');
    exit(1);
  }

  final stringsContent = await stringsFile.readAsString();

  // Update values in strings.xml
  var updatedContent = stringsContent;
  bool facebookUpdated = false;
  bool googleUpdated = false;

  // Update Facebook App ID
  if (facebookAppId != null) {
    final appIdRegex = RegExp(r'<string name="facebook_app_id">([^<]*)</string>');
    if (appIdRegex.hasMatch(updatedContent)) {
      updatedContent = updatedContent.replaceAll(appIdRegex, '<string name="facebook_app_id">$facebookAppId</string>');
      facebookUpdated = true;
    }

    // Update Facebook Login Protocol Scheme
    final schemeRegex = RegExp(r'<string name="fb_login_protocol_scheme">([^<]*)</string>');
    if (schemeRegex.hasMatch(updatedContent)) {
      updatedContent =
          updatedContent.replaceAll(schemeRegex, '<string name="fb_login_protocol_scheme">fb$facebookAppId</string>');
      facebookUpdated = true;
    }
  }

  // Update Facebook Client Token
  if (facebookClientToken != null) {
    final tokenRegex = RegExp(r'<string name="facebook_client_token">([^<]*)</string>');
    if (tokenRegex.hasMatch(updatedContent)) {
      updatedContent =
          updatedContent.replaceAll(tokenRegex, '<string name="facebook_client_token">$facebookClientToken</string>');
      facebookUpdated = true;
    }
  }
  
  // Update Google Web Client ID
  if (googleWebClientId != null) {
    final googleWebClientIdRegex = RegExp(r'<string name="google_web_client_id">([^<]*)</string>');
    if (googleWebClientIdRegex.hasMatch(updatedContent)) {
      updatedContent = updatedContent.replaceAll(googleWebClientIdRegex, '<string name="google_web_client_id">$googleWebClientId</string>');
      googleUpdated = true;
    }
  }

  // Write the updated content back to strings.xml
  if (facebookUpdated || googleUpdated) {
    await stringsFile.writeAsString(updatedContent);
    if (facebookUpdated) {
      print('Successfully updated Android resources with Facebook values from .env');
    }
    if (googleUpdated) {
      print('Successfully updated Android resources with Google values from .env');
    }
  } else {
    print('No Android resources updated.');
  }
}
